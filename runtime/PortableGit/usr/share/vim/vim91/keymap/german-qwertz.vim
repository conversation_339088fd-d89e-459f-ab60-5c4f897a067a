" Vim Keymap file for German standard keyboard layout (without AltGr keys as
" they don't work reliably in all version of vim)

" Maintainer:   <PERSON><PERSON><PERSON> <<EMAIL>>
" Last Changed: 2020-07-18

scriptencoding utf-8

let b:keymap_name = "de"

loadkeymap
`` ^ ASCII CIRCUMFLEX
`e ê LATIN SMALL LETTER E WITH CIRCUMFLEX
`u û LATIN SMALL LETTER U WITH CIRCUMFLEX
`i î LATIN SMALL LETTER I WITH CIRCUMFLEX
`o ô LATIN SMALL LETTER O WITH CIRCUMFLEX
`a â LATIN SMALL LETTER A WITH CIRCUMFLEX
`E Ê LATIN CAPITAL LETTER E WITH CIRCUMFLEX
`U Û LATIN CAPITAL LETTER U WITH CIRCUMFLEX
`I Î LATIN CAPITAL LETTER I WITH CIRCUMFLEX
`O Ô LATIN CAPITAL LETTER O WITH CIRCUMFLEX
`A Â LATIN CAPITAL LETTER A WITH CIRCUMFLEX
- ß LATIN SMALL LETTER SZ
== ´ ASCII ACUTE ACCENT
=e é LATIN SMALL LETTER E WITH ACUTE
=u ú LATIN SMALL LETTER U WITH ACUTE
=i í LATIN SMALL LETTER I WITH ACUTE
=o ó LATIN SMALL LETTER O WITH ACUTE
=a á LATIN SMALL LETTER A WITH ACUTE
=z ý LATIN SMALL LETTER Y WITH ACUTE
=E É LATIN CAPITAL LETTER E WITH ACUTE
=U Ú LATIN CAPITAL LETTER U WITH ACUTE
=I Í LATIN CAPITAL LETTER I WITH ACUTE
=O Ó LATIN CAPITAL LETTER O WITH ACUTE
=A Á LATIN CAPITAL LETTER A WITH ACUTE
=Z Ý LATIN CAPITAL LETTER Y WITH ACUTE
=S ẞ LATIN CAPITAL LETTER SZ
++ ` ASCII GRAVE
+e è LATIN SMALL LETTER E WITH GRAVE
+u ù LATIN SMALL LETTER U WITH GRAVE
+i ì LATIN SMALL LETTER I WITH GRAVE
+o ò LATIN SMALL LETTER O WITH GRAVE
+a à LATIN SMALL LETTER A WITH GRAVE
+E È LATIN CAPITAL LETTER E WITH GRAVE
+U Ù LATIN CAPITAL LETTER U WITH GRAVE
+I Ì LATIN CAPITAL LETTER I WITH GRAVE
+O Ò LATIN CAPITAL LETTER O WITH GRAVE
+A À LATIN CAPITAL LETTER A WITH GRAVE
y z LATIN SMALL LETTER Z
[ ü LATIN SMALL LETTER U WITH UMLAUT
] + ASCII PLUS
\\ # ASCII NUMBER SIGN
; ö LATIN SMALL LETTER O WITH UMLAUT
' ä LATIN SMALL LETTER A WITH UMLAUT
z y LATIN SMALL LETTER Y
/ - ASCII MINUS
~ ° DEGREE SIGN
@ " ASCII DOUBLE QUOTES
# § SECTION SIGN
^ & ASCII AMPERSAND
& / ASCII SLASH
* ( ASCII LEFT PARENTHESIS
( ) ASCII RIGHT PARENTHESIS
) = ASCII EQUAL SIGN
_ ? ASCII QUESTION MARK
+ ` ASCII GRAVE
Y Z LATIN CAPITAL LETTER Z
{ Ü LATIN CAPITAL LETTER U WITH UMLAUT
} * ASCII ASTERISK
| ' ASCII SINGLE QUOTE
: Ö LATIN CAPITAL LETTER O WITH UMLAUT
\" Ä LATIN CAPITAL LETTER A WITH UMLAUT
Z Y LATIN CAPITAL LETTER Y
< ; ASCII SEMICOLON 
> : ASCII COLON 
? _ ASCII UNDERSCORE
