" Vim Keymap file for Brazilian Portuguese - ABNT Compact (for keyboards
" with less keys and without altgr because that would be ABNT2)

" Maintainer:   Elsarques
" Last Changed: 2025-04-06

scriptencoding utf-8

let b:keymap_name = "abnt-compact"

loadkeymap
[[ ´ LATIN ACUTE ACCENT
[a á LATIN SMALL LETTER A WITH ACUTE
[e é LATIN SMALL LETTER E WITH ACUTE
[u ú LATIN SMALL LETTER U WITH ACUTE
[i í LATIN SMALL LETTER I WITH ACUTE
[o ó LATIN SMALL LETTER O WITH ACUTE
[A Á LATIN CAPITAL LETTER A WITH ACUTE
[E É LATIN CAPITAL LETTER E WITH ACUTE
[U Ú LATIN CAPITAL LETTER U WITH ACUTE
[I Í LATIN CAPITAL LETTER I WITH ACUTE
[O Ó LATIN CAPITAL LETTER O WITH ACUTE
{{ ` GRAVE
{e è LATIN SMALL LETTER E WITH GRAVE
{a à LATIN SMALL LETTER A WITH GRAVE
{u ù LATIN SMALL LETTER U WITH GRAVE
{i ì LATIN SMALL LETTER I WITH GRAVE
{o ò LATIN SMALL LETTER O WITH GRAVE
{u ù LATIN SMALL LETTER U WITH GRAVE
{E È LATIN CAPITAL LETTER E WITH GRAVE
{A À LATIN CAPITAL LETTER A WITH GRAVE
{U Ù LATIN CAPITAL LETTER U WITH GRAVE
{I Ì LATIN CAPITAL LETTER I WITH GRAVE
{O Ò LATIN CAPITAL LETTER O WITH GRAVE
{U Ù LATIN CAPITAL LETTER U WITH GRAVE
] [ ASCII OPENING BRACKETS
} { ASCII OPENING CURLY BRACKETS
; ç LATIN SMALL LETTER C WITH CEDILLA
: Ç LATIN CAPITAL LETTER C WITH CEDILLA
' ~ ASCII TILDE
'a ã LATIN SMALL LETTER A WITH TILDE
'e ẽ LATIN SMALL LETTER E WITH TILDE
'i ĩ LATIN SMALL LETTER I WITH TILDE
'o õ LATIN SMALL LETTER O WITH TILDE
'u ũ LATIN SMALL LETTER U WITH TILDE
'A Ã LATIN CAPITAL LETTER A WITH TILDE
'E Ẽ LATIN CAPITAL LETTER E WITH TILDE
'I Ĩ LATIN CAPITAL LETTER I WITH TILDE
'O Õ LATIN CAPITAL LETTER O WITH TILDE
'U Ũ LATIN CAPITAL LETTER U WITH TILDE
[- \\ ASCII BACKSLASH
[= / ASCII SLASH
\" ^ ASCII CIRCUMFLEX
\"a â LATIN SMALL LETTER A WITH CIRCUMFLEX
\"e ê LATIN SMALL LETTER E WITH CIRCUMFLEX
\"i î LATIN SMALL LETTER I WITH CIRCUMFLEX
\"o ô LATIN SMALL LETTER O WITH CIRCUMFLEX
\"u û LATIN SMALL LETTER U WITH CIRCUMFLEX
\"A Â LATIN CAPITAL LETTER A WITH CIRCUMFLEX
\"E Ê LATIN CAPITAL LETTER E WITH CIRCUMFLEX
\"I Î LATIN CAPITAL LETTER I WITH CIRCUMFLEX
\"O Ô LATIN CAPITAL LETTER O WITH CIRCUMFLEX
\"U Û LATIN CAPITAL LETTER U WITH CIRCUMFLEX
\"- | ASCII VERTICAL-BAR
\\ ] ASCII CLOSING BRACKETS
| } ASCII CLOSING CURLY BRACKETS
/ ; ASCII SEMICOLON
? : ASCII COLON
~ " ASCII DOUBLE QUOTES
'- ? ASCII EXCLAMATION
'= | ASCII VERTICAL SLASH
` ' ASCII SINGLE QUOTE
