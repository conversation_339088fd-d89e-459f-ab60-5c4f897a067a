" Vim <PERSON> file for hebrew
" Maintainer  : <PERSON> <<EMAIL>>
" Last Updated: Sun 10 Feb 2002 11:50:56
" This is my version of a phonetic Hebrew

" Use this short name in the status line.
let b:keymap_name = "hebp"

loadkeymap
K	<char-234>	" final kaf
M	<char-237>	" final mem
N	<char-239>	" final nun
P	<char-243>	" final pe
T	<char-232>	" tet
X	<char-245>	" final tsadi
a	<char-224>	" alef
b	<char-225>	" bet
d	<char-227>	" dalet
g	<char-226>	" gimel
h	<char-228>	" he
j	<char-231>	" het
k	<char-235>	" kaf
l	<char-236>	" lamed
m	<char-238>	" mem
n	<char-240>	" nun
s	<char-241>	" samekh
p	<char-244>	" pe
q	<char-247>	" qof
r	<char-248>	" resh
t	<char-250>	" tav
u	<char-242>	" ayin
v	<char-229>	" vav
w	<char-249>	" shin
x	<char-246>	" tsadi
y	<char-233>	" yod
z	<char-230>	" zayin
