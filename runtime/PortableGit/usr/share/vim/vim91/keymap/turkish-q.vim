" Vim keymap file for Turkish Q layout
" Maintainer: <PERSON>ir SARI <<EMAIL>>
" Last changed: 5 August 2020

scriptencoding utf-8
let b:keymap_name = "tr-q"
loadkeymap

\> Ç LATIN CAPITAL LETTER C WITH CEDILLA
\{ Ğ LATIN CAPITAL LETTER G WITH BREVE
\" İ LATIN CAPITAL LETTER I WITH DOT ABOVE
\< Ö LATIN CAPITAL LETTER O WITH DIAERESIS
\: Ş LATIN CAPITAL LETTER S WITH CEDILLA
\} Ü LATIN CAPITAL LETTER U WITH DIAERESIS

± é LATIN SMALL LETTER E WITH ACUTE
@ ' APOSTROPHE
# ^ CIRCUMFLEX ACCENT
$ + PLUS SIGN
^ & AMPERSAND
& / SOLIDUS
* ( LEFT PARENTHESIS
( ) RIGHT PARENTHESIS
) = EQUAL SIGN
_ ? QUESTION MARK
+ _ LOW LINE
~ > GREATER-THAN SIGN
| ; SEMICOLON
? : COLON

\. ç LATIN SMALL LETTER C WITH CEDILLA
\[ ğ LATIN SMALL LETTER G WITH BREVE
\' i LATIN SMALL LETTER I
\, ö LATIN SMALL LETTER O WITH DIAERESIS
\; ş LATIN SMALL LETTER S WITH CEDILLA
\] ü LATIN SMALL LETTER U WITH DIAERESIS
i ı LATIN SMALL LETTER DOTLESS I

§ " QUOTATION MARK
- * ASTERISK
= - HYPHEN-MINUS
\\ , COMMA
\/ . FULL STOP
` < LESS-THAN SIGN

™ £ POUND SIGN
£ # NUMBER SIGN
¢ $ DOLLAR SIGN
∞ ½ VULGAR FRACTION ONE HALF
¶ { LEFT CURLY BRACKET
• [ LEFT SQUARE BRACKET
ª ] RIGHT SQUARE BRACKET
º } RIGHT CURLY BRACKET
– \\ REVERSE SOLIDUS
≠ | VERTICAL LINE
œ @ COMMERCIAL AT
´ € EURO SIGN
† ₺ TURKISH LIRA SIGN
¥ ¥ YEN SIGN
‘ ~ TILDE
… ´ ACUTE ACCENT
« ` GRAVE ACCENT
