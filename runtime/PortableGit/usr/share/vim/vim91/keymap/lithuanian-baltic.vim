" Vim <PERSON>ma<PERSON> file for lithuanian characters, phonetic layout 'Baltic'
" Useful mainly with utf-8 but may work with other encodings

" Maintainer:   <PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@kemek.lt>
" Last Changed: 2002 Apr 12

" All characters are given literally, conversion to another encoding (e.g.,
" UTF-8) should work.
scriptencoding utf-8

let b:keymap_name = "lt"

loadkeymap
!	Ą	LITHUANIAN CAPITAL A NOSINE
@	Č	LITHUANIAN CAPITAL CH
#	Ę	LITHUANIAN CAPITAL E NOSINE
$	Ė	LITHUANIAN CAPITAL E SU TASKU
%	Į	LITHUANIAN CAPITAL I NOSINE
^	Š	LITHUANIAN CAPITAL SH
&	Ų	LITHUANIAN CAPITAL U NOSINE
*	Ū	LITHUANIAN CAPITAL U SU BRUKSNIU
+	Ž	LITHUANIAN CAPITAL ZH
1	ą	LITHUANIAN LOWERCASE A NOSINE
2	č	LITHUANIAN LOWERCASE CH
3	ę	LITHUANIAN LOWERCASE E NOSINE
4	ė	LITHUANIAN LOWERCASE E SU TASKU
5	į	LITHUANIAN LOWERCASE I NOSINE
6	š	LITHUANIAN LOWERCASE SH
7	ų	LITHUANIAN LOWERCASE U NOSINE
8	ū	LITHUANIAN LOWERCASE U SU BRUKSNIU
=	ž	LITHUANIAN LOWERCASE ZH
