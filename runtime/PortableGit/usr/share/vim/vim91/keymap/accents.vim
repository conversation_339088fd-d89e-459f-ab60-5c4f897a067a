" Vim Keymap file for latin1 accents through dead characters
" Maintainer:	The Vim Project <https://github.com/vim/vim>
" Last Change:	2023 Aug 13

" All characters are given literally, conversion to another encoding (e.g.,
" UTF-8) should work.
scriptencoding latin1

" Use this short name in the status line.
let b:keymap_name = "acc"

loadkeymap
`A	�
'A	�
�A	�
^A	�
~A	�
:A	�
'C	�
�C	�
`E	�
'E	�
�E	�
^E	�
`I	�
'I	�
�I	�
^I	�
~N	�
`O	�
'O	�
�O	�
^O	�
~O	�
:O	�
`U	�
'U	�
�U	�
^U	�
:U	�
'Y	�
�Y	�
`a	�
'a	�
�a	�
^a	�
~a	�
:a	�
'c	�
�c	�
`e	�
'e	�
�e	�
^e	�
`i	�
'i	�
�i	�
^i	�
~n	�
`o	�
'o	�
�o	�
^o	�
~o	�
:o	�
`u	�
'u	�
�u	�
^u	�
:u	�
'y	�
�y	�
''	'
��	�
``	`
^^	^
~~	~
::	:
