" Vim <PERSON> file for Croatian characters, classical variant, iso-8859-2 encoding
" 
" Maintainer:   <PERSON> <<EMAIL>>
" Last Changed: 2007 Oct 14

scriptencoding iso-8859-2

let b:keymap_name = "croatian-iso-8859-2"
" Uncomment line below if you prefer short name
"let b:keymap_name = "hr-iso-8859-2"

loadkeymap
" swap y and z, not important
z	y
Z	Y
y	z
Y	Z

" s<
[	�
" S<
{	�
" D/
}	�
" d/
]	�
" c<
;	�
" c'
'	�
" C<
:	�
" C'
"	�
" z<
\	�
" Z<
|	�
�	|
�	@
�	\
�	�
�	�
�	�
�	�
�	�
�	�
�	�
�	{
�	}
�	[
�	]
@	"
^	&
&	/
*	(
(	)
)	=
_	?
-	'
=	+
+	*
/	-
<	;
>	:
?	_
�	~
�	�
�	�
�	�
�	^
�	�
�	�
�	`
�	�
�	�
�	�

" you still want to be able to type <, >
�	<
�	>

`	�
�	�
