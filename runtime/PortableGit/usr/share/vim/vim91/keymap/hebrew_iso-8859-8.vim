" Vim <PERSON> file for hebrew
" Maintainer  : <PERSON> <<EMAIL>>
" Last Updated: Wed 28 Feb 2001 21:28:51
" This is the standard Israeli keyboard layout

" Use this short name in the status line.
let b:keymap_name = "heb"

loadkeymap
a	<char-249>	" � - shin
b	<char-240>	" � - nun
c	<char-225>	" � - bet
d	<char-226>	" � - gimel
e	<char-247>	" � - qof
f	<char-235>	" � - kaf
g	<char-242>	" � - ayin
h	<char-233>	" � - yod
i	<char-239>	" � - final nun
j	<char-231>	" � - het
k	<char-236>	" � - lamed
l	<char-234>	" � - final kaf
m	<char-246>	" � - tsadi
n	<char-238>	" � - mem
o	<char-237>	" � - final mem
p	<char-244>	" � - pe
q	/		" / - slash
r	<char-248>	" � - resh
s	<char-227>	" � - dalet
t	<char-224>	" � - alef
u	<char-229>	" � - vav
v	<char-228>	" � - he
w	'		" ' - single-quote
x	<char-241>	" � - samekh
y	<char-232>	" � - tet
z	<char-230>	" � - zay<PERSON>
,	<char-250>	" � - tav
.	<char-245>	" � - final tsadi
;	<char-243>	" � - final pe
'	,		" , - comma
/	.		" . - period
`	;		" ; - semicolon
