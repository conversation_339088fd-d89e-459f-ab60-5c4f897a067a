Some tools that can be used with Vim:

blink.c:	C program to make the cursor blink in an xterm.

ccfilter*:	C program to filter the output of a few compilers to a common
		QuickFix format.

efm_filter.*:	Perl script to filter compiler messages to QuickFix format.

efm_perl.pl:	Perl script to filter error messages from the Perl interpreter
		for use with Vim quickfix mode.

mve.*		Awk script to filter error messages to QuickFix format.

pltags.pl:	Perl script to create a tags file from Perl scripts.

ref:		Shell script for the K command.

shtags.*:	Perl script to create a tags file from a shell script.

vim132:		Shell script to edit in 132 column mode on vt100 compatible
		terminals.

vimm:		Shell script to start Vim on a DEC terminal with mouse
		enabled.

vimspell.*:	Shell script for highlighting spelling mistakes.

vim_vs_net.cmd: MS-Windows command file to use Vim with MS Visual Studio 7 and
		later.

xcmdsrv_client.c:  Example for a client program that communicates with a Vim
		   server through the X-Windows interface.

unicode.vim	Vim script to generate tables for src/mbyte.c.

[xxd can be found in the src directory]
