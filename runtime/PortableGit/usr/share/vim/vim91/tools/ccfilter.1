.TH ccfilter 1 "01-Apr-97"
.SH NAME
ccfilter \- a compiler's output filter for vim quickfix
.SH SYNOPSIS
ccfilter [
.B <options>
]
.SH DESCRIPTION
The ccfilter utility "filters" the output of several compilers
and makers (make/gmake) from several platforms (see NOTES below)
to a standardized format which easily fits in vim's quickfix
feature. For further details, see in vim ":help quickfix".
.PP
ccfilter reads
.B 'stdin'
and outputs to
.B 'stdout'
\.
.PP
The need for ccfilter is clear, as some compilers have irregular
and/or multiple line error messages (with the relevant information on
line 2), which makes it impossible for the errorformat to correctly
display them !

When working on different platforms, and with different compilers,
ccfilter eases the utilization of quickfix, due to its standardized
output, allowing to have in .vimrc a plain
.br
.B \ \ \ \ :set\ errorformat=%f:%l:%c:%t:%m

.SH USAGE
When using ccfilter, one would include the following lines in .vimrc:
.br
.B \ \ \ \ :set shellpipe=\\\\|&ccfilter\\\\>
.br
.B \ \ \ \ :set errorformat=%f:%l:%c:%t:%m

.SH OPTIONS
.TP 16
-c
Decrement column by one. This may be needed, depending on
the compiler being used.
.TP
-r
Decrement row by one.  This may be needed, depending on
the compiler being used.
.TP
-v
Verbose (Outputs also invalid lines).
This option makes ccfilter output also the lines that
couldn't be correctly parsed. This is used mostly for
ccfilter debugging.
.TP
-o <COMPILER>
Treat input as <COMPILER>'s output.
Even when configuring ccfilter to assume a default
COMPILER, sometimes it's helpful to be able to specify
the COMPILER used to generate ccfilter's input.
For example, when cross-compiling on a network from a
single machine.
.TP
-h
Shows a brief help, describing the configured default COMPILER
and the valid parameters for COMPILER.

.SH NOTES
Currently, ccfilter accepts output from several compilers, as
described below:
.TP 10
GCC
GCC compiler
.TP
AIX
AIX's C compiler
.TP
ATT
AT&T/NCR's High Performance C Compiler
.TP
IRIX
IRIX's MIPS/MIPSpro C compiler
.TP
SOLARIS
SOLARIS's SparcWorks C compiler
.TP
HPUX
HPUX's C compiler

.SH AUTHOR
.B ccfilter
was developed by
.B Pablo Ariel Kohan
.BR
.B mailto:<EMAIL>
