" Menu Translations:    Simplified Chinese
" Maintainer:           <PERSON><PERSON> <<EMAIL>>
" Previous Maintainer:  <PERSON><PERSON><PERSON> <<EMAIL>>
" Last Change:          2019-09-09

" This causes trouble for a broken iconv (symptom: last character is always
" ??).  Without this it works fine anyway, because gbk/cp936 is a superset of
" gb2312. (suggested by <PERSON>)
" scriptencoding gb2312

" As mentioned above, gbk/cp936 is a superset of (and backward compatible with)
" gb2312, then source the translation encoded in cp936 should be ok. -- Shun
source <sfile>:p:h/menu_zh_cn.cp936.vim
