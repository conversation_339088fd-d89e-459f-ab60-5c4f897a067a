Article 2846 of alt.sources:
Path: oce-rd1!hp4nl!mcsun!uunet!munnari.oz.au!metro!otc!gregm
From: <EMAIL> (<PERSON>)
Newsgroups: alt.sources
Subject: VI SOLVES MAZE (commented macros)
Message-ID: <<EMAIL>>
Date: 10 Feb 91 23:31:02 GMT
Sender: <EMAIL>
Reply-To: <EMAIL> (<PERSON>)
Organization: OTC Development Unit, Australia
Lines: 464

Submitted-by: <EMAIL>
Archive-name: maze_solving_vi_macros

A real working model. See it walk the maze in front of your very own eyes.

To prove that you can do anything in vi, I wrote a couple of macros that
allows vi to solve mazes. It will solve any maze produced by maze.c
that was posted to the alt.sources last month. (Maze.c is also included
in this posting as well as an example of its output.)

The uncommented version of the macros was sent to alt.sources last month.
However, so many people mailed me requesting the commented version of the
macros that I decided to post it. I have made some modifications to the
original macros to make them easier to follow and also after I learnt
that you can escape the special meaning of '|' in macros by using '^V|'.

Save this article and unshar it. Then read maze.README.

After studying these macros, anyone who cannot write an emacs emulator
in vi macros should just curl up and :q!.

Coming soon to a newsgroup near you: "Vi macros solve Tower of Hanoi",
and a repost of the original "Turing Machine implemented in Vi macros"

Anyone who has a version of these macros for edlin or nroff, please post.
