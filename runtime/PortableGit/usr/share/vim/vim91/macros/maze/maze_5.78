._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._._
| ._| . . ._| | |_._._. . ._|_._._._._. ._|_. ._|_._. ._| . ._|_. | . ._._. |
| ._|_| |_. | | | | ._._|_._|_._. . |_. | | | ._._| |_._._| | ._. ._| . . |_|
|_._._._. | ._|_. ._._._. | | ._. |_._. . | ._._| |_. | ._._._. |_. | |_|_| |
| | . |_._| . ._._._| ._._. ._._| | | |_| . | |_. . ._|_| ._._. |_._|_| . | |
|_._|_._._._|_._._._|_|_._._._|_._|_._._._|_._._._|_._._._|_._._._._._._|_._|

See Vim solve a maze!

   type ":so maze_mac<RETURN>" to load the macros

   type "g" to start

to interrupt type "<CTRL-C>"
     to quit type ":q!<RETURN>"

