Article 2913 of alt.sources:
Path: oce-rd1!hp4nl!mcsun!uunet!munnari.oz.au!metro!cluster!swift!softway!otc!gregm
From: <EMAIL> (<PERSON>)
Newsgroups: comp.sources.d,alt.sources,comp.editors
Subject: VI SOLVES HANOI
Message-ID: <<EMAIL>>
Date: 19 Feb 91 01:32:14 GMT
Sender: <EMAIL>
Reply-To: <EMAIL> (<PERSON>)
Organization: OTC Development Unit, Australia
Lines: 80
Xref: oce-rd1 comp.sources.d:5702 alt.sources:2913 comp.editors:2313

Submitted-by: <EMAIL>
Archive-name: hanoi.vi.macros/part01

Everyone seems to be writing stupid Tower of Hanoi programs.
Well, here is the stupidest of them all: the hanoi solving vi macros.

Save this article, unshar it, and run uudecode on hanoi.vi.macros.uu.
This will give you the macro file hanoi.vi.macros.
Then run vi (with no file: just type "vi") and type:
	:so hanoi.vi.macros
	g
and watch it go.

The default height of the tower is 7 but can be easily changed by editing
the macro file.

The disks aren't actually shown in this version, only numbers representing
each disk, but I believe it is possible to write some macros to show the
disks moving about as well. Any takers?

(For maze solving macros, see alt.sources or comp.editors)

Greg
