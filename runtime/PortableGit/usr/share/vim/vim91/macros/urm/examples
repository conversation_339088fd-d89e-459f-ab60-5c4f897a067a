Note that enough temporary registers should be provided for each example.
All should be initialised to 0.

Initial register values for benchmarking: 0,8,3,0,...

Performed on a Xenix 386/16:
Operation [sec, kbyte tmp space]: program

Asym. Diff.[ 7, 4]: (s2;s3)3. 
Abs. Diff. [90,81]: (a1;a4;s2)2; (a2;s1)1; (a1;a5;s3)3; (a3;s1)1; (s2;s3)3; (s5;s4)4; (a2;s5)5. 
Add  [  7,   4]: (a2;s3)3. 
Mult [227, 161]: (a4;a5;s2)2; ((a2;s4)4; s3; (a1;a4;s5)5; (a5;s1)1)3. 
<PERSON>py [ 48,  25]: (a1;a3;s2)2; (a2;s1)1. 
sign [ 30,  17]: (a3;s2)2; (a2;(s3)3)3. 
!sign[ 36,  28]: (a3;s2)2; (a2;(s3)3)3; a3; (s3;s2)2; (s3;a2)3.  
Div  [630,1522]: (a9;s2)2; (a2;a10;s3)3; (a3;s2)2; (a2;(s3)3)3; a3; (s3;s2)2; (s3;a2)3; (a2)2;(a2;s9)9;(a3;s10)10; (a9;a10;s2)2; (a11;a12;s3)3; (a2;s12)12; (a3;s9)9; (s2;s3)3; (a3;s2)2; (a2;(s3)3)3; a3; (s3;s2)2; (s3;a2)3; (a1;s2)2; (a2;s10)10; (a3;s11)11; ((a12;a13;s3)3; (a3;s13)13; (s2;s3)3; (a3;s12)12; a14; (s1)1; (a9;a10;s2)2; (a11;a12;s3)3; (a2;s12)12; (a3;s9)9; (s2;s3)3; (a3;s2)2; (a2;(s3)3)3; a3; (s3;s2)2; (s3;a2)3; (a1;s2)2; (a2;s10)10; (a3;s11)11)1; (s2)2; (a2;s14)14. 
