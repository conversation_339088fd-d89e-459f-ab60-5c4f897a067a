*version5.txt*  For Vim version 9.1.  Last change: 2022 Nov 09


		  VIM REFERENCE MANUAL    by <PERSON>

Welcome to Vim Version 5.0!

This document lists the differences between Vim 4.x and Vim 5.0.
Although 5.0 is mentioned here, this is also for version 5.1, 5.2, etc.
See |vi_diff.txt| for an overview of differences between Vi and Vim 5.0.
See |version4.txt| for differences between Vim 3.0 and Vim 4.0.

INCOMPATIBLE:				|incompatible-5|

Default value for 'compatible' changed	|cp-default|
Text formatting command "Q" changed	|Q-command-changed|
Command-line arguments changed		|cmdline-changed|
Autocommands are kept			|autocmds-kept|
Use of 'hidden' changed			|hidden-changed|
Text object commands changed		|text-objects-changed|
X-Windows Resources removed		|x-resources|
Use of $VIM				|$VIM-use|
Use of $HOME for MS-DOS and Win32	|$HOME-use|
Tags file format changed		|tags-file-changed|
Options changed				|options-changed|
CTRL-B in Insert mode gone		|i_CTRL-B-gone|

NEW FEATURES:				|new-5|

Syntax highlighting			|new-highlighting|
Built-in script language		|new-script|
Perl and Python support			|new-perl-python|
Win32 GUI version			|added-win32-GUI|
VMS version				|added-VMS|
BeOS version				|added-BeOS|
Macintosh GUI version			|added-Mac|
More Vi compatible			|more-compatible|
Read input from stdin			|read-stdin|
Regular expression patterns		|added-regexp|
Overloaded tags				|tag-overloaded|
New commands				|new-commands|
New options				|added-options|
New command-line arguments		|added-cmdline-args|
Various additions			|added-various|

IMPROVEMENTS				|improvements-5|

COMPILE TIME CHANGES			|compile-changes-5|

BUG FIXES				|bug-fixes-5|

VERSION 5.1			|version-5.1|
Changed					|changed-5.1|
Added					|added-5.1|
Fixed					|fixed-5.1|

VERSION 5.2			|version-5.2|
Long lines editable			|long-lines|
File browser added			|file-browser-5.2|
Dialogs added				|dialogs-added|
Popup menu added			|popup-menu-added|
Select mode added			|new-Select-mode|
Session files added			|new-session-files|
User defined functions and commands	|new-user-defined|
New interfaces				|interfaces-5.2|
New ports				|ports-5.2|
Multi-byte support			|new-multi-byte|
New functions				|new-functions-5.2|
New options				|new-options-5.2|
New Ex commands				|new-ex-commands-5.2|
Changed					|changed-5.2|
Added					|added-5.2|
Fixed					|fixed-5.2|

VERSION 5.3			|version-5.3|
Changed					|changed-5.3|
Added					|added-5.3|
Fixed					|fixed-5.3|

VERSION 5.4			|version-5.4|
Runtime directory introduced		|new-runtime-dir|
Filetype introduced			|new-filetype-5.4|
Vim script line continuation		|new-line-continuation|
Improved session files			|improved-sessions|
Autocommands improved			|improved-autocmds-5.4|
Encryption				|new-encryption|
GTK GUI port				|new-GTK-GUI|
Menu changes				|menu-changes-5.4|
Viminfo improved			|improved-viminfo|
Various new commands			|new-commands-5.4|
Various new options			|new-options-5.4|
Vim scripts				|new-script-5.4|
Avoid hit-enter prompt			|avoid-hit-enter|
Improved quickfix			|improved-quickfix|
Regular expressions			|regexp-changes-5.4|
Changed					|changed-5.4|
Added					|added-5.4|
Fixed					|fixed-5.4|

VERSION 5.5			|version-5.5|
Changed					|changed-5.5|
Added					|added-5.5|
Fixed					|fixed-5.5|

VERSION 5.6			|version-5.6|
Changed					|changed-5.6|
Added					|added-5.6|
Fixed					|fixed-5.6|

VERSION 5.7			|version-5.7|
Changed					|changed-5.7|
Added					|added-5.7|
Fixed					|fixed-5.7|

VERSION 5.8			|version-5.8|
Changed					|changed-5.8|
Added					|added-5.8|
Fixed					|fixed-5.8|

==============================================================================
INCOMPATIBLE						*incompatible-5*

Default value for 'compatible' changed			*cp-default*
--------------------------------------

Vim version 5.0 tries to be more Vi compatible.  This helps people who use Vim
as a drop-in replacement for Vi, but causes some things to be incompatible
with version 4.x.

In version 4.x the default value for the 'compatible' option was off.  Now the
default is on.  The first thing you will notice is that the "u" command undoes
itself.  Other side effects will be that mappings may work differently or not
work at all.

Since a lot of people switching from Vim 4.x to 5.0 will find this annoying,
the 'compatible' option is switched off if Vim finds a vimrc file.  This is a
bit of magic to make sure that 90% of the Vim users will not be bitten by
this change.

What does this mean?
- If you prefer to run in 'compatible' mode and don't have a vimrc file, you
  don't have to do anything.
- If you prefer to run in 'nocompatible' mode and do have a vimrc file, you
  don't have to do anything.
- If you prefer to run in 'compatible' mode and do have a vimrc file, you
  should put this line first in your vimrc file: >
	:set compatible
- If you prefer to run in 'nocompatible' mode and don't have a vimrc file,
  you can do one of the following:
    - Create an empty vimrc file (e.g.: "~/.vimrc" for Unix).
    - Put this command in your .exrc file or $EXINIT: >
		:set nocompatible
<   - Start Vim with the "-N" argument.

If you are new to Vi and Vim, using 'nocompatible' is strongly recommended,
because Vi has a lot of unexpected side effects, which are avoided by this
setting.  See 'compatible'.

If you like some things from 'compatible' and some not, you can tune the
compatibility with 'cpoptions'.

When you invoke Vim as "ex" or "gex", Vim always starts in compatible mode.


Text formatting command "Q" changed			*Q-command-changed*
-----------------------------------

The "Q" command formerly formatted lines to the width the 'textwidth' option
specifies.  The command for this is now "gq" (see |gq| for more info).  The
reason for this change is that "Q" is the standard Vi command to enter "Ex"
mode, and Vim now does in fact have an "Ex" mode (see |Q| for more info).

If you still want to use "Q" for formatting, use this mapping: >
	:noremap Q gq
And if you also want to use the functionality of "Q": >
	:noremap gQ Q


Command-line arguments changed				*cmdline-changed*
------------------------------

Command-line file-arguments and option-arguments can now be mixed.  You can
give options after the file names.  Example: >
   vim main.c -g

This is not possible when editing a file that starts with a '-'.  Use the "--"
argument then |---|: >
   vim -g -- -main.c

"-v" now means to start Ex in Vi mode, use "-R" for read-only mode.
old: "vim -v file"	|-v|
new: "vim -R file"	|-R|

"-e" now means to start Vi in Ex mode, use "-q" for quickfix.
old: "vim -e errorfile"	|-e|
new: "vim -q errorfile" |-q|

"-s" in Ex mode now means to run in silent (batch) mode. |-s-ex|

"-x" reserved for crypt, use "-f" to avoid starting a new CLI (Amiga).
old: "vim -x file"	|-x|
new: "vim -f file"	|-f|

Vim allows up to ten "+cmd" and "-c cmd" arguments.  Previously Vim executed
only the last one.

"-n" now overrides any setting for 'updatecount' in a vimrc file, but not in
a gvimrc file.


Autocommands are kept					*autocmds-kept*
---------------------

Before version 5.0, autocommands with the same event, file name pattern, and
command could appear only once.  This was fine for simple autocommands (like
setting option values), but for more complicated autocommands, where the same
command might appear twice, this restriction caused problems.  Therefore
Vim stores all autocommands and keeps them in the order that they are defined.

The most obvious side effect of this change is that when you source a vimrc
file twice, the autocommands in it will be defined twice.  To avoid this, do
one of these:

- Remove any autocommands that might already be defined before defining
  them.  Example: >
	:au! * *.ext
	:au BufEnter *.ext ...

- Put the autocommands inside an ":if" command.  Example: >
	if !exists("did_ext_autocmds")
	  let did_ext_autocmds = 1
	  autocmd BufEnter *.ext ...
	endif

- Put your autocommands in a different autocommand group so you can remove
  them before defining them |:augroup|: >
	augroup uncompress
	  au!
	  au BufReadPost *.gz ...
	augroup END


Use of 'hidden' changed					*hidden-changed*
-----------------------

In version 4.x, only some commands used the 'hidden' option.  Now all commands
uses it whenever a buffer disappears from a window.

Previously you could do ":buf xxx" in a changed buffer and that buffer would
then become hidden.  Now you must set the 'hidden' option for this to work.

The new behavior is simpler: whether Vim hides buffers no longer depends on
the specific command that you use.
- with 'hidden' not set, you never get hidden buffers.  Exceptions are the
  ":hide" and ":close!" commands and, in rare cases, where you would otherwise
  lose changes to the buffer.
- With 'hidden' set, you almost never unload a buffer.  Exceptions are the
  ":bunload" or ":bdel" commands.

":buffer" now supports a "!": abandon changes in current buffer.  So do
":bnext", ":brewind", etc.


Text object commands changed				*text-objects-changed*
----------------------------

Text object commands have new names.  This allows more text objects and makes
characters available for other Visual mode commands.  Since no more single
characters were available, text objects names now require two characters.
The first one is always 'i' or 'a'.
	OLD	NEW	~
	a	aw	a word			|v_aw|
	A	aW	a WORD			|v_aW|
	s	as	a sentence		|v_as|
	p	ap	a paragraph		|v_ap|
	S	ab	a () block		|v_ab|
	P	aB	a {} block		|v_aB|

There is another set of text objects that starts with "i", for "inner".  These
select the same objects, but exclude white space.


X-Windows Resources removed				*x-resources*
--------------------------

Vim no longer supports the following X resources:
- boldColor
- italicColor
- underlineColor
- cursorColor

Vim now uses highlight groups to set colors.  This avoids the confusion of
using a bold Font, which would imply a certain color.  See |:highlight| and
|gui-resources|.


Use of $VIM						*$VIM-use*
-----------

Vim now uses the VIM environment variable to find all Vim system files.  This
includes the global vimrc, gvimrc, and menu.vim files and all on-line help
and syntax files.  See |$VIM|.  Starting with version 5.4, |$VIMRUNTIME| can
also be used.
For Unix, Vim sets a default value for $VIM when doing "make install".
When $VIM is not set, its default value is the directory from 'helpfile',
excluding "/doc/help.txt".


Use of $HOME for MS-DOS and Win32			*$HOME-use*
---------------------------------

The MS-DOS and Win32 versions of Vim now first check $HOME when searching for
a vimrc or exrc file and for reading/storing the viminfo file.  Previously Vim
used $VIM for these systems, but this causes trouble on a system with several
users.  Now Vim uses $VIM only when $HOME is not set or the file is not found
in $HOME.  See |_vimrc|.


Tags file format changed				*tags-file-changed*
------------------------

Only tabs are allowed to separate fields in a tags file.  This allows for
spaces in a file name and is still Vi compatible.  In previous versions of
Vim, any white space was allowed to separate the fields.  If you have a file
which doesn't use a single tab between fields, edit the tags file and execute
this command: >
	:%s/\(\S*\)\s\+\(\S*\)\s\+\(.*\)/\1\t\2\t\3/


Options changed						*options-changed*
---------------

The default value of 'errorfile' has changed from "errors.vim" to "errors.err".
The reason is that only Vim scripts should have the ".vim" extensions.

The ":make" command no longer uses the 'errorfile' option.  This prevents the
output of the ":make" command from overwriting a manually saved error file.
":make" uses the 'makeef' option instead.  This also allows for generating a
unique name, to prevent concurrently running ":make" commands from overwriting
each other's files.

With 'insertmode' set, a few more things change:
- <Esc> in Normal mode goes to Insert mode.
- <Esc> in Insert mode doesn't leave Insert mode.
- When doing ":set im", go to Insert mode immediately.

Vim considers a buffer to be changed when the 'fileformat' (formerly the
'textmode' option) is different from the buffer's initial format.


CTRL-B in Insert mode gone				*i_CTRL-B-gone*
--------------------------

When Vim was compiled with the |+rightleft| feature, you could use CTRL-B to
toggle the 'revins' option.  Unfortunately, some people hit the 'B' key
accidentally when trying to type CTRL-V or CTRL-N and then didn't know how to
undo this.  Since toggling the 'revins' option can easily be done with the
mapping below, this use of the CTRL-B key is disabled.  You can still use the
CTRL-_ key for this |i_CTRL-_|. >
   :imap <C-B> <C-O>:set revins!<CR>

==============================================================================
NEW FEATURES						*new-5*

Syntax highlighting					*new-highlighting*
-------------------

Vim now has a very flexible way to highlighting just about any type of file.
See |syntax|.  Summary: >
   :syntax on

Colors and attributes can be set for the syntax highlighting, and also for
other highlighted items with the ':' flag in the 'highlight' option.  All
highlighted items are assigned a highlight group which specifies their
highlighting.  See |:highlight|.  The default colors have been improved.

You can use the "Normal" group to set the default fore/background colors for a
color terminal.  For the GUI, you can use this group to specify the font, too.

The "2html.vim" script can be used to convert any file that has syntax
highlighting to HTML.  The colors will be exactly the same as how you see them
in Vim.  With a HTML viewer you can also print the file with colors.


Built-in script language				*new-script*
------------------------

A few extra commands and an expression evaluator enable you to write simple
but powerful scripts.  Commands include ":if" and ":while".  Expressions can
manipulate numbers and strings.  You can use the '=' register to insert
directly the result of an expression.  See |expression|.


Perl and Python support					*new-perl-python*
-----------------------

Vim can call Perl commands with ":perldo", ":perl", etc.  See |perl|.
Patches made by Sven Verdoolaege and Matt Gerassimoff.

Vim can call Python commands with ":python" and ":pyfile".  See |python|.

Both of these are only available when enabled at compile time.


Win32 GUI version					*added-win32-GUI*
-----------------

The GUI has been ported to MS-Windows 95 and NT.  All the features of the X11
GUI are available to Windows users now.  |gui-w32|
This also fixes problems with running the Win32 console version under Windows
95, where console support has always been bad.
There is also a version that supports OLE automation interface.  |if_ole.txt|
Vim can be integrated with Microsoft Developer Studio using the VisVim DLL.
It is possible to produce a DLL version of gvim with Borland C++ (Aaron).


VMS version						*added-VMS*
-----------

Vim can now also be used on VMS systems.  Port done by Henk Elbers.
This has not been tested much, but it should work.
Sorry, no documentation!


BeOS version						*added-BeOS*
------------

Vim can be used on BeOS systems (including the BeBox).  (Olaf Seibert)
See |os_beos.txt|.


Macintosh GUI version					*added-Mac*
---------------------

Vim can now be used on the Macintosh.  (Dany St-Amant)
It has not been tested much yet, be careful!
See |os_mac.txt|.


More Vi compatible					*more-compatible*
------------------

There is now a real Ex mode.  Started with the "Q" command, or by calling the
executable "ex" or "gex".  |Ex-mode|

Always allow multi-level undo, also in Vi compatible mode.  When the 'u' flag
in 'cpoptions' is included, CTRL-R is used for repeating the undo or redo
(like "." in Nvi).


Read input from stdin					*read-stdin*
---------------------

When using the "-" command-line argument, Vim reads its text input from stdin.
This can be used for putting Vim at the end of a pipe: >
   grep "^a.*" *.c | vim -
See |--|.


Regular expression patterns				*added-regexp*
---------------------------

Added specifying a range for the number of matches of an atom: "\{a,b}". |/\{|
Added the "shortest match" regexp "\{-}" (Webb).
Added "\s", matches a white character.  Can replace "[ \t]".		 |/\s|
Added "\S", matches a non-white character.  Can replace "[^ \t]".	 |/\S|


Overloaded tags						*tag-overloaded*
---------------

When using a language like C++, there can be several tags for the same
tagname.  Commands have been added to be able to jump to any of these
overloaded tags:
|:tselect|	List matching tags, and jump to one of them.
|:stselect|	Idem, and split window.
|g_CTRL-]|	Do ":tselect" with the word under the cursor.

	After ":ta {tagname}" with multiple matches:
|:tnext|	Go to next matching tag.
|:tprevious|	Go to previous matching tag.
|:trewind|	Go to first matching tag.
|:tlast|	Go to last matching tag.

The ":tag" command now also accepts wildcards.  When doing command-line
completion on tags, case-insensitive matching is also available (at the end).


New commands						*new-commands*
------------

|:amenu|	Define menus for all modes, inserting a CTRL-O for Insert
		mode, ESC for Visual and CTRL-C for Cmdline mode.  "amenu" is
		used for the default menus and the Syntax menu.

|:augroup|	Set group to be used for following autocommands.  Allows the
		grouping of autocommands to enable deletion of a specific
		group.

|:crewind|	Go to first error.
|:clast|	Go to last error.

|:doautoall|	Execute autocommands for all loaded buffers.

|:echo|		Echo its argument, which is an expression.  Can be used to
		display messages which include variables.

|:execute|	Execute its argument, which is an expression.  Can be used to
		built up an Ex command with anything.

|:hide|		Works like ":close".

|:if|		Conditional execution, for built-in script language.

|:intro|	Show introductory message.  This is always executed when Vim
		is started without file arguments.

|:let|		Assign a value to an internal variable.

|:omap|		Map only in operator-pending mode.  Makes it possible to map
		text-object commands.

|:redir|	Redirect output of messages to a file.

|:update|	Write when buffer has changed.

|:while|	While-loop for built-in script language.

Visual mode:
|v_O|		"O" in Visual block mode, moves the cursor to the other corner
		horizontally.
|v_D|		"D" in Visual block mode deletes till end of line.

Insert mode:
|i_CTRL-]|	Triggers abbreviation, without inserting any character.


New options						*added-options*
-----------

'background'	Used for selecting highlight color defaults.  Also used in
		"syntax.vim" for selecting the syntax colors.  Often set
		automatically, depending on the terminal used.

'complete'	Specifies how Insert mode completion works.

'eventignore'	Makes it possible to ignore autocommands temporarily.

'fileformat'	Current file format.  Replaces 'textmode'.
'fileformats'	Possible file formats.  Replaces 'textauto'.
		New is that this also supports Macintosh format: A single <CR>
		separates lines.
		The default for 'fileformats' for MS-DOS, Win32 and OS/2 is
		"dos,unix", also when 'compatible' set.  Unix type files
		didn't work anyway when 'fileformats' was empty.

'guicursor'	Set the cursor shape and blinking in various modes.
		Default is to adjust the cursor for Insert and Replace mode,
		and when an operator is pending.  Blinking is default on.

'fkmap'		Farsi key mapping.

'hlsearch'	Highlight all matches with the last used search pattern.

'hkmapp'	Phonetic Hebrew mapping. (Ilya Dogolazky)

'iconstring'	Define the name of the icon, when not empty.  (Version 5.2: the
		string is used literally, a newline can be used to make two
		lines.)

'lazyredraw'	Don't redraw the screen while executing macros, registers or
		other not typed commands.

'makeef'	Errorfile to be used for ":make".  "##" is replaced with a
		unique number.  Avoids that two Vim sessions overwrite each
		others errorfile.  The Unix default is "/tmp/vim##.err"; for
		Amiga "t:vim##.Err, for others "vim##.err".

'matchtime'	1/10s of a second to show a matching paren, when 'showmatch'
		is set.  Like Nvi.

'mousehide'	Hide mouse pointer in GUI when typing text.

'nrformats'	Defines what bases Vim will consider for numbers when using
		the CTRL-A and CTRL-X commands.  Default: "hex,octal".

'shellxquote'	Add extra quotes around the whole shell command, including
		redirection.

'softtabstop'	Make typing behave like tabstop is set at this value, without
		changing the value of 'tabstop'.  Makes it more easy to keep
		'ts' at 8, while still getting four spaces for a <Tab>.

'titlestring'	String for the window title, when not empty.  (Version 5.2:
		this string is used literally, a newline can be used to make
		two lines.)

'verbose'	Level of verbosity.  Makes it possible to show which .vimrc,
		.exrc, .viminfo files etc. are used for initializing.  Also
		to show autocommands that are being executed.  Can also be set
		by using the "-V" command-line argument.


New command-line arguments				*added-cmdline-args*
--------------------------

|-U|		Set the gvimrc file to be used.  Like "-u" for the vimrc.

|-V|		Set the 'verbose' option.  E.g. "vim -V10".

|-N|		Start in non-compatible mode.

|-C|		Start in compatible mode.

|-Z|		Start in restricted mode, disallow shell commands.  Can also
		be done by calling the executable "rvim".

|-h|		Show usage information and exit.


Various additions					*added-various*
-----------------

Added support for SNiFF+ connection (submitted by Toni Leherbauer).  Vim can
be used as an editor for SNiFF.  No documentation available...

For producing a bug report, the bugreport.vim script has been included.
Can be used with ":so $VIMRUNTIME/bugreport.vim", which creates the file
"bugreport.txt" in the current directory. |bugs|

Added range to ":normal" command.  Now you can repeat the same command for
each line in the range.  |:normal-range|

Included support for the Farsi language (Shiran).  Only when enabled at
compile time.  See |farsi|.

==============================================================================
IMPROVEMENTS						*improvements-5*

Performance:
- When 'showcmd' was set, mappings would execute much more slowly because the
  output would be flushed very often.  Helps a lot when executing the "life"
  macros with 'showcmd' set.
- Included patches for binary searching in tags file (David O'Neill).
  Can be disabled by resetting the 'tagbsearch' option.
- Don't update the ruler when repeating insert (slowed it down a lot).
- For Unix, file name expansion is now done internally instead of starting a
  shell for it.
- Expand environment variables with expand_env(), instead of calling the
  shell.  Makes ":so $VIMRUNTIME/syntax/syntax.vim" a LOT faster.
- Reduced output for cursor positioning: Use CR-LF for moving to first few
  columns in next few lines;  Don't output CR twice when using termios.
- Optimized cursor positioning.  Use CR, BS and NL when it's shorter than
  absolute cursor positioning.
- Disable redrawing while repeating insert "1000ii<Esc>".
- Made "d$" or "D" for long lines a lot faster (delete all characters at once,
  instead of one by one).
- Access option table by first letter, instead of searching from start.
- Made setting special highlighting attributes a lot faster by using
  highlight_attr[], instead of searching in the 'highlight' string.
- Don't show the mode when redrawing is disabled.
- When setting an option, only redraw the screen when required.
- Improved performance of Ex commands by using a lookup table for the first
  character.

Options:
'cinoptions'	Added 'g' flag, for C++ scope declarations.
'cpoptions'	Added 'E' flag: Disallow yanking, deleting, etc. empty text
		area.  Default is to allow empty yanks.  When 'E' is included,
		"y$" in an empty line now is handled as an error (Vi
		compatible).
		Added 'j' flag: Only add two spaces for a join after a '.',
		not after a '?' or '!'.
		Added 'A' flag: don't give ATTENTION message.
		Added 'L' flag: When not included, and 'list' is set,
		'textwidth' formatting works like 'list' is not set.
		Added 'W' flag:  Let ":w!" behave like Vi: don't overwrite
		readonly files, or a file owned by someone else.
'highlight'	Added '@' flag, for '@' characters after the last line on the
		screen, and '$' at the end of the line when 'list' is set.
		Added 'i' flag: Set highlighting for 'incsearch'.  Default
		uses "IncSearch" highlight group, which is linked to "Visual".
		Disallow 'h' flag in 'highlight' (wasn't used anymore since
		3.0).
'guifont'	Win32 GUI only: When set to "*" brings up a font requester.
'guipty'	Default on, because so many people need it.
'path'		Can contain wildcards, and "**" for searching a whole tree.
'shortmess'	Added 'I' flag to avoid the intro message.
'viminfo'	Added '%' flag: Store buffer list in viminfo file.

- Increased defaults for 'maxmem' and 'maxmemtot' for Unix and Win32.  Most
  machines have much more RAM now that prices have dropped.
- Implemented ":set all&", set all options to their default value. |:set|

Swap file:
- Don't create a swap file for a readonly file.  Then create one on the first
  change.  Also create a swapfile when the amount of memory used is getting
  too high. |swap-file|
- Make swap file "hidden", if possible.  On Unix this is done by prepending a
  dot to the swap file name.  When long file names are used, the DJGPP and
  Win32 versions also prepend a dot, in case a file on a mounted Unix file
  system is edited.  |:swapname|  On MSDOS the hidden file attribute is NOT
  set, because this causes problems with share.exe.
- 'updatecount' always defaults to non-zero, also for Vi compatible mode.
  This means there is a swap file, which can be used for recovery.

Tags:
- Included ctags 2.0 (Darren Hiebert).  The syntax for static tags changed
  from
	{tag}:{fname}	{fname}	{command}
  to
	{tag}	{fname}	{command};"	file:
  Which is both faster to parse, shorter and Vi compatible.  The old format is
  also still accepted, unless disabled in src/feature.h (see OLD_STATIC_TAGS).
  |tags-file-format|
- Completion of tags now also includes static tags for other files, at the
  end.
- Included "shtags" from Stephen Riehm.
- When finding a matching tag, but the file doesn't exist, continue searching
  for another match.  Helps when using the same tags file (with links) for
  different versions of source code.
- Give a tag with a global match in the current file a higher priority than a
  global match in another file.

Included xxd version V1.8 (Juergen Weigert).

Autocommands:
- VimLeave autocommands are executed after writing the viminfo file, instead
  of before.  |VimLeave|
- Allow changing autocommands while executing them.  This allows for
  self-modifying autocommands.  (idea from Goldberg)
- When using autocommands with two or more patterns, could not split
  ":if/:endif" over two lines.  Now all matching autocommands are executed in
  one do_cmdline().
- Autocommands no longer change the command repeated with ".".
- Search patterns are restored after executing autocommands.  This avoids
  that the 'hlsearch' highlighting is messed up by autocommands.
- When trying to execute an autocommand, also try matching the pattern with
  the short file name.  Helps when short file name is different from full
  file name (expanded symbolic links). |autocmd-patterns|
- Made the output of ":autocmd" shorter and look better.
- Expand <sfile> in an ":autocmd" when it is defined.  |<sfile>|
- Added "nested" flag to ":autocmd", allows nesting.  |autocmd-nested|
- Added [group] argument to ":autocmd".  Overrides the currently set group.
  |autocmd-groups|
- new events:
  |BufUnload|		before a buffer is unloaded
  |BufDelete|		before a buffer is deleted from the buffer list
  |FileChangedShell|	when a file's modification time has changed after
			executing a shell command
  |User|		user-defined autocommand
- When 'modified' was set by a BufRead* autocommand, it was reset again
  afterwards.  Now the ":set modified" is remembered.

GUI:
- Improved GUI scrollbar handling when redrawing is slower than the scrollbar
  events are generated.
- "vim -u NONE" now also stops loading the .gvimrc and other GUI inits.  |-u|
  Use "-U" to use another gvimrc file.  |-U|
- Handle CTRL-C for external command, also for systems where "setsid()" is
  supported.
- When starting the GUI, restrict the window size to the screen size.
- The default menus are read from $VIMRUNTIME/menu.vim.  This allows for a
  customized default menu.  |menu.vim|
- Improved the default menus.  Added File/Print, a Window menu, Syntax menu,
  etc.
- Added priority to the ":menu" command.  Now each menu can be put in a place
  where you want it, independent of the order in which the menus are defined.
  |menu-priority|

Give a warning in the intro screen when running the Win32 console version on
Windows 95 because there are problems using this version under Windows 95.
|win32-problems|

Added 'e' flag for ":substitute" command: Don't complain when not finding a
match (Campbell).  |:s|

When using search commands in a mapping, only the last one is kept in the
history.  Avoids that the history is trashed by long mappings.

Ignore characters after "ex", "view" and "gvim" when checking startup mode.
Allows the use of "gvim5" et. al.  |gvim| "gview" starts the GUI in readonly
mode.  |gview|

When resizing windows, the cursor is kept in the same relative position, if
possible.  (Webb)

":all" and ":ball" no longer close and then open a window for the same buffer.
Avoids losing options, jumplist, and other info.

"-f" command-line argument is now ignored if Vim was compiled without GUI.
|-f|

In Visual block mode, the right mouse button picks up the nearest corner.

Changed default mappings for DOS et al.  Removed the DOS-specific mappings,
only use the Windows ones.  Added Shift-Insert, Ctrl-Insert, Ctrl-Del and
Shift-Del.

Changed the numbers in the output of ":jumps", so you can see where {count}
CTRL-O takes you.  |:jumps|

Using "~" for $HOME now works for all systems.  |$HOME|

Unix: Besides using CTRL-C, also use the INTR character from the tty settings.
Somebody has INTR set to DEL.

Allow a <NL> in a ":help" command argument to end the help command, so another
command can follow.

Doing "%" on a line that starts with "   #if" didn't jump to matching "#else".
Don't recognize "#if", "#else" etc. for '%' when 'cpo' contains the '%' flag.
|%|

Insert mode expansion with "CTRL-N", "CTRL-P" and "CTRL-X" improved
|ins-completion|:
- 'complete' option added.
- When 'nowrapscan' is set, and no match found, report the searched direction
  in the error message.
- Repeating CTRL-X commands adds following words/lines after the match.
- When adding-expansions, accept single character matches.
- Made repeated CTRL-X CTRL-N not break undo, and "." repeats the whole
  insertion.  Also fixes not being able to backspace over a word that has been
  inserted with CTRL-N.

When copying characters in Insert mode from previous/next line, with CTRL-E or
CTRL-Y, 'textwidth' is no longer used.  |i_CTRL-E|

Commands that move in the arglist, like ":n" and ":rew", keep the old cursor
position of the file (this is mostly Vi compatible).

Vim now remembers the '< and '> marks for each buffer.  This fixes a problem
that a line-delete in one buffer invalidated the '< and '> marks in another
buffer.  |'<|

For MSDOS, Unix and OS/2: When $VIM not set, use the path from the executable.
When using the executable path for $VIM, remove "src/" when present.  Should
make Vim find the docs and syntax files when it is run directly after
compiling.  |$VIM|

When quitting Visual mode with <Esc>, the cursor is put at start of the Visual
area (like after executing an operator).

Win32 and Unix version: Removed 1100 character limit on external commands.

Added possibility to include a space in a ":edit +command" argument, by
putting a backslash before it.  |+cmd|

After recovery, BufReadPost autocommands are applied.  |:recover|

Added color support for "os2ansi", OS/2 console. (Slootman)

Allow "%:p:h" when % is empty.  |:_%|

Included "<sfile>": file name from the ":source" command.  |<sfile>|

Added "<Bslash>" special character.  Helps for avoiding multiple backslashes
in mappings and menus.

In a help window, a double-click jumps to the tag under the cursor (like
CTRL-]).

<C-Left> and <C-Right> now work like <S-Left> and <S-Right>, move a word
forward/backward (Windows compatible). |<C-Left>|

Removed the requirement for a ":version" command in a .vimrc file.  It wasn't
used for anything.  You can use ":if" to handle differences between versions.
|:version|

For MS-DOS, Win32 and OS/2: When comparing file names for autocommands, don't
make a difference between '/' and '\' for path separator.

New termcap options:
"mb": blink.  Can only be used by assigning it to one of the other highlight
      options.  |t_mb|
"bc": backspace character.  |t_bc|
"nd": Used for moving the cursor right in the GUI, to avoid removing one line
      of pixels from the last bold character.  |t_nd|
"xs": highlighting not erased by overwriting, for hpterm.  Combined with
      'weirdinvert'.  Visual mode works on hpterm now.  |t_xs|

Unix: Set time of patch and backup file same as original file.  (Hiebert).

Amiga: In QuickFix mode no longer opens another window.  Shell commands can be
used now.

Added decmouse patches from David Binette.  Can now use Dec and Netterm mouse.
But only when enabled at compile time.

Added '#' register: Alternate file name |quote#|.  Display '#' register with
":dis" command. |:display|

Removed ':' from 'isfname' default for Unix.  Check for "://" in a file name
anyway.  Also check for ":\\", for MS-DOS.

Added count to "K"eyword command, when 'keywordprg' is "man", is inserted in
the man command.  "2K" results in "!man 2 <cword>".  |K|

When using "gf" on a relative path name, remove "../" from the file name, like
it's done for file names in the tags file. |gf|

When finishing recording, don't make the recorded register the default put
register.

When using "!!", don't put ":5,5!" on the command-line, but ":.!".  And some
other enhancements to replace the line number with "." or "$" when possible.

MSDOS et al.: Renamed $VIM/viminfo to $VIM/_viminfo.  It's more consistent:
.vimrc/_vimrc and .viminfo/_viminfo

For systems where case doesn't matter in file names (MSDOS, Amiga), ignore
case while sorting file names.  For buffer names too.

When reading from stdin doesn't work, read from stderr (helps for "foo | xargs
vim").

32 bit MS-DOS version: Replaced csdpmi3 by csdpmi4.

Changed <C-Left> and <C-Right> to skip a WORD instead of a word.

Warning for changed modified time when overwriting a file now also works on
other systems than Unix.

Unix: Changed the defaults for configure to be the same as the defaults for
Makefile: include GUI, Perl, and Python.

Some versions of Motif require "-lXpm".  Added check for this in configure.

Don't add "-L/usr/lib" to the link line, causes problems on a few systems.

==============================================================================
COMPILE TIME CHANGES					*compile-changes-5*

When compiling, allow a choice for minimal, normal or maximal features in an
easy way, by changing a single line in src/feature.h.
The DOS16 version has been compiled with minimal features to avoid running
out of memory too quickly.
The Win32, DJGPP, and OS/2 versions use maximal features, because they have
enough memory.
The Amiga version is available with normal and maximal features.

Added "make test" to Unix version Makefile.  Allows for a quick check if most
"normal" commands work properly.  Also tests a few specific commands.

Added setlocale() with codepage support for DJGPP version.

autoconf:
- Added autoconf check for -lXdmcp.
- Included check for -lXmu, no longer needed to edit the Makefile for this.
- Switched to autoconf 2.12.
- Added configure check for <poll.h>.  Seems to be needed when including
  Perl on Linux?
- termlib is now checked before termcap.
- Added configure check for strncasecmp(), stricmp() and strnicmp().  Added
  vim_stricmp() for when there's no library function for stricmp().
- Use "datadir" in configure, instead of our own check for HELPDIR.

Removed "make proto" from Makefile.manx.  Could not make it work without a lot
of #ifdefs.

Removed "proto/" from paths in proto.h.  Needed for the Mac port.

Drastically changed Makefile.mint.  Now it includes the Unix Makefile.

Added support for Dos16 in Makefile.b32 (renamed Makefile.b32 to Makefile.bor)

All source files are now edited with a tabstop of 8 instead of 4, which is
better when debugging and using other tools.  'softtabstop' is set to 4, to
make editing easier.

Unix: Added "link.sh" script, which removes a few unnecessary libraries from
the link command.

Don't use HPUX digraphs by default, but only when HPUX_DIGRAPHS is defined.
|digraphs-default|

==============================================================================
BUG FIXES						*bug-fixes-5*

Note:	Some of these fixes may only apply to test versions which were
	created after version 4.6, but before 5.0.


When doing ":bdel", try going to the next loaded buffer.  Don't rewind to the
start of the buffer list.

mch_isdir() for Unix returned TRUE for "" on some systems.

Win32: 'shell' set to "mksnt/sh.exe" breaks ":!" commands.  Don't use
backslashes in the temp file names.

On linux, with a FAT file system, could get spurious "file xxx changed since
editing started" messages, because the time is rounded off to two seconds
unexpectedly.

Crash in GUI, when selecting a word (double click) and then extend until an
empty line.

For systems where isdigit() can't handle characters > 255, get_number() caused
a crash when moving the mouse during the prompt for recovery.

In Insert mode, "CTRL-O P" left the cursor on the last inserted character.
Now the cursor is left after the last putted character.

When quickfix found an error type other than 'e' or 'w', it was never printed.

A setting for 'errorfile' in a .vimrc overruled the "-q errorfile" argument.

Some systems create a file when generating a temp file name.  Filtering would
then create a backup file for this, which was never deleted.  Now no backup
file is made when filtering.

simplify_filename() could remove a ".." after a link, resulting in the wrong
file name.  Made simplify_filename also work for MSDOS.  Don't use it for
Amiga, since it doesn't have "../".

otherfile() was unreliable when using links.  Could think that reading/writing
was for a different file, when it was the same.

Pasting with mouse in Replace mode didn't replace anything.

Window height computed wrong when resizing a window with an autocommand (could
cause a crash).

":s!foo!bar!" wasn't possible (Vi compatible).

do_bang() freed memory twice when called recursively, because of autocommands
(test11).  Thanks to Electric Fence!

"v$d" on an empty line didn't remove the "-- VISUAL --" mode message from the
command-line, and inverted the cursor.

":mkexrc" didn't check for failure to open the file, causing a crash.
(Felderhoff).

Win32 mch_write() wrote past fixed buffer, causing terminal keys no longer to
be recognized.  Both console and GUI version.

Athena GUI: Crash when removing a menu item.  Now Vim doesn't crash, but the
reversing of the menu item is still wrong.

Always reset 'list' option for the help window.

When 'scrolloff' is non-zero, a 'showmatch' could cause the shown match to be
in the wrong line and the window to be scrolled (Acevedo).

After ":set all&", 'lines' and 'ttytype' were still non-default, because the
defaults never got set.  Now the defaults for 'lines' and 'columns' are set
after detecting the window size.  'term' and 'ttytype' defaults are set when
detecting the terminal type.

For (most) non-Unix systems, don't add file names with illegal characters when
expanding.  Fixes "cannot open swapfile" error when doing ":e *.burp", when
there is no match.

In X11 GUI, drawing part of the cursor obscured the text.  Now the text is
drawn over the cursor, like when it fills the block. (Seibert)

when started with "-c cmd -q errfile", the cursor would be left in line 1.
Now a ":cc" is done after executing "cmd".

":ilist" never ignored case, even when 'ignorecase' set.

"vim -r file" for a readonly file, then making a change, got ATTENTION message
in insert mode, display mixed up until <Esc> typed.  Also don't give ATTENTION
message after recovering a file.

The abbreviation ":ab #i #include" could not be removed.

CTRL-L completion (longest common match) on command-line didn't work properly
for case-insensitive systems (MS-DOS, Windows, etc.).  (suggested by Richard
Kilgore).

For terminals that can hide the cursor ("vi" termcap entry), resizing the
window caused the cursor to disappear.

Using an invalid mark in an Ex address didn't abort the command.

When 'smarttab' set, would use 'shiftround' when inserting a TAB after a
space.  Now it always rounds to a tabstop.

Set '[ and '] marks for ":copy", ":move", ":append", ":insert", ":substitute"
and ":change".  (Acevedo).

"d$" in an empty line still caused an error, even when 'E' is not in
'cpoptions'.

Help files were stored in the viminfo buffer list without a path.

GUI: Displaying cursor was not synchronized with other displaying.  Caused
several display errors.  For example, when the last two lines in the file
start with spaces, "dd" on the last line copied text to the (then) last line.

Win32: Needed to type CTRL-SHIFT-- to get CTRL-_.

GUI: Moving the cursor forwards over bold text would remove one column of bold
pixels.

X11 GUI: When a bold character in the last column was scrolled up or down, one
column of pixels would not be copied.

Using <BS> to move the cursor left can sometimes erase a character.  Now use
"le" termcap entry for this.

Keyword completion with regexp didn't work.  e.g., for "b.*crat".

Fixed: With CTRL-O that jumps to another file, cursor could end up just after
the line.

Amiga: '$' was missing from character recognized as wildcards, causing $VIM
sometimes not to be expanded.

":change" didn't adjust marks for deleted lines.

":help [range]" didn't work.  Also for [pattern], [count] and [quotex].

For 'cindent'ing, typing "class::method" doesn't align like a label when the
second ':' is typed.
When inserting a CR with 'cindent' set (and a bunch of other conditions) the
cursor went to a wrong location.
'cindent' was wrong for a line that ends in '}'.
'cindent' was wrong after "else {".

While editing the cmdline in the GUI, could not use the mouse to select text
from the command-line itself.

When deleting lines, marks in tag stack were only adjusted for the current
window, not for other windows on the same buffer.

Tag guessing could find a function "some_func" instead of the "func" we were
looking for.

Tags file name relative to the current file didn't work.

":g/pat2/s//pat2/g", causing the number of subs to be reported, used to cause
a scroll up.  Now you no longer have to hit <CR>.

X11 GUI: Selecting text could cause a crash.

32 bit DOS version: CTRL-C in external command killed Vim.  When SHELL is set
to "sh.exe", external commands didn't work.  Removed using of command.com, no
longer need to set 'shellquote'.

Fixed crash when using ":g/pat/i".

Fixed (potential) crash for X11 GUI, when using an X selection.  Was giving a
pointer on the stack to a callback function, now it's static.

Using "#" and "*" with an operator didn't work.  E.g. "c#".

Command-line expansion didn't work properly after ":*". (Acevedo)

Setting 'weirdinvert' caused highlighting to be wrong in the GUI.

":e +4 #" didn't work, because the "4" was in unallocated memory (could cause
a crash).

Cursor position was wrong for ":e #", after ":e #" failed, because of changes
to the buffer.

When doing ":buf N", going to a buffer that was edited with ":view", the
readonly flag was reset.  Now make a difference between ":e file" and ":buf
file": Only set/reset 'ro' for the first one.

Avoid |hit-enter| prompt when not able to write viminfo on exit.

When giving error messages in the terminal where the GUI was started, GUI
escape codes would be written to the terminal.  In an xterm this could be seen
as a '$' after the message.

Mouse would not work directly after ":gui", because full_screen isn't set,
which causes starttermcap() not to do its work.

'incsearch' did not scroll the window in the same way as the actual search.
When 'nowrap' set, incsearch didn't show a match when it was off the side of
the screen.  Now it also shows the whole match, instead of just the cursor
position (if possible).

":unmap", ":unab" and ":unmenu" did not accept a double quote, it was seen as
the start of a comment.  Now it's Vi compatible.

Using <Up><Left><Left><Up> in the command-line, when there is no previous
cmdline in the history, inserted a NUL on the command-line.

"i<Esc>" when on a <Tab> in column 0 left the cursor in the wrong place.

GUI Motif: When adding a lot of menu items, the menu bar goes into two rows.
Deleting menu items, reducing the number of rows, now also works.

With ":g/pat/s//foo/c", a match in the first line was scrolled off of the
screen, so you could not see it.
When using ":s//c", with 'nowrap' set, a match could be off the side of the
screen, so you could not see it.

When 'helpfile' was set to a fixed, non-absolute path in feature.h, Vim would
crash.  mch_Fullname can now handle file names in read-only memory. (Lottem)

When using CTRL-A or CTRL-@ in Insert mode, there could be strange effects
when using CTRL-D next.  Also, when repeating inserted text that included "0
CTRL-D" or "^ CTRL-D" this didn't work. (Acevedo)
Using CTRL-D after using CTRL-E or CTRL-Y in Insert mode that inserted a '0'
or '^', removed the '0' or '^' and more indent.

The command "2".p" caused the last inserted text to be executed as commands.
(Acevedo)

Repeating the insert of "CTRL-V 048" resulted in "^@" to be inserted.

Repeating Insert completion could fail if there are special characters in the
text. (Acevedo)

":normal /string<CR>" caused the window to scroll.  Now all ":normal" commands
are executed without scrolling messages.

Redo of CTRL-E or CTRL-Y in Insert mode interpreted special characters as
commands.

Line wrapping for 'tw' was done one character off for insert expansion
inserts.

buffer_exists() function didn't work properly for buffer names with a symbolic
link in them (e.g. when using buffer_exists(#)).

Removed the "MOTIF_COMMENT" construction from Makefile.  It now works with
FreeBSD make, and probably with NeXT make too.

Matching the 'define' and 'include' arguments now honor the settings for
'ignorecase'. (Acevedo)

When one file shown in two windows, Visual selection mixed up cursor position
in current window and other window.

When doing ":e file" from a help file, the 'isk' option wasn't reset properly,
because of a modeline in the help file.

When doing ":e!", a cursor in another window on the same buffer could become
invalid, leading to "ml_get: invalid lnum" errors.

Matching buffer name for when expanded name has a different path from not
expanded name (Brugnara).

Normal mappings didn't work after an operator.  For example, with ":map Q gq",
"QQ" didn't work.

When ":make" resulted in zero errors, a "No Errors" error message was given
(which breaks mappings).

When ":sourcing" a file, line length was limited to 1024 characters.  CTRL-V
before <EOL> was not handled Vi compatible.  (Acevedo)

Unexpected exit for X11 GUI, caused by SAVE_YOURSELF event.  (Heimann)

CTRL-X CTRL-I only found one match per line. (Acevedo)
When using an illegal CTRL-X key in Insert mode, the CTRL-X mode message
was stuck.

Finally managed to ignore the "Quit" menu entry of the Window manager!  Now
Vim only exists when there are no changed buffers.

Trying to start the GUI when $DISPLAY is not set resulted in a crash.
When $DISPLAY is not set and gvim starts vim, title was restored to "Thanks
for flying Vim".
When $DISPLAY not set, starting "gvim" (dropping back to vim) and then
selecting text with the mouse caused a crash.

"J", with 'joinspaces' set, on a line ending in ". ", caused one space too
many to be added.  (Acevedo)

In insert mode, a CTRL-R {regname} which didn't insert anything left the '"'
on the screen.

":z10" didn't work. (Clapp)

"Help "*" didn't work.

Renamed a lot of functions, to avoid clashes with POSIX name space.

When adding characters to a line, making it wrap, the following lines were
sometimes not shifted down (e.g. after a tag jump).

CTRL-E, with 'so' set and cursor on last line, now does not move cursor as
long as the last line is on the screen.

When there are two windows, doing "^W+^W-" in the bottom window could cause
the status line to be doubled (not redrawn correctly).

This command would hang: ":n `cat`".  Now connect stdin of the external
command to /dev/null, when expanding.

Fixed lalloc(0,) error for ":echo %:e:r".  (Acevedo)

The "+command" argument to ":split" didn't work when there was no file name.

When selecting text in the GUI, which is the output of a command-line command
or an external command, the inversion would sometimes remain.

GUI: "-mh 70" argument was broken.  Now, when menuheight is specified, it is
not changed anymore.

GUI: When using the scrollbar or mouse while executing an external command,
this caused garbage characters.

Showmatch sometimes jumped to the wrong position.  Was caused by a call to
findmatch() when redrawing the display (when syntax highlighting is on).

Search pattern "\(a *\)\{3} did not work correctly, also matched "a a".
Problem with brace_count not being decremented.

Wildcard expansion added too many non-matching file names.

When 'iskeyword' contains characters like '~', "*" and "#" didn't work
properly. (Acevedo)

On Linux, on a FAT file system, modification time can change by one second.
Avoid a "file has changed" warning for a one second difference.

When using the page-switching in an xterm, Vim would position the cursor on
the last line of the window on exit.  Also removed the cursor positioning for
":!" commands.

":g/pat/p" command (partly) overwrote the command.  Now the output is on a
separate line.

With 'ic' and 'scs' set, a search for "Keyword", ignore-case matches were
highlighted too.

"^" on a line with only white space, put cursor beyond the end of the line.

When deleting characters before where insertion started ('bs' == 2), could not
use abbreviations.

CTRL-E at end of file puts cursor below the file, in Visual mode, when 'so' is
non-zero.  CTRL-E didn't work when 'so' is big and the line below the window
wraps.  CTRL-E, when 'so' is non-zero, at end of the file, caused jumping
up-down.

":retab" didn't work well when 'list' is set.

Amiga: When inserting characters at the last line on the screen, causing it
to wrap, messed up the display.  It appears that a '\n' on the last line
doesn't always cause a scroll up.

In Insert mode "0<C-D><C-D>" deleted an extra character, because Vim thought
that the "0" was still there. (Acevedo)

"z{count}l" ignored the count.  Also for "zh" et. al. (Acevedo)

"S" when 'autoindent' is off didn't delete leading white space.

"/<Tab>" landed on the wrong character when 'incsearch' is set.

Asking a yes/no question could cause a |hit-enter| prompt.

When the file consists of one long line (>4100 characters), making changes
caused various errors and a crash.

DJGPP version could not save long lines (>64000) for undo.

"yw" on the last char in the file didn't work.  Also fixed "6x" at the end of
the line.  "6X" at the start of a line fails, but does not break a mapping.  In
general, a movement for an operator doesn't beep or flush a mapping, but when
there is nothing to operate on it beeps (this is Vi compatible).

"m'" and "m`" now set the '' mark at the cursor position.

Unix: Resetting of signals for external program didn't work, because SIG_DFL
and NULL are the same!  For "!!yes|dd count=1|, the yes command kept on
running.

Partly fixed: Unix GUI: Typeahead while executing an external command was lost.
Now it's not lost while the command is producing output.

Typing <S-Tab> in Insert mode, when it isn't mapped, inserted "<S-Tab>".  Now
it works like a normal <Tab>, just like <C-Tab> and <M-Tab>.

Redrawing ruler didn't check for old value correctly (caused UMR warnings in
Purify).

Negative array index in finish_viminfo_history().

":g/^/d|mo $" deleted all the lines.  The ":move" command now removes the
:global mark from the moved lines.

Using "vG" while the last line in the window is a "@" line, didn't update
correctly.  Just the "v" showed "~" lines.

"daw" on the last char of the file, when it's a space, moved the cursor beyond
the end of the line.

When 'hlsearch' was set or reset, only the current buffer was redrawn, while
this affects all windows.

CTRL-^, positioning the cursor somewhere from 1/2 to 1 1/2 screen down the
file, put the cursor at the bottom of the window, instead of halfway.

When scrolling up for ":append" command, not all windows were updated
correctly.

When 'hlsearch' is set, and an auto-indent is highlighted, pressing <Esc>
didn't remove the highlighting, although the indent was deleted.

When 'ru' set and 'nosc', using "$j" showed a wrong ruler.

Under Xfree 3.2, Shift-Tab didn't work (wrong keysym is used).

Mapping <S-Tab> didn't work.  Changed the key translations to use the shortest
key code possible.  This makes the termcode translations and mappings more
consistent.  Now all modifiers work in all combinations, not only with <Tab>,
but also with <Space>, <CR>, etc.

For Unix, restore three more signals.  And Vim catches SIGINT now, so CTRL-C
in Ex mode doesn't make Vim exit.

""a5Y" yanked 25 lines instead of 5.

"vrxxx<Esc>" in an empty line could not be undone.

A CTRL-C that breaks ":make" caused the errorfile not to be read (annoying
when you want to handle what ":make" produced so far).

":0;/pat" didn't find "pat" in line 1.

Search for "/test/s+1" at first char of file gave bottom-top message, or
didn't work at all with 'nowrapscan'.

Bug in viminfo history.  Could cause a crash on exit.

":print" didn't put cursor on first non-blank in line.

":0r !cat </dev/null" left cursor in line zero, with very strange effects.

With 'showcmd' set and 'timeoutlen' set to a few seconds, trick to position
the cursor leftwards didn't work.

AIX stty settings were restored to cs5 instead of cs8 (Winn).

File name completion didn't work for "zsh" versions that put spaces between
file names, instead of NULs.

Changed "XawChain*" to "XtChain*", should work for more systems.

Included quite a few fixes for rightleft mode (Lottem).

Didn't ask to |hit-enter| when GUI is started and error messages are printed.

When trying to edit a file in a non-existent directory, ended up with editing
"No file".

"gqap" to format a paragraph did too much redrawing.

When 'hlsearch' set, only the current window was updated for a new search
pattern.

Sometimes error messages on startup didn't cause a |hit-enter| prompt,
because of autocommands containing an empty line.

Was possible to select part of the window in the border, below the command
line.

'< and '> marks were not at the correct position after linewise Visual
selection.

When translating a help argument to "CTRL-x", prepend or append a '_', when
applicable.

Blockwise visual mode wasn't correct when moving vertically over a special
character (displayed as two screen characters).

Renamed "struct option" to "struct vimoption" to avoid name clash with GNU
getopt().

":abclear" didn't work (but ":iabclear" and ":cabclear" did work).

When 'nowrap' used, screen wasn't always updated correctly.

"vim -c split file" displayed extra lines.

After starting the GUI, searched the termcap for a "gui" term.

When 'hls' used, search for "^$" caused a hang.
When 'hls' was set, an error in the last regexp caused trouble.

Unix: Only output an extra <EOL> on exit when outputted something in the
alternate screen, or when there is a message that needs to be cleared.

"/a\{" did strange things, depending on previous search.

"c}" only redrew one line (with -u NONE).

For mappings, CTRL-META-A was shown as <M-^A> instead of <MC-A>, while :map
only accepts <MC-A>.  Now <M-C-A> is shown.

Unix: When using full path name in a tags file, which contains a link, and
'hidden' set and jumping to a tag in the current file, would get bogus
ATTENTION message.  Solved by always expanding file names, even when starting
with '/'.

'hlsearch' highlighting of special characters (e.g., a TAB) didn't highlight
the whole thing.

"r<CR>" didn't work correctly on the last char of a line.

Sometimes a window resize or other signal caused an endless loop, involving
set_winsize().

"vim -r" didn't work, it would just hang (using tgetent() while 'term' is
empty).

"gk" while 'nowrap' set moved two lines up.

When windows are split, a message that causes a scroll-up messed up one of the
windows, which required a CTRL-L to be typed.

Possible endless loop when using shell command in the GUI.

Menus defined in the .vimrc were removed when GUI started.

Crash when pasting with the mouse in insert mode.

Crash with ":unmenu *" in .gvimrc for Athena.

"5>>" shifted 5 lines 5 times, instead of 1 time.

CTRL-C when getting a prompt in ":global" didn't interrupt.

When 'so' is non-zero, and moving the scrollbar completely to the bottom,
there was a lot of flashing.

GUI: Scrollbar ident must be long for DEC Alpha.

Some functions called vim_regcomp() without setting reg_magic, which could
lead to unpredictable magicness.

Crash when clicking around the status line, could get a selection with a
backwards range.

When deleting more than one line characterwise, the last character wasn't
deleted.

GUI: Status line could be overwritten when moving the scrollbar quickly (or
when 'wd' is non-zero).

An ESC at the end of a ":normal" command caused a wait for a terminal code to
finish.  Now, a terminal code is not recognized when its start comes from a
mapping or ":normal" command.

Included patches from Robert Webb for GUI.  Layout of the windows is now done
inside Vim, instead of letting the layout manager do this.  Makes Vim work
with Lesstif!

UMR warning in set_expand_context().

Memory leak: b_winlnum list was never freed.

Removed TIOCLSET/TIOCLGET code from os_unix.c.  Was changing some of the
terminal settings, and looked like it wasn't doing anything good.  (suggested
by Juergen Weigert).

Ruler overwrote "is a directory" message.  When starting up, and 'cmdheight'
set to > 1, first message could still be in the last line.

Removed prototype for putenv() from proto.h, it's already in osdef2.h.in.

In replace mode, when moving the cursor and then backspacing, wrong characters
were inserted.

Win32 GUI was checking for a CTRL-C too often, making it slow.

Removed mappings for MS-DOS that were already covered by commands.

When visually selecting all lines in a file, cursor at last line, then "J".
Gave ml_get errors.  Was a problem with scrolling down during redrawing.

When doing a linewise operator, and then an operator with a mouse click, it
was also linewise, instead of characterwise.

When 'list' is set, the column of the ruler was wrong.

Spurious error message for "/\(b\+\)*".

When visually selected many lines, message from ":w file" disappeared when
redrawing the screen.

":set <M-b>=^[b", then insert "^[b", waited for another character.  And then
inserted "<M-b>" instead of the real <M-b> character.  Was trying to insert
K_SPECIAL x NUL.

CTRL-W ] didn't use count to set window height.

GUI: "-font" command-line argument didn't override 'guifont' setting from
.gvimrc. (Acevedo)

GUI: clipboard wasn't used for "*y".  And some more Win32/X11 differences
fixed for the clipboard (Webb).

Jumping from one help file to another help file, with 'compatible' set,
removed the 'help' flag from the buffer.

File-writable bit could be reset when using ":w!" for a readonly file.

There was a wait for CTRL-O n in Insert mode, because the search pattern was
shown.
Reduced wait, to allow reading a message, from 10 to 3 seconds.  It seemed
nothing was happening.

":recover" found same swap file twice.

GUI: "*yy only worked the second time (when pasting to an xterm)."

DJGPP version (dos32): The system flags were cleared.

Dos32 version: Underscores were sometimes replaced with y-umlaut (Levin).

Version 4.1 of ncurses can't handle tputs("", ..).  Avoid calling tputs() with
an empty string.

<S-Tab> in the command-line worked like CTRL-P when no completion started yet.
Now it does completion, last match first.

Unix: Could get annoying "can't write viminfo" message after doing "su".  Now
the viminfo file is overwritten, and the user set back to the original one.

":set term=builtin_gui" started the GUI in a wrong way.  Now it's not
allowed anymore.  But "vim -T gui" does start the GUI correctly now.

GUI: Triple click after a line only put last char in selection, when it is a
single character word.

When the window is bigger than the screen, the scrolling up of messages was
wrong (e.g. ":vers", ":hi").  Also when the bottom part of the window was
obscured by another window.

When using a wrong option only an error message is printed, to avoid that the
usage information makes it scroll off the screen.

When exiting because of not being able to read from stdin, didn't preserve the
swap files properly.

Visual selecting all chars in more than one line, then hit "x" didn't leave an
empty line.  For one line it did leave an empty line.

Message for which autocommand is executing messed up file write message (for
FileWritePost event).

"vim -h" included "-U" even when GUI is not available, and "-l" when lisp is
not available.

Crash for ":he <C-A>" (command-line longer than screen).

":s/this/that/gc", type "y" two times, then undo, did reset the modified
option, even though the file is still modified.

Empty lines in a tags file caused a ":tag" to be aborted.

When hitting 'q' at the more prompt for ":menu", still scrolled a few lines.

In an xterm that uses the bold trick a single row of characters could remain
after an erased bold character.  Now erase one extra char after the bold char,
like for the GUI.

":pop!" didn't work.

When the reading a buffer was interrupted, ":w" should not be able to
overwrite the file, ":w!" is required.

":cf%" caused a crash.

":gui longfilename", when forking is enabled, could leave part of the
longfilename at the shell prompt.

==============================================================================
VERSION 5.1						*version-5.1*

Improvements made between version 5.0 and 5.1.

This was mostly a bug-fix release, not many new features.


Changed							*changed-5.1*
-------

The expand() function now separates file names with <NL> instead of a space.
This avoids problems for file names with embedded spaces.  To get the old
result, use substitute(expand(foo), "\n", " ", "g").

For Insert-expanding dictionaries allow a backslash to be used for
wildchars.  Allows expanding "ze\kra", when 'isk' includes a backslash.

New icon for the Win32 GUI.

":tag", ":tselect" etc. only use the argument as a regexp when it starts
with '/'.  Avoids that ":tag xx~" gives an error message: "No previous sub.
regexp".  Also, when the :tag argument contained wildcard characters, it was
not Vi compatible.
When using '/', the argument is taken literally too, with a higher priority,
so it's found before wildcard matches.
Only when the '/' is used are matches with different case found, even though
'ignorecase' isn't set.
Changed "g^]" to only do ":tselect" when there is more than on matching tag.

Changed some of the default colors, because they were not very readable on a
dark background.

A character offset to a search pattern can move the cursor to the next or
previous line.  Also fixes that "/pattern/e+2" got stuck on "pattern" at the
end of a line.

Double-clicks in the status line do no longer start Visual mode.  Dragging a
status line no longer stops Visual mode.

Perl interface: Buffers() and Windows() now use more logical arguments, like
they are used in the rest of Vim (Moore).

Init '" mark to the first character of the first line.  Makes it possible to
use '" in an autocommand without getting an error message.


Added							*added-5.1*
-----

"shell_error" internal variable: result of last shell command.

":echohl" command: Set highlighting for ":echo".

'S' flag in 'highlight' and StatusLineNC highlight group: highlighting for
status line of not-current window.  Default is to use bold for current
window.

Added buffer_name() and buffer_number() functions (Aaron).
Added flags argument "g" to substitute() function (Aaron).
Added winheight() function.

Win32: When an external command starts with "start ", no console is opened
for it (Aaron).

Win32 console: Use termcap codes for bold/reverse based on the current
console attributes.

Configure check for "strip". (Napier)

CTRL-R CTRL-R x in Insert mode: Insert the contents of a register literally,
instead of as typed.

Made a few "No match" error messages more informative by adding the pattern
that didn't match.

"make install" now also copies the macro files.

tools/tcltags, a shell script to generate a tags file from a TCL file.

"--with-tlib" setting for configure.  Easy way to use termlib: "./configure
--with-tlib=termlib".

'u' flag in 'cino' for setting the indent for contained () parts.

When Win32 OLE version can't load the registered type library, ask the user
if he wants to register Vim now. (Erhardt)
Win32 with OLE: When registered automatically, exit Vim.
Included VisVim 1.1b, with a few enhancements and the new icon (Heiko
Erhardt).

Added patch from Vince Negri for Win32s support.  Needs to be compiled with
VC 4.1!

Perl interface: Added $curbuf.  Rationalized Buffers() and Windows().
(Moore) Added "group" argument to Msg().

Included Perl files in DOS source archive.  Changed Makefile.bor and
Makefile.w32 to support building a Win32 version with Perl included.

Included new Makefile.w32 from Ken Scott.  Now it's able to make all Win32
versions, including OLE, Perl and Python.

Added CTRL-W g ] and CTRL-W g ^]: split window and do g] or g^].

Added "g]" to always do ":tselect" for the ident under the cursor.
Added ":tjump" and ":stjump" commands.
Improved listing of ":tselect" when tag names are a bit long.

Included patches for the Macintosh version.  Also for Python interface.
(St-Amant)

":buf foo" now also restores cursor column, when the buffer was used before.

Adjusted the Makefile for different final destinations for the syntax files
and scripts (for Debian Linux).

Amiga: $VIM can be used everywhere.  When $VIM is not defined, "VIM:" is
used.  This fixes that "VIM:" had to be assigned for the help files, and
$VIM set for the syntax files.  Now either of these work.

Some xterms send vt100 compatible function keys F1-F4.  Since it's not
possible to detect this, recognize both type of keys and translate them to
<F1> - <F4>.

Added "VimEnter" autocommand.  Executed after loading all the startup stuff.

BeOS version now also runs on Intel CPUs (Seibert).


Fixed							*fixed-5.1*
-----

":ts" changed position in the tag stack when cancelled with <CR>.
":ts" changed the cursor position for CTRL-T when cancelled with <CR>.
":tn" would always jump to the second match.	Was using the wrong entry in
the tag stack.
Doing "tag foo", then ":tselect", overwrote the original cursor position in
the tag stack.

"make install" changed the vim.1 manpage in a wrong way, causing "doc/doc"
to appear for the documentation files.

When compiled with MAX_FEAT, xterm mouse handling failed.  Was caused by DEC
mouse handling interfering.

Was leaking memory when using selection in X11.

CTRL-D halfway a command-line left some characters behind the first line(s)
of the listing.

When expanding directories for ":set path=", put two extra backslashes
before a space in a directory name.

When 'lisp' set, first line of a function would be indented.  Now its indent
is set to zero.  And use the indent of the first previous line that is at
the same () level.  Added test33.

"so<Esc>u" in an empty file didn't work.

DOS: "seek error in swap file write" errors, when using DOS 6.2 share.exe,
because the swap file was made hidden.  It's no longer hidden.

":global" command would sometimes not execute on a matching line.  Happened
when a data block is full in ml_replace().

For AIX use a tgetent buffer of 2048 bytes, instead of 1024.

Win32 gvim now only sets the console size for external commands to 25x80
on Windows 95, not on NT.

Win32 console: Dead key could cause a crash, because of a missing "WINAPI"
(Deshpande).

The right mouse button started Visual mode, even when 'mouse' is empty, and
in the command-line, a left click moved the cursor when 'mouse' is empty.
In Visual mode, 'n' in 'mouse' would be used instead of 'v'.

A blinking cursor or focus change cleared a non-Visual selection.

CTRL-Home and CTRL-End didn't work for MS-DOS versions.

Could include NUL in 'iskeyword', causing a crash when doing insert mode
completion.

Use _dos_commit() to flush the swap file to disk for MSDOS 16 bit version.

In mappings, CTRL-H was replaced by the backspace key code.  This caused
problems when it was used as text, e.g. ":map _U :%s/.^H//g<CR>".

":set t_Co=0" was not handled like a normal term.  Now it's translated into
":set t_Co=", which works.

For ":syntax keyword" the "transparent" option did work, although not
mentioned in the help.  But synID() returned wrong name.

"gqG" in a file with one-word-per-line (e.g. a dictionary) was very slow and
not interruptible.

"gq" operator inserted screen lines in the wrong situation.  Now screen
lines are inserted or deleted when this speeds up displaying.

cindent was wrong when an "if" contained "((".

'r' flag in 'viminfo' was not used for '%'.  Could get files in the buffer
list from removable media.

Win32 GUI with OLE: if_ole_vc.mak could not be converted into a project.
Hand-edited to fix this...

With 'nosol' set, doing "$kdw" below an empty line positioned the cursor at
the end of the line.

Dos32 version changed "\dir\file" into "/dir/file", to work around a DJGPP
bug.  That bug appears to have been fixed, therefore this translation has
been removed.

"/^*" didn't work (find '*' in first column).

"<afile>" was not always set for autocommands.  E.g., for ":au BufEnter *
let &tags = expand("<afile>:p:h") . "/tags".

In an xterm, the window may be a child of the outer xterm window.  Use the
parent window when getting the title and icon names. (Smith)

When starting with "gvim -bg black -fg white", the value of 'background' is
only set after reading the .gvimrc file.  This causes a ":syntax on" to use
the wrong colors.  Now allow using ":gui" to open the GUI window and set the
colors.  Previously ":gui" in a gvimrc crashed Vim.

tempname() returned the same name all the time, unless the file was actually
created.  Now there are at least 26 different names.

File name used for <afile> was sometimes full path, sometimes file name
relative to current directory.

When 'background' was set after the GUI window was opened, it could change
colors that were set by the user in the .gvimrc file.  Now it only changes
colors that have not been set by the user.

Ignore special characters after a CSI in the GUI version.  These could be
interpreted as special characters in a wrong way. (St-Amant)

Memory leak in farsi code, when using search or ":s" command.
Farsi string reversing for a mapping was only done for new mappings.  Now it
also works for replacing a mapping.

Crash in Win32 when using a file name longer than _MAX_PATH. (Aaron)

When BufDelete autocommands were executed, some things for the buffer were
already deleted (esp. Perl stuff).

Perl interface: Buffer specific items were deleted too soon; fixes "screen
no longer exists" messages.  (Moore)

The Perl functions didn't set the 'modified' flag.

link.sh did not return an error on exit, which may cause Vim to start
installing, even though there is no executable to install. (Riehm)

Vi incompatibility: In Vi "." redoes the "y" command.  Added the 'y' flag to
'cpoptions'.  Only for 'compatible' mode.

":echohl" defined a new group, when the argument was not an existing group.

"syn on" and ":syn off" could move the cursor, if there is a hidden buffer
that is shorter that the current cursor position.

The " mark was not set when doing ":b file".

When a "nextgroup" is used with "skipwhite" in syntax highlighting, space at
the end of the line made the nextgroup also be found in the next line.

":he g<CTRL-D>", then ":" and backspace to the start didn't redraw.

X11 GUI: "gvim -rv" reversed the colors twice on Sun.  Now Vim checks if the
result is really reverse video (background darker than foreground).

"cat link.sh | vim -" didn't set syntax highlighting.

Win32: Expanding "file.sw?" matched ".file.swp".  This is an error of
FindnextFile() that we need to work around.  (Kilgore)

"gqgq" gave an "Invalid lnum" error on the last line.
Formatting with "gq" didn't format the first line after a change of comment
leader.

There was no check for out-of-memory in win_alloc().

"vim -h" didn't mention "-register" and "-unregister" for the OLE version.

Could not increase 'cmdheight' when the last window is only one line.  Now
other windows are also made smaller, when necessary.

Added a few {} to avoid "suggest braces around" warnings from gcc 2.8.x.
Changed return type of main() from void to int. (Nam)

Using '~' twice in a substitute pattern caused a crash.

"syn on" and ":syn off" could scroll the window, if there is a hidden buffer
that is shorter that the current cursor position.

":if 0 | if 1 | endif | endif" didn't work.  Same for ":while" and "elseif".

With two windows on modified files, with 'autowrite' set, cursor in second
window, ":qa" gave a warning for the file in the first window, but then
auto-wrote the file in the second window. (Webb)

Win32 GUI scrollbar could only handle 32767 lines.  Also makes the
intellimouse wheel use the configurable number of scrolls. (Robinson)

When using 'patchmode', and the backup file is on another partition, the file
copying messed up the write-file message.

GUI X11: Alt-Backspace and Alt-Delete didn't work.

"`0" could put the cursor after the last character in the line, causing
trouble for other commands, like "i".

When completing tags in insert mode with ^X^], some matches were skipped,
because the compare with other tags was wrong.  E.g., when "mnuFileSave" was
already there, "mnuFile" would be skipped. (Negri)

When scrolling up/down, a syntax item with "keepend" didn't work properly.
Now the flags are also stored for the syntax state at the start of each line.

When 'ic' was changed while 'hlsearch' is on, there was no redraw to show the
effect.

Win32 GUI: Don't display "No write since last chance" in a message box, but in
the Vim window.

==============================================================================
VERSION 5.2						*version-5.2*

Improvements made between version 5.1 and 5.2.


Long lines editable					*long-lines*
-------------------

A single long line that doesn't fit in the window doesn't show a line of @@@
anymore.  Redrawing starts at a character further on in the line, such that
the text around the cursor can be seen.  This makes it possible to edit these
long lines when wrapping is on.


File browser added					*file-browser-5.2*
------------------

The Win32, Athena and Motif GUI bring up a file requester if the user asks to
":browse" for the ":e", ":w", ":r", ":so", ":redirect" and
":mkexrc/vimrc/vsess" commands.  ":browse e /foo/bar" opens the requester in
the /foo/bar directory, so you can have nice mapping rhs's like ":browse so
$vim/macros".  If no initial dir specified for ":browse e", can be compiled to
either begin in the current directory, or that of the current buffer. (Negri
and Kahn)
Added the 'browsedir' option, with value "current", "last" or "buffer".  Tells
whether a browse dialog starts in last used dir, dir of current buffer, or
current dir.  ":browse w" is unaffected.
The default menus have been changed to use the ":browse" command.


Dialogs added						*dialogs-added*
-------------

Added the ":confirm" command.  Works on ":e", ":q", ":w", ":cl".  Win32,
Athena and Motif GUI uses a window-dialog.  All other platforms can use
prompt in command-line.  ":confirm qa" offers a choice to save all modified
files.

confirm() function: allows user access to the confirm engine.

Added 'v' flag to 'guioptions'.  When included, a vertical button layout is
always used for the Win32 GUI dialog.  Otherwise, a horizontal layout is
preferred.

Win32 GUI: ":promptfind" and ":promptrepl" pop up a dialog to find/replace.
To be used from a menu entry. (Negri)


Popup menu added					*popup-menu-added*
----------------

When the 'mousemodel' option is set to "popup", the right mouse button
displays the top level menu headed with "PopUp" as pop-up context menu.  The
"PopUp" menu is not displayed in the normal menu bar.  This currently only
works for Win32 and Athena GUI.


Select mode added					*new-Select-mode*
-----------------

A new mode has been added: "Select mode".  It is like Visual mode, but typing
a printable character replaces the selection.
- CTRL-G can be used to toggle between Visual mode and Select mode.
- CTRL-O can be used to switch from Select mode to Visual mode for one command.
- Added 'selectmode' option: tells when to start Select mode instead of Visual
  mode.
- Added 'mousemodel' option: Change use of mouse buttons.
- Added 'keymodel' option: tells to use shifted special keys to start a
  Visual or Select mode selection.
- Added ":behave".  Can be used to quickly set 'selectmode', 'mousemodel'
  and 'keymodel' for MS-Windows and xterm behavior.
- The xterm-like selection is now called modeless selection.
- Visual mode mappings and menus are used in Select mode.  They automatically
  switch to Visual mode first.  Afterwards, reselect the area, unless it was
  deleted.  The "gV" command can be used in a mapping to skip the reselection.
- Added the "gh", "gH" and "g^H" commands: start Select (highlight) mode.
- Backspace in Select mode deletes the selected area.

"mswin.vim" script.  Sets behavior mostly like MS-Windows.


Session files added					*new-session-files*
-------------------

":mks[ession]" acts like "mkvimrc", but also writes the full filenames of the
currently loaded buffers and current directory, so that :so'ing the file
re-loads those files and cd's to that directory.  Also stores and restores
windows.  File names are made relative to session file.
The 'sessionoptions' option sets behavior of ":mksession". (Negri)


User defined functions and commands			*new-user-defined*
-----------------------------------

Added user defined functions.  Defined with ":function" until ":endfunction".
Called with "Func()".  Allows the use of a variable number of arguments.
Included support for local variables "l:name".  Return a value with ":return".
See |:function|.
Call a function with ":call".  When using a range, the function is called for
each line in the range. |:call|
"macros/justify.vim" is an example of using user defined functions.
User functions do not change the last used search pattern or the command to be
redone with ".".
'maxfuncdepth' option.  Restricts the depth of function calls.  Avoids trouble
(crash because of out-of-memory) when a function uses endless recursion.

User definable Ex commands: ":command", ":delcommand" and ":comclear".
(Moore)  See |user-commands|.


New interfaces						*interfaces-5.2*
--------------

Tcl interface. (Wilken)  See |tcl|.
Uses the ":tcl", ":tcldo" and "tclfile" commands.

Cscope support. (Kahn) (Sekera)  See |cscope|.
Uses the ":cscope" and ":cstag" commands.  Uses the options 'cscopeprg',
'cscopetag', 'cscopetagorder' and 'cscopeverbose'.


New ports						*ports-5.2*
---------

Amiga GUI port. (Nielsen)  Not tested much yet!

RISC OS version. (Thomas Leonard)  See |riscos|.
This version can run either with a GUI or in text mode, depending upon where
it is invoked.
Deleted the "os_archie" files, they were not working anyway.


Multi-byte support			*new-multi-byte* *new-multibyte*
------------------

MultiByte support for Win32 GUI. (Baek)
The 'fileencoding' option decides how the text in the file is encoded.
":ascii" works for multibyte characters.  Multi-byte characters work on
Windows 95, even when using the US version. (Aaron)
Needs to be enabled in feature.h.
This has not been tested much yet!


New functions						*new-functions-5.2*
-------------

|browse()|	puts up a file requester when available. (Negri)
|escape()|	escapes characters in a string with a backslash.
|fnamemodify()|	modifies a file name.
|input()|	asks the user to enter a line. (Aaron)  There is a separate
		history for lines typed for the input() function.
|argc()|
|argv()|	can be used to access the argument list.
|winbufnr()|	buffer number of a window. (Aaron)
|winnr()|	window number. (Aaron)
|matchstr()|	Return matched string.
|setline()|	Set a line to a string value.


New options						*new-options-5.2*
-----------

'allowrevins'	Enable the CTRL-_ command in Insert and Command-line mode.
'browsedir'	Tells in which directory a browse dialog starts.
'confirm'	when set, :q :w and :e commands always act as if ":confirm"
		is used.  (Negri)
'cscopeprg'
'cscopetag'
'cscopetagorder'
'cscopeverbose'	Set the |cscope| behavior.
'filetype'	RISC-OS specific type of file.
'grepformat'
'grepprg'	For the |:grep| command.
'keymodel'	Tells to use shifted special keys to start a Visual or Select
		mode selection.
'listchars'	Set character to show in 'list' mode for end-of-line, tabs and
		trailing spaces. (partly by Smith) Also sets character to
		display if a line doesn't fit when 'nowrap' is set.
'matchpairs'	Allows matching '<' with '>', and other single character
		pairs.
'mousefocus'	Window focus follows mouse (partly by Terhaar).  Changing the
		focus with a keyboard command moves the pointer to that
		window.  Also move the pointer when changing the window layout
		(split window, change window height, etc.).
'mousemodel'	Change use of mouse buttons.
'selection'	When set to "inclusive" or "exclusive", the cursor can go one
		character past the end of the line in Visual or Select mode.
		When set to "old" the old behavior is used.  When
		"inclusive", the character under the cursor is included in the
		operation.  When using "exclusive", the new "ve" entry of
		'guicursor' is used.  The default is a vertical bar.
'selectmode'	Tells when to start Select mode instead of Visual mode.
'sessionoptions' Sets behavior of ":mksession". (Negri)
'showfulltag'	When completing a tag in Insert mode, show the tag search
		pattern (tidied up) as a choice as well (if there is one).
'swapfile'	Whether to use a swap file for a buffer.
'syntax'	When it is set, the syntax by that name is loaded.  Allows for
		setting a specific syntax from a modeline.
'ttymouse'	Allows using xterm mouse codes for terminals which name
		doesn't start with "xterm".
'wildignore'	List of patterns for files that should not be completed at
		all.
'wildmode'	Can be used to set the type of expansion for 'wildchar'.
		Replaces the CTRL-T command for command line completion.
		Don't beep when listing all matches.
'winaltkeys'	Win32 and Motif GUI.  When "yes", ALT keys are handled
		entirely by the window system.  When "no", ALT keys are never
		used by the window system.  When "menu" it depends on whether
		a key is a menu shortcut.
'winminheight'	Minimal height for each window.  Default is 1.  Set to 0 if
		you want zero-line windows.  Scrollbar is removed for
		zero-height windows. (Negri)



New Ex commands						*new-ex-commands-5.2*
---------------

|:badd|		Add file name to buffer list without side effects.  (Negri)
|:behave|	Quickly set MS-Windows or xterm behavior.
|:browse|	Use file selection dialog.
|:call|		Call a function, optionally with a range.
|:cnewer|
|:colder|	To access a stack of quickfix error lists.
|:comclear|	Clear all user-defined commands.
|:command|	Define a user command.
|:continue|	Go back to ":while".
|:confirm|	Ask confirmation if something unexpected happens.
|:cscope|	Execute cscope command.
|:cstag|	Use cscope to jump to a tag.
|:delcommand|	Delete a user-defined command.
|:delfunction|	Delete a user-defined function.
|:endfunction|	End of user-defined function.
|:function|	Define a user function.
|:grep|		Works similar to ":make". (Negri)
|:mksession|	Create a session file.
|:nohlsearch|	Stop 'hlsearch' highlighting for a moment.
|:Print|	This is Vi compatible.  Does the same as ":print".
|:promptfind|	Search dialog (Win32 GUI).
|:promptrepl|	Search/replace dialog (Win32 GUI).
|:return|	Return from a user-defined function.
|:simalt|	Win32 GUI: Simulate alt-key pressed.  (Negri)
|:smagic|	Like ":substitute", but always use 'magic'.
|:snomagic|	Like ":substitute", but always use 'nomagic'.
|:tcl|		Execute TCL command.
|:tcldo|	Execute TCL command for a range of lines.
|:tclfile|	Execute a TCL script file.
|:tearoff|	Tear-off a menu (Win32 GUI).
|:tmenu|
|:tunmenu|	Win32 GUI: menu tooltips.  (Negri)
|:star|	:*	Execute a register.


Changed							*changed-5.2*
-------

Renamed functions:
		buffer_exists()	   -> bufexists()
		buffer_name()      -> bufname()
		buffer_number()    -> bufnr()
		file_readable()    -> filereadable()
		highlight_exists() -> hlexists()
		highlightID()      -> hlID()
		last_buffer_nr()   -> bufnr("$")
The old ones are still there, for backwards compatibility.

The CTRL-_ command in Insert and Command-line mode is only available when the
new 'allowrevins' option is set.  Avoids that people who want to type SHIFT-_
accidentally enter reverse Insert mode, and don't know how to get out.

When a file name path in ":tselect" listing is too long, remove a part in the
middle and put "..." there.

Win32 GUI: Made font selector appear inside Vim window, not just any odd
place. (Negri)

":bn" skips help buffers, unless currently in a help buffer. (Negri)

When there is a status line and only one window, don't show '^' in the status
line of the current window.

":*" used to be used for "'<,'>", the Visual area.  But in Vi it's used as an
alternative for ":@".  When 'cpoptions' includes '*' this is Vi compatible.

When 'insertmode' is set, using CTRL-O to execute a mapping will work like
'insertmode' was not set.  This allows "normal" mappings to be used even when
'insertmode' is set.

When 'mouse' was set already (e.g., in the .vimrc file), don't automatically
set 'mouse' when the GUI starts.

Removed the 'N', 'I' and 'A' flags from the 'mouse' option.

Renamed "toggle option" to "boolean option".  Some people thought that ":set
xyz" would toggle 'xyz' on/off each time.

The internal variable "shell_error" contains the error code from the shell,
instead of just 0 or 1.

When inserting or replacing, typing CTRL-V CTRL-<CR> used to insert "<C-CR>".
That is not very useful.  Now the CTRL key is ignored and a <CR> is inserted.
Same for all other "normal" keys with modifiers.  Mapping these modified key
combinations is still possible.
In Insert mode, <C-CR> and <S-Space> can be inserted by using CTRL-K and then
the special character.

Moved "quotes" file to doc/quotes.txt, and "todo" file to doc/todo.txt.  They
are now installed like other documentation files.

winheight() function returns -1 for a non-existing window.  It used to be
zero, but that is a valid height now.

The default for 'selection' is "inclusive", which makes a difference when
using "$" or the mouse to move the cursor in Visual mode.

":q!" does not exit when there are changed buffers which are hidden.  Use
":qa!" to exit anyway.

Disabled the Perl/Python/Tcl interfaces by default.  Not many people use them
and they make the executable a lot bigger.  The internal scripting language is
now powerful enough for most tasks.

The strings from the 'titlestring' and 'iconstring' options are used
untranslated for the Window title and icon.  This allows for including a <CR>.
Previously a <CR> would be shown as "^M" (two characters).

When a mapping is started in Visual or Select mode which was started from
Insert mode (the mode shows "(insert) Visual"), don't return to Insert mode
until the mapping has ended.  Makes it possible to use a mapping in Visual
mode that also works when the Visual mode was started from Select mode.

Menus in $VIMRUNTIME/menu.vim no longer overrule existing menus.  This helps
when defining menus in the .vimrc file, or when sourcing mswin.vim.

Unix: Use /var/tmp for .swp files, if it exists.  Files there survive a
reboot (at least on Linux).


Added							*added-5.2*
-----

--with-motif-lib configure argument.  Allows for using a static Motif library.

Support for mapping numeric keypad +,-,*,/ keys. (Negri)
When not mapped, they produce the normal character.

Win32 GUI: When directory dropped on gVim, cd there and edit new buffer.
(Negri)

Win32 GUI: Made CTRL-Break work as interrupt, so that CTRL-C can be
used for mappings.

In the output of ":map", highlight the "*" to make clear it's not part of the
rhs. (Roemer)

When showing the Visual area, the cursor is not switched off, so that it can
be located.  The Visual area is now highlighted with a grey background in the
GUI.  This makes the cursor visible when it's also reversed.

Win32: When started with single full pathname (e.g. via double-clicked file),
cd to that file's directory. (Negri)

Win32 GUI: Tear-off menus, with ":tearoff <menu-name>" command. (Negri)
't' option to 'guioptions': Add tearoff menu items for Win32 GUI and Motif.
It's included by default.
Win32 GUI: tearoff menu with submenus is indicated with a ">>". (Negri)

Added ^Kaa and ^KAA digraphs.
Added "euro" symbol to digraph.c. (Corry)

Support for Motif menu shortcut keys, using '&' like MS-Windows (Ollis).
Other GUIs ignore '&' in a menu name.

DJGPP: Faster screen updating (John Lange).

Clustering of syntax groups ":syntax cluster" (Bigham).
Including syntax files: ":syntax include" (Bigham).

Keep column when switching buffers, when 'nosol' is set (Radics).

Number function for Perl interface.

Support for Intellimouse in Athena GUI. (Jensen)

":sleep" also accepts an argument in milliseconds, when "m" is used.

Added 'p' flag in 'guioptions': Install callbacks for enter/leave window
events.  Makes cursor blinking work for Terhaar, breaks it for me.

"--help" and "--version" command-line arguments.

Non-text in ":list" output is highlighted with NonText.

Added text objects: "i(" and "i)" as synonym for "ib".  "i{" and "i}" as
synonym for "iB".  New: "i<" and "i>", to select <thing>.  All this also for
"a" objects.

'O' flag in 'shortmess': message for reading a file overwrites any previous
message. (Negri)

Win32 GUI: 'T' flag in 'guioptions': switch toolbar on/off.
Included a list with self-made toolbar bitmaps.  (Negri)

Added menu priority for sub-menus.  Implemented for Win32 and Motif GUI.
Display menu priority with ":menu" command.
Default and Syntax menus now include priority for items.  Allows inserting
menu items in between the default ones.

When the 'number' option is on, highlight line numbers with the LineNr group.

"Ignore" highlight group: Text highlighted with this is made blank.  It is
used to hide special characters in the help text.

Included Exuberant Ctags version 2.3, with C++ support, Java support and
recurse into directories. (Hiebert)

When a tags file is not sorted, and this is detected (in a simplistic way), an
error message is given.

":unlet" accepts a "!", to ignore non-existing variables, and accepts more
than one argument. (Roemer)
Completion of variable names for ":unlet". (Roemer)

When there is an error in a function which is called by another function, show
the call stack in the error message.

New file name modifiers:
":.": reduce file name to be relative to current dir.
":~": reduce file name to be relative to home dir.
":s?pat?sub?": substitute "pat" with "sub" once.
":gs?pat?sub?": substitute "pat" with "sub" globally.

New configure arguments: --enable-min-features and --enable-max-features.
Easy way to switch to minimum or maximum features.

New compile-time feature: modify_fname.  For file name modifiers, e.g,
"%:p:h".  Can be disabled to save some code (16 bit DOS).

When using whole-line completion in Insert mode, and 'cindent' is set, indent
the line properly.

MSDOS and Win32 console: 'guicursor' sets cursor thickness. (Negri)

Included new set of Farsi fonts. (Shiran)

Accelerator text now also works in Motif.  All menus can be defined with & for
mnemonic and TAB for accelerator text.  They are ignored on systems that don't
support them.
When removing or replacing a menu, compare the menu name only up to the <Tab>
before the mnemonic.

'i' and 'I' flags after ":substitute": ignore case or not.

"make install" complains if the runtime files are missing.

Unix: When finding an existing swap file that can't be opened, mention the
owner of the file in the ATTENTION message.

The 'i', 't' and 'k' options in 'complete' now also print the place where they
are looking for matches. (Acevedo)

"gJ" command: Join lines without inserting a space.

Setting 'keywordprg' to "man -s" is handled specifically.  The "-s" is removed
when no count given, the count is added otherwise.  Configure checks if "man
-s 2 read" works, and sets the default for 'keywordprg' accordingly.

If you do a ":bd" and there is only one window open, Vim tries to move to a
buffer of the same type (i.e. non-help to non-help, help to help), for
consistent behavior to :bnext/:bprev. (Negri)

Allow "<Nop>" to be used as the rhs of a mapping.  ":map xx <Nop>", maps "xx"
to nothing at all.

In a ":menu" command, "<Tab>" can be used instead of a real tab, in the menu
path.  This makes it more easy to type, no backslash needed.

POSIX compatible character classes for regexp patterns: [:alnum:], [:alpha:],
[:blank:], [:cntrl:], [:digit:], [:graph:], [:lower:], [:print:], [:punct:],
[:space:], [:upper:] and [:xdigit:]. (Briscoe)

regexp character classes (for fast syntax highlight matching):
	digits:	    \d [0-9]		\D  not digit (Roemer)
	hex:	    \x [0-9a-fA-F]	\X  not hex
	octal:	    \o [0-7]		\O  not octal
	word:	    \w [a-zA-Z0-9_]	\W  not word
	head:	    \h [a-zA-Z_]	\H  not head
	alphabetic: \a [a-zA-Z]		\A  not alphabetic
	lowercase:  \l [a-z]		\L  not lowercase
	uppercase:  \u [A-Z]		\U  not uppercase

":set" now accepts "+=", |^=" and "-=": add or remove parts of a string
option, add or subtract a number from a number option.  A comma is
automagically inserted or deleted for options that are a comma-separated list.

Filetype feature, for autocommands.  Uses a file type instead of a pattern to
match a file.  Currently only used for RISC OS.  (Leonard)

In a pattern for an autocommand, environment variables can be used.  They are
expanded when the autocommand is defined.

"BufFilePre" and "BufFilePost" autocommand evens: Before and after applying
the ":file" command to change the name of a buffer.
"VimLeavePre" autocommand event: before writing the .viminfo file.

For autocommands argument: <abuf> is buffer number, like <afile>.

Made syntax highlighting a bit faster when scrolling backwards, by keeping
more syncing context.

Win32 GUI: Made scrolling faster by avoiding a redraw when deleting or
inserting screen lines.

GUI: Made scrolling faster by not redrawing the scrollbar when the thumb moved
less than a pixel.

Included ":highlight" in bugreport.vim.

Created install.exe program, for simplistic installation on DOS and
MS-Windows.

New register: '_', the black hole.  When writing to it, nothing happens.  When
reading from it, it's always empty.  Can be used to avoid a delete or change
command to modify the registers, or reduce memory use for big changes.

CTRL-V xff enters character by hex number.  CTRL-V o123 enters character by
octal number. (Aaron)

Improved performance of syntax highlighting by skipping check for "keepend"
when there isn't any.

Moved the mode message ("-- INSERT --") to the last line of the screen.  When
'cmdheight' is more than one, messages will remain readable.

When listing matching files, they are also sorted on 'suffixes', such that
they are listed in the same order as CTRL-N retrieves them.

synIDattr() takes a third argument (optionally), which tells for which
terminal type to get the attributes for.  This makes it possible to run
2html.vim outside of gvim (using color names instead of #RRGGBB).

Memory profiling, only for debugging.  Prints at exit, and with "g^A" command.
(Kahn)

DOS: When using a file in the current drive, remove the drive name:
"A:\dir\file" -> "\dir\file".  This helps when moving a session file on a
floppy from "A:\dir" to "B:\dir".

Increased number of remembered jumps from 30 to 50 per window.

Command to temporarily disable 'hls' highlighting until the next search:
":nohlsearch".

"gp" and "gP" commands: like "p" and "P", but leave the cursor just after the
inserted text.  Used for the CTRL-V command in MS-Windows mode.


Fixed							*fixed-5.2*
-----

Win32 GUI: Could draw text twice in one place, for fake-bold text.  Removed
this, Windows will handle the bold text anyway. (Negri)

patch 5.1.1: Win32s GUI: pasting caused a crash (Negri)

patch 5.1.2: When entering another window, where characters before the cursor
have been deleted, could have a cursor beyond the end of the line.

patch 5.1.3: Win32s GUI: Didn't wait for external command to finish. (Negri)

patch 5.1.4: Makefile.w32 can now also be used to generate the OLE version
(Scott).

patch 5.1.5: Crashed when using syntax highlighting: cursor on a line that
doesn't fit in the window, and splitting that line in two.

patch 5.1.6: Visual highlighting bug: After ":set nowrap", go to end of line
(so that the window scrolls horizontally), ":set wrap".  Following Visual
selection was wrong.

patch 5.1.7: When 'tagbsearch' off, and 'ignorecase' off, still could do
binary searching.

patch 5.1.8: Win32 GUI: dragging the scrollbar didn't update the ruler.

patch 5.1.9: Using ":gui" in .vimrc, caused xterm cursor to disappear.

patch 5.1.10: A CTRL-N in Insert mode could cause a crash, when a buffer
without a name exists.

patch 5.1.11: "make test" didn't work in the shadow directory.  Also adjusted
"make shadow" for the links in the ctags directory.

patch 5.1.12: "buf 123foo" used "123" as a count, instead as the start of a
buffer name.

patch 5.1.13: When completing file names on the command-line, reallocating the
command-line may go wrong.

patch 5.1.14: ":[nvci]unmenu" removed menu for all modes, when full menu patch
specified.

Graceful handling of NULLs in drag-dropped file list.  Handle passing NULL to
Fullname_save(). (Negri)

Win32: ":!start" to invoke a program without opening a console, swapping
screens, or waiting for completion in either console or gui version, e.g. you
can type ":!start winfile".  ALSO fixes "can't delete swapfile after spawning
a shell" bug. (enhancement of Aaron patch) (Negri)

Win32 GUI: Fix CTRL-X default keymapping to be more Windows-like. (Negri)

Shorten filenames on startup.  If in /foo/bar, entering "vim ../bar/bang.c"
displays "bang.c" in status bar, not "/foo/bar/bang.c"  (Negri)

Win32 GUI: No copy to Windows clipboard when it's not desired.

Win32s: Fix pasting from clipboard - made an assumption not valid under
Win32s. (Negri)

Win32 GUI: Speed up calls to gui_mch_draw_string() and cursor drawing
functions. (Negri)

Win32 GUI: Middle mouse button emulation now works in GUI! (Negri)

Could skip messages when combining commands in one line, e.g.:
":echo "hello" | write".

Perl interpreter was disabled before executing VimLeave autocommands.  Could
not use ":perl" in them.  (Aaron)

Included patch for the Intellimouse (Aaron/Robinson).

Could not set 'ls' to one, when last window has only one line.  (Mitterand)

Fixed a memory leak when removing menus.

After ":only" the ruler could overwrite a message.

Dos32: removed changing of __system_flags.  It appears to work better when
it's left at the default value.

p_aleph was an int instead of along, caused trouble on systems where
sizeof(int) != sizeof(long). (Schmidt)

Fixed enum problems for Ultrix. (Seibert)

Small redraw problem: "dd" on last line in file cleared wrong line.

Didn't interpret "cmd | endif" when "cmd" starts with a range.  E.g. "if 0 |
.d | endif".

Command "+|" on the last line of the file caused ml_get errors.

Memory underrun in eval_vars(). (Aaron)

Don't rename files in a difficult way, except on Windows 95 (was also done on
Windows NT).

Win32 GUI: An external command that produces an error code put the error
message in a dialog box.  had to close the window and close the dialog.  Now
the error code is displayed in the console. (Negri)

"comctl32.lib" was missing from the GUI libraries in Makefile.w32. (Battle)

In Insert mode, when entering a window in Insert mode, allow the cursor to be
one char beyond the text.

Renamed machine dependent rename() to mch_rename().  Define mch_rename() to
rename() when it works properly.

Rename vim_chdir() to mch_chdir(), because it's machine dependent.

When using an arglist, and editing file 5 of 4, ":q" could cause "-1 more
files to edit" error.

In if_python.c, VimCommand() caused an assertion when a do_cmdline() failed.
Moved the Python_Release_Vim() to before the VimErrorCheck().  (Harkins)

Give an error message for an unknown argument after "--".  E.g. for "vim
--xyz".

The FileChangedShell autocommand didn't set <afile> to the name of the changed
file.

When doing ":e file", causing the attention message, there sometimes was no
hit-enter prompt.  Caused by empty line or "endif" at end of sourced file.

A large number of patches for the VMS version. (Hunsaker)

When CTRL-L completion (find longest match) results in a shorter string, no
completion is done (happens with ":help").

Crash in Win32 GUI version, when using an Ex "@" command, because
LinePointers[] was used while not initialized.

Win32 GUI: allow mapping of Alt-Space.

Output from "vim -h" was sent to stderr.  Sending it to stdout is better, so
one can use "vim -h | more".

In command-line mode, ":vi[!]" should reload the file, just like ":e[!]".
In Ex mode, ":vi" stops Ex mode, but doesn't reload the file.  This is Vi
compatible.

When using a ":set ls=1" in the .gvimrc file, would get a status line for a
single window.  (Robinson)

Didn't give an error message for ":set ai,xx". (Roemer)
Didn't give an error message for ":set ai?xx", ":set ai&xx", ":set ai!xx".

Non-Unix systems: That a file exists but is unreadable is recognized as "new
file".  Now check for existence when file can't be opened (like Unix).

Unix: osdef.sh didn't handle declarations where the function name is at the
first column of the line.

DJGPP: Shortening of file names didn't work properly, because get_cwd()
returned a path with backslashes. (Negri)

When using a 'comments' part where a space is required after the middle part,
always insert a space when starting a new line.  Helps for C comments, below a
line with "/****".

Replacing path of home directory with "~/" could be wrong for file names
with embedded spaces or commas.

A few fixes for the Sniff interface. (Leherbauer)

When asking to hit 'y' or 'n' (e.g. for ":3,1d"), using the mouse caused
trouble.  Same for ":s/x/y/c" prompt.

With 'nowrap' and 'list', a Tab halfway on the screen was displayed as blanks,
instead of the characters specified with 'listchars'.  Also for other
characters that take more than one screen character.

When setting 'guifont' to an unknown font name, the previous font was lost and
a default font would be used. (Steed)

DOS: Filenames in the root directory didn't get shortened properly. (Negri)

DJGPP: making a full path name out of a file name didn't work properly when
there is no _fullpath() function. (Negri)

Win32 console: ":sh" caused a crash. (Negri)

Win32 console: Setting 'lines' and/or 'columns' in the _vimrc failed miserably
(could hang Windows 95). (Negri)

Win32: The change-drive function was not correct, went to the wrong drive.
(Tsindlekht)

GUI: When editing a command line in Ex mode, Tabs were sometimes not
backspaced properly, and unprintable characters were displayed directly.
non-GUI can still be wrong, because a system function is called for this.

":set" didn't stop after an error.  For example ":set no ai" gave an error for
"no", but still set "ai".  Now ":set" stops after the first error.

When running configure for ctags, $LDFLAGS wasn't passed to it, causing
trouble for IRIX.

"@%" and "@#" when file name not set gave an error message.  Now they just
return an empty string. (Steed)

CTRL-X and CTRL-A didn't work correctly with negative hex and octal numbers.
(Steed)

":echo" always started with a blank.

Updating GUI cursor shape didn't always work (e.g., when blinking is off).

In silent Ex mode ("ex -s" or "ex <file") ":s///p" didn't print a line.  Also
a few other commands that explicitly print a text line didn't work.  Made this
Vi compatible.

Win32 version of _chdrive() didn't return correct value. (Tsindlekht)

When using 't' in 'complete' option, no longer give an error message for a
missing tags file.

Unix: tgoto() can return NULL, which was not handled correctly in configure.

When doing ":help" from a buffer where 'binary' is set, also edited the help
file in binary mode.  Caused extra ^Ms for DOS systems.

Cursor position in a file was reset to 1 when closing a window.

":!ls" in Ex mode switched off echo.

When doing a double click in window A, while currently in window B, first
click would reset double click time, had to click three times to select a
word.

When using <F11> in mappings, ":mkexrc" produced an exrc file that can't be
used in Vi compatible mode.  Added setting of 'cpo' to avoid this.  Also, add
a CTRL-V in front of a '<', to avoid a normal string to be interpreted as a
special key name.

Gave confusing error message for ":set guifont=-*-lucida-*": first "font is
not fixed width", then "Unknown font".

Some options were still completely left out, instead of included as hidden
options.

While running the X11 GUI, ignore SIGHUP signals.  Avoids a crash after
executing an external command (in rare cases).

In os_unixx.h, signal() was defined to sigset(), while it already was.

Memory leak when executing autocommands (was reported as a memory leak in
syntax highlighting).

Didn't print source of error sometimes, because pointers were the same,
although names were different.

Avoid a number of UMR errors from Purify (third argument to open()).

A swap file could still be created just after setting 'updatecount' to zero,
when there is an empty buffer and doing ":e file". (Kutschera)

Test 35 failed on 64 bit machines. (Schild)

With "p" and "P" commands, redrawing was slow.

Awk script for html documentation didn't work correctly with AIX awk.
Replaced "[ ,.);\]	]" with "[] ,.);	]". (Briscoe)
The makehtml.awk script had a small problem, causing extra lines to be
inserted. (Briscoe)

"gqgq" could not be repeated.  Repeating for "gugu" and "gUgU" worked in a
wrong way.  Also made "gqq" work to be consistent with "guu".

C indent was wrong after "case ':':".

":au BufReadPre *.c put": Line from put text was deleted, because the buffer
was still assumed to be empty.

Text pasted with the Edit/Paste menu was subject to 'textwidth' and
'autoindent'.  That was inconsistent with using the mouse to paste.  Now "*p
is used.

When using CTRL-W CTRL-] on a word that's not a tag, and then CTRL-] on a tag,
window was split.

":ts" got stuck on a tags line that has two extra fields.

In Insert mode, with 'showmode' on, <C-O><C-G> message was directly
overwritten by mode message, if preceded with search command warning message.

When putting the result of an expression with "=<expr>p, newlines were
inserted like ^@ (NUL in the file).  Now the string is split up in lines at
the newline.

putenv() was declared with "const char *" in pty.c, but with "char *" in
osdef2.h.in.  Made the last one also "const char *".

":help {word}", where +{word} is a feature, jumped to the feature list instead
of where the command was explained.  E.g., ":help browse", ":help autocmd".

Using the "\<xx>" form in an expression only got one byte, even when using a
special character that uses several bytes (e.g., "\<F9>").
Changed "\<BS>" to produce CTRL-H instead of the special key code for the
backspace key.  "\<Del>" produces 0x7f.

":mkvimrc" didn't write a command to set 'compatible' or 'nocompatible'.

The shell syntax didn't contain a "syn sync maxlines" setting.  In a long file
without recognizable items, syncing took so long it looked like Vim hangs.
Added a maxlines setting, and made syncing interruptible.

The "gs" command didn't flush output before waiting.

Memory leaks for:
    ":if 0 | let a = b . c | endif"
    "let a = b[c]"
    ":so {file}" where {file} contains a ":while"

GUI: allocated fonts were never released. (Leonard)

Makefile.bor:
- Changed $(DEFINES) into a list of "-D" options, so that it can also be used
  for the resource compiler. (not tested!)
- "bcc.cfg" was used for all configurations.  When building for another
  configuration, the settings for the previous one would be used.  Moved
  "bcc.cfg" to the object directory. (Geddes)
- Included targets for vimrun, install, ctags and xxd.  Changed the default to
  use the Borland DLL Runtime Library, makes Vim.exe a log smaller. (Aaron)

"2*" search for the word under the cursor with "2" prepended. (Leonard)

When deleting into a specific register, would still overwrite the non-Win32
GUI selection.  Now ""x"*P works.

When deleting into the "" register, would write to the last used register.
Now ""x always writes to the unnamed register.

GUI Athena: A submenu with a '.' in it didn't work.  E.g.,
":amenu Syntax.XY\.Z.foo lll".

When first doing ":tag foo" and then ":tnext" and/or ":tselect" the order of
matching tags could change, because the current file is different.  Now the
existing matches are kept in the same order, newly found matches are added
after them, not matter what the current file is.

":ta" didn't find the second entry in a tags file, if the second entry was
longer than the first one.

When using ":set si tw=7" inserting "foo {^P}" made the "}" inserted at the
wrong position.  can_si was still TRUE when the cursor is not in the indent of
the line.

Running an external command in Win32 version had the problem that Vim exits
when the X on the console is hit (and confirmed).  Now use the "vimrun"
command to start the external command indirectly. (Negri)

Win32 GUI: When running an external filter, do it in a minimized DOS box.
(Negri)

":let" listed variables without translation into printable characters.

Win32 console: When resizing the window, switching back to the old size
(when exiting or executing an external command) sometimes failed. (Negri)
This appears to also fix a "non fixable" problem:
Win32 console in NT 4.0: When running Vim in a cmd window with a scrollbar,
the scrollbar disappeared and was not restored when Vim exits.  This does work
under NT 3.51, it appears not to be a Vim problem.

When executing BufDelete and BufUnload autocommands for a buffer without a
name, the name of the current buffer was used for <afile>.

When jumping to a tag it reported "tag 1 of >2", while in fact there could be
only two matches.  Changed to "tag 1 of 2 or more".

":tjump tag" did a linear search in the tags file, which can be slow.

Configure didn't find "LibXm.so.2.0", a Xm library with a version number.

Win32 GUI: When using a shifted key with ALT, the shift modifier would remain
set, even when it was already used by changing the used key.  E.g., "<M-S-9>"
resulted in "<M-S-(>", but it should be "<M-(>". (Negri)

A call to ga_init() was often followed by setting growsize and itemsize.
Created ga_init2() for this, which looks better. (Aaron)

Function filereadable() could call fopen() with an empty string, which might
be illegal.

X Windows GUI: When executing an external command that outputs text, could
write one character beyond the end of a buffer, which caused a crash. (Kohan)

When using "*" or "#" on a string that includes '/' or '?' (when these are
included in 'isk'), they were not escaped. (Parmelan)

When adding a ToolBar menu in the Motif GUI, the submenu_id field was not
cleared, causing random problems.

When adding a menu, the check if this menu (or submenu) name already exists
didn't compare with the simplified version (no mnemonic or accelerator) of the
new menu.  Could get two menus with the same name, e.g., "File" and "&File".

Breaking a line because of 'textwidth' at the last line in the window caused a
redraw of the whole window instead of a scroll.  Speeds up normal typing with
'textwidth' a lot for slow terminals.

An invalid line number produced an "invalid range" error, even when it wasn't
to be executed (inside "if 0").

When the unnamed, first buffer is re-used, the "BufDelete" autocommand was
not called.  It would stick in a buffer list menu.

When doing "%" on the NUL after the line, a "{" or "}" in the last character
of the line was not found.

The Insert mode menu was not used for the "s" command, the Operator-pending
menu was used instead.

With 'compatible' set, some syntax highlighting was not correct, because of
using "[\t]" for a search pattern.  Now use the regexps for syntax
highlighting like the 'cpoptions' option is empty (as was documented already).

When using "map <M-Space> ms" or "map <Space> sss" the output of ":map" didn't
show any lhs for the mapping (if 'isprint' includes 160).  Now always use
<Space> and <M-Space>, even when they are printable.

Adjusted the Syntax menu, so that the lowest entry fits on a small screen (for
Athena, where menus don't wrap).

When using CTRL-E or CTRL-Y in Insert mode for characters like 'o', 'x' and
digits, repeating the insert didn't work.

The file "tools/ccfilter.README.txt" could not be unpacked when using short
file names, because of the two dots.  Renamed it to
"tools/ccfilter_README.txt".

For a dark 'background', using Blue for Directory and SpecialKey highlight
groups is not very readable.  Use Cyan instead.

In the function uc_scan_attr() in ex_docmd.c there was a goto that jumped into
a block with a local variable.  That's illegal for some compilers.

Win32 GUI: There was a row of pixels at the bottom of the window which was not
drawn. (Aaron)

Under DOS, editing "filename/" created a swap file of "filename/.swp".  Should
be "filename/_swp".

Win32 GUI: pointer was hidden when executing an external command.

When 'so' is 999, "J" near the end of the file didn't redisplay correctly.

":0a" inserted after the first line, instead of before the first line.

Unix: Wildcard expansion didn't handle single quotes and {} patterns.  Now
":file 'window.c'" removes the quotes and ":e 'main*.c'" works (literal '*').
":file {o}{n}{e}" now results in file name "one".

Memory leak when setting a string option back to its default value.

==============================================================================
VERSION 5.3						*version-5.3*

Version 5.3 was a bug-fix version of 5.2.  There are not many changes.
Improvements made between version 5.2 and 5.3:

Changed							*changed-5.3*
-------

Renamed "IDE" menu to "Tools" menu.


Added							*added-5.3*
-----

Win32 GUI: Give a warning when Vim is activated, and one of the files changed
since editing started. (Negri)


Fixed							*fixed-5.3*
-----

5.2.1: Win32 GUI: space for external command was not properly allocated, could
cause a crash. (Aaron)  This was the reason to bring out 5.3 quickly after
5.2.

5.2.2: Some commands didn't complain when used without an argument, although
they need one: ":badd", ":browse", ":call", ":confirm", ":behave",
":delfunction", ":delcommand" and ":tearoff".
":endfunction" outside of a function gave wrong error message: "Command not
implemented".  Should be ":endfunction not inside a function".

5.2.3: Win32 GUI: When gvim was installed in "Program files", or another path
with a space in it, executing external commands with vimrun didn't work.

5.2.4: Pasting with the mouse in Insert mode left the cursor on the last
pasted character, instead of behind it.

5.2.5: In Insert mode, cursor after the end of the line, a shift-cursor-left
didn't include the last character in the selection.

5.2.6: When deleting text from Insert mode (with "<C-O>D" or the mouse), which
includes the last character in the line, the cursor could be left on the last
character in the line, instead of just after it.

5.2.7: Win32 GUI: scrollbar was one pixel too big.

5.2.8: Completion of "PopUp" menu showed the derivatives "PopUpc", "PopUPi",
etc.  ":menu" also showed these.

5.2.9: When using two input() functions on a row, the prompt would not be
drawn in column 0.

5.2.10: A loop with input() could not be broken with CTRL-C.

5.2.11: ":call asdf" and ":call asdf(" didn't give an error message.

5.2.12: Recursively using ":normal" crashes Vim after a while.  E.g.:
":map gq :normal gq<CR>"

5.2.13: Syntax highlighting used 'iskeyword' from wrong buffer.  When using
":help", then "/\k*" in another window with 'hlsearch' set.

5.2.14: When using ":source" from a function, global variables would not be
available unless "g:" was used.

5.2.15: XPM files can have the extension ".pm", which is the same as for Perl
modules.  Added "syntax/pmfile.vim" to handle this.

5.2.16: On Win32 and Amiga, "echo expand("%:p:h")" removed one dirname in an
empty buffer.  mch_Fullname() didn't append a slash at the end of a directory
name.

Should include the character under the cursor in the Visual area when using
'selection' "exclusive".  This wasn't done for "%", "e", "E", "t" and "f".

""p would always put register 0, instead of the unnamed (last used) register.
Reverse the change that ""x doesn't write in the unnamed (last used) register.
It would always write in register 0, which isn't very useful.  Use "-x for the
paste mappings in Visual mode.

When there is one long line on the screen, and 'showcmd' is off, "0$" didn't
redraw the screen.

Win32 GUI: When using 'mousehide', the pointer would flicker when the cursor
shape is changed. (Negri)

When cancelling Visual mode, and the cursor moves to the start, the wanted
column wasn't set, "k" or "j" moved to the wrong column.

When using ":browse" or ":confirm", was checking for a comment and separating
bar, which can break some commands.

Included fixes for Macintosh. (Kielhorn)

==============================================================================
VERSION 5.4						*version-5.4*

Version 5.4 adds new features, useful changes and a lot of bug fixes.


Runtime directory introduced				*new-runtime-dir*
----------------------------

The distributed runtime files are now in $VIMRUNTIME, the user files in $VIM.
You normally don't set $VIMRUNTIME but let Vim find it, by using
$VIM/vim{version}, or use $VIM when that doesn't exist.  This allows for
separating the user files from the distributed files and makes it more easy to
upgrade to another version.  It also makes it possible to keep two versions of
Vim around, each with their own runtime files.

In the Unix distribution the runtime files have been moved to the "runtime"
directory.  This makes it possible to copy all the runtime files at once,
without the need to know what needs to be copied.

The archives for DOS, Windows, Amiga and OS/2 now have an extra top-level
"vim" directory.  This is to make clear that user-modified files should be put
here.  The directory that contains the executables doesn't have '-' or '.'
characters.  This avoids strange extensions.

The $VIM and $VIMRUNTIME variables are set when they are first used.  This
allows them to be used by Perl, for example.

The runtime files are also found in a directory called "$VIM/runtime".  This
helps when running Vim after just unpacking the runtime archive.  When using
an executable in the "src" directory, Vim checks if "vim54" or "runtime" can
be added after removing it.  This make the runtime files be found just after
compiling.

A default for $VIMRUNTIME can be given in the Unix Makefile.  This is useful
if $VIM doesn't point to above the runtime directory but to e.g., "/etc/".


Filetype introduced					*new-filetype-5.4*
-------------------

Syntax files are now loaded with the new FileType autocommand.  Old
"mysyntaxfile" files will no longer work. |filetypes|

The scripts for loading syntax highlighting have been changed to use the
new Syntax autocommand event.

This combination of Filetype and Syntax events allows tuning the syntax
highlighting a bit more, also when selected from the Syntax menu.  The
FileType autocommand can also be used to set options and mappings specifically
for that type of file.

The "$VIMRUNTIME/filetype.vim" file is not loaded automatically.  The
":filetype on" command has been added for this.  ":syntax on" also loads it.

The 'filetype' option has been added.  It is used to trigger the FileType
autocommand event, like the 'syntax' option does for the Syntax event.

":set syntax=OFF" and ":set syntax=ON" can be used (in a modeline) to switch
syntax highlighting on/off for the current file.

The Syntax menu commands have been moved to $VIMRUNTIME/menu.vim.  The Syntax
menu is included both when ":filetype on" and when ":syntax manual" is used.

Renamed the old 'filetype' option to 'osfiletype'.  It was only used for
RISCOS.  'filetype' is now used for the common file type.

Added the ":syntax manual" command.  Allows manual selection of the syntax to
be used, e.g., from a modeline.


Vim script line continuation			*new-line-continuation*
----------------------------

When an Ex line starts with a backslash, it is concatenated to the previous
line.  This avoids the need for long lines. |line-continuation| (Roemer)
Example: >
	if   has("dialog_con") ||
	   \ has("dialog_gui")
	    :let result = confirm("Enter your choice",
				\ "&Yes\n&No\n&Maybe",
				\ 2)
	endif


Improved session files				*improved-sessions*
----------------------

New words for 'sessionoptions':
- "help"	Restore the help window.
- "blank"	Restore empty windows.
- "winpos"	Restore the Vim window position.  Uses the new ":winpos"
		command
- "buffers"	Restore hidden and unloaded buffers.  Without it only the
		buffers in windows are restored.
- "slash"	Replace backward by forward slashes in file names.
- "globals"	Store global variables.
- "unix"	Use unix file format (<NL> instead of <CR><NL>)

The ":mksession" and 'sessionoptions' are now in the +mksession feature.

The top line of the window is also restored when using a session file.

":mksession" and ":mkvimrc" don't store 'fileformat', it should be detected
when loading a file.

(Most of this was done by Vince Negri and Robert Webb)


Autocommands improved				*improved-autocmds-5.4*
---------------------

New events:
|FileType|	When the file type has been detected.
|FocusGained|	When Vim got input focus. (Negri)
|FocusLost|	When Vim lost input focus. (Negri)
|BufCreate|	Called just after a new buffer has been created or has been
		renamed. (Madsen)
|CursorHold|	Triggered when no key has been typed for 'updatetime'.  Can be
		used to do something with the word under the cursor. (Negri)
		Implemented CursorHold autocommand event for Unix. (Zellner)
		Also for Amiga and MS-DOS.
|GUIEnter|	Can be used to do something with the GUI window after it has
		been created (e.g., a ":winpos 100 50").
|BufHidden|	When a buffer becomes hidden.  Used to delete the
		option-window when it becomes hidden.

Also trigger |BufDelete| just before a buffer is going to be renamed. (Madsen)

The "<amatch>" pattern can be used like "<afile>" for autocommands, except
that it is the matching value for the FileType and Syntax events.

When ":let @/ = <string>" is used in an autocommand, this last search pattern
will be used after the autocommand finishes.

Made loading autocommands a bit faster.  Avoid doing strlen() on each exiting
pattern for each new pattern by remembering the length.


Encryption						*new-encryption*
----------

Files can be encrypted when writing and decrypted when reading.  Added the
'key' option, "-x" command line argument and ":X" command. |encryption| (based
on patch from Mohsin Ahmed)

When reading a file, there is an automatic detection whether it has been
encrypted.  Vim will then prompt for the key.

Note that the encryption method is not compatible with Vi.  The encryption is
not unbreakable.  This allows it to be exported from the US.


GTK GUI port						*new-GTK-GUI*
------------

New GUI port for GTK+.  Includes a toolbar, menu tearoffs, etc. |gui-gtk|
Added the |:helpfind| command. (Kahn and Dalecki)


Menu changes						*menu-changes-5.4*
------------

Menus can now also be used in the console.  It is enabled by the new
'wildmenu' option.  This shows matches for command-line completion like a
menu.  This works as a minimal file browser.

The new |:emenu| command can be used to execute a menu item.

Uses the last status line to list items, or inserts a line just above the
command line.  (Negri)

The 'wildcharx' option can be used to trigger 'wildmenu' completion from a
mapping.

When compiled without menus, this can be detected with has("menu").  Also show
this in the ":version" output.  Allow compiling GUI versions without menu
support.  Only include toolbar support when there is menu support.

Moved the "Window" menu all the way to the right (priority 70).  Looks more
familiar for people working with MS-Windows, shouldn't matter for others.

Included "Buffers" menu.  Works with existing autocommands and functions.  It
can be disabled by setting the "no_buffers_menu" variable.  (Aaron and Madsen)

Win32 supports separators in a menu: "-.*-". (Geddes)
Menu separators for Motif now work too.

Made Popup menu for Motif GUI work. (Madsen)

'M' flag in 'guioptions': Don't source the system menu.

All the menu code has been moved from gui.c to menu.c.


Viminfo improved					*improved-viminfo*
----------------

New flags for 'viminfo':
'!'	Store global variables in the viminfo file if they are in uppercase
	letters. (Negri)
'h'	Do ":nohlsearch" when loading a viminfo file.

Store search patterns in the viminfo file with their offset, magic, etc.  Also
store the flag whether 'hlsearch' highlighting is on or off (which is not used
if the 'h' flag is in 'viminfo').

Give an error message when setting 'viminfo' without commas.


Various new commands					*new-commands-5.4*
--------------------

Operator |g?|: rot13 encoding. (Negri)

|zH| and |zL| commands: Horizontal scrolling by half a page.
|gm| move cursor to middle of screen line. (Ideas by Campbell)

Operations on Visual blocks: |v_b_I|, |v_b_A|, |v_b_c|, |v_b_C|, |v_b_r|,
|v_b_<| and |v_b_>|. (Kelly)

New command: CTRL-\ CTRL-N, which does nothing in Normal mode, and goes to
Normal mode when in Insert or Command-line mode.  Can be used by VisVim or
other OLE programs to make sure Vim is in Normal mode, without causing a beep.
|CTRL-\_CTRL-N|

":cscope kill" command to use the connection filename. |:cscope| (Kahn)

|:startinsert| command: Start Insert mode next.

|:history| command, to show all four types of histories. (Roemer)

|[m|, |[M|, |]m| and |]M| commands, for jumping backward/forward to start/end
of method in a (Java) class.

":@*" executes the * register. |:@| (Acevedo)

|go| and |:goto| commands: Jump to byte offset in the file.

|gR| and |gr| command: Virtual Replace mode.  Replace characters without
changing the layout. (Webb)

":cd -" changes to the directory from before the previous ":cd" command.
|:cd-| (Webb)

Tag preview commands |:ptag|.  Shows the result of a ":tag" in a dedicated
window.  Can be used to see the context of the tag (e.g., function arguments).
(Negri)
|:pclose| command, and CTRL-W CTRL-Z: Close preview window. (Moore)
'previewheight' option, height for the preview window.
Also |:ppop|, |:ptnext|, |:ptprevious|, |:ptNext|, |:ptrewind|, |:ptlast|.

|:find| and |:sfind| commands: Find a file in 'path', (split window) and edit
it.

The |:options| command opens an option window that shows the current option
values.  Or use ":browse set" to open it.  Options are grouped by function.
Offers short help on each option.  Hit <CR> to jump to more help.  Edit the
option value and hit <CR> on a "set" line to set a new value.


Various new options					*new-options-5.4*
-------------------

Scroll-binding: 'scrollbind' and 'scrollopt' options.  Added |:syncbind|
command.  Makes windows scroll the same amount (horizontally and/or
vertically). (Ralston)

'conskey' option for MS-DOS.  Use direct console I/O.  This should work with
telnet (untested!).

'statusline' option: Configurable contents of the status line.  Also allows
showing the byte offset in the file.  Highlighting with %1* to %9*, using the
new highlight groups User1 to User9.  (Madsen)

'rulerformat' option: Configurable contents of the ruler, like 'statusline'.
(Madsen)

'write' option: When off, writing files is not allowed.  Avoids overwriting a
file even with ":w!".  The |-m| command line option resets 'write'.

'clipboard' option: How the clipboard is used.  Value "unnamed": Use unnamed
register like "*. (Cortopassi)  Value "autoselect": Like what 'a' in
'guioptions' does but works in the terminal.

'guifontset' option: Specify fonts for the +fontset feature, for the X11 GUI
versions.  Allows using normal fonts when vim is compiled with this feature.
(Nam)

'guiheadroom' option: How much room to allow above/below the GUI window.
Used for Motif, Athena and GTK.

Implemented 'tagstack' option: When off, pushing tags onto the stack is
disabled (Vi compatible).  Useful for mappings.

'shellslash' option.  Only for systems that use a backslash as a file
separator.  This option will use a forward slash in file names when expanding
it.  Useful when 'shell' is sh or csh.

'pastetoggle' option: Key sequence that toggles 'paste'.  Works around the
problem that mappings don't work in Insert mode when 'paste' is set.

'display' option: When set to "lastline", the last line fills the window,
instead of being replaced with "@" lines.  Only the last three characters are
replaced with "@@@", to indicate that the line has not finished yet.

'switchbuf' option: Allows re-using existing windows on a buffer that is being
jumped to, or split the window to open a new buffer. (Roemer)

'titleold' option.  Replaces the fixed string "Thanks for flying Vim", which
is used to set the title when exiting. (Schild)


Vim scripts						*new-script-5.4*
-----------

The |exists()| function can also check for existence of a function. (Roemer)
An internal function is now found with a binary search, should be a bit
faster. (Roemer)

New functions:
- |getwinposx()| and |getwinposy()|: get Vim window position. (Webb)
- |histnr()|, |histadd()|, |histget()| and |histdel()|: Make history
  available. (Roemer)
- |maparg()|: Returns rhs of a mapping.  Based on a patch from Vikas.
- |mapcheck()|: Check if a map name matches with an existing one.
- |visualmode()|: Return type of last Visual mode. (Webb)
- |libcall()|: Call a function in a library.  Currently only for Win32. (Negri)
- |bufwinnr()|: find window that contains the specified buffer. (Roemer)
- |bufloaded()|: Whether a buffer exists and is loaded.
- |localtime()| and |getftime()|: wall clock time and last modification time
  of a file (Webb)
- |glob()|: expand file name wildcards only.
- |system()|: get the raw output of an external command. (based on a patch
  from Aaron).
- |strtrans()|: Translate String into printable characters.  Used for
  2html.vim script.
- |append()|: easy way to append a line of text in a buffer.

Changed functions:
- Optional argument to |strftime()| to give the time in seconds. (Webb)
- |expand()| now also returns names for files that don't exist.

Allow numbers in the name of a user command. (Webb)

Use "v:" for internal Vim variables: "v:errmsg", "v:shell_error", etc.  The
ones from version 5.3 can be used without "v:" too, for backwards
compatibility.

New variables:
"v:warningmsg" and "v:statusmsg" internal variables.  Contain the last given
warning and status message. |v:warningmsg| |v:statusmsg| (Madsen)
"v:count1" variable: like "v:count", but defaults to one when no count is
used. |v:count1|

When compiling without expression evaluation, "if 1" can be used around the
not supported commands to avoid it being executed.  Works like in Vim 4.x.
Some of the runtime scripts gave errors when used with a Vim that was compiled
with minimal features.  Now "if 1" is used around code that is not always
supported.

When evaluating an expression with && and ||, skip the parts that will not
influence the outcome.  This makes it faster and avoids error messages. (Webb)
Also optimized the skipping of expressions inside an "if 0".


Avoid hit-enter prompt					*avoid-hit-enter*
-----------------------

Added 'T' flag to 'shortmess': Truncate all messages that would cause the
hit-enter prompt (unless that would happen anyway).
The 'O' flag in 'shortmess' now also applies to quickfix messages, e.g., from
the ":cn" command.

The default for 'shortmess' is now "filnxtToO", to make most messages fit on
the command line, and not cause the hit-enter prompt.

Previous messages can be viewed with the new |:messages| command.

Some messages are shown fully, even when 'shortmess' tells to shorten
messages, because the user is expected to want to see them in full: CTRL-G and
some quickfix commands.


Improved quickfix					*improved-quickfix*
-----------------

Parse change-directory lines for gmake: "make[1]: Entering directory 'name'".
Uses "%D" and "%X" in 'errorformat'.
Also parse "Making {target} in {dir}" messages from make.  Helps when not
using GNU make. (Schandl)

Use 'isfname' for "%f" in 'errorformat'.

Parsing of multi-line messages. |errorformat-multi-line|

Allow a range for the |:clist| command. (Roemer)

Support for "global" file names, for error formats that output the file name
once for several errors. (Roemer)

|:cnfile| jumps to first error in next file.

"$*" in 'makeprg' is replaced by arguments to ":make". (Roemer)


Regular expressions					*regexp-changes-5.4*
-------------------

In a regexp, a '$' before "\)" is also considered to be an end-of-line. |/$|
In patterns "^" after "\|" or "\(" is a start-of-line. |/^| (Robinson)

In a regexp, in front of "\)" and "\|" both "$" and "\$" were considered
end-of-line.  Now use "$" as end-of-line and "\$" for a literal dollar.  Same
for '^' after "\(" and "\|". |/\$| |/\^|

Some search patterns can be extremely slow, even though they are not really
illegal.  For example: "\([^a-z]\+\)\+Q".  Allow interrupting any regexp
search with CTRL-C.

Register "/: last search string (read-only). (Kohan)  Changed to use last used
search pattern (like what 'hlsearch' uses).  Can set the search pattern with
":let @/ = {expr}".

Added character classes to search patterns, to avoid the need for removing the
'l' flag from 'cpoptions': |[:tab:]|, |[:return:]|, |[:backspace:]| and
|[:escape:]|.

By adding a '?' after a comparative operator in an expression, the comparison
is done by ignoring case. |expr-==?|


Other improvements made between version 5.3 and 5.4
---------------------------------------------------

Changed							*changed-5.4*
-------

Unix: Use $TMPDIR for temporary files, if it is set and exists.

Removed "Empty buffer" message.  It isn't useful and can cause a hit-enter
prompt. (Negri)

"ex -" now reads commands from stdin and works in silent mode.  This is to be
compatible with the original "ex" command that is used for scripts.

Default range for ":tcldo" is the whole file.

Cancelling Visual mode with ESC moved the cursor.  There appears to be no
reason for this.  Now leave the cursor where it is.

The ":grep" and ":make" commands see " as part of the arguments, instead of
the start of a comment.

In expressions the "=~" and "!~" operators no longer are affected by
'ignorecase'.

Renamed vimrc_example to vimrc_example.vim and gvimrc_example to
gvimrc_example.vim.  Makes them being recognized as vim scripts.

"gd" no longer starts searching at the end of the previous function, but at
the first blank line above the start of the current function.  Avoids that
using "gd" in the first function finds global a variable.

Default for 'complete' changed from ".,b" to ".,w,b,u,t,i".  Many more matches
will be found, at the cost of time (the search can be interrupted).

It is no longer possible to set 'shell*' options from a modeline.  Previously
only a warning message was given.  This reduces security risks.

The ordering of the index of documentation files was changed to make it more
easy to find a subject.

On MS-DOS and win32, when $VIM was not set, $HOME was used.  This caused
trouble if $HOME was set to e.g., "C:\" for some other tool, the runtime files
would not be found.  Now use $HOME only for _vimrc, _gvimrc, etc., not to find
the runtime file.

When 'tags' is "./{fname}" and there is no file name for the current buffer,
just use it.  Previously it was skipped, causing "vim -t {tag}" not to find
many tags.

When trying to select text in the 'scrolloff' area by mouse dragging, the
resulting scrolling made this difficult.  Now 'scrolloff' is temporarily set
to 0 or 1 to avoid this.  But still allow scrolling in the top line to extend
to above the displayed text.

Default for 'comments' now includes "sl:/*,mb: *,ex:*/", to make javadoc
comments work.  Also helps for C comments that start with "/*******".

CTRL-X CTRL-] Insert mode tag expansion tried to expand to all tags when used
after a non-ID character, which can take a very long time.  Now limit this to
200 matches.  Also used for command-line tag completion.

The OS/2 distribution has been split in two files.  It was too big to fit on a
floppy.  The same runtime archive as for the PC is now used.

In the documentation, items like <a-z> have been replaced with {a-z} for
non-optional arguments.  This avoids confusion with key names: <C-Z> is a
CTRL-Z, not a character between C and Z, that is {C-Z}.


Added							*added-5.4*
-----

Color support for the iris-ansi builtin termcap entry. (Tubman)

Included VisVim version 1.3a. (Erhardt)

Win32 port for SNiFF+ interface. (Leherbauer)
Documentation file for sniff interface: if_sniff.txt. (Leherbauer)

Included the "SendToVim" and "OpenWithVim" programs in the OleVim directory.
To be used with the OLE version of gvim under MS-Windows. (Schaller)

Included Exuberant Ctags version 3.2.4 with Eiffel support. (Hiebert)

When a file that is being edited is deleted, give a warning (like when the
time stamp changed).

Included newer versions of the HTML-generating Awk and Perl scripts. (Colombo)

Linux console mouse support through "gpm". (Tsindlekht)

Security fix: Disallow changing 'secure' and 'exrc' from a modeline.  When
'secure' is set, give a warning for changing options that contain a program
name.

Made the Perl interface work with Perl 5.005 and threads. (Verdoolaege)

When giving an error message for an ambiguous mapping, include the offending
mapping. (Roemer)

Command line editing:
- Command line completion of mappings. (Roemer)
- Command line completion for ":function", ":delfunction", ":let", ":call",
  ":if", etc. (Roemer)
- When using CTRL-D completion for user commands that have
  "-complete=tag_listfiles" also list the file names.  (Madsen)
- Complete the arguments of the ":command" command. (Webb)
- CTRL-R . in command line inserts last inserted text.  CTRL-F, CTRL-P, CTRL-W
  and CTRL-A after CTRL-R are used to insert an object from under the cursor.
  (Madsen)

Made the text in uganda.txt about copying Vim a bit more clear.

Updated the Vim tutor.  Added the "vimtutor" command, which copies the tutor
and starts Vim on it.  "make install" now also copies the tutor.

In the output of ":clist" the current entry is highlighted, with the 'i'
highlighting (same as used for 'incsearch').

For the ":clist" command, you can scroll backwards with "b" (one screenful),
"u" (half a screenful) and "k" (one line).

Multi-byte support:
- X-input method for multibyte characters.  And various fixes for multibyte
  support. (Nam)
- Hangul input method feature: |hangul|. (Nam)
- Cleaned up configuration of multibyte support, XIM, fontset and Hangul
  input.  Each is now configurable separately.
- Changed check for GTK_KEYBOARD to HANGUL_KEYBOARD_TYPE. (Nam)
- Added doc/hangulin.txt: Documentation for the Hangul input code. (Nam)
- XIM support for GTK+. (Nam)
- First attempt to include support for SJIS encoding. (Nagano)
- When a double-byte character doesn't fit at the end of the line, put a "~"
  there and print it on the next line.
- Optimize output of multibyte text. (Park)
- Win32 IME: preedit style is like over-the-spot. (Nagano)
- Win32 IME: IME mode change now done with ImmSetOpenStatus. (Nagano)
- GUI Athena: file selection dialog can display multibyte characters.
  (Nagano)
- Selection reply for XA_TEXT as XA_STRING. (Nagano)

"runtime/macros/diffwin.vim".  Mappings to make a diff window. (Campbell)

Added ".obj" to the 'suffixes' option.

Reduced size of syntax/synload.vim by using the ":SynAu" user command.
Automated numbering of Syntax menu entries in menu.vim.
In the Syntax menu, insert separators between syntax names that start with
a different letter. (Geddes)

Xterm:
- Clipboard support when using the mouse in an xterm. (Madsen)
- When using the xterm mouse, track dragging of the mouse.  Use xterm escape
  sequences when possible.  It is more precise than other methods, but
  requires a fairly recent xterm version.  It is enabled with "xterm2" in
  'ttymouse'.  (Madsen)
- Check xterm patch level, to set the value of 'ttymouse'.  Has only been
  added to xterm recently (patch level > 95).  Uses the new 't_RV' termcap
  option.  Set 'ttymouse' to "xterm2" when a correct response is recognized.
  Will make xterm mouse dragging work better.
- Support for shifted function keys on xterm.  Changed codes for shifted
  cursor keys to what the xterm actually produces.  Added codes for shifted
  <End> and <Home>.
- Added 't_WP' to set the window position in pixels and 't_WS' to set the
  window size in characters.  Xterm can now move (used for ":winpos") and
  resize (use for ":set lines=" and ":set columns=").

X11:
- When in Visual mode but not owning the selection, display the Visual area
  with the VisualNOS group to show this. (Madsen)
- Support for requesting the type of clipboard support.  Used for AIX and
  dtterm. (Wittig)
- Support compound_text selection (even when compiled without multibyte).

Swap file:
- New variation for naming swap files: Replace path separators into %, place
  all swap files in one directory.  Used when a name in 'dir' ends in two path
  separators. (Madsen)
- When a swap file is found, show whether it contains modifications or not in
  the informative message. (Madsen)
- When dialogs are supported, use a dialog to ask the user what to do when a
  swapfile already exists.

"popup_setpos" in 'mousemodel' option.  Allows for moving the cursor when
using the right mouse button.

When a buffer is deleted, the selection for which buffer to display instead
now uses the most recent entry from the jump list. (Madsen)

When using CTRL-O/CTRL-I, skip deleted buffers.

A percentage is shown in the ruler, when there is room.

Used autoconf 1.13 to generate configure.

Included get_lisp_indent() from Dirk van Deun.  Does better Lisp indenting
when 'p' flag in 'cpoptions' is not included.

Made the 2html.vim script quite a bit faster.  (based on ideas from Geddes)

Unix:
- Included the name of the user that compiled Vim and the system name it was
  compiled on in the version message.
- "make install" now also installs the "tools" directory.  Makes them
  available for everybody.
- "make check" now does the same as "make test".  "make test" checks for
  Visual block mode shift, insert, replace and change.
- Speed up comparing a file name with existing buffers by storing the
  device/inode number with the buffer.
- Added configure arguments "--disable-gtk", "--disable-motif" and
  "--disable-athena", to be able to disable a specific GUI (when it doesn't
  work).
- Renamed the configure arguments for disabling the check for specific GUIs.
  Should be clearer now. (Kahn)
- On a Digital Unix system ("OSF1") check for the curses library before
  termlib and termcap. (Schild)
- "make uninstall_runtime" will only delete the version-specific files.  Can
  be used to delete the runtime files of a previous version.

Macintosh: (St-Amant)
- Dragging the scrollbar, like it's done for the Win32 GUI.  Moved common code
  from gui_w32.c to gui.c
- Added dialogs and file browsing.
- Resource fork preserved, warning when it will be lost.
- Copy original file attributes to newly written file.
- Set title/notitle bug solved.
- Filename completion improved.
- Grow box limit resize to a char by char size.
- Use of rgb.txt for more colors (but give back bad color).
- Apple menu works (beside the about...).
- Internal border now vim compliant.
- Removing a menu doesn't crash anymore.
- Weak-linking of Python 1.5.1 (only on PPC).  Python is supported when the
  library is available.
- If an error is encountered when sourcing the users .vimrc, the alert box now
  shows right away with the OK button defaulted.  There's no more "Delete"-key
  sign at the start of each line
- Better management of environment variables.  Now $VIM is calculated only
  once, not regenerated every time it is used.
- No more CPU hog when in background.
- In a sourced Vim script the Mac file format can be recognized, just like DOS
  file format is.

When both "unix" and "mac" are present in 'fileformats', prefer "mac" format
when there are more CR than NL characters.
When using "mac" fileformat, use CR instead of a NL, because NL is used for
NUL.  Will preserve all characters in a file. (Madsen)

The DOS install.exe now contains checks for an existing installation.  It
avoids setting $VIM and $PATH again.
The install program for Dos/Windows can now install Vim in the popup menu, by
adding two registry keys.

Port to EGCS/mingw32.  New Makefile.ming. (Aaron)

DOS 16 bit: Don't include cursor shape stuff.  Save some bytes.

TCL support to Makefile.w32. (Duperval)

OS/2: Use argv[0] to find runtime files.

When using "gf" to go to a buffer that has already been used, jump to the
line where the cursor last was.

Colored the output of ":tselect" a bit more.  Different highlighting between
tag name and file name.  Highlight field name ("struct:") separately from
argument.

Backtick expansion for non-Unix systems.  Based on a patch from Aaron.
Allows the use of things like ":n `grep -l test *.c`" and
"echo expand('`ls m*`')".

Check for the 'complete' option when it is set. (Acevedo)
'd' flag in 'complete' searches for defined names or macros.
While searching for Insert mode completions in include files and tags files,
check for typeahead, so that you can use matches early. (Webb)
The '.' flag in 'complete' now scans the current buffer completely, ignoring
'nowrapscan'.  (Webb)

Added '~' flag to 'whichwrap'. (Acevedo)

When ending the Visual mode (e.g., with ESC) don't grab ownership of the
selection.

In a color terminal, "fg" and "bg" can be used as color names.  They stand for
the "Normal" colors.

A few cscope cleanups. (Kahn)

Included changed vimspell.sh from Schemenauer.

Concatenation of strings in an expression with "." is a bit faster. (Roemer)

The ":redir" command can now redirect to a register: ":redir @r". (Roemer)

Made the output of ":marks" and ":jumps" look similar.  When the mark is in
the current file, show the text at the mark.  Also for ":tags".

When configure finds ftello() and fseeko(), they are used in tag.c (for when
you have extremely big tags files).

Configure check for "-FOlimit,2000" argument for the compiler. (Borsenkow)

GUI:
- When using ":gui" in a non-GUI Vim, give a clear error message.
- "gvim -v" doesn't start the GUI (if console support is present).
- When in Ex mode, use non-Visual selection for the whole screen.
- When starting with "gvim -f" and using ":gui" in the .gvimrc file, Vim
  forked anyway.  Now the "-f" flag is remembered for ":gui".  Added "gui -b"
  to run gvim in the background anyway.

Motif GUI:
- Check for "-lXp" library in configure (but it doesn't work yet...).
- Let configure check for Lesstif in "/usr/local/Lesstif/Motif*".  Changed the
  order to let a local Motif version override a system standard version.

Win32 GUI:
- When using "-register" or "-unregister" in the non-OLE version, give an
  error message.
- Use GTK toolbar icons.  Make window border look better.  Use sizing handles
  on the lower left&right corners of the window. (Negri)
- When starting an external command with ":!start" and the command can not be
  executed, give an error message. (Webb)
- Use sizing handles for the grey rectangles below the scrollbars.  Can draw
  toolbar in flat mode now, looks better. (Negri)
- Preparations for MS-Windows 3.1 addition.  Mostly changing WIN32 to MSWIN
  and USE_GUI_WIN32 to USE_GUI_MSWIN. (Negri)

Avoid allocating the same string four times in buflist_findpat(). (Williams)

Set title and icon text with termcap options 't_ts', 't_fs', 't_IS' and
't_IE'.  Allows doing this on any terminal that supports setting the title
and/or icon text. (Schild)

New 'x' flag in 'comments': Automatically insert the end part when its last
character is typed.  Helps to close a /* */ comment in C. (Webb)

When expand() has a second argument which is non-zero, don't use 'suffixes'
and 'wildignore', return all matches.

'O' flag in 'cpoptions' When not included, Vim will not overwrite a file, if
it didn't exist when editing started but it does exist when the buffer is
written to the file.  The file must have been created outside of Vim, possibly
without the user knowing it.  When this is detected after a shell command,
give a warning message.

When editing a new file, CTRL-G will show [New file].  When there were errors
while reading the file, CTRL-G will show [Read errors].

":wall" can now use a dialog and file-browsing when needed.

Grouped functionality into new features, mainly to reduce the size of the
minimal version:
+linebreak:	'showbreak', 'breakat' and 'linebreak'
+visualextra:	"I"nsert and "A"ppend in Visual block mode, "c"hange all lines
		in a block, ">" and "<": Shifting a block, "r": Replacing a
		Visual area with one character.
+comments:	'comments'
+cmdline_info:	'ruler' and 'showcmd'.  Replaces +showcmd.
"+title"	Don't add code to set title or icon for MSDOS, this was not
		possible anyway.
+cmdline_compl	Disable commandline completion at compile time, except for
		files, directories and help items.

Moved features from a list of function calls into an array.  Should save a bit
of space.

While entering the body of a function, adjust indent according to "if" and
"while" commands.

VMS: Adjusted os_vms.mms a bit according to suggestions from Arpadffy.

The flags in the 'comments' option can now include an offset.  This makes it
possible to align "/*****", "/*   xxx" and "/*" comments with the same
'comments' setting.  The default value for 'comments' uses this.
Added 'O' flag: Don't use this part for the "O" command.  Useful for "set
com=sO:*\ -,mO:*\ \ ,exO:*/"

FileType autocommands recognize ".bak", ".orig" and "~" extensions and remove
them to find the relevant extension.

The tutorial for writing a Vim script file has been extended.

Some more highlighting in help files, for items that are not typed literally.

Can use "CTRL-W CTRL-G" like "CTRL-W g".

"make test" for OS/2.

Adjusted configure to automatically use the GUI for BeOS.


Fixed							*fixed-5.4*
-----

5.3.1: When using an autocommand for BufWritePre that changes the name of the
buffer, freed memory would be used. (Geddes)

Mac: Compiler didn't understand start of skip_class_name().

Win32 GUI:
- When cancelling the font requester, don't give an error message.
- When a tearoff-menu is open and its menu is deleted, Vim could crash.
  (Negri)
- There was a problem on Windows 95 with (un)maximizing the window.
  (Williams)
- when 'mousehide' is set, the mouse would stay hidden when a menu is dropped
  with the keyboard. (Ralston)
- The tempname() function already created the file.  Caused problems when
  using ":w".  Now the file is deleted.
- Cursor disappeared when ending up in the top-left character on the screen
  after scrolling. (Webb)
- When adding a submenu for a torn-off menu, it was not updated.
- Menu tooltip was using the toolbar tooltip. (Negri)
- Setting 'notitle' didn't remove the title. (Steed)
- Using ":!start cmd" scrolled the screen one line up, and didn't wait for
  return when the command wasn't found.

Cscope interface: Sorting of matches was wrong.  Starting the interface could
fail. (Kahn)

Motif GUI: Could not compile with Motif 1.1, because some tear-off
functionality was not in #ifdefs.

Configure could sometimes not compile or link the test program for sizeof(int)
properly.  This caused alignment problems for the undo structure allocations.
Added a safety check that SIZEOF_INT is not zero.

Added configure check to test if strings.h can be included after string.h.
Some systems can't handle it.
Some systems need both string.h and strings.h included.  Adjusted vim.h for
that.  Removed including string.h from os_unixx.h, since it's already in
vim.h. (Savage)
AIX: defining _NO_PROTO in os_unix.h causes a conflict between string.h and
strings.h, but after the configure check said it was OK.  Also define
_NO_PROTO for AIX in the configure check. (Winn)

When closing a window with CTRL-W c, the value of 'hidden' was not taken into
account, the buffer was always unloaded. (Negri)

Unix Makefile: "make install" always tried to rename an older executable and
remove it.  This caused an error message when it didn't exit.  Added a check
for the existence of an old executable.
The command line for "make install" could get too long, because of the many
syntax files.  Now first do a "cd" to reduce the length.

On RISCOS and MSDOS, reading a file could fail, because the short filename was
used, which can be wrong after a ":!cd".

In the DOS versions, the wrong install.exe was included (required Windows).
Now the install.exe version is included that is the same as the Vim version.
This also supports long file names where possible.

When recording, and stopping while in Insert mode with CTRL-O q, the CTRL-O
would also be recorded.

32bit DOS version: "vim \file", while in a subdirectory, resulted in "new
file" for "file" in the local directory, while "\file" did exist.  When
"file" in the current directory existed, this didn't happen.

MSDOS: Mouse could not go beyond 80 columns in 132 columns mode. (Young)

"make test" failed in the RedHat RPM, because compatible is off by default.

In Insert mode <C-O><C-W><C-W> changes to other window, but the status bars
were not updated until another character was typed.

MSDOS: environment options in lowercase didn't work, although they did in the
Win32 versions. (Negri)

After ":nohlsearch", a tag command switched highlighting back on.

When using "append" command as the last line in an autocommand, Vim would
crash.

RISCOS: The scroll bumpers (?) were not working properly. (Leonard)

"zl" and "zh" could move the cursor, but this didn't set the column in which
e.g., "k" would move the cursor.

When doing ":set all&" the value of 'scroll' was not set correctly.  This
caused an error message when later setting any other number option.

When 'hlsearch' highlighting has been disabled with ":nohlsearch",
incremental searching would switch it back on too early.

When listing tags for ":tselect", and using a non-search command, and the last
character was equal to the first (e.g., "99"), the last char would not be
shown.

When searching for tags with ":tag" Vim would assume that all matches had been
found when there were still more (e.g. from another tags file).

Win32: Didn't recognize "c:\" (e.g., in tags file) as absolute path when
upper/lowercase was different.

Some xterms (Debian) send <Esc>OH for HOME and <Esc>OF for END.  Added these
to the builtin-xterm.

In ex mode, any CR was seen as the end of the line.  Only a NL should be
handled that way.  broke ":s/foo/some^Mtext/".

In menu.vim, a vmenu was used to override an amenu.  That didn't work, because
the system menu file doesn't overwrite existing menus.  Added explicit vunmenu
to solve this.

Configure check for terminal library could find a library that doesn't work at
runtime (Solaris: shared library not found).  Added a check that a program
with tgoto() can run correctly.

Unix: "echo -n" in the Makefile doesn't work on all systems, causing errors
compiling pathdef.c.  Replaced it with "tr".

Perl: DO_JOIN was redefined by Perl.  Undefined it in the perl files.

Various XIM and multibyte fixes:
- Fix user cannot see his language while he is typing his language with
  off-the-spot method. (Nagano)
- Fix preedit position using text/edit area (using gui.wid). (Nagano)
- remove 'fix dead key' codes.  It was needed since XNFocusWindow was
  "x11_window", XNFocusWindow is now gui.wid. (Nagano)
- Remove some compile warnings and fix typos. (Namsh)
- For status area, check the gtk+ version while Vim runs.  I believe it is
  better than compile time check. (Namsh)
- Remove one FIXME for gtk+-xim. (Namsh)
- XIM: Dead keys didn't work for Czech. (Vyskovsky)
- Multibyte: If user input only 3byte such as mb1_mb2_eng or eng_mb1_mb2 VIM
  could convert it to special character. (Nam)
- Athena/Motif with XIM: fix preedit area. (Nam)
- XIM: Composed strings were sometimes ignored.  Vim crashed when compose
  string was longer than 256 bytes.  IM's geometry control is fixed. (Nam,
  Nagano)
- Win32 multibyte: hollowed cursor width on a double byte char was wrong.
  (Nagano)
- When there is no GUI, selecting XIM caused compilation problems.
  Automatically disable XIM when there is no GUI in configure.
- Motif and Athena: When compiled with XIM, but the input method was not
  enabled, there would still be a status line.  Now the status line is gone if
  the input method doesn't work. (Nam)

Win32: tooltip was not removed when selecting a parent menu (it was when
selecting a menu entry). (Negri)

Unix with X: Some systems crash on exit, because of the XtCloseDisplay() call.
Removed it, it should not be necessary when exiting.

Win32: Crash on keypress when compiled with Borland C++. (Aaron)

When checking for Motif library files, prefer the same location as the include
files (with "include" replaced with "lib") above another entry.

Athena GUI: Changed "XtOffset()" in gui_at_fs.c to "XtOffsetOf()", like it's
used in gui_x11.c.

Win32: When testing for a timestamp of a file on floppy, would get a dialog
box when the floppy has been removed.  Now return with an error.  (Negri)

Win32 OLE: When forced to come to the foreground, a minimized window was still
minimized, now it's restored. (Zivkov)

There was no check for a positive 'shiftwidth'.  A negative value could cause
a hangup, a zero value a crash.

Athena GUI: horizontal scrollbar wasn't updated correctly when clicking right
or left of the thumb.

When making a Visual-block selection in one window, and trying to scroll
another, could cause errors for accessing non-existent line numbers.

When 'matchpairs' contains "`:'", jumping from the ` to the ' didn't work
properly.

Changed '\"' to '"' to make it compatible with old C compilers.

The command line expansion for mappings caused a script with a TAB between lhs
and rhs of a map command to fail.  Assume the TAB is to separate lhs and rhs
when there are no mappings to expand.

When editing a file with very long lines with 'scrolloff' set, "j" would
sometimes end up in a line which wasn't displayed.

When editing a read-only file, it was completely read into memory, even when
it would not fit.  Now create a swap file for a read-only file when running
out of memory while reading the file.

When using ":set cino={s,e-s", a line after "} else {" was not indented
properly.  Also added a check for this in test3.in.

The Hebrew mapping for the command line was remembered for the next command
line.  That isn't very useful, a command is not Hebrew. (Kol)

When completing file names with embedded spaces, like "Program\ files", this
didn't work.  Also for user commands.  Moved backslash_halve() down to
mch_expandpath().

When using "set mouse=a" in Ex mode, mouse events were handled like typed
text.  Then typing "quit" screwed up the mouse behavior of the xterm.

When repeating an insert with "." that contains a CTRL-Y, a number 5 was
inserted as "053".

Yanking a Visual area, with the cursor past the line, didn't move the cursor
back onto the line.  Same for "~", "u", "U" and "g?"

Win32: Default for 'grepprg' could be "findstr /n" even though there is no
findstr.exe (Windows 95).  Check if it exists, and fall back to "grep -n" if
it doesn't.

Because gui_mouse_moved() inserted a leftmouse click in the input buffer,
remapping a leftmouse click caused strange effects.  Now Insert another code
in the input buffer.  Also insert a leftmouse release, to avoid the problem
with ":map <LeftMouse> l" that the next release is seen as the release for the
focus click.

With 'wrap' on, when using a line that doesn't fit on the screen, if the start
of the Visual area is before the start of the screen, there was no
highlighting.  Also, 'showbreak' doesn't work properly.

DOS, Win32: A pattern "[0-9]\+" didn't work in autocommands.

When creating a swap file for a buffer which isn't the current buffer, could
get a mixup of short file name, resulting in a long file name when a short
file name was required.  makeswapname() was calling modname() instead of
buf_modname().

When a function caused an error, and the error message was very long because
of recursiveness, this would cause a crash.

'suffixes' were always compared with matching case.  For MS-DOS, Win32 and
OS/2 case is now ignored.

The use of CHARBITS in regexp.c didn't work on some Linux.  Don't use it.

When generating a script file, 'cpo' was made empty.  This caused backslashes
to disappear from mappings.  Set it to "B" to avoid that.

Lots of typos in the documentation. (Campbell)

When editing an existing (hidden) buffer, jump to the last used cursor
position. (Madsen)

On a Sun the xterm screen was not restored properly when suspending. (Madsen)

When $VIMINIT is processed, 'nocompatible' was only set after processing it.

Unix: Polling for a character wasn't done for GPM, Sniff and Xterm clipboard
all together.  Cleaned up the code for using select() too.

When executing external commands from the GUI, some typeahead was lost.  Added
some code to regain as much typeahead as possible.

When the window height is 5 lines or fewer, <PageDown> didn't use a one-line
overlap, while <PageUp> does.  Made sure that <PageUp> uses the same overlap
as <PageDown>, so that using them both always displays the same lines.

Removed a few unused functions and variables (found with lint).

Dictionary completion didn't use 'infercase'. (Raul)

Configure tests failed when the Perl library was not in LD_LIBRARY_PATH.
Don't use the Perl library for configure tests, add it to the linker line only
when linking Vim.

When using ncurses/terminfo, could get a 't_Sf' and 't_Sb' termcap entry that
has "%d" instead of "%p1%d".  The light background colors didn't work then.

GTK GUI with ncurses: Crashed when starting up in tputs().  Don't use tputs()
when the GUI is active.

Could use the ":let" command to set the "count", "shell_error" and "version"
variables, but that didn't work.  Give an error message when trying to set
them.

On FreeBSD 3.0, tclsh is called tclsh8.0.  Adjusted configure.in to find it.

When Vim is linked with -lncurses, but python uses -ltermcap, this causes
trouble: "OOPS".  Configure now removes the -ltermcap.

:@" and :*" didn't work properly, because the " was recognized as the start of
a comment.

Win32s GUI: Minimizing the console where a filter command runs in caused
trouble for detecting that the filter command has finished. (Negri)

After executing a filter command from an xterm, the mouse would be disabled.
It would work again after changing the mode.

Mac GUI: Crashed in newenv(). (St-Amant)

The menus and mappings in mswin.vim didn't handle text ending in a NL
correctly. (Acevedo)

The ":k" command didn't check if it had a valid argument or extra characters.
Now give a meaningful error message. (Webb)

On SGI, the signal function doesn't always have three arguments.  Check for
struct sigcontext to find out.  Might still be wrong...

Could crash when using 'hlsearch' and search pattern is "^".

When search patterns were saved and restored, status of no_hlsearch was not
also saved and restored (from ":nohlsearch" command).

When using setline() to make a line shorter, the cursor position was not
adjusted.

MS-DOS and Win95: When trying to edit a file and accidentally adding a slash
or backslash at the end, the file was deleted.  Probably when trying to create
the swap file.  Explicitly check for a trailing slash or backslash before
trying to read a file.

X11 GUI: When starting the GUI failed and received a deadly signal while
setting the title, would lock up when trying to exit, because the title is
reset again.  Avoid using mch_settitle() recursively.

X11 GUI: When starting the GUI fails, and then trying it again, would crash,
because argv[] has been freed and x11_display was reset to NULL.

Win32: When $HOME was set, would put "~user" in the swap file, which would
never compare with a file name, and never cause the attention message.  Put
the full path in the swap file instead.

Win32 console: There were funny characters at the end of the "vim -r" swap
files message (direct output of CR CR LF).

DOS 32 bit: "vim -r" put the text at the top of the window.

GUI: With 'mousefocus' set, got mouse codes as text with "!sleep 100" or "Q".

Motif and Win32 GUI: When changing 'guifont' to a font of the same size the
screen wasn't redrawn.

Unix: When using ":make", jumping to a file b.c, which is already open as a
symbolic link a.c, opened a new buffer instead of using the existing one.

Inserting text in the current buffer while sourcing the .vimrc file would
cause a crash or hang.  The memfile for the current buffer was never
allocated.  Now it's allocated as soon as something is written in the buffer.

DOS 32 bit: "lightblue" background worked for text, but not drawn parts were
black.

DOS: Colors of console were not restored upon exiting.

When recording, with 'cmdheight' set to 2 and typing Esc> in Insert mode
caused the "recording" message to be doubled.

Spurious "file changed" messages could happen on Windows.  Now tolerate a one
second difference, like for Linux.

GUI: When returning from Ex mode, scrollbars were not updated.

Win32: Copying text to the clipboard containing a <CR>, pasting it would
replace it with a <NL> and drop the next character.

Entering a double byte character didn't work if the second byte is in [xXoO].
(Eric Lee)

vim_realloc was both defined and had a prototype in proto/misc2.pro.  Caused
conflicts on Solaris.

A pattern in an autocommand was treated differently on DOS et al. than on
Unix.  Now it's the same, also when using backslashes.

When using <Tab> twice for command line completion, without a match, the <Tab>
would be inserted. (Negri)

Bug in MS-Visual C++ 6.0 when compiling ex_docmd.c with optimization. (Negri)

Testing the result of mktemp() for failure was wrong.  Could cause a crash.
(Peters)

GUI: When checking for a ".gvimrc" file in the current directory, didn't check
for a "_gvimrc" file too.

Motif GUI: When using the popup menu and then adding an item to the menu bar,
the menu bar would get very high.

Mouse clicks and special keys (e.g. cursor keys) quit the more prompt and
dialogs.  Now they are ignored.

When at the more-prompt, xterm selection didn't work.  Now use the 'r' flag in
'mouse' also for the more-prompt.

When selecting a Visual area of more than 1023 lines, with 'guioptions' set to
"a", could mess up the display because of a message in free_yank().  Removed
that message, except for the Amiga.

Moved auto-selection from ui_write() to the screen update functions.  Avoids
unexpected behavior from a low-level function.  Also makes the different
feedback of owning the selection possible.

Vi incompatibility: Using "i<CR>" in an indent, with 'ai' set, used the
original indent instead of truncating it at the cursor. (Webb)

":echo x" didn't stop at "q" for the more prompt.

Various fixes for Macintosh. (St-Amant)

When using 'selectmode' set to "exclusive", selecting a word and then using
CTRL-] included the character under the cursor.

Using ":let a:name" in a function caused a crash. (Webb)

When using ":append", an empty line didn't scroll up.

DOS etc.: A file name starting with '!' didn't work.  Added '!' to default for
'isfname'.

BeOS: Compilation problem with prototype of skip_class_name(). (Price)

When deleting more than one line, e.g., with "de", could still use "U"
command, which didn't work properly then.

Amiga: Could not compile ex_docmd.c, it was getting too big.  Moved some
functions to ex_cmds.c.

The expand() function would add a trailing slash for directories.

Didn't give an error message when trying to assign a value to an argument of a
function.  (Webb)

Moved including sys/ptem.h to after termios.h.  Needed for Sinix.

OLE interface: Don't delete the object in CVimCF::Release() when the reference
count becomes zero. (Cordell)
VisVim could still crash on exit. (Erhardt)

"case a: case b:" (two case statements in one line) aligned with the second
case.  Now it uses one 'sw' for indent. (Webb)

Font initialisation wasn't right for Athena/Motif GUI.  Moved the call to
highlight_gui_started() gui_mch_init() to gui_mch_open(). (Nam)

In Replace mode, backspacing over a TAB before where the replace mode started
while 'sts' is different from 'ts', would delete the TAB.

Win32 console: When executing external commands and switching between the two
console screens, Vim would copy the text between the buffers.  That caused the
screen to be messed up for backtick expansion.

":winpos -1" then ":winpos" gave wrong error message.

Windows commander creates files called c:\tmp\$wc\abc.txt.  Don't remove the
backslash before the $.  Environment variables were not expanded anyway,
because of the backslash before the dollar.

Using "-=" with ":set" could remove half a part when it contains a "\,".
E.g., ":set path+=a\\,b" and then "set path-=b"  removed ",b".

When Visually selecting lines, with 'selection' set to "inclusive", including
the last char of the line, "<<" moved an extra line.  Also for other operators
that always work on lines.

link.sh changed "-lnsl_s" to "_s" when looking for "nsl" to be removed.
Now it only removes whole words.

When jumped to a mark or using "fz", and there is an error, the current column
was lost.  E.g. when using "$fzj".

The "g CTRL-G" command could not be interrupted, even though it can take a
long time.

Some terminals do have <F4> and <xF4>.  <xF4> was always interpreted as <F4>.
Now map <xF4> to <F4>, so that the user can override this.

When compiling os_win32.c with MIN_FEAT the apply_autocmds() should not be
used. (Aaron)

This autocommand looped forever: ":au FileChangedShell * ++nested e <afile>"
Now FileChangeShell never nests. (Roemer)

When evaluating an ":elseif" that was not going to matter anyway, ignore
errors. (Roemer)

GUI Lesstif: Tearoff bar was the last item, instead of the first.

GUI Motif: Colors of tear-off widgets was wrong when 't' flag added to
'guioptions' afterwards.  When 't' flag in 'guioptions' is excluded, would
still get a tearoff item in a new menu.

An inode number can be "long long".  Use ino_t instead of long.  Added
configure check for ino_t.

Binary search for tags was using a file offset "long" instead of "off_t".

Insert mode completion of tags was not using 'ignorecase' properly.

In Insert mode, the <xFn> keys were not properly mapped to <Fn> for the
default mappings.  Also caused errors for ":mkvimrc" and ":mksession".

When jumping to another window while in Insert mode, would get the "warning:
changing readonly file" even when not making a change.

A '(' or '{' inside a trailing "//" comment would disturb C-indenting.
When using two labels below each other, the second one was not indented
properly.  Comments could mess up C-indenting in many places.  (Roemer)

Could delete or redefine a function while it was being used.  Could cause a
crash.
In a function it's logical to prepend "g:" to a system variable, but this
didn't work. (Roemer)

Hangul input: Buffer would overflow when user inputs invalid key sequence.
(Nam)

When BufLoad or BufEnter autocommands change the topline of the buffer in the
window, it was overruled and the cursor put halfway the window.  Now only put
the cursor halfway if the autocommands didn't change the topline.

Calling exists("&option") always returned 1. (Roemer)

Win32: Didn't take actually available memory into account. (Williams)

White space after an automatically inserted comment leader was not removed
when 'ai' is not set and <CR> hit just after inserting it. (Webb)

A few menus had duplicated accelerators. (Roemer)

Spelling errors in documentation, quite a few "the the". (Roemer)

Missing prototypes for Macintosh. (Kielhorn)

Win32: When using 'shellquote' or 'shellxquote', the "!start cmd" wasn't
executed in a disconnected process.

When resizing the window, causing a line before the cursor to wrap or unwrap,
the cursor was displayed in the wrong position.

There was quite a bit of dead code when compiling with minimal features.

When doing a ":%s///" command that makes lines shorter, such that lines above
the final cursor position no longer wrap, the cursor position was not updated.

get_id_list() could allocate an array one too small, when a "contains=" item
has a wildcard that matches a group name that is added just after it.  E.g.:
"contains=a.*b,axb".  Give an error message for it.

When yanking a Visual area and using the middle mouse button -> crash.  When
clipboard doesn't work, now make "* always use "".

Win32: Using ":buf a\ b\file" didn't work, it was interpreted as "ab\file".

Using ":ts ident", then hit <CR>, with 'cmdheight' set to 2: command line was
not cleared, the tselect prompt was on the last but one line.

mksession didn't restore the cursor column properly when it was after a tab.
Could not get all windows back when using a smaller terminal screen.  Didn't
restore all windows when "winsize" was not in 'sessionoptions'. (Webb)

Command line completion for ":buffer" depended on 'ignorecase' for Unix, but
not for DOS et al.  Now don't use 'ignorecase', but let it depend on whether
file names are case sensitive or not (like when expanding file names).

Win32 GUI: (Negri)
- Redrawing the background caused flicker when resizing the window.  Removed
  _OnEraseBG().  Removed CS_HREDRAW and CS_VREDRAW flags from the
  sndclass.style.
- Some parts of the window were drawn in grey, instead of using the color from
  the user color scheme.
- Dropping a file on gvim didn't activate the window.
- When there is no menu ('guioptions' excludes 'm'), never use the ALT key for
  it.

GUI: When resizing the window, would make the window height a bit smaller.
Now round off to the nearest char cell size. (Negri)

In Vi the ")" and "(" commands don't stop at a single space after a dot.
Added 'J' flag in 'cpoptions' to make this behave Vi compatible. (Roemer)

When saving a session without any buffers loaded, there would be a ":normal"
command without arguments in it. (Webb)

Memory leaks fixed: (Madsen)
- eval.c: forgot to release func structure when func deleted
- ex_docmd.c: forgot to release string after "<sfile>"
- misc1.c: leak when completion pattern had no matches.
- os_unix.c: forgot to release regexp after file completions

Could crash when using a buffer without a name. (Madsen)
Could crash when doing file name completion, because of backslash_halve().
(Madsen)

":@a" would do mappings on register a, which is not Vi compatible. (Roemer)

":g/foo.*()/s/foobar/_&/gc" worked fine, but then "n" searched for "foobar"
and displayed "/foo.*()". (Roemer)

OS/2: get_cmd_output() was not included.  Didn't check for $VIM/.vimrc file.

Command line completion of options didn't work after "+=" and "-=".

Unix configure: Test for memmove()/bcopy()/memcpy() tried redefining these
functions, which could fail if they are defined already.  Use mch_memmove() to
redefine.

Unix: ":let a = expand("`xterm`&")" started an xterm asynchronously, but
":let a = expand("`xterm&`")" generated an error message, because the
redirection was put after the '&'.

Win32 GUI: Dialog buttons could not be selected properly with cursor keys,
when the default is not the first button. (Webb)

The "File has changed since editing started" (when regaining focus) could not
always be seen. (Webb)

When starting with "ex filename", the file message was overwritten with
the "entering Ex mode" message.

Output of ":tselect" listed name of file directly from the tags file.  Now it
is corrected for the position of the tags file.

When 'backspace' is 0, could backspace over autoindent.  Now it is no longer
allowed (Vi compatible).

In Replace mode, when 'noexpandtab' and 'smarttab' were set, and inserting
Tabs, backspacing didn't work correctly for Tabs inserted at the start of the
line (unless 'sts' was set too).  Also, when replacing the first non-blank
after which is a space, rounding the indent was done on the first non-blank
instead of on the character under the cursor.

When 'sw' at 4, 'ts' at 8 and 'smarttab' set: When a tab was appended after
four spaces (they are replaced with a tab) couldn't backspace over the tab.

In Insert mode, with 'bs' set to 0, couldn't backspace to before autoindent,
even when it was removed with CTRL-D.

When repeating an insert command where a <BS>, <Left> or other key causes an
error, would flush buffers and remain in Insert mode.  No longer flush
buffers, only beep and continue with the insert command.

Dos and Win32 console: Setting t_me didn't work to get another color.  Made
this works backwards compatible.

For Turkish (LANG = "tr") uppercase 'i' is not an 'I'.  Use ASCII uppercase
translation in vim_strup() to avoid language problems. (Komur)

Unix: Use usleep() or nanosleep() for mch_delay() when available.  Hopefully
this avoids a hangup in select(0, ..) for Solaris 2.6.

Vim would crash when using a script file with 'let &sp = "| tee"', starting
vim with "vim -u test", then doing ":set sp=".  The P_WAS_SET flag wasn't set
for a string option, could cause problems with any string option.

When using "cmd | vim -", stdin is not a terminal.  This gave problems with
GPM (Linux console mouse) and when executing external commands.  Now close
stdin and re-open it as a copy of stderr.

Syntax highlighting: A "nextgroup" item was not properly stored in the state
list.  This caused missing of next groups when not redrawing from start to
end, but starting halfway.

Didn't check for valid values of 'ttymouse'.

When executing an external command from the GUI, waiting for the child to
terminate might not work, causing a hang. (Parmelan)

"make uninstall" didn't delete the vimrc_example.vim and gvimrc_example.vim
files and the vimtutor.

Win32: "expand("%:p:h")" with no buffer name removed the directory name.
"fnamemodify("", ":p")" did not add a trailing slash, fname_case() removed it.

Fixed: When 'hlsearch' was set and the 'c' flag was not in 'cpoptions':
highlighting was not correct.  Now overlapping matches are handled correctly.

Athena, Motif and GTK GUI: When started without focus, cursor was shown as if
with focus.

Don't include 'shellpipe' when compiled without quickfix, it's not used.
Don't include 'dictionary' option when compiled without the +insert_expand
feature.
Only include the 'shelltype' option for the Amiga.

When making a change to a line, with 'hlsearch' on, causing it to wrap, while
executing a register, the screen would not be updated correctly.  This was a
generic problem in update_screenline() being called while must_redraw is
VALID.

Using ":bdelete" in a BufUnload autocommand could cause a crash.  The window
height was added to another window twice in close_window().

Win32 GUI: When removing a menu item, the tearoff wasn't updated. (Negri)

Some performance bottlenecks removed.  Allocating memory was not efficient.
For Win32 checking for available memory was slow, don't check it every time
now.  On NT obtaining the user name takes a long time, cache the result (for
all systems).

fnamemodify() with an argument ":~:." or ":.:~" didn't work properly.

When editing a new file and exiting, the marks for the buffer were not saved
in the viminfo file.

":confirm only" didn't put up a dialog.

These text objects didn't work when 'selection' was "exclusive": va( vi( va{
vi{ va< vi< vi[ va[.

The dialog for writing a readonly file didn't have a valid default. (Negri)

The line number used for error messages when sourcing a file was reset when
modelines were inspected.  It was wrong when executing a function.

The file name and line number for an error message wasn't displayed when it
was the same as for the last error, even when this was long ago.  Now reset
the name/lnum after a hit-enter prompt.

In a session file, a "%" in a file name caused trouble, because fprintf() was
used to write it to the file.

When skipping statements, a mark in an address wasn't skipped correctly:
"ka|if 0|'ad|else|echo|endif". (Roemer)

":wall" could overwrite a not-edited file without asking.

GUI: When $DISPLAY was not set or starting the GUI failed in another way, the
console mode then started with wrong colors and skipped initializations.  Now
do an early check if the GUI can be started.  Don't source the menu.vim or
gvimrc when it will not.  Also do normal terminal initializations if the GUI
might not start.

When using a BufEnter autocommand to position the cursor and scroll the
window, the cursor was always put at the last used line and halfway the window
anyhow.

When 'wildmode' was set to "longest,list:full", ":e *.c<Tab><Tab>" didn't list
the matches.  Also avoid that listing after a "longest" lists the wrong
matches when the first expansion changed the string in front of the cursor.

When using ":insert", ":append" or ":change" inside a while loop, was not able
to break out of it with a CTRL-C.

Win32: ":e ." took an awful long time before an error message when used in
"C:\".  Was caused by adding another backslash and then trying to get the full
name for "C:\\".

":winpos -10 100" was working like ":winpos -10 -10", because a pointer was
not advanced past the '-' sign.

When obtaining the value of a hidden option, would give an error message.  Now
just use a zero value.

OS/2: Was using argv[0], even though it was not a useful name.  It could be
just "vim", found in the search path.

Xterm: ":set columns=78" didn't redraw properly (when lines wrap/unwrap) until
after a delay of 'updatetime'.  Didn't check for the size-changed signal.

'scrollbind' didn't work in Insert mode.
Horizontal scrollbinding didn't always work for "0" and "$" commands (e.g.,
when 'showcmd' was off).

When compiled with minimal features but with GUI, switching on the mouse in an
xterm caused garbage, because the mouse codes were not recognized.  Don't
enable the mouse when it can't be recognized.  In the GUI it also didn't work,
the arguments to the mouse code were not interpreted.

When 'showbreak' used, in Insert mode, when the cursor is just after the last
character in the line, which is also the in the rightmost column, the cursor
position would be like the 'showbreak' string is shown, but it wasn't.

Autocommands could move the cursor in a new file, so that CTRL-W i didn't show
the right line.  Same for when using a filemark to jump to another file.

When redefining the argument list, the title used for other windows could be
showing the wrong info about the position in the argument list.  Also update
this for a ":split" command without arguments.

When editing file 97 of 13, ":Next" didn't work.  Now it goes to the last
file in the argument list.

Insert mode completion (for dictionaries or included files) could not be
interrupted by typing an <Esc>.  Could get hit-enter prompt after line
completion, or whenever the informative message would get too long.

When using the ":edit" command to re-edit the same file, an autocommand to
jump to the last cursor position caused the cursor to move.  Now set the last
used cursor position to avoid this.

When 'comments' has a part that starts with white space, formatting the
comment didn't work.

At the ":tselect" prompt Normal mode mappings were used.  That has been
disabled.

When 'selection' is not "old", some commands still didn't allow the cursor
past the end-of-line in Visual mode.

Athena: When a menu was deleted, it would appear again (but not functional)
when adding another menu.  Now they don't reappear anymore (although they are
not really deleted either).

Borland C++ 4.x had an optimizer problem in fill_breakat_flags(). (Negri)

"ze" didn't work when 'number' was on. (Davis)

Win32 GUI: Intellimouse code didn't work properly on Windows 98. (Robinson)

A few files were including proto.h a second time, after vim.h had already done
that, which could cause problems with the vim_realloc() macro.

Win32 console: <M-x> or ALT-x was not recognized.  Also keypad '+', '-' and
'*'. (Negri)
MS-DOS: <M-x> didn't work, produced a two-byte code.  Now the alphabetic and
number keys work. (Negri)

When finding a lot of matches for a tag completion, the check for avoiding
double matches could take a lot of time.  Add a line_breakcheck() to be able
to interrupt this. (Deshpande)

When the command line was getting longer than the screen, the more-prompt
would be given regularly, and the cursor position would be wrong.  Now only
show the part of the command line that fits on the screen and force the cursor
to be positioned on the visible part.  There can be text after the cursor
which isn't editable.

At the more prompt and with the console dialog, a cursor key was interpreted
as <Esc> and OA.  Now recognize special keys in get_keystroke().  Ignore mouse
and scrollbar events.

When typing a BS after inserting a middle comment leader, typing the last char
of the end comment leader still changed it into the end comment leader. (Webb)

When a file system is full, writing to a swap file failed.  Now first try to
write one block to the file.  Try next entry in 'dir' if it fails.

When "~" is in 'whichwrap', doing "~" on last char of a line didn't update the
display.

Unix: Expanding wildcards for ":file {\\}" didn't work, because "\}" was
translated to "}" before the shell got it.  Now don't remove backslashes when
wildcards are going to be expanded.

Unix: ":e /tmp/$uid" didn't work.  When expanding environment variables in a
file name doesn't work, use the shell to expand the file name.  ":e /tmp/$tty"
still doesn't work though.

"make test" didn't always work on DOS/Windows for test30, because it depended
on the external "echo" command.

The link.sh script used "make" instead of $MAKE from the Makefile.  Caused
problems for generating pathdef.c when "make" doesn't work properly.

On versions that can do console and GUI: In the console a typed CSI code could
cause trouble.

The patterns in expression evaluation didn't ignore the 'l' flag in
'cpoptions'.  This broke the working of <CR> in the options window.

When 'hls' off and 'ai' on, "O<Esc>" did remove the indent, but it was still
highlighted red for trailing space.

Win32 GUI: Dropping an encrypted file on a running gvim didn't work right.  Vim
would loop while outputting "*" characters.  vgetc() was called recursively,
thus it returns NUL.  Added safe_vgetc(), which reads input directly from the
user in this situation.

While reading text from stdin, only an empty screen was shown.  Now show that
Vim is reading from stdin.

The cursor shape wasn't set properly when returning to Insert mode, after
using a CTRL-O /asdf command which fails.  It would be OK after a few seconds.
Now it's OK right away.

The 'isfname' default for DOS/Windows didn't include the '@' character.  File
names that contained "dir\@file" could not be edited.

Win32 console: <C-S-Left> could cause a crash when compiled with Borland or
egcs. (Aaron)

Unix and VMS: "#if HAVE_DIRENT_H" caused problems for some compilers.  Use
"#ifdef HAVE_DIRENT_H" instead. (Jones)

When a matching tag is in the current file but has a search pattern that
doesn't match, the cursor would jump to the first line.

Unix: Dependencies for pty.c were not included in Makefile.  Dependency of
ctags/config.h was not included (only matters for parallel make).

Removed a few Uninitialized Memory Reads (potential crashes).  In do_call()
calling clear_var() when not evaluating.  In win32_expandpath() and
dos_expandpath() calling backslash_halve() past the end of a file name.

Removed memory leaks: Set_vim_var_string() never freed the value.  The
next_list for a syntax keyword was never freed.

On non-Unix systems, using a file name with wildcards without a match would
silently fail.  E.g., ":e *.sh".  Now give a "No match" error message.

The life/life.mac, urm/urm.mac and hanoi/hanoi.mac files were not recognized
as Vim scripts.  Renamed them to *.vim.

[Note: some numbered patches are not relevant when upgrading from version 5.3,
they have been removed]

Patch 5.4m.1
Problem:    When editing a file with a long name, would get the hit-enter
	    prompt, even though all settings are such that the name should be
	    truncated to avoid that.  filemess() was printing the file name
	    without truncating it.
Solution:   Truncate the message in filemess().  Use the same code as for
	    msg_trunc_attr(), which is moved to the new function
	    msg_may_trunc().
Files:	    src/message.c, src/proto/message.pro, src/fileio.c

Patch 5.4m.3
Problem:    The Motif libraries were not found by configure for Digital Unix.
Solution:   Add "/usr/shlib" to the search path. (Andy Kahn)
Files:	    src/configure.in, src/configure

Patch 5.4m.5
Problem:    Win32 GUI: When using the Save-As menu entry and selecting an
	    existing file in the file browser, would get a dialog to confirm
	    overwriting twice.  (Ed Krall)
Solution:   Removed the dialog from the file browser.  It would be nicer to
	    set the "forceit" flag and skip Vim's ":confirm" dialog, but it
	    requires quite a few changes to do that.
Files:	    src/gui_w32.c

Patch 5.4m.6
Problem:    Win32 GUI: When reading text from stdin, e.g., "cat foo | gvim -",
	    a message box would pop up with "-stdin-" (when exiting). (Michael
	    Schaap)
Solution:   Don't switch off termcap mode for versions that are GUI-only.
	    They use another terminal to read from stdin.
Files:	    src/main.c, src/fileio.c

Patch 5.4m.7
Problem:    Unix: running configure with --enable-gtk-check,
	    --enable-motif-check, --enable-athena-check or --enable-gtktest
	    had the reverse effect. (Thomas Koehler)
Solution:   Use $enable_gtk_check variable correctly in AC_ARG_ENABLE().
Files:	    src/configure.in, src/configure

Patch 5.4m.9
Problem:    Multi-byte: With wrapping lines, the cursor was sometimes 2
	    characters to the left.  Syntax highlighting was wrong when a
	    double-byte character was split for a wrapping line.  When
	    'showbreak' was on the splitting also didn't work.
Solution:   Adjust getvcol() and win_line(). (Chong-Dae Park)
Files:	    src/charset.c, src/screen.c

Patch 5.4m.11
Problem:    The ":call" command didn't check for illegal trailing characters.
	    (Stefan Roemer)
Solution:   Add the check in do_call().
Files:	    src/eval.c

Patch 5.4m.13
Problem:    With the ":s" command:
	    1. When performing a substitute command, the mouse would be
	       disabled and enabled for every substitution.
	    2. The cursor position could be beyond the end of the line.
	       Calling line_breakcheck() could try to position the cursor,
	       which causes a crash in the Win32 GUI.
	    3. When using ":s" in a ":g" command, the cursor was not put on
	       the first non-white in the line.
	    4. There was a hit-enter prompt when confirming the substitution
	       and the replacement was a bit longer.
Solution:   1. Only disable/enable the mouse when asking for confirmation.
	    2. Always put the cursor on the first character, it is going to be
	       moved to the first non-blank anyway.
	       Don't use the cursor position in gui_mch_draw_hollow_cursor(),
	       get the character from the screen buffer.
	    3. Added global_need_beginline flag to call beginline() after ":g"
	       has finished all substitutions.
	    4. Clear the need_wait_return flag after prompting the user.
Files:	    src/ex_cmds.c, src/gui_w32.c

Patch 5.4m.14
Problem:    When doing "vim xxx", ":opt", ":only" and then ":e xxx" we end
	    up with two swapfiles for "xxx".  That is caused by the ":bdel"
	    command which is executed when unloading the option-window.
	    Also, there was no check if closing a buffer made the new one
	    invalid, this could cause a crash.
Solution:   When closing a buffer causes the current buffer to be deleted,
	    use the new buffer to replace it.  Also detect that the new buffer
	    has become invalid as a side effect of closing the current one.
	    Make autocommand that calls ":bdel" in optwin.vim nested, so that
	    the buffer loading it triggers also executes autocommands.
	    Also added a test for this in test13.
Files:	    runtime/optwin.vim, src/buffer.c, src/ex_cmds.c, src/globals.h
	    src/testdir/test13.in, src/testdir/test13.ok

Patch 5.4m.15
Problem:    When using a BufEnter autocommand to reload the syntax file,
	    conversion to HTML caused a crash. (Sung-Hyun Nam)
Solution:   When using ":syntax clear" the current stack of syntax items was
	    not cleared.  This will cause memory to be used that has already
	    been freed.  Added call to invalidate_current_state() in
	    syntax_clear().
Files:	    src/syntax.c

Patch 5.4m.17
Problem:    When omitting a ')' in an expression it would not be seen as a
	    failure.
	    When detecting an error inside (), there would be an error message
	    for a missing ')' too.
	    When using ":echo 1+|echo 2" there was no error message. (Roemer)
	    When using ":exe 1+" there was no error message.
	    When using ":return 1+" there was no error message.
Solution:   Fix do_echo(), do_execute() and do_return() to give an error
	    message when eval1() returns FAIL.
	    Fix eval6() to handle trailing ')' correctly and return FAIL when
	    it's missing.
Files:	    src/eval.c

Patch 5.4m.18
Problem:    When using input() from inside an expression entered with
	    "CTRL-R =" on the command line, there could be a crash.  And the
	    resulting command line was wrong.
Solution:   Added getcmdline_prompt(), which handles recursive use of
	    getcmdline() correctly.  It also sets the command line prompt.
	    Removed cmdline_prompt().  Also use getcmdline_prompt() for
	    getting the crypt key in get_crypt_key().
Files:	    src/proto/ex_getln.pro, src/ex_getln.c, src/eval.c, src/misc2.c

Patch 5.4m.21
Problem:    When starting up, the screen structures were first allocated at
	    the minimal size, then initializations were done with Rows
	    possibly different from screen_Rows.  Caused a crash in rare
	    situations (GTK with XIM and fontset).
Solution:   Call screenalloc() in main() only after calling ui_get_winsize().
	    Also avoids a potential delay because of calling screenclear()
	    while "starting" is non-zero.
Files:	    src/main.c

Patch 5.4m.22
Problem:    In the GUI it was possible that the screen was resized and the
	    screen structures re-allocated while redrawing the screen.  This
	    could cause a crash (hard to reproduce).  The call sequence goes
	    through update_screen() .. syntax_start() .. ui_breakcheck() ..
	    gui_resize_window() .. screenalloc().
Solution:   Set updating_screen while redrawing.  If the window is resized
	    remember the new size and handle it only after redrawing is
	    finished.
	    This also fixes that resizing the screen while still redrawing
	    (slow syntax highlighting) would not work properly.
	    Also disable display_hint, it was never used.
Files:	    src/globals.h, src/gui.c, src/screen.c, src/proto/gui.pro

Patch 5.4m.23
Problem:    When using expand("<cword>") when there was no word under the
	    cursor, would get an error message.  Same for <cWORD> and <cfile>.
Solution:   Don't give an error message, return an empty string.
Files:	    src/eval.c

Patch 5.4m.24
Problem:    ":help \|" didn't find anything.  It was translated to "/\\|".
Solution:   Translate "\|" into "\\bar".  First check the table for specific
	    translations before checking for "\x".
Files:	    src/ex_cmds.c

Patch 5.4m.25
Problem:    Unix: When using command line completion on files that contain
	    ''', '"' or '|' the file name could not be used.
	    Adding this file name to the Buffers menu caused an error message.
Solution:   Insert a backslash before these three characters.
	    Adjust Mungename() function to insert a backslash before '|'.
Files:	    src/ex_getln.c, runtime/menu.vim

Patch 5.4m.26
Problem:    When using a mapping of two function keys, e.g., <F1><F1>, and
	    only the first char of the second key has been read, the mapping
	    would not be recognized.  Noticed on some Unix systems with xterm.
Solution:   Add 'K' flag to 'cpoptions' to wait for the whole key code, even
	    when halfway a mapping.
Files:	    src/option.h, src/term.c

Patch 5.4m.27
Problem:    When making test33 without the lisp feature it hangs. Interrupting
	    the execution of the script then might cause a crash.
Solution:   In inchar(), after closing a script, don't use buf[] anymore.
	    closescript() has freed typebuf[] and buf[] might be pointing
	    inside typebuf[].
	    Avoid that test33 hangs when the lisp feature is missing.
Files:	    src/getchar.c src/testdir/test33.in

"os2" was missing from the feature list.  Useful for has("os2").

BeOS:
- Included patches from Richard Offer for BeOS R4.5.
- menu code didn't work right.  Crashed in the Buffers menu.  The window title
  wasn't set. (Offer)

Patch 5.4n.3
Problem:    C-indenting was wrong after "  } else".  The white space was not
	    skipped.  Visible when 'cino' has "+10".
Solution:   Skip white space before calling cin_iselse(). (Norbert Zeh)
Files:	    src/misc1.c

Patch 5.4n.4
Problem:    When the 't' flag in 'cpoptions' is included, after a
	    ":nohlsearch" the search highlighting would not be enabled again
	    after a tag search. (Norbert Zeh)
Solution:   When setting the new search pattern in jumpto_tag(), don't restore
	    no_hlsearch.
Files:	    src/tag.c

Patch 5.4n.5
Problem:    When using ":normal" from a CursorHold autocommand Vim hangs.  The
	    autocommand is executed down from vgetc().  Calling vgetc()
	    recursively to execute the command doesn't work then.
Solution:   Forbid the use of ":normal" when vgetc_busy is set.  Give an error
	    message when this happens.
Files:	    src/ex_docmd.c, runtime/doc/autocmd.txt

Patch 5.4n.6
Problem:    "gv" could reselect a Visual that starts and/or ends past the end
	    of a line. (Robert Webb)
Solution:   Check that the start and end of the Visual area are on a valid
	    character by calling adjust_cursor().
Files:	    src/normal.c

Patch 5.4n.8
Problem:    When a mark was on a non existing line (e.g., when the .viminfo
	    was edited), jumping to it caused ml_get errors. (Alexey
	    Marinichev).
Solution:   Added check_cursor_lnum() in nv_gomark().
Files:	    src/normal.c

Patch 5.4n.9
Problem:    ":-2" moved the cursor to a negative line number. (Ralf Schandl)
Solution:   Give an error message for a negative line number.
Files:	    src/ex_docmd.c

Patch 5.4n.10
Problem:    Win32 GUI: At the hit-enter prompt, it was possible to scroll the
	    text.  This erased the prompt and made Vim look like it is in
	    Normal mode, while it is actually still waiting for a <CR>.
Solution:   Disallow scrolling at the hit-enter prompt for systems that use
	    on the fly scrolling.
Files:	    src/message.c

Patch 5.4n.14
Problem:    Win32 GUI: When using ":winsize 80 46" and the height is more than
	    what fits on the screen, the window size was made smaller than
	    asked for (that's OK) and Vim crashed (that's not OK)>
Solution:   Call check_winsize() from gui_set_winsize() to resize the windows.
Files:	    src/gui.c

Patch 5.4n.16
Problem:    Win32 GUI: The <F10> key both selected the menu and was handled as
	    a key hit.
Solution:   Apply 'winaltkeys' to <F10>, like it is used for Alt keys.
Files:	    src/gui_w32.c

Patch 5.4n.17
Problem:    Local buffer variables were freed when the buffer is unloaded.
	    That's not logical, since options are not freed. (Ron Aaron)
Solution:   Free local buffer variables only when deleting the buffer.
Files:	    src/buffer.c

Patch 5.4n.19
Problem:    Doing ":e" (without argument) in an option-window causes trouble.
	    The mappings for <CR> and <Space> are not removed.  When there is
	    another buffer loaded, the swap file for it gets mixed up.
	    (Steve Mueller)
Solution:   Also remove the mappings at the BufUnload event, if they are still
	    present.
	    When re-editing the same file causes the current buffer to be
	    deleted, don't try editing it.
	    Also added a test for this situation.
Files:	    runtime/optwin.vim, src/ex_cmds.c, src/testdir/test13.in,
	    src/testdir/test13.ok

Patch 5.4n.24
Problem:    BeOS: configure never enabled the GUI, because $with_x was "no".
	    Unix prototypes caused problems, because Display and Widget are
	    undefined.
	    Freeing fonts on exit caused a crash.
Solution:   Only disable the GUI when $with_x is "no" and  $BEOS is not "yes".
	    Add dummy defines for Display and Widget in proto.h.
	    Don't free the fonts in gui_exit() for BeOS.
Files:	    src/configure.in, src/configure, src/proto.h, src/gui.c.


The runtime/vim48x48.xpm icon didn't have a transparent background. (Schild)

Some versions of the mingw32/egcs compiler didn't have WINBASEAPI defined.
(Aaron)

VMS:
- mch_setenv() had two arguments instead of three.
- The system vimrc and gvimrc files were called ".vimrc" and ".gvimrc".
  Removed the dot.
- call to RealWaitForChar() had one argument too many. (Campbell)
- WaitForChar() is static, removed the prototype from proto/os_vms.pro.
- Many file accesses failed, because Unix style file names were used.
  Translate file names to VMS style by using vim_fopen().
- Filtering didn't work, because the temporary file name was generated wrong.
- There was an extra newline every 9192 characters when writing a file.  Work
  around it by writing line by line. (Campbell)
- os_vms.c contained "# typedef int DESC".  Should be "typedef int DESC;".
  Only mattered for generating prototypes.
- Added file name translation to many places.  Made easy by defining macros
  mch_access(), mch_fopen(), mch_fstat(), mch_lstat() and mch_stat().
- Set default for 'tagbsearch' to off, because binary tag searching apparently
  doesn't work for VMS.
- make mch_get_host_name() work with /dec and /standard=vaxc. (Campbell)


Patch 5.4o.2
Problem:    Crash when using "gf" on "file.c://comment here". (Scott Graham)
Solution:   Fix wrong use of pointers in get_file_name_in_path().
Files:	    src/window.c

Patch 5.4o.3
Problem:    The horizontal scrollbar was not sized correctly when 'number' is
	    set and 'wrap' not set.
	    Athena: Horizontal scrollbar wasn't updated when the cursor was
	    positioned with a mouse click just after dragging.
Solution:   Subtract 8 from the size when 'number' set and 'wrap' not set.
	    Reset gui.dragged_sb when a mouse click is received.
Files:	    src/gui.c

Patch 5.4o.4
Problem:    When running in an xterm and $WINDOWID is set to an illegal value,
	    Vim would exit with "Vim: Got X error".
Solution:   When using the display which was opened for the xterm clipboard,
	    check if x11_window is valid by trying to obtain the window title.
	    Also add a check in setup_xterm_clip(), for when using X calls to
	    get the pointer position in an xterm.
Files:	    src/os_unix.c

Patch 5.4o.5
Problem:    Motif version with Lesstif: When removing the menubar and then
	    using a menu shortcut key, Vim would crash. (raf)
Solution:   Disable the menu mnemonics when the menu bar is removed.
Files:	    src/gui_motif.c

Patch 5.4o.9
Problem:    The DOS install.exe program used the "move" program.  That doesn't
	    work on Windows NT, where "move" is internal to cmd.exe.
Solution:   Don't use an external program for moving the executables.  Use C
	    functions to copy the file and delete the original.
Files:	    src/dosinst.c

Motif and Athena obtained the status area height differently from GTK.  Moved
status_area_enabled from global.h to gui_x11.c and call
xim_get_status_area_height() to get the status area height.

Patch 5.4p.1
Problem:    When using auto-select, and the "gv" command is used, would not
	    always obtain ownership of the selection.  Caused by the Visual
	    area still being the same, but ownership taken away by another
	    program.
Solution:   Reset the clipboard Visual mode to force updating the selection.
Files:	    src/normal.c

Patch 5.4p.2
Problem:    Motif and Athena with XIM: Typing 3-byte
	    <multibyte><multibyte><space> doesn't work correctly with Ami XIM.
Solution:   Avoid using key_sym XK_VoidSymbol. (Nam)
Files:	    src/multbyte.c, src/gui_x11.c

Patch 5.4p.4
Problem:    Win32 GUI: The scrollbar values were reduced for a file with more
	    than 32767 lines.  But this info was kept global for all
	    scrollbars, causing a mixup between the windows.
	    Using the down arrow of a scrollbar in a large file didn't work.
	    Because of round-off errors there is no scroll at all.
Solution:   Give each scrollbar its own scroll_shift field.  When the down
	    arrow is used, scroll several lines.
Files:	    src/gui.h, src/gui_w32.c

Patch 5.4p.5
Problem:    When changing buffers in a BufDelete autocommand, there could be
	    ml_line errors and/or a crash. (Schandl)  Was caused by deleting
	    the current buffer.
Solution:   When the buffer to be deleted unexpectedly becomes the current
	    buffer, don't delete it.
	    Also added a check for this in test13.
Files:	    src/buffer.c, src/testdir/test13.in, src/testdir/test13.ok

Patch 5.4p.7
Problem:    Win32 GUI: When using 'mousemodel' set to "popup_setpos" and
	    clicking the right mouse button outside of the selected area, the
	    selected area wasn't removed until the popup menu has gone.
	    (Aaron)
Solution:   Set the cursor and update the display before showing the popup
	    menu.
Files:	    src/normal.c

Patch 5.4p.8
Problem:    The generated bugreport didn't contain information about
	    $VIMRUNTIME and whether runtime files actually exist.
Solution:   Added a few checks to the bugreport script.
Files:	    runtime/bugreport.vim

Patch 5.4p.9
Problem:    The windows install.exe created a wrong entry in the popup menu.
	    The "%1" was "".  The full directory was included, even when the
	    executable had been moved elsewhere. (Ott)
Solution:   Double the '%' to get one from printf.  Only include the path to
	    gvim.exe when it wasn't moved and it's not in $PATH.
Files:	    src/dosinst.c

Patch 5.4p.10
Problem:    Win32: On top of 5.4p.9: The "Edit with Vim" entry sometimes used
	    a short file name for a directory.
Solution:   Change the "%1" to "%L" in the registry entry.
Files:	    src/dosinst.c

Patch 5.4p.11
Problem:    Motif, Athena and GTK: When closing the GUI window when there is a
	    changed buffer, there was only an error message and Vim would not
	    exit.
Solution:   Put up a dialog, like for ":confirm qa".  Uses the code that was
	    already used for MS-Windows.
Files:	    src/gui.c, src/gui_w32.c

Patch 5.4p.12
Problem:    Win32: Trying to expand a string that is longer than 256
	    characters could cause a crash. (Steed)
Solution:   For the buffer in win32_expandpath() don't use a fixed size array,
	    allocate it.
Files:	    src/os_win32.c

MSDOS: Added "-Wall" to Makefile.djg compile flags.  Function prototypes for
fname_case() and mch_update_cursor() were missing.  "fd" was unused in
mf_sync().  "puiLocation" was unused in myputch().  "newcmd" unused in
mch_call_shell() for DJGPP version.

==============================================================================
VERSION 5.5						*version-5.5*

Version 5.5 is a bug-fix version of 5.4.


Changed							*changed-5.5*
-------

The DJGPP version is now compiled with "-O2" instead of "-O4" to reduce the
size of the executables.

Moved the src/STYLE file to runtime/doc/develop.txt.  Added the design goals
to it.

'backspace' is now a string option.  See patch 5.4.15.


Added							*added-5.5*
-----

Included Exuberant Ctags version 3.3. (Darren Hiebert)

In runtime/mswin.vim, map CTRL-Q to CTRL-V, so that CTRL-Q can be used
everywhere to do what CTRL-V used to do.

Support for decompression of bzip2 files in vimrc_example.vim.

When a patch is included, the patch number is entered in a table in version.c.
This allows skipping a patch without breaking a next one.

Support for mouse scroll wheel in X11.  See patch 5.5a.14.

line2byte() can be used to get the size of the buffer.  See patch 5.4.35.

The CTRL-R CTRL-O r and CTRL-R CTRL-P r commands in Insert mode are used to
insert a register literally.  See patch 5.4.48.

Uninstall program for MS-Windows.  To be able to remove the registry entries
for "Edit with Vim".  It is registered to be run from the "Add/Remove
programs" application.  See patch 5.4.x7.


Fixed							*fixed-5.5*
-----

When using vimrc_example.vim: An error message when the cursor is on a line
higher than the number of lines in the compressed file.  Move the autocommand
for jumping to the last known cursor position to after the decompressing
autocommands.

":mkexrc" and ":mksession" wrote the current value of 'textmode'.  That may
mark a file as modified, which causes problems.  This is a buffer-specific
setting, it should not affect all files.

"vim --version" wrote two empty lines.

Unix: The alarm signal could kill Vim.  It is generated by the Perl alarm()
function.  Ignore SIGALRM.

Win32 GUI: Toolbar still had the yellow bitmap for running a Vim script.

BeOS: "tmo" must be bigtime_t, instead of double. (Seibert)

Patch 5.4.1
Problem:    Test11 fails when $GZIP is set to "-v". (Matthew Jackson)
Solution:   Set $GZIP to an empty string.
Files:	    src/testdir/test11.in

Patch 5.4.2
Problem:    Typing <Esc> at the crypt key prompt caused a crash. (Kallingal)
Solution:   Check for a NULL pointer returned from get_crypt_key().
Files:	    src/fileio.c

Patch 5.4.3
Problem:    Python: Trying to use the name of an unnamed buffer caused a
	    crash. (Daniel Burrows)
Solution:   Check for b_fname being a NULL pointer.
Files:	    src/if_python.c

Patch 5.4.4
Problem:    Win32: When compiled without toolbar, but the 'T' flag is in
	    'guioptions', there would be an empty space for the toolbar.
Solution:   Add two #ifdefs where checking for the 'T' flag. (Vince Negri)
Files:	    src/gui.c

Patch 5.4.5
Problem:    Athena GUI: Using the Buffers.Refresh menu entry caused a crash.
	    Looks like any ":unmenu" command may cause trouble.
Solution:   Disallow ":unmenu" in the Athena version.  Disable the Buffers
	    menu, because the Refresh item would not work.
Files:	    src/menu.c, runtime/menu.vim

Patch 5.4.6
Problem:    GTK GUI: Using ":gui" in the .gvimrc file caused an error.  Only
	    happens when the GUI forks.
Solution:   Don't fork in a recursive call of gui_start().
Files:	    src/gui.c

Patch 5.4.7
Problem:    Typing 'q' at the more prompt for the ATTENTION message causes the
	    file loading to be interrupted. (Will Day)
Solution:   Reset got_int after showing the ATTENTION message.
Files:	    src/memline.c

Patch 5.4.8
Problem:    Edit some file, ":he", ":opt": options from help window are shown,
	    but pressing space updates from the other window. (Phillipps)
	    Also: When there are changes in the option-window, ":q!" gives an
	    error message.
Solution:   Before creating the option-window, go to a non-help window.
	    Use ":bdel!" to delete the buffer.
Files:	    runtime/optwin.vim

Patch 5.4.9
	    Just updates version.h.  The real patch has been moved to 5.4.x1.
	    This patch is just to keep the version number correct.

Patch 5.4.10
Problem:    GTK GUI: When $DISPLAY is invalid, "gvim -f" just exits.  It
	    should run in the terminal.
Solution:   Use gtk_init_check() instead of gtk_init().
Files:	    src/gui_gtk_x11.c

Patch 5.4.11
Problem:    When using the 'S' flag in 'cpoptions', 'tabstop' is not copied to
	    the next buffer for some commands, e.g., ":buffer".
Solution:   When the BCO_NOHELP flag is given to buf_copy_options(), still
	    copy the options used by do_help() when neither the "from" or "to"
	    buffer is a help buffer.
Files:	    src/option.c

Patch 5.4.12
Problem:    When using 'smartindent', there would be no extra indent if the
	    current line did not have any indent already. (Hanus Adler)
Solution:   There was a wrongly placed "else", that previously matched with
	    the "if" that set trunc_line.  Removed the "else" and added a
	    check for trunc_line to be false.
Files:	    src/misc1.c

Patch 5.4.13
Problem:    New SGI C compilers need another option for optimisation.
Solution:   Add a check in configure for "-OPT:Olimit". (Chin A Young)
Files:	    src/configure.in, src/configure

Patch 5.4.14
Problem:    Motif GUI: When the popup menu is present, a tiny window appears
	    on the desktop for some users.
Solution:   Set the menu widget ID for a popup menu to 0. (Thomas Koehler)
Files:	    src/gui_motif.c

Patch 5.4.15
Problem:    Since 'backspace' set to 0 has been made Vi compatible, it is no
	    longer possible to only allow deleting autoindent.
Solution:   Make 'backspace' a list of parts, to allow each kind of
	    backspacing separately.
Files:	    src/edit.c, src/option.c, src/option.h, src/proto/option.pro,
	    runtime/doc/option.txt, runtime/doc/insert.txt

Patch 5.4.16
Problem:    Multibyte: Locale zh_TW.Big5 was not checked for in configure.
Solution:   Add zh_TW.Big5 to configure check. (Chih-Tsun Huang)
Files:	    src/configure.in, src/configure

Patch 5.4.17
Problem:    GUI: When started from inside gvim with ":!gvim", Vim would not
	    start.  ":!gvim -f" works fine.
Solution:   After forking, wait a moment in the parent process, to give the
	    child a chance to set its process group.
Files:	    src/gui.c

Patch 5.4.18
Problem:    Python: The clear_history() function also exists in a library.
Solution:   Rename clear_history() to clear_hist().
Files:	    src/ex_getln.c, src/eval.c, src/proto/ex_getln.pro

Patch 5.4.19
Problem:    In a terminal with 25 lines, there is a more prompt after the
	    ATTENTION message.  When hitting 'q' here the dialog prompt
	    doesn't appear and file loading is interrupted. (Will Day)
Solution:   Don't allow quitting the printing of a message for the dialog
	    prompt.  Added the msg_noquit_more flag for this.
Files:	    src/message.c

Patch 5.4.20
Problem:    GTK: When starting gvim, would send escape sequences to the
	    terminal to switch the cursor off and on.
Solution:   Don't call msg_start() if the GUI is expected to start.
Files:	    src/main.c

Patch 5.4.21
Problem:    Motif: Toplevel menu ordering was wrong when using tear-off items.
Solution:   Don't add one to the index for a toplevel menu.
Files:	    src/gui_motif.c

Patch 5.4.22
Problem:    In Insert mode, <C-Left>, <S-Left>, <C-Right> and <S-Right> didn't
	    update the column used for vertical movement.
Solution:   Set curwin->w_set_curswant for those commands.
Files:	    src/edit.c

Patch 5.4.23
Problem:    When a Visual selection is lost to another program, and then the
	    same text is Visually selected again, the clipboard ownership
	    wasn't regained.
Solution:   Set clipboard.vmode to NUL to force regaining the clipboard.
Files:	    src/normal.c

Patch 5.4.24
Problem:    Encryption: When using ":r file" while 'key' has already entered,
	    the 'key' option would be messed up.  When writing the file it
	    would be encrypted with an unknown key and lost! (Brad Despres)
Solution:   Don't free cryptkey when it is equal to the 'key' option.
Files:	    src/fileio.c

Patch 5.4.25
Problem:    When 'cindent' is set, but 'autoindent' isn't, comments are not
	    properly indented when starting a new line. (Mitterand)
Solution:   When there is a comment leader for the new line, but 'autoindent'
	    isn't set, do C-indenting.
Files:	    src/misc1.c

Patch 5.4.26
Problem:    Multi-byte: a multibyte character is never recognized in a file
	    name, causing a backslash before it to be removed on Windows.
Solution:   Assume that a leading-byte character is a file name character in
	    vim_isfilec().
Files:	    src/charset.c

Patch 5.4.27
Problem:    Entries in the PopUp[nvic] menus were added for several modes, but
	    only deleted for the mode they were used for.  This resulted in
	    the  entry remaining in the PopUp menu.
	    When removing a PopUp[nvic] menu, the name had been truncated,
	    could result in greying-out the whole PopUp menu.
Solution:   Remove entries for all modes from the PopUp[nvic] menus.  Remove
	    the PopUp[nvic] menu entries first, before the name is changed.
Files:	    src/menu.c

Patch 5.4.28
Problem:    When using a BufWritePre autocommand to change 'fileformat', the
	    new value would not be used for writing the file.
Solution:   Check 'fileformat' after executing the autocommands instead of
	    before.
Files:	    src/fileio.c

Patch 5.4.29
Problem:    Athena GUI: When removing the 'g' flag from 'guioptions', using a
	    menu can result in a crash.
Solution:   Always grey-out menus for Athena, don't hide them.
Files:	    src/menu.c

Patch 5.4.30
Problem:    BeOS: Suspending Vim with CTRL-Z didn't work (killed Vim).  The
	    first character typed after ":sh" goes to Vim, instead of the
	    started shell.
Solution:   Don't suspend Vim, start a new shell.  Kill the async read thread
	    when starting a new shell.  It will be restarted later. (Will Day)
Files:	    src/os_unix.c, src/ui.c

Patch 5.4.31
Problem:    GUI: When 'mousefocus' is set, moving the mouse over where a
	    window boundary was, causes a hit-enter prompt to be finished.
	    (Jeff Walker)
Solution:   Don't use 'mousefocus' at the hit-enter prompt.  Also ignore it
	    for the more prompt and a few other situations.  When an operator
	    is pending, abort it first.
Files:	    src/gui.c

Patch 5.4.32
Problem:    Unix: $LDFLAGS was not passed to configure.
Solution:   Pass $LDFLAGS to configure just like $CFLAGS. (Jon Miner)
Files:	    src/Makefile

Patch 5.4.33
Problem:    Unix: After expanding an environment variable with the shell, the
	    next expansion would also use the shell, even though it is not
	    needed.
Solution:   Reset "recursive" before returning from gen_expand_wildcards().
Files:	    src/misc1.c

Patch 5.4.34 (also see 5.4.x5)
Problem:    When editing a file, and the file name is relative to a directory
	    above the current directory, the file name was made absolute.
	    (Gregory Margo)
Solution:   Add an argument to shorten_fnames() which indicates if all file
	    names should be shortened, or only absolute names.  In main() only
	    use shorten_fnames() to shorten absolute names.
Files:	    src/ex_docmd.c, src/fileio.c, src/main.c, src/proto/fileio.pro

Patch 5.4.35
Problem:    There is no function to get the current file size.
Solution:   Allow using line2byte() with the number of lines in the file plus
	    one.  This returns the offset of the line past the end of the
	    file, which is the file size plus one.
Files:	    src/eval.c, runtime/doc/eval.txt

Patch 5.4.36
Problem:    Comparing strings while ignoring case didn't work correctly for
	    some machines. (Mide Steed)
Solution:   vim_stricmp() and vim_strnicmp() only returned 0 or 1.  Changed
	    them to return -1 when the first argument is smaller.
Files:	    src/misc2.c

Patch 5.4.37 (also see 5.4.40 and 5.4.43)
Problem:    Long strings from the viminfo file are truncated.
Solution:   When writing a long string to the viminfo file, first write a line
	    with the length, then the string itself in a second line.
Files:	    src/eval.c, src/ex_cmds.c, src/ex_getln.c, src/mark.c, src/ops.c,
	    src/search.c, src/proto/ex_cmds.pro, runtime/syntax/viminfo.vim

Patch 5.4.38
Problem:    In the option-window, ":set go&" resulted in 'go' being handled
	    like a boolean option.
	    Mappings for <Space> and <CR> were overruled by the option-window.
Solution:   When the value of an option isn't 0 or 1, don't handle it like a
	    boolean option.
	    Save and restore mappings for <Space> and <CR> when entering and
	    leaving the option-window.
Files:	    runtime/optwin.vim

Patch 5.4.39
Problem:    When setting a hidden option, spaces before the equal sign were
	    not skipped and cause an error message.  E.g., ":set csprg =cmd".
Solution:   When skipping over a hidden option, check for a following "=val"
	    and skip it too.
Files:	    src/option.c

Patch 5.4.40 (depends on 5.4.37)
Problem:    Compiler error for "atol(p + 1)". (Axel Kielhorn)
Solution:   Add a typecast: "atol((char *)p + 1)".
Files:	    src/ex_cmds.c

Patch 5.4.41
Problem:    Some commands that were not included would give an error message,
	    even when after "if 0".
Solution:   Don't give an error message for an unsupported command when not
	    executing the command.
Files:	    src/ex_docmd.c

Patch 5.4.42
Problem:    ":w" would also cause a truncated message to appear in the message
	    history.
Solution:   Don't put a kept message in the message history when it starts
	    with "<".
Files:	    src/message.c

Patch 5.4.43 (depends on 5.4.37)
Problem:    Mixing long lines with multiple lines in a register causes errors
	    when writing the viminfo file. (Robinson)
Solution:   When reading the viminfo file to skip register contents, skip
	    lines that start with "<".
Files:	    src/ops.c

Patch 5.4.44
Problem:    When 'whichwrap' includes '~', a "~" command that goes on to the
	    next line cannot be properly undone. (Zellner)
Solution:   Save each line for undo in n_swapchar().
Files:	    src/normal.c

Patch 5.4.45 (also see 5.4.x8)
Problem:    When expand("$ASDF") fails, there is an error message.
Solution:   Remove the global expand_interactively.  Pass a flag down to skip
	    the error message.
	    Also: expand("$ASDF") returns an empty string if $ASDF isn't set.
	    Previously it returned "$ASDF" when 'shell' is "sh".
	    Also: system() doesn't print an error when the command returns an
	    error code.
Files:	    many

Patch 5.4.46
Problem:    Backspacing did not always use 'softtabstop' after hitting <CR>,
	    inserting a register, moving the cursor, etc.
Solution:   Reset inserted_space much more often in edit().
Files:	    src/edit.c

Patch 5.4.47
Problem:    When executing BufWritePre or BufWritePost autocommands for a
	    hidden buffer, the cursor could be moved to a non-existing
	    position. (Vince Negri)
Solution:   Save and restore the cursor and topline for the current window
	    when it is going to be used to execute autocommands for a hidden
	    buffer.  Use an existing window for the buffer when it's not
	    hidden.
Files:	    src/fileio.c

Patch 5.4.48
Problem:    A paste with the mouse in Insert mode was not repeated exactly the
	    same with ".".  For example, when 'autoindent' is set and pasting
	    text with leading indent. (Perry)
Solution:   Add the CTRL-R CTRL-O r and CTRL-R CTRL-P r commands in Insert
	    mode, which insert the contents of a register literally.
Files:	    src/edit.c, src/normal.c, runtime/doc/insert.txt

Patch 5.4.49
Problem:    When pasting text with [ <MiddleMouse>, the cursor could end up
	    after the last character of the line.
Solution:   Correct the cursor position for the change in indent.
Files:	    src/ops.c

Patch 5.4.x1 (note: Replaces patch 5.4.9)
Problem:    Win32 GUI: menu hints were never used, because WANT_MENU is not
	    defined until vim.h is included.
Solution:   Move the #ifdef WANT_MENU from where MENUHINTS is defined to where
	    it is used.
Files:	    src/gui_w32.c

Patch 5.4.x2
Problem:    BeOS: When pasting text, one character was moved to the end.
Solution:   Re-enable the BeOS code in fill_input_buf(), and fix timing out
	    with acquire_sem_etc(). (Will Day)
Files:	    src/os_beos.c, src/ui.c

Patch 5.4.x3
Problem:    Win32 GUI: When dropping a directory on a running gvim it crashes.
Solution:   Avoid using a NULL file name.  Also display a message to indicate
	    that the current directory was changed.
Files:	    src/gui_w32.c

Patch 5.4.x4
Problem:    Win32 GUI: Removing an item from the popup menu doesn't work.
Solution:   Don't remove the item from the menubar, but from the parent popup
	    menu.
Files:	    src/gui_w32.c

Patch 5.4.x5 (addition to 5.4.34)
Files:	    src/gui_w32.c

Patch 5.4.x6
Problem:    Win32: Expanding (dir)name starting with a dot doesn't work.
	    (McCormack)  Only when there is a path before it.
Solution:   Fix the check, done before expansion, if the file name pattern
	    starts with a dot.
Files:	    src/os_win32.c

Patch 5.4.x7
Problem:    Win32 GUI: Removing "Edit with Vim" from registry is difficult.
Solution:   Add uninstall program to remove the registry keys. It is installed
	    in the "Add/Remove programs" list for ease of use.
	    Also: don't set $VIM when the executable is with the runtime files.
	    Also: Add a text file with a step-by-step description of how to
	    uninstall Vim for DOS and Windows.
Files:	    src/uninstal.c, src/dosinst.c, src/Makefile.w32, uninstal.txt

Patch 5.4.x8 (addition to 5.4.45)
Files:	    many

Patch 5.4.x9
Problem:    Win32 GUI: After executing an external command, focus is not
	    always regained (when using focus-follows-mouse).
Solution:   Add SetFocus() in mch_system(). (Mike Steed)
Files:	    src/os_win32.c


Patch 5.5a.1
Problem:    ":let @* = @:" did not work.  The text was not put on the
	I   clipboard.  (Fisher)
Solution:   Own the clipboard and put the text on it.
Files:	    src/ops.c

Patch 5.5a.2
Problem:    append() did not mark the buffer modified.  Marks below the
	    new line were not adjusted.
Solution:   Fix the f_append() function.
Files:	    src/eval.c

Patch 5.5a.3
Problem:    Editing compressed ".gz" files doesn't work on non-Unix systems,
	    because there is no "mv" command.
Solution:   Add the rename() function and use it instead of ":!mv".
	    Also: Disable the automatic jump to the last position, because it
	    changes the jumplist.
Files:	    src/eval.c, runtime/doc/eval.txt, runtime/vimrc_example.vim

Patch 5.5a.4
Problem:    When using whole-line completion in insert mode while the cursor
	    is in the indent, get "out of memory" error. (Stekrt)
Solution:   Don't allocate a negative amount of memory in ins_complete().
Files:	    src/edit.c

Patch 5.5a.5
Problem:    Win32: The 'path' option can hold only up to 256 characters,
	    because _MAX_PATH is 256.  (Robert Webb)
Solution:   Use a fixed path length of 1024.
Files:	    src/os_win32.h

Patch 5.5a.6
Problem:    Compiling with gcc on Win32, using the Unix Makefile, didn't work.
Solution:   Add $(SUFFIX) to all places where an executable is used.  Also
	    pass it to ctags.  (Reynolds)
Files:	    src/Makefile

Patch 5.5a.7
Problem:    When using "cat | vim -" in an xterm, the xterm version reply
	    would end up in the file.
Solution:   Read the file from stdin before switching the terminal to RAW
	    mode.  Should also avoid problems with programs that use a
	    specific terminal setting.
	    Also: when using the GUI, print "Reading from stdin..." in the GUI
	    window, to give a hint why it doesn't do anything.
Files:	    src/main.c, src/fileio.c

Patch 5.5a.8
Problem:    On multi-threaded Solaris, suspending doesn't work.
Solution:   Call pause() when the SIGCONT signal was not received after
	    sending the SIGTSTP signal. (Nagano)
Files:	    src/os_unix.c

Patch 5.5a.9
Problem:    'winaltkeys' could be set to an empty argument, which is illegal.
Solution:   Give an error message when doing ":set winaltkeys=".
Files:	    src/option.c

Patch 5.5a.10
Problem:    Win32 console: Using ALTGR on a German keyboard to produce "}"
	    doesn't work, because the 8th bit is set when ALT is pressed.
Solution:   Don't set the 8th bit when ALT and CTRL are used. (Leipert)
Files:	    src/os_win32.c

Patch 5.5a.11
Problem:    Tcl: Configure always uses tclsh8.0.
	    Also: Loading a library doesn't work.
Solution:   Add "--with-tclsh" configure argument to allow specifying another
	    name for the tcl shell.
	    Call Tcl_Init() in tclinit() to make loading libraries work.
	    (Johannes Zellner)
Files:	    src/configure.in, src/configure, src/if_tcl.c

Patch 5.5a.12
Problem:    The "user_commands" feature is called "user-commands".
Solution:   Replace "user-commands" with "user_commands". (Kim Sung-bom)
	    Keep "user-commands" for the has() function, to remain backwards
	    compatible with 5.4.
Files:	    src/eval.c, src/version.c

Patch 5.5a.13
Problem:    OS/2: When $HOME is not defined, "C:/" is used for the viminfo
	    file.  That is very wrong when OS/2 is on another partition.
Solution:   Use $VIM for the viminfo file when it is defined, like for MSDOS.
	    Also: Makefile.os2 didn't depend on os_unix.h.
Files:	    src/os_unix.h, src/Makefile.os2

Patch 5.5a.14
Problem:    Athena, Motif and GTK: The Mouse scroll wheel doesn't work.
Solution:   Interpret a click of the wheel as a key press of the <MouseDown>
	    or <MouseUp> keys.  Default behavior is to scroll three lines, or
	    a full page when Shift is used.
Files:	    src/edit.c, src/ex_getln.c, src/gui.c, src/gui_gtk_x11.c,
	    src/gui_x11.c, src/keymap.h, src/message.c, src/misc1.c,
	    src/misc2.c, src/normal.c,  src/proto/normal.pro, src/vim.h,
	    runtime/doc/scroll.txt

Patch 5.5a.15
Problem:    Using CTRL-A in Insert mode doesn't work correctly when the insert
	    started with the <Insert> key. (Andreas Rohrschneider)
Solution:   Replace <Insert> with "i" before setting up the redo buffer.
Files:	    src/normal.c

Patch 5.5a.16
Problem:    VMS: GUI does not compile and run.
Solution:   Various fixes. (Zoltan Arpadffy)
	    Moved functions from os_unix.c to ui.c, so that VMS can use them
	    too: open_app_context(), x11_setup_atoms() and clip_x11* functions.
	    Made xterm_dpy global, it's now used by ui.c and os_unix.c.
	    Use gethostname() always, sys_hostname doesn't exist.
Files:	    src/globals.h, src/gui_x11.c, src/os_vms.mms, src/os_unix.c,
	    src/os_vms.c, src/ui.c, src/proto/os_unix.pro, src/proto/ui.pro

Renamed AdjustCursorForMultiByteCharacter() to AdjustCursorForMultiByteChar()
to avoid symbol length limit of 31 characters. (Steve P. Wall)

Patch 5.5b.1
Problem:    SASC complains about dead assignments and implicit type casts.
Solution:   Removed the dead assignments.  Added explicit type casts.
Files:	    src/buffer.c, src/edit.c, src/eval.c, src/ex_cmds.c,
	    src/ex_getln.c, src/fileio.c, src/getchar.c, src/memline.c,
	    src/menu.c, src/misc1.c, src/normal.c, src/ops.c, src/quickfix.c,
	    src/screen.c

Patch 5.5b.2
Problem:    When using "CTRL-O O" in Insert mode, hit <Esc> and then "o" in
	    another line truncates that line. (Devin Weaver)
Solution:   When using a command that starts Insert mode from CTRL-O, reset
	    "restart_edit" first.  This avoids that edit() is called with a
	    mix of starting a new edit command and restarting a previous one.
Files:	    src/normal.c

==============================================================================
VERSION 5.6						*version-5.6*

Version 5.6 is a bug-fix version of 5.5.


Changed							*changed-5.6*
-------

Small changes to OleVim files. (Christian Schaller)

Inserted "/**/" between patch numbers in src/version.c.  This allows for one
line of context, which some versions of patch need.

Reordered the Syntax menu to avoid long submenus.  Removed keyboard shortcuts
for alphabetical items to avoid a clash with fixed items.


Added							*added-5.6*
-----

Included Exuberant Ctags version 3.4. (Darren Hiebert)

OpenWithVim in Python. (Christian Schaller)

Win32 GUI: gvimext.dll, for the context menu "Edit with Vim" entry.  Avoids
the reported problems with the MS Office taskbar.  Now it's a Shell Extension.
(Tianmiao Hu)

New syntax files:
abel		Abel (John Cook)
aml		Arc Macro Language (Nikki Knuit)
apachestyle	Apache-style config file (Christian Hammers)
cf		Cold Fusion (Jeff Lanzarotta)
ctrlh		files with CTRL-H sequences (Bram Moolenaar)
cupl		CUPL (John Cook)
cuplsim		CUPL simulation (John Cook)
erlang		Erlang (Kresimir Marzic)
gedcom		Gedcom (Paul Johnson)
icon		Icon (Wendell Turner)
ist		MakeIndex style (Peter Meszaros)
jsp		Java Server Pages (Rafael Garcia-Suarez)
rcslog		Rcslog (Joe Karthauser)
remind		Remind (Davide Alberani)
sqr		Structured Query Report Writer (Paul Moore)
tads		TADS (Amir Karger)
texinfo		Texinfo (Sandor Kopanyi)
xpm2		X Pixmap v2 (Steve Wall)

The 'C' flag in 'cpoptions' can be used to switch off concatenation for
sourced lines.  See patch 5.5.013 below. |line-continuation|

"excludenl" argument for the ":syntax" command.  See patch 5.5.032 below.
|:syn-excludenl|

Implemented |z+| and |z^| commands.  See patch 5.5.050 below.

Vim logo in Corel Draw format.  Can be scaled to any resolution.


Fixed							*fixed-5.6*
-----

Using this mapping in Select mode, terminated completion:
":vnoremap <C-N> <Esc>a<C-N>" (Benji Fisher)
Ignore K_SELECT in ins_compl_prep().

VMS (Zoltan Arpadffy, David Elins):
- ioctl() in pty.c caused trouble, #ifndef VMS added.
- Cut & paste mismatch corrected.
- Popup menu line crash corrected.  (Patch 5.5.047)
- Motif directories during open and save as corrected.
- Handle full file names with version numbers. (Patch 5.5.046)
- Directory handling (CD command etc.)
- Corrected file name conversion VMS to Unix and v.v.
- Recovery was not working.
- Terminal and signal handling was outdated compared to os_unix.c.
- Improved os_vms.txt.

Configure used fprintf() instead of printf() to check for __DATE__ and
__TIME__. (John Card II)

BeOS: Adjust computing the char_height and char_ascent.  Round them up
separately, avoids redrawing artifacts. (Mike Steed)

Fix a few multibyte problems in menu_name_skip(), set_reg_ic(), searchc() and
findmatchlimit(). (Taro Muraoka)

GTK GUI:
- With GTK 1.2.5 and later the scrollbars were not redrawn correctly.
- Adjusted the gtk_form_draw() function.
- SNiFF connection didn't work.
- 'mousefocus' was not working. (Dalecki)
- Some keys were not working with modifiers: Shift-Tab, Ctrl-Space and CTRL-@.


Patch 5.5.001
Problem:    Configure in the top directory did not pass on an argument with a
	    space correctly.  For example "./configure --previs="/My home".
	    (Stephane Chazelas)
Solution:   Use '"$@"' instead of '$*' to pass on the arguments.
Files:	    configure

Patch 5.5.002
Problem:    Compilation error for using "fds[] & POLLIN". (Jeff Walker)
Solution:   Use "fds[].revents & POLLIN".
Files:	    src/os_unix.c

Patch 5.5.003
Problem:    The autoconf check for sizeof(int) is wrong on machines where
	    sizeof(size_t) != sizeof(int).
Solution:   Use our own configure check.  Also fixes the warning for
	    cross-compiling.
Files:	    src/configure.in, src/configure

Patch 5.5.004
Problem:    On Unix it's not possible to interrupt ":sleep 100".
Solution:   Switch terminal to cooked mode while asleep, to allow a SIGINT to
	    wake us up.  But switch off echo, added TMODE_SLEEP.
Files:	    src/term.h, src/os_unix.c

Patch 5.5.005
Problem:    When using <f-args> with a user command, an empty argument to the
	    command resulted in one empty string, while no string was
	    expected.
Solution:   Catch an empty argument and pass no argument to the function.
	    (Paul Moore)
Files:	    src/ex_docmd.c

Patch 5.5.006
Problem:    Python: When platform-dependent files are in another directory
	    than the platform-independent files it doesn't work.
Solution:   Also check the executable directory, and add it to CFLAGS. (Tessa
	    Lau)
Files:	    src/configure.in, src/configure

Patch 5.5.007 (extra)
Problem:    Win32 OLE: Occasional crash when exiting while still being used
	    via OLE.
Solution:   Move OleUninitialize() to before deleting the application object.
	    (Vince Negri)
Files:	    src/if_ole.cpp

Patch 5.5.008
Problem:    10000@@ takes a long time and cannot be interrupted.
Solution:   Check for CTRL-C typed while in the loop to push the register.
Files:	    src/normal.c

Patch 5.5.009
Problem:    Recent Sequent machines don't link with "-linet". (Kurtis Rader)
Solution:   Remove configure check for Sequent.
Files:	    src/configure.in, src/configure

Patch 5.5.010
Problem:    Ctags freed a memory block twice when exiting.  When out of
	    memory, a misleading error message was given.
Solution:   Update to ctags 3.3.2.  Also fixes a few other problems. (Darren
	    Hiebert)
Files:	    src/ctags/*

Patch 5.5.011
Problem:    After "CTRL-V s", the cursor jumps back to the start, while all
	    other operators leave the cursor on the last changed character.
	    (Xiangjiang Ma)
Solution:   Position cursor on last changed character, if possible.
Files:	    src/ops.c

Patch 5.5.012
Problem:    Using CTRL-] in Visual mode doesn't work when the text includes a
	    space (just where it's useful). (Stefan Bittner)
Solution:   Don't escape special characters in a tag name with a backslash.
Files:	    src/normal.c

Patch 5.5.013
Problem:    The ":append" and ":insert" commands allow using a leading
	    backslash in a line.  The ":source" command concatenates those
	    lines. (Heinlein)
Solution:   Add the 'C' flag in 'cpoptions' to switch off concatenation.
Files:	    src/ex_docmd.c, src/option.h, runtime/doc/options.txt,
	    runtime/filetype.vim, runtime/scripts.vim

Patch 5.5.014
Problem:    When executing a register with ":@", the ":append" command would
	    get text lines with a ':' prepended. (Heinlein)
Solution:   Remove the ':' characters.
Files:	    src/ex_docmd.c, src/ex_getln.c, src/globals.h

Patch 5.5.015
Problem:    When using ":g/pat/p", it's hard to see where the output starts,
	    the ":g" command is overwritten.  Vi keeps the ":g" command.
Solution:   Keep the ":g" command, but allow overwriting it with the report
	    for the number of changes.
Files:	    src/ex_cmds.c

Patch 5.5.016 (extra)
Problem:    Win32: Using regedit to install Vim in the popup menu requires the
	    user to confirm this in a dialog.
Solution:   Use "regedit /s" to avoid the dialog
Files:	    src/dosinst.c

Patch 5.5.017
Problem:    If an error occurs when closing the current window, Vim could get
	    stuck in the error handling.
Solution:   Don't set curwin to NULL when closing the current window.
Files:	    src/window.c

Patch 5.5.018
Problem:    Absolute paths in shell scripts do not always work.
Solution:   Use /usr/bin/env to find out the path.
Files:	    runtime/doc/vim2html.pl, runtime/tools/efm_filter.pl,
	    runtime/tools/shtags.pl

Patch 5.5.019
Problem:    A function call in 'statusline' stops using ":q" twice from
	    exiting, when the last argument hasn't been edited.
Solution:   Don't decrement quitmore when executing a function. (Madsen)
Files:	    src/ex_docmd.c

Patch 5.5.020
Problem:    When the output of CTRL-D completion in the commandline goes all
	    the way to the last column, there is an empty line.
Solution:   Don't add a newline when the cursor wrapped already. (Madsen)
Files:	    src/ex_getln.c

Patch 5.5.021
Problem:    When checking if a file name in the tags file is relative,
	    environment variables were not expanded.
Solution:   Expand the file name before checking if it is relative. (Madsen)
Files:	    src/tag.c

Patch 5.5.022
Problem:    When setting or resetting 'paste' the ruler wasn't updated.
Solution:   Update the status lines when 'ruler' changes because of 'paste'.
Files:	    src/option.c

Patch 5.5.023
Problem:    When editing a new file and autocommands change the cursor
	    position, the cursor was moved back to the first non-white, unless
	    'startofline' was reset.
Solution:   Keep the new column, just like the line number.
Files:	    src/ex_cmds.c

Patch 5.5.024 (extra)
Problem:    Win32 GUI: When using confirm() to put up a dialog without a
	    default button, the dialog would not have keyboard focus.
	    (Krishna)
Solution:   Always set focus to the dialog window.  Only set focus to a button
	    when a default one is specified.
Files:	    src/gui_w32.c

Patch 5.5.025
Problem:    When using "keepend" in a syntax region, a contained match that
	    includes the end-of-line could still force that region to
	    continue, if there is another contained match in between.
Solution:   Check the keepend_level in check_state_ends().
Files:	    src/syntax.c

Patch 5.5.026
Problem:    When starting Vim in a white-on-black xterm, with 'bg' set to
	    "dark", and then starting the GUI with ":gui", setting 'bg' to
	    "light" in the gvimrc, the highlighting isn't set.  (Tsjokwing)
Solution:   Set the highlighting when 'bg' is changed in the gvimrc, even
	    though full_screen isn't set.
Files:	    src/option.c

Patch 5.5.027
Problem:    Unix: os_unix.c doesn't compile when XTERM_CLIP is used but
	    WANT_TITLE isn't. (Barnum)
Solution:   Move a few functions that are used by the X11 title and clipboard
	    and put another "#if" around it.
Files:	    src/os_unix.c

Patch 5.5.028 (extra)
Problem:    Win32 GUI: When a file is dropped on Win32 gvim while at the ":"
	    prompt, the file is edited but the command line is actually still
	    there, the cursor goes back to command line on the next command.
	    (Krishna)
Solution:   When dropping a file or directory on gvim while at the ":" prompt,
	    insert the name of the file/directory.  Allows using the
	    file/directory name for any Ex command.
Files:	    src/gui_w32.c

Patch 5.5.029
Problem:    "das" at the end of the file didn't delete the last character of
	    the sentence.
Solution:   When there is no character after the sentence, make the operation
	    inclusive in current_sent().
Files:	    src/search.c

Patch 5.5.030
Problem:    Unix: in os_unix.c, "term_str" is used, which is also defined in
	    vim.h as a macro. (wuxin)
Solution:   Renamed "term_str" to "buf" in do_xterm_trace().
Files:	    src/os_unix.c

Patch 5.5.031 (extra)
Problem:    Win32 GUI: When exiting Windows, gvim will leave swap files behind
	    and will be killed ungracefully. (Krishna)
Solution:   Catch the WM_QUERYENDSESSION and WM_ENDSESSION messages and try to
	    exit gracefully.  Allow the user to cancel the shutdown if there
	    is a changed buffer.
Files:	    src/gui_w32.c

Patch 5.5.032
Problem:    Patch 5.5.025 wasn't right.  And C highlighting was still not
	    working correctly for a #define.
Solution:   Added "excludenl" argument to ":syntax", to be able not to extend
	    a containing item when there is a match with the end-of-line.
Files:	    src/syntax.c, runtime/doc/syntax.txt, runtime/syntax/c.vim

Patch 5.5.033
Problem:    When reading from stdin, a long line in viminfo would mess up the
	    file message.  readfile() uses IObuff for keep_msg, which could be
	    overwritten by anyone.
Solution:   Copy the message from IObuff to msg_buf and set keep_msg to that.
	    Also change vim_fgets() to not use IObuff any longer.
Files:	    src/fileio.c

Patch 5.5.034
Problem:    "gvim -rv" caused a crash.  Using 't_Co' before it's set.
Solution:   Don't try to initialize the highlighting before it has been
	    initialized from main().
Files:	    src/syntax.c

Patch 5.5.035
Problem:    GTK with XIM: Resizing with status area was messy, and
	    ":set guioptions+=b" didn't work.
Solution:   Make status area a separate widget, but not a separate window.
	    (Chi-Deok Hwang)
Files:	    src/gui_gtk_f.c, src/gui_gtk_x11.c, src/multbyte.c

Patch 5.5.036
Problem:    The GZIP_read() function in $VIMRUNTIME/vimrc_example.vim to
	    uncompress a file did not do detection for 'fileformat'.  This is
	    because the filtering is done with 'binary' set.
Solution:   Split the filtering into separate write, filter and read commands.
Files:	    runtime/vimrc_example.vim

Patch 5.5.037
Problem:    The "U" command didn't mark the buffer as changed. (McCormack)
Solution:   Set the 'modified' flag when using "U".
Files:	    src/undo.c

Patch 5.5.038
Problem:    When typing a long ":" command, so that the screen scrolls up,
	    causes the hit-enter prompt, even though the user just typed
	    return to execute the command.
Solution:   Reset need_wait_return if (part of) the command was typed in
	    getcmdline().
Files:	    src/ex_getln.c

Patch 5.5.039
Problem:    When using a custom status line, "%a" (file # of #) reports the
	    index of the current window for all windows.
Solution:   Pass a window pointer to append_arg_number(), and pass the window
	    being updated from build_stl_str_hl(). (Stephen P. Wall)
Files:	    src/buffer.c, src/screen.c, src/proto/buffer.pro

Patch 5.5.040
Problem:    Multi-byte: When there is some error in xim_real_init(), it can
	    close XIM and return.  After this there can be a segv.
Solution:   Test "xic" for being non-NULL, don't set "xim" to NULL.  Also try
	    to find more matches for supported styles. (Sung-Hyun Nam)
Files:	    src/multbyte.c

Patch 5.5.041
Problem:    X11 GUI: CTRL-_ requires the SHIFT key only on some machines.
Solution:   Translate CTRL-- to CTRL-_. (Robert Webb)
Files:	    src/gui_x11.c

Patch 5.5.042
Problem:    X11 GUI: keys with ALT were assumed to be used for the menu, even
	    when the menu has been disabled by removing 'm' from 'guioptions'.
Solution:   Ignore keys with ALT only when gui.menu_is_active is set. (Raf)
Files:	    src/gui_x11.c

Patch 5.5.043
Problem:    GTK: Handling of fontset fonts was not right when 'guifontset'
	    contains exactly 14 times '-'.
Solution:   Avoid setting fonts when working with a fontset. (Sung-Hyun Nam)
Files:	    src/gui_gtk_x11.c

Patch 5.5.044
Problem:    pltags.pl contains an absolute path "/usr/local/bin/perl".  That
	    might not work everywhere.
Solution:   Use "/usr/bin/env perl" instead.
Files:	    runtime/tools/pltags.pl

Patch 5.5.045
Problem:    Using "this_session" variable does not work, requires preceding it
	    with "v:".  Default filename for ":mksession" isn't mentioned
	    in the docs. (Fisher)
Solution:   Support using "this_session" to be backwards compatible.
Files:	    src/eval.c, runtime/doc/options.txt

Patch 5.5.046 (extra)
Problem:    VMS: problems with path and filename.
Solution:   Truncate file name at last ';', etc. (Zoltan Arpadffy)
Files:	    src/buffer.c, src/fileio.c, src/gui_motif.c, src/os_vms.c,
	    src/proto/os_vms.pro

Patch 5.5.047
Problem:    VMS: Crash when using the popup menu
Solution:   Turn the #define MENU_MODE_CHARS into an array. (Arpadffy)
Files:	    src/structs.h, src/menu.c

Patch 5.5.048
Problem:    HP-UX 11: Compiling doesn't work, because both string.h and
	    strings.h are included. (Squassabia)
Solution:   The configure test for including both string.h and strings.h
	    must include <Xm/Xm.h> first, because it causes problems.
Files:	    src/configure.in, src/configure, src/config.h.in

Patch 5.5.049
Problem:    Unix: When installing Vim, the protection bits of files might be
	    influenced by the umask.
Solution:   Add $(FILEMOD) to Makefile. (Shetye)
Files:	    src/Makefile

Patch 5.5.050
Problem:    "z+" and "z^" commands are missing.
Solution:   Implemented "z+" and "z^".
Files:	    src/normal.c, runtime/doc/scroll.txt, runtime/doc/index.txt

Patch 5.5.051
Problem:    Several Unix systems have a problem with the optimization limits
	    check in configure.
Solution:   Removed the configure check, let the user add it manually in
	    Makefile or the environment.
Files:	    src/configure.in, src/configure, src/Makefile

Patch 5.5.052
Problem:    Crash when using a cursor key at the ATTENTION prompt. (Alberani)
Solution:   Ignore special keys at the console dialog.  Also ignore characters
	    > 255 for other uses of tolower() and toupper().
Files:	    src/menu.c, src/message.c, src/misc2.c

Patch 5.5.053
Problem:    Indenting is wrong after a function when 'cino' has "fs".  Another
	    problem when 'cino' has "{s".
Solution:   Put line after closing "}" of a function at the left margin.
	    Apply ind_open_extra in the right way after a '{'.
Files:	    src/misc1.c, src/testdir/test3.in, src/testdir/test3.ok

Patch 5.5.054
Problem:    Unix: ":e #" doesn't work if the alternate file name contains a
	    space or backslash. (Hudacek)
Solution:   When replacing "#", "%" or other items that stand for a file name,
	    prepend a backslash before special characters.
Files:	    src/ex_docmd.c

Patch 5.5.055
Problem:    Using "<C-V>$r-" in blockwise Visual mode replaces one character
	    beyond the end of the line. (Zivkov)
Solution:   Only replace existing characters.
Files:	    src/ops.c

Patch 5.5.056
Problem:    After "z20<CR>" messages were printed at the old command line
	    position once.  (Veselinovic)
Solution:   Set msg_row and msg_col when changing cmdline_row in
	    win_setheight().
Files:	    src/window.c

Patch 5.5.057
Problem:    After "S<Esc>" it should be possible to restore the line with "U".
	    (Veselinovic)
Solution:   Don't call u_clearline() in op_delete() when changing only one
	    line.
Files:	    src/ops.c

Patch 5.5.058
Problem:    Using a long search pattern and then "n" causes the hit-enter
	    prompt.  (Krishna)
Solution:   Truncate the echoed pattern, like other messages.  Moved code for
	    truncating from msg_attr() to msg_strtrunc().
Files:	    src/message.c, src/proto/message.pro, src/search.c

Patch 5.5.059
Problem:    GTK GUI: When $term is invalid, using "gvim" gives an error
	    message, even though $term isn't really used.  (Robbins)
Solution:   When the GUI is about to start, skip the error messages for a
	    wrong $term.
Files:	    src/term.c

Patch 5.5.060 (extra)
Problem:    Dos 32 bit: When a directory in 'backupdir' doesn't exist, ":w"
	    causes the file to be renamed to "axlqwqhy.ba~". (Matzdorf)
Solution:   The code to work around a LFN bug in Windows 95 doesn't handle a
	    non-existing target name correctly.  When renaming fails, make
	    sure the file has its original name.  Also do this for the Win32
	    version, although it's unlikely that it runs into this problem.
Files:	    src/os_msdos.c, src/os_win32.c

Patch 5.5.061
Problem:    When using "\:" in a modeline, the backslash is included in the
	    option value. (Mohsin)
Solution:   Remove one backslash before the ':' in a modeline.
Files:	    src/buffer.c, runtime/doc/options.txt

Patch 5.5.062 (extra)
Problem:    Win32 console: Temp files are created in the root of the current
	    drive, which may be read-only. (Peterson)
Solution:   Use the same mechanism of the GUI version: Use $TMP, $TEMP or the
	    current directory.  Cleaned up vim_tempname() a bit.
Files:	    src/fileio.c, src/os_win32.h, runtime/doc/os_dos.txt

Patch 5.5.063
Problem:    When using whole-line completion in Insert mode, 'cindent' is
	    applied, even after changing the indent of the line.
Solution:   Don't reindent the completed line after inserting/removing indent.
	    (Robert Webb)
Files:	    src/edit.c

Patch 5.5.064
Problem:    has("sniff") doesn't work correctly.
Solution:   Return 1 when Vim was compiled with the +sniff feature. (Pruemmer)
Files:	    src/eval.c

Patch 5.5.065
Problem:    When dropping a file on Vim, the 'shellslash' option is not
	    effective. (Krishna)
Solution:   Fix the slashes in the dropped file names according to
	    'shellslash'.
Files:	    src/ex_docmd.c, runtime/doc/options.txt

Patch 5.5.066
Problem:    For systems with backslash in file name: Setting a file name
	    option to a value starting with "\\machine" removed a backslash.
Solution:   Keep the double backslash for "\\machine", but do change
	    "\\\\machine" to "\\machine" for backwards compatibility.
Files:	    src/option.c, runtime/doc/options.txt

Patch 5.5.067
Problem:    With 'hlsearch' set, the pattern "\>" doesn't highlight the first
	    match in a line. (Benji Fisher)
Solution:   Fix highlighting an empty match.  Also highlight the first
	    character in an empty line for "$".
Files:	    src/screen.c

Patch 5.5.068
Problem:    Crash when a ":while" is used with an argument that has an error.
	    (Sylvain Viart)
Solution:   Was using an uninitialized index in the cs_line[] array.  The
	    crash only happened when the index was far off.  Made sure the
	    uninitialized index isn't used.
Files:	    src/ex_docmd.c

Patch 5.5.069
Problem:    Shifting lines in blockwise Visual mode didn't set the 'modified'
	    flag.
Solution:   Do set the 'modified' flag.
Files:	    src/ops.c

Patch 5.5.070
Problem:    When editing a new file, creating that file outside of Vim, then
	    editing it again, ":w" still warns for overwriting an existing
	    file. (Nam)
Solution:   The BF_NEW flag in the "b_flags" field wasn't cleared properly.
Files:	    src/buffer.c, src/fileio.c

Patch 5.5.071
Problem:    Using a matchgroup in a ":syn region", which is the same syntax
	    group as the region, didn't stop a contained item from matching in
	    the start pattern.
Solution:   Also push an item on the stack when the syntax ID of the
	    matchgroup is the same as the syntax ID of the region.
Files:	    src/syntax.c

Patch 5.5.072 (extra)
Problem:    Dos 32 bit: When setting 'columns' to a too large value, Vim may
	    crash, and the DOS console too.
Solution:   Check that the value of 'columns' isn't larger than the number of
	    columns that the BIOS reports.
Files:	    src/os_msdos.c, src/proto/os_msdos.pro, src/option.c

Patch 5.5.073 (extra)
Problem:    Win 32 GUI: The Find and Find/Replace dialogs didn't show the
	    "match case" checkbox.  The Find/Replace dialog didn't handle the
	    "match whole word" checkbox.
Solution:   Support the "match case" and "match whole word" checkboxes.
Files:	    src/gui_w32.c

Patch 5.6a.001
Problem:    Using <C-End> with a count doesn't work like it does with "G".
	    (Benji Fisher)
Solution:   Accept a count for <C-End> and <C-Home>.
Files:	    src/normal.c

Patch 5.6a.002
Problem:    The script for conversion to HTML was an older version.
Solution:   Add support for running 2html.vim on a color terminal.
Files:	    runtime/syntax/2html.vim

Patch 5.6a.003
Problem:    Defining a function inside a function didn't give an error
	    message.  A missing ":endfunction" doesn't give an error message.
Solution:   Allow defining a function inside a function.
Files:	    src/eval.c, runtime/doc/eval.txt

Patch 5.6a.004
Problem:    A missing ":endwhile" or ":endif" doesn't give an error message.
	    (Johannes Zellner)
Solution:   Check for missing ":endwhile" and ":endif" in sourced files.
	    Add missing ":endif" in file selection macros.
Files:	    src/ex_docmd.c, runtime/macros/file_select.vim

Patch 5.6a.005
Problem:    'hlsearch' was not listed alphabetically.  The value of 'toolbar'
	    was changed when 'compatible' is set.
Solution:   Moved entry of 'hlsearch' in options[] table down.
	    Don't reset 'toolbar' option to the default value when
	    'compatible' is set.
Files:	    src/option.c

Patch 5.6a.006
Problem:    Using a backwards range inside ":if 0" gave an error message.
Solution:   Don't complain about a range when it is not going to be used.
	    (Stefan Roemer)
Files:	    src/ex_docmd.c

Patch 5.6a.007
Problem:    ":let" didn't show internal Vim variables.  (Ron Aaron)
Solution:   Do show ":v" variables for ":let" and ":let v:name".
Files:	    src/eval.c

Patch 5.6a.008
Problem:    Selecting a syntax from the Syntax menu gives an error message.
Solution:   Replace "else if" in SetSyn() with "elseif".  (Ronald Schild)
Files:	    runtime/menu.vim

Patch 5.6a.009
Problem:    When compiling with +extra_search but without +syntax, there is a
	    compilation error in screen.c. (Axel Kielhorn)
Solution:   Adjust the #ifdef for declaring and initializing "line" in
	    win_line().  Also solve compilation problem when +statusline is
	    used without +eval.  Another one when +cmdline_compl is used
	    without +eval.
Files:	    src/screen.c, src/misc2.c

Patch 5.6a.010
Problem:    In a function, ":startinsert!" does not append to the end of the
	    line if a ":normal" command was used to move the cursor. (Fisher)
Solution:   Reset "w_set_curswant" to avoid that w_curswant is changed again.
Files:	    src/ex_docmd.c

Patch 5.6a.011 (depends on 5.6a.004)
Problem:    A missing ":endif" or ":endwhile" in a function doesn't give an
	    error message.
Solution:   Give that error message.
Files:	    src/ex_docmd.c

Patch 5.6a.012 (depends on 5.6a.008)
Problem:    Some Syntax menu entries caused a hit-enter prompt.
Solution:   Call a function to make the command shorter.  Also rename a few
	    functions to avoid name clashes.
Files:	    runtime/menu.vim

Patch 5.6a.013
Problem:    Command line completion works different when another completion
	    was done earlier. (Johannes Zellner)
Solution:   Reset wim_index when starting a new completion.
Files:	    src/ex_getln.c

Patch 5.6a.014
Problem:    Various warning messages when compiling and running lint with
	    different combinations of features.
Solution:   Fix the warning messages.
Files:	    src/eval.c, src/ex_cmds.c, src/ex_docmd.c, src/gui_gtk_x11.c,
	    src/option.c, src/screen.c, src/search.c, src/syntax.c,
	    src/feature.h, src/globals.h

Patch 5.6a.015
Problem:    The vimtutor command doesn't always know the value of $VIMRUNTIME.
Solution:   Let Vim expand $VIMRUNTIME, instead of the shell.
Files:	    src/vimtutor

Patch 5.6a.016 (extra)
Problem:    Mac: Window size is restricted when starting.  Cannot drag the
	    window all over the desktop.
Solution:   Get real screen size instead of assuming 640x400.  Do not use a
	    fixed number for the drag limits. (Axel Kielhorn)
Files:	    src/gui_mac.c

Patch 5.6a.017
Problem:    The "Paste" entry in popup menu for Visual, Insert and Cmdline
	    mode is in the wrong position. (Stol)
Solution:   Add priority numbers for all Paste menu entries.
Files:	    runtime/menu.vim

Patch 5.6a.018
Problem:    GTK GUI: submenu priority doesn't work.
	    Help dialog could be destroyed too soon.
	    When closing a dialog window (e.g. the "ATTENTION" one), Vim would
	    just hang.
	    When GTK theme is changed, Vim doesn't adjust to the new colors.
	    Argument for ":promptfind" isn't used.
Solution:   Fixed the mentioned problems.
	    Made the dialogs look&feel nicer.
	    Moved functions to avoid the need for a forward declaration.
	    Fixed reentrancy of the file browser dialog.
	    Added drag&drop support for GNOME.
	    Init the text for the Find/replace dialog from the last used
	    search string.  Set "match whole word" toggle button correctly.
	    Made repeat rate for drag outside of window depend on the
	    distance from the window.  (Marcin Dalecki)
	    Made the drag in Visual mode actually work.
	    Removed recursiveness protection from gui_mch_get_rgb(), it might
	    cause more trouble than it solves.
Files:	    src/ex_docmd.c, src/gui_gtk.c, src/gui_gtk_x11.c, src/ui.c,
	    src/proto/ui.pro, src/misc2.c

Patch 5.6a.019
Problem:    When trying to recover through NFS, which uses a large block size,
	    Vim might think the swap file is empty, because mf_blocknr_max is
	    zero.  (Scott McDermott)
Solution:   When computing the number of blocks of the file in mf_open(),
	    round up instead of down.
Files:	    src/memfile.c

Patch 5.6a.020
Problem:    GUI GTK: Could not set display for gvim.
Solution:   Add "-display" and "--display" arguments. (Marcin Dalecki)
Files:	    src/gui_gtk_x11.c

Patch 5.6a.021
Problem:    Recovering still may not work when the block size of the device
	    where the swap file is located is larger than 4096.
Solution:   Read block 0 with the minimal block size.
Files:	    src/memline.c, src/memfile.c, src/vim.h

Patch 5.6a.022 (extra)
Problem:    Win32 GUI: When an error in the vimrc causes a dialog to pop up
	    (e.g., for an existing swap file), Vim crashes. (David Elins)
Solution:   Before showing a dialog, open the main window.
Files:	    src/gui_w32.c

Patch 5.6a.023
Problem:    Using expand("%:gs??/?") causes a crash. (Ron Aaron)
Solution:   Check for running into the end of the string in do_string_sub().
Files:	    src/eval.c

Patch 5.6a.024
Problem:    Using an autocommand to delete a buffer when leaving it can cause
	    a crash when jumping to a tag. (Franz Gorkotte)
Solution:   In do_tag(), store tagstacklen before jumping to another buffer.
	    Check tagstackidx after jumping to another buffer.
	    Add extra check in win_split() if tagname isn't NULL.
Files:	    src/tag.c, src/window.c

Patch 5.6a.025 (extra)
Problem:    Win32 GUI: The tables for toupper() and tolower() are initialized
	    too late. (Mike Steed)
Solution:   Move the initialization to win32_init() and call it from main().
Files:	    src/main.c, src/os_w32.c, src/proto/os_w32.pro

Patch 5.6a.026
Problem:    When the SNiFF connection is open, shell commands hang. (Pruemmer)
Solution:   Skip a second wait() call if waitpid() already detected that the
	    child has exited.
Files:	    src/os_unix.c

Patch 5.6a.027 (extra)
Problem:    Win32 GUI: The "Edit with Vim" popup menu entry causes problems
	    for the Office toolbar.
Solution:   Use a shell extension dll. (Tianmiao Hu)
	    Added it to the install and uninstal programs, replaces the old
	    "Edit with Vim" menu registry entries.
Files:	    src/dosinst.c, src/uninstal.c, gvimext/*, runtime/doc/gui_w32.txt

Patch 5.6a.028 (extra)
Problem:    Win32 GUI: Dialogs and tear-off menus can't handle multibyte
	    characters.
Solution:   Adjust nCopyAnsiToWideChar() to handle multibyte characters
	    correctly.
Files:	    src/gui_w32.c

==============================================================================
VERSION 5.7						*version-5.7*

Version 5.7 is a bug-fix version of 5.6.

Changed							*changed-5.7*
-------

Renamed src/INSTALL.mac to INSTALL_mac.txt to avoid it being recognized with a
wrong file type.  Also renamed src/INSTALL.amiga to INSTALL_ami.txt.


Added							*added-5.7*
-----

New syntax files:
stp		Stored Procedures (Jeff Lanzarotta)
snnsnet, snnspat, snnsres	SNNS (Davide Alberani)
mel		MEL (Robert Minsk)
ruby		Ruby (Mirko Nasato)
tli		TealInfo (Kurt W. Andrews)
ora		Oracle config file (Sandor Kopanyi)
abaqus		Abaqus (Carl Osterwisch)
jproperties	Java Properties (Simon Baldwin)
apache		Apache config (Allan Kelly)
csp		CSP (Jan Bredereke)
samba		Samba config (Rafael Garcia-Suarez)
kscript		KDE script (Thomas Capricelli)
hb		Hyper Builder (Alejandro Forero Cuervo)
fortran		Fortran (rewritten) (Ajit J. Thakkar)
sml		SML (Fabrizio Zeno Cornelli)
cvs		CVS commit (Matt Dunford)
aspperl		ASP Perl (Aaron Hope)
bc		BC calculator (Vladimir Scholtz)
latte		Latte (Nick Moffitt)
wml		WML (Gerfried Fuchs)

Included Exuberant ctags 3.5.1. (Darren Hiebert)

"display" and "fold" arguments for syntax items.  For future extension, they
are ignored now.

strftime() function for the Macintosh.

macros/explorer.vim: A file browser script (M A Aziz Ahmed)


Fixed							*fixed-5.7*
-----

The 16 bit MS-DOS version is now compiled with Bcc 3.1 instead of 4.0.  The
executable is smaller.

When a "make test" failed, the output file was lost.  Rename it to
test99.failed to be able to see what went wrong.

After sourcing bugreport.vim, it's not clear that bugreport.txt has been
written in the current directory.  Edit bugreport.txt to avoid that.

Adding IME support when using Makefile.w32 didn't work. (Taro Muraoka)

Win32 console: Mouse drags were passed on even when the mouse didn't move.

Perl interface: In Buffers(), type of argument to SvPV() was int, should be
STRLEN. (Tony Leneis)

Problem with prototype for index() on AIX 4.3.0.  Added check for _AIX43 in
os_unix.h. (Jake Hamby)

Mappings in mswin.vim could break when some commands are mapped.  Add "nore"
to most mappings to avoid re-mapping.

modify_fname() made a copy of a file name for ":p" when it already was a full
path name, which is a bit slow.

Win32 with Borland C++ 5.5: Pass the path to the compiler on to xxd and ctags,
to avoid depending on $PATH.  Fixed "make clean".

Many fixes to Macintosh specific parts: (mostly by Dany StAmant)
- Only one Help menu.
- No more crash when removing a menu item.
- Support as External Editor for Codewarrior (still some little glitches).
- Popup menu support.
- Fixed crash when pasting after application switch.
- Color from rgb.txt properly displayed.
- 'isprint' default includes all chars above '~'. (Axel Kielhorn)
- mac_expandpath() was leaking memory.
- Add digraphs table. (Axel Kielhorn)
- Multi-byte support: (Kenichi Asai)
  Switch keyscript when going in/out of Insert mode.
  Draw multibyte character correctly.
  Don't use mblen() but highest bit of char to detect multibyte char.
  Display value of multibyte in statusline (also for other systems).
- mouse button was not initialized properly to MOUSE_LEFT when
  USE_CTRLCLICKMENU not defined.
- With Japanese SJIS characters: Make "w", "b", and "e" work
  properly.  (Kenichi Asai)
- Replaced old CodeWarrior file os_mac.CW9.hqx with os_mac.cw5.sit.hqx.

Fixes for VMS: (Zoltan Arpadffy) (also see patch 5.6.045 below)
- Added Makefile_vms.mms and vimrc.vms to src/testdir to be able to run the
  tests.
- Various fixes.
- Set 'undolevels' to 1000 by default.
- Made mch_settitle() equivalent to the one in os_unix.c.

RiscOS: A few prototypes for os_riscos.c were outdated.  Generate prototypes
automatically.


Previously released patches:

Patch 5.6.001
Problem:    When using "set bs=0 si cin", Inserting "#<BS>" or "}<BS>" which
	    reduces the indent doesn't delete the "#" or "}". (Lorton)
Solution:   Adjust ai_col in ins_try_si().
Files:	    src/edit.c

Patch 5.6.002
Problem:    When using the vim.vim syntax file, a comment with all uppercase
	    characters causes a hang.
Solution:   Adjust pattern for vimCommentTitle (Charles Campbell)
Files:	    runtime/syntax/vim.vim

Patch 5.6.003
Problem:    GTK GUI: Loading a user defined toolbar bitmap gives a warning
	    about the colormap.  Probably because the window has not been
	    opened yet.
Solution:   Use gdk_pixmap_colormap_create_from_xpm() to convert the xpm file.
	    (Keith Radebaugh)
Files:	    src/gui_gtk.c

Patch 5.6.004 (extra)
Problem:    Win32 GUI with IME: When setting 'guifont' to "*", the font
	    requester appears twice.
Solution:   In gui_mch_init_font() don't call get_logfont() but copy
	    norm_logfont from fh. (Yasuhiro Matsumoto)
Files:	    src/gui_w32.c

Patch 5.6.005
Problem:    When 'winminheight' is zero, CTRL-W - with a big number causes a
	    crash.  (David Kotchan)
Solution:   Check for negative window height in win_setheight().
Files:	    src/window.c

Patch 5.6.006
Problem:    GTK GUI: Bold font cannot always be used.  Memory is freed too
	    early in gui_mch_init_font().
Solution:   Move call to g_free() to after where sdup is used. (Artem Hodyush)
Files:	    src/gui_gtk_x11.c

Patch 5.6.007 (extra)
Problem:    Win32 IME: Font is not changed when screen font is changed. And
	    IME composition window does not trace the cursor.
Solution:   Initialize IME font.  When cursor is moved, set IME composition
	    window with ImeSetCompositionWindow().  Add call to
	    ImmReleaseContext() in several places. (Taro Muraoka)
Files:	    src/gui.c, src/gui_w32.c, src/proto/gui_w32.pro

Patch 5.6.008 (extra)
Problem:    Win32: When two files exist with the same name but different case
	    (through NFS or Samba), fixing the file name case could cause the
	    wrong one to be edited.
Solution:   Prefer a perfect match above a match while ignoring case in
	    fname_case().  (Flemming Madsen)
Files:	    src/os_win32.c

Patch 5.6.009 (extra)
Problem:    Win32 GUI: Garbage in Windows Explorer help line when selecting
	    "Edit with Vim" popup menu entry.
Solution:   Only return the help line when called with the GCS_HELPTEXT flag.
	    (Tianmiao Hu)
Files:	    GvimExt/gvimext.cpp

Patch 5.6.010
Problem:    A file name which contains a TAB was not read correctly from the
	    viminfo file and the ":ls" listing was not aligned properly.
Solution:   Parse the buffer list lines in the viminfo file from the end
	    backwards.  Count a Tab for two characters to align the ":ls" list.
Files:	    src/buffer.c

Patch 5.6.011
Problem:    When 'columns' is huge (using a tiny font) and 'statusline' is
	    used, Vim can crash.
Solution:   Limit maxlen to MAXPATHL in win_redr_custom(). (John Mullin)
Files:	    src/screen.c

Patch 5.6.012
Problem:    When using "zsh" for /bin/sh, toolcheck may hang until "exit" is
	    typed. (Kuratczyk)
Solution:   Add "-c exit" when checking for the shell version.
Files:	    src/toolcheck

Patch 5.6.013
Problem:    Multibyte char in tooltip is broken.
Solution:   Consider multibyte char in replace_termcodes(). (Taro Muraoka)
Files:      src/term.c

Patch 5.6.014
Problem:    When cursor is at the end of line and the character under cursor
	    is a multibyte character, "yl" doesn't yank 1 multibyte-char.
	    (Takuhiro Nishioka)
Solution:   Recognize a multibyte-char at end-of-line correctly in oneright().
	    (Taro Muraoka)
	    Also: make "+quickfix" in ":version" output appear alphabetically.
Files:	    src/edit.c

Patch 5.6.015
Problem:    New xterm delete key sends <Esc>[3~ by default.
Solution:   Added <kDel> and <kIns> to make the set of keypad keys complete.
Files:	    src/edit.c, src/ex_getln.c, src/keymap.h, src/misc1.c,
	    src/misc2.c, src/normal.c, src/os_unix.c, src/term.c

Patch 5.6.016
Problem:    When deleting a search string from history from inside a mapping,
	    another entry is deleted too. (Benji Fisher)
Solution:   Reset last_maptick when deleting the last entry of the search
	    history.  Also: Increment maptick when starting a mapping from
	    typed characters to avoid a just added search string being
	    overwritten or removed from history.
Files:	    src/ex_getln.c, src/getchar.c

Patch 5.6.017
Problem:    ":s/e/\^M/" should replace an "e" with a CTRL-M, not split the
	    line. (Calder)
Solution:   Replace the backslash with a CTRL-V internally. (Stephen P. Wall)
Files:	    src/ex_cmds.c

Patch 5.6.018
Problem:    ":help [:digit:]" takes a long time to jump to the wrong place.
Solution:   Insert a backslash to avoid the special meaning of '[]'.
Files:	    src/ex_cmds.c

Patch 5.6.019
Problem:    "snd.c", "snd.java", etc. were recognized as "mail" filetype.
Solution:   Make pattern for mail filetype more strict.
Files:	    runtime/filetype.vim

Patch 5.6.020 (extra)
Problem:    The DJGPP version eats processor time (Walter Briscoe).
Solution:   Call __dpmi_yield() in the busy-wait loop.
Files:	    src/os_msdos.c

Patch 5.6.021
Problem:    When 'selection' is "exclusive", a double mouse click in Insert
	    mode doesn't select last char in line. (Lutz)
Solution:   Allow leaving the cursor on the NUL past the line in this case.
Files:	    src/edit.c

Patch 5.6.022
Problem:    ":e \~<Tab>" expands to ":e ~\$ceelen", which doesn't work.
Solution:   Re-insert the backslash before the '~'.
Files:	    src/ex_getln.c

Patch 5.6.023 (extra)
Problem:    Various warnings for the Ming compiler.
Solution:   Changes to avoid the warnings. (Bill McCarthy)
Files:	    src/ex_cmds.c, src/gui_w32.c, src/os_w32exe.c, src/os_win32.c,
	    src/syntax.c, src/vim.rc

Patch 5.6.024 (extra)
Problem:    Win32 console: Entering CTRL-_ requires the shift key. (Kotchan)
Solution:   Specifically catch keycode 0xBD, like the GUI.
Files:	    src/os_win32.c

Patch 5.6.025
Problem:    GTK GUI: Starting the GUI could be interrupted by a SIGWINCH.
	    (Nils Lohner)
Solution:   Repeat the read() call to get the gui_in_use value when
	    interrupted by a signal.
Files:	    src/gui.c

Patch 5.6.026 (extra)
Problem:    Win32 GUI: Toolbar bitmaps are searched for in
	    $VIMRUNTIME/bitmaps, while GTK looks in $VIM/bitmaps. (Keith
	    Radebaugh)
Solution:   Use $VIM/bitmaps for both, because these are not part of the
	    distribution but defined by the user.
Files:	    src/gui_w32.c, runtime/doc/gui.txt

Patch 5.6.027
Problem:    TCL: Crash when using a Tcl script (reported for Win32).
Solution:   Call Tcl_FindExecutable() in main(). (Brent Fulgham)
Files:	    src/main.c

Patch 5.6.028
Problem:    Xterm patch level 126 sends codes for mouse scroll wheel.
	    Fully works with xterm patch level 131.
Solution:   Recognize the codes for button 4 (0x60) and button 5 (0x61).
Files:	    src/term.c

Patch 5.6.029
Problem:    GTK GUI: Shortcut keys cannot be used for a dialog. (Johannes
	    Zellner)
Solution:   Add support for shortcut keys. (Marcin Dalecki)
Files:	    src/gui_gtk.c

Patch 5.6.030
Problem:    When closing a window and 'ea' is set, Vim can crash. (Yasuhiro
	    Matsumoto)
Solution:   Set "curbuf" to a valid value in win_close().
Files:	    src/window.c

Patch 5.6.031
Problem:    Multi-byte: When a double-byte character ends in CSI, Vim waits
	    for another character to be typed.
Solution:   Recognize the CSI as the second byte of a character and don't wait
	    for another one. (Yasuhiro Matsumoto)
Files:	    src/getchar.c

Patch 5.6.032
Problem:    Functions with an argument that is a line number don't all accept
	    ".", "$", etc. (Ralf Arens)
Solution:   Add get_art_lnum() and use it for setline(), line2byte() and
	    synID().
Files:	    src/eval.c

Patch 5.6.033
Problem:    Multi-byte: "f " sometimes skips to the second space.  (Sung-Hyun
	    Nam)
Solution:   Change logic in searchc() to skip trailing byte of a double-byte
	    character.
	    Also: Ask for second byte when searching for double-byte
	    character. (Park Chong-Dae)
Files:	    src/search.c

Patch 5.6.034 (extra)
Problem:    Compiling with Borland C++ 5.5 fails on tolower() and toupper().
Solution:   Use TO_LOWER() and TO_UPPER() instead.  Also adjust the Makefile
	    to make using bcc 5.5 easier.
Files:	    src/edit.c, src/ex_docmd.c, src/misc1.c, src/Makefile.bor

Patch 5.6.035
Problem:    Listing the"+comments" feature in the ":version" output depended
	    on the wrong ID. (Stephen P. Wall)
Solution:   Change "CRYPTV" to "COMMENTS".
Files:	    src/version.c

Patch 5.6.036
Problem:    GTK GUI: Copy/paste text doesn't work between gvim and Eterm.
Solution:   Support TEXT and COMPOUND_TEXT selection targets. (ChiDeok Hwang)
Files:	    src/gui_gtk_x11.c

Patch 5.6.037
Problem:    Multi-byte: Can't use "f" command with multibyte character in GUI.
Solution:   Enable XIM in Normal mode for the GUI. (Sung-Hyun Nam)
Files:	    src/gui_gtk_x11.c, src/multbyte.c

Patch 5.6.038
Problem:    Multi-clicks in GUI are interpreted as a mouse wheel click.  When
	    'ttymouse' is "xterm" a mouse click is interpreted as a mouse
	    wheel click.
Solution:   Don't recognize the mouse wheel in check_termcode() in the GUI.
	    Use 0x43 for a mouse drag in do_xterm_trace(), not 0x63.
Files:	    src/term.c, src/os_unix.c

Patch 5.6.039
Problem:    Motif GUI under KDE: When trying to logout, Vim hangs up the
	    system. (Hermann Rochholz)
Solution:   When handling the WM_SAVE_YOURSELF event, set the WM_COMMAND
	    property of the window to let the session manager know we finished
	    saving ourselves.
Files:	    src/gui_x11.c

Patch 5.6.040
Problem:    When using ":s" command, matching the regexp is done twice.
Solution:   After copying the matched line, adjust the pointers instead of
	    finding the match again. (Loic Grenie)  Added vim_regnewptr().
Files:	    src/ex_cmds.c, src/regexp.c, src/proto/regexp.pro

Patch 5.6.041
Problem:    GUI: Athena, Motif and GTK don't give more than 10 dialog buttons.
Solution:   Remove the limit on the number of buttons.
	    Also support the 'v' flag in 'guioptions'.
	    For GTK: Center the buttons.
Files:	    src/gui_athena.c, src/gui_gtk.c, src/gui_motif.c

Patch 5.6.042
Problem:    When doing "vim -u vimrc" and vimrc contains ":q", the cursor in
	    the terminal can remain off.
Solution:   Call cursor_on() in mch_windexit().
Files:	    src/os_unix.c

Patch 5.6.043 (extra)
Problem:    Win32 GUI: When selecting guifont with the dialog, 'guifont'
	    doesn't include the bold or italic attributes.
Solution:   Append ":i" and/or ":b" to 'guifont' in gui_mch_init_font().
Files:	    src/gui_w32.c

Patch 5.6.044 (extra)
Problem:    MS-DOS and Windows: The line that dosinst.exe appends to
	    autoexec.bat to set PATH is wrong when Vim is in a directory with
	    an embedded space.
Solution:   Use double quotes for the value when there is an embedded space.
Files:	    src/dosinst.c

Patch 5.6.045 (extra) (fixed version)
Problem:    VMS: Various small problems.
Solution:   Many small changes. (Zoltan Arpadffy)
	    File name modifier ":h" keeps the path separator.
	    File name modifier ":e" also removes version.
	    Compile with MAX_FEAT by default.
	    When checking for autocommands ignore version in file name.
	    Be aware of file names being case insensitive.
	    Added vt320 builtin termcap.
	    Be prepared for an empty default_vim_dir.
Files:	    runtime/gvimrc_example.vim, runtime/vimrc_example.vim,
	    runtime/doc/os_vms.txt, src/eval.c, src/feature.h, src/fileio.c,
	    src/gui_motif.c, src/gui_vms_conf.h, src/main.c, src/memline.c,
	    src/misc1.c, src/option.c, src/os_vms_conf.h, src/os_vms.c,
	    src/os_vms.h, src/os_vms.mms, src/tag.c, src/term.c, src/version.c

Patch 5.6.046
Problem:    Systems with backslash in file name: With 'shellslash' set, "vim
	    */*.c" only uses a slash for the first file name.  (Har'El)
Solution:   Fix slashes in file name arguments after reading the vimrc file.
Files:	    src/option.c

Patch 5.6.047
Problem:    $CPPFLAGS is not passed on to ctags configure.
Solution:   Add it. (Walter Briscoe)
Files:	    src/config.mk.in, src/Makefile

Patch 5.6.048
Problem:    CTRL-R in Command-line mode is documented to insert text as typed,
	    but inserts text literally.
Solution:   Make CTRL-R insert text as typed, use CTRL-R CTRL-R to insert
	    literally.  This is consistent with Insert mode.  But characters
	    that end Command-line mode are inserted literally.
Files:	    runtime/doc/index.txt, runtime/doc/cmdline.txt, src/ex_getln.c,
	    src/ops.c, src/proto/ops.pro

Patch 5.6.049
Problem:    Documentation for [!] after ":ijump" is wrong way around. (Benji
	    Fisher)
Solution:   Fix the documentation.  Also improve the code to check for a match
	    after a /* */ comment.
Files:	    runtime/doc/tagsearch.txt, src/search.c

Patch 5.6.050
Problem:    Replacing is wrong when replacing a single-byte char with
	    double-byte char or the other way around.
Solution:   Shift the text after the character when it is replaced.
	    (Yasuhiro Matsumoto)
Files:	    src/normal.c, src/misc1.c

Patch 5.6.051
Problem:    ":tprev" and ":tnext" don't give an error message when trying to
	    go before the first or beyond the last tag. (Robert Webb)
Solution:   Added error messages.  Also: Delay a second when a file-read
	    message is going to overwrite an error message, otherwise it won't
	    be seen.
Files:	    src/fileio.c, src/tag.c

Patch 5.6.052
Problem:    Multi-byte: When an Ex command has a '|' or '"' as a second byte,
	    it terminates the command.
Solution:   Skip second byte of multibyte char when checking for '|' and '"'.
	    (Asai Kenichi)
Files:	    src/ex_docmd.c

Patch 5.6.053
Problem:    CTRL-] doesn't work on a tag that contains a '|'. (Cesar Crusius)
Solution:   Escape '|', '"' and '\' in tag names when using CTRL-] and also
	    for command-line completion.
Files:	    src/ex_getln.c, src/normal.c

Patch 5.6.054
Problem:    When using ":e" and ":e #" the cursor is put in the first column
	    when 'startofline' is set. (Cordell)
Solution:   Use the last known column when 'startofline' is set.
	    Also, use ECMD_LAST more often to simplify the code.
Files:	    src/buffer.c, src/ex_cmds.c, src/ex_docmd.c, src/proto/buffer.pro

Patch 5.6.055
Problem:    When 'statusline' only contains a text without "%" and doesn't fit
	    in the window, Vim crashes. (Ron Aaron)
Solution:   Don't use the pointer for the first item if there is no item.
Files:	    src/screen.c

Patch 5.6.056 (extra)
Problem:    MS-DOS: F11 and F12 don't work when 'bioskey' is set.
Solution:   Use enhanced keyboard functions. (Vince Negri)
	    Detect presence of enhanced keyboard and set bioskey_read and
	    bioskey_ready.
Files:	    src/os_msdos.c

Patch 5.6.057 (extra)
Problem:    Win32 GUI: Multi-byte characters are wrong in dialogs and tear-off
	    menus.
Solution:   Use system font instead of a fixed font. (Matsumoto, Muraoka)
Files:	    src/gui_w32.c

Patch 5.6.058
Problem:    When the 'a' flag is not in 'guioptions', non-Windows systems
	    copy Visually selected text to the clipboard/selection on a yank
	    or delete command anyway.  On Windows it isn't done even when the
	    'a' flag is included.
Solution:   Respect the 'a' flag in 'guioptions' on all systems.
Files:	    src/normal.c

Patch 5.6.059 (extra)
Problem:    When moving the cursor over italic text and the characters spill
	    over to the cell on the right, that spill-over is deleted.
	    Noticed in the Win32 GUI, can happen on other systems too.
Solution:   Redraw italic text starting from a blank, like this is already
	    done for bold text. (Vince Negri)
Files:	    src/gui.c, src/gui.h, src/gui_w32.c

Patch 5.6.060
Problem:    Some bold characters spill over to the cell on the left, that
	    spill-over can remain sometimes.
Solution:   Redraw a character when the next character was bold and needs
	    redrawing. (Robert Webb)
Files:	    src/screen.c

Patch 5.6.061
Problem:    When xterm sends 8-bit controls, recognizing the version response
	    doesn't work.
	    When using CSI instead of <Esc>[ for the termcap color codes,
	    using 16 colors doesn't work. (Neil Bird)
Solution:   Also accept CSI in place of <Esc>[ for the version string.
	    Also check for CSI when handling colors 8-15 in term_color().
	    Use CSI for builtin xterm termcap entries when 'term' contains
	    "8bit".
Files:	    runtime/doc/term.txt, src/ex_cmds.c, src/option.c, src/term.c,
	    src/os_unix.c, src/proto/option.pro, src/proto/term.pro

Patch 5.6.062
Problem:    The documentation says that setting 'smartindent' doesn't have an
	    effect when 'cindent' is set, but it does make a difference for
	    lines starting with "#". (Neil Bird)
Solution:   Really ignore 'smartindent' when 'cindent' is set.
Files:	    src/misc1.c, src/ops.c

Patch 5.6.063
Problem:    Using "I" in Visual-block mode doesn't accept a count. (Johannes
	    Zellner)
Solution:   Pass the count on to do_insert() and edit(). (Allan Kelly)
Files:	    src/normal.c, src/ops.c, src/proto/ops.pro

Patch 5.6.064
Problem:    MS-DOS and Win32 console: Mouse doesn't work correctly after
	    including patch 5.6.28. (Vince Negri)
Solution:   Don't check for mouse scroll wheel when the mouse code contains
	    the number of clicks.
Files:	    src/term.c

Patch 5.6.065
Problem:    After moving the cursor around in Insert mode, typing a space can
	    still trigger an abbreviation. (Benji Fisher)
Solution:   Don't check for an abbreviation after moving around in Insert mode.
Files:	    src/edit.c

Patch 5.6.066
Problem:    Still a few bold character spill-over remains after patch 60.
Solution:   Clear character just in front of blanking out rest of the line.
	    (Robert Webb)
Files:	    src/screen.c

Patch 5.6.067
Problem:    When a file name contains a NL, the viminfo file is corrupted.
Solution:   Use viminfo_writestring() to convert the NL to CTRL-V n.
	    Also fix the Buffers menu and listing a menu name with a newline.
Files:	    runtime/menu.vim, src/buffer.c, src/mark.c, src/menu.c

Patch 5.6.068
Problem:    Compiling the Perl interface doesn't work with Perl 5.6.0.
	    (Bernhard Rosenkraenzer)
Solution:   Also check xs_apiversion for the version number when prepending
	    defines for PL_*.
Files:	    src/Makefile

Patch 5.6.069
Problem:    "go" doesn't always end up at the right character when
	    'fileformat' is "dos". (Bruce DeVisser)
Solution:   Correct computations in ml_find_line_or_offset().
Files:	    src/memline.

Patch 5.6.070 (depends on 5.6.068)
Problem:    Compiling the Perl interface doesn't work with Perl 5.6.0.
	    (Bernhard Rosenkraenzer)
Solution:   Simpler check instead of the one from patch 68.
Files:	    src/Makefile

Patch 5.6.071
Problem:    "A" in Visual block mode on a Tab positions the cursor one char to
	    the right. (Michael Haumann)
Solution:   Correct the column computation in op_insert().
Files:	    src/ops.c

Patch 5.6.072
Problem:    When starting Vim with "vim +startinsert", it enters Insert mode
	    only after typing the first command. (Andrew Pimlott)
Solution:   Insert a dummy command in the stuff buffer.
Files:	    src/main.c

Patch 5.6.073 (extra) (depends on 5.6.034)
Problem:    Win32 GUI: When compiled with Bcc 5.5 menus don't work.
	    In dosinst.c toupper() and tolower() give an "internal compiler
	    error" for Bcc 5.5.
Solution:   Define WINVER to 4 to avoid compiling for Windows 2000. (Dan
	    Sharp)  Also cleaned up compilation arguments.
	    Use our own implementation of toupper() in dosinst.c.  Use
	    mytoupper() instead of tolower().
Files:	    src/Makefile.bor, src/dosinst.c

Patch 5.6.074 (extra)
Problem:    Entering CSI directly doesn't always work, because it's recognized
	    as the start of a special key.  Mostly a problem with multibyte
	    in the GUI.
Solution:   Use K_CSI for a typed CSI character.  Use <CSI> for a normal CSI,
	    <xCSI> for a CSI typed in the GUI.
Files:	    runtime/doc/intro.txt, src/getchar.c, src/gui_amiga.c,
	    src/gui_gtk_x11.c, src/gui_mac.c, src/gui_riscos.c, src/gui_w32.c,
	    src/keymap.h, src/misc2.c

Patch 5.6.075
Problem:    When using "I" or "A" in Visual block mode while 'sts' is set may
	    change spaces to a Tab the inserted text is not correct. (Mike
	    Steed)  And some other problems when using "A" to append after the
	    end of the line.
Solution:   Check for change in spaces/tabs after inserting the text.  Append
	    spaces to fill the gap between the end-of-line and the right edge
	    of the block.
Files:	    src/ops.c

Patch 5.6.076
Problem:    GTK GUI: Mapping <M-Space> doesn't work.
Solution:   Don't use the "Alt" modifier twice in key_press_event().
Files:	    src/gui_gtk_x11.c

Patch 5.6.077
Problem:    GUI: When interrupting an external program with CTRL-C, gvim might
	    crash. (Benjamin Korvemaker)
Solution:   Avoid using a NULL pointer in ui_inchar_undo().
Files:	    src/ui.c

Patch 5.6.078
Problem:    Locale doesn't always work on FreeBSD. (David O'Brien)
Solution:   Link with the "xpg4" library when available.
Files:	    src/configure.in, src/configure

Patch 5.6.079
Problem:    Vim could crash when several Tcl interpreters are created and
	    destroyed.
Solution:   handle the "exit" command and nested ":tcl" commands better. (Ingo
	    Wilken)
Files:	    runtime/doc/if_tcl.txt, src/if_tcl.c

Patch 5.6.080
Problem:    When jumping to a tag, generating the tags file and jumping to the
	    same tag again uses the old search pattern. (Sung-Hyun Nam)
Solution:   Flush cached tag matches when executing an external command.
Files:	    src/misc2.c, src/proto/tag.pro, src/tag.c

Patch 5.6.081
Problem:    ":syn include" uses a level for the included file, this confuses
	    contained items included at the same level.
Solution:   Use a unique tag for each included file.  Changed sp_syn_inc_lvl
	    to sp_syn_inc_tag. (Scott Bigham)
Files:	    src/syntax.c, src/structs.h

Patch 5.6.082
Problem:    When using cscope, Vim can crash.
Solution:   Initialize tag_fname in find_tags(). (Anton Blanchard)
Files:	    src/tag.c

Patch 5.6.083 (extra)
Problem:    Win32: The visual beep can't be seen. (Eric Roesinger)
Solution:   Flush the output before waiting with GdiFlush(). (Maurice S. Barnum)
	    Also: Allow specifying the delay in t_vb for the GUI.
Files:	    src/gui.c, src/gui_amiga.c, src/gui_gtk_x11.c, src/gui_mac.c,
	    src/gui_riscos.c, src/gui_w32.c, src/gui_x11.c, src/gui_beos.cc,
	    src/proto/gui_amiga.pro, src/proto/gui_gtk_x11.pro,
	    src/proto/gui_mac.pro, src/proto/gui_riscos.pro,
	    src/proto/gui_w32.pro, src/proto/gui_x11.pro,
	    src/proto/gui_beos.pro

Patch 5.6.084 (depends on 5.6.074)
Problem:    GUI: Entering CSI doesn't always work for Athena and Motif.
Solution:   Handle typed CSI as <xCSI> (forgot this bit in 5.6.074).
Files:	    src/gui_x11.c

Patch 5.6.085
Problem:    Multi-byte: Using "r" to replace a double-byte char with a
	    single-byte char moved the cursor one character. (Matsumoto)
	    Also, using a count when replacing a single-byte char with a
	    double-byte char didn't work.
Solution:   Don't use del_char() to delete the second byte.
	    Get "ptr" again after calling ins_char().
Files:	    src/normal.c

Patch 5.6.086 (extra)
Problem:    Win32: When using libcall() and the returned value is not a valid
	    pointer, Vim crashes.
Solution:   Use IsBadStringPtr() to check if the pointer is valid.
Files:	    src/os_win32.c

Patch 5.6.087
Problem:    Multi-byte: Commands and messages with multibyte characters are
	    displayed wrong.
Solution:   Detect double-byte characters. (Yasuhiro Matsumoto)
Files:	    src/ex_getln.c, src/message.c, src/misc2.c, src/screen.c

Patch 5.6.088
Problem:    Multi-byte with Motif or Athena: The message "XIM requires
	    fontset" is annoying when Vim was compiled with XIM support but it
	    is not being used.
Solution:   Remove that message.
Files:	    src/multbyte.c

Patch 5.6.089
Problem:    On non-Unix systems it's possible to overwrite a read-only file
	    without using "!".
Solution:   Check if the file permissions allow overwriting before moving the
	    file to become the backup file.
Files:	    src/fileio.c

Patch 5.6.090
Problem:    When editing a file in "/home/<USER>/home/<USER>" this was replaced with
	    "~~".  (Andreas Jellinghaus)
Solution:   Replace the home directory only once in home_replace().
Files:	    src/misc1.c

Patch 5.6.091
Problem:    When editing many "no file" files, can't create swap file, because
	    .sw[a-p] have all been used.  (Neil Bird)
Solution:   Also use ".sv[a-z]", ".su[a-z]", etc.
Files:	    src/memline.c

Patch 5.6.092
Problem:    FreeBSD: When setting $TERM to a non-valid terminal name, Vim
	    hangs in tputs().
Solution:   After tgetent() returns an error code, call it again with the
	    terminal name "dumb".  This apparently creates an environment in
	    which tputs() doesn't fail.
Files:	    src/term.c

Patch 5.6.093 (extra)
Problem:    Win32 GUI: "ls | gvim -" will show a message box about reading
	    stdin when Vim exits. (Donohue)
Solution:   Don't write a message about the file read from stdin until the GUI
	    has started.
Files:	    src/fileio.c

Patch 5.6.094
Problem:    Problem with multibyte string for ":echo var".
Solution:   Check for length in msg_outtrans_len_attr(). (Sung-Hyun Nam)
	    Also make do_echo() aware of multibyte characters.
Files:	    src/eval.c, src/message.c

Patch 5.6.095
Problem:    With an Emacs TAGS file that include another a relative path
	    doesn't always work.
Solution:   Use expand_tag_fname() on the name of the included file.
	    (Utz-Uwe Haus)
Files:	    src/tag.c

Patch 5.6.096
Problem:    Unix: When editing many files, startup can be slow. (Paul
	    Ackersviller)
Solution:   Halve the number of stat() calls used to add a file to the buffer
	    list.
Files:	    src/buffer.c

Patch 5.7a.001
Problem:    GTK doesn't respond on drag&drop from ROX-Filer.
Solution:   Add "text/uri-list" target. (Thomas Leonard)
	    Also: fix problem with checking for trash arguments.
Files:	    src/gui_gtk_x11.c

Patch 5.7a.002
Problem:    Multi-byte: 'showmatch' is performed when second byte of an
	    inserted double-byte char is a paren or brace.
Solution:   Check IsTrailByte() before calling showmatch(). (Taro Muraoka)
Files:	    src/misc1.c

Patch 5.7a.003
Problem:    Multi-byte: After using CTRL-O in Insert mode with the cursor at
	    the end of the line on a multibyte character the cursor moves to
	    the left.
Solution:   Check for multibyte character at end-of-line. (Taro Muraoka)
	    Also: fix cls() to detect a double-byte character. (Chong-Dae Park)
Files:	    src/edit.c, src/search.c

Patch 5.7a.004
Problem:    When reporting the search pattern offset, the string could be
	    unterminated, which may cause a crash.
Solution:   Terminate the string for the search offset. (Stephen P. Wall)
Files:	    src/search.c

Patch 5.7a.005
Problem:    When ":s//~/" doesn't find a match it reports "[NULL]" for the
	    pattern.
Solution:   Use get_search_pat() to obtain the actually used pattern.
Files:	    src/ex_cmds.c, src/proto/search.pro, src/search.c

Patch 5.7a.006 (extra)
Problem:    VMS: Various problems, also with the VAXC compiler.
Solution:   In many places use the Unix code for VMS too.
	    Added time, date and compiler version to version message.
	    (Zoltan Arpadffy)
Files:	    src/ex_cmds.c, src/ex_docmd.c, src/globals.h, src/gui_vms_conf.h,
	    src/main.c, src/message.c, src/misc1.c, src/os_vms.c,
	    src/os_vms.h, src/os_vms.mms, src/os_vms_conf.h,
	    src/proto/os_vms.pro, src/proto/version.pro, src/term.c,
	    src/version.c, src/xxd/os_vms.mms, src/xxd/xxd.c

Patch 5.7a.007
Problem:    Motif and Athena GUI: CTRL-@ is interpreted as CTRL-C.
Solution:   Only use "intr_char" when it has been set.
Files:	    src/gui_x11.c

Patch 5.7a.008
Problem:    GTK GUI: When using CTRL-L the screen is redrawn twice, causing
	    trouble for bold characters.  Also happens when moving with the
	    scrollbar.  Best seen when 'writedelay' is non-zero.
	    When starting the GUI with ":gui" the screen is redrawn once with
	    the wrong colors.
Solution:   Only set the geometry hints when the window size really changed.
	    This avoids setting it each time the scrollbar is forcefully
	    redrawn.
	    Don't redraw in expose_event() when gui.starting is still set.
Files:	    src/gui_gtk_x11.c


==============================================================================
VERSION 5.8						*version-5.8*

Version 5.8 is a bug-fix version of 5.7.


Changed							*changed-5.8*
-------

Ctags is no longer included with Vim.  It has grown into a project of its own.
You can find it here:  http://ctags.sf.net.  It is highly recommended as a Vim
companion when you are writing programs.


Added							*added-5.8*
-----

New syntax files:
acedb		AceDB (Stewart Morris)
aflex		Aflex (Mathieu Clabaut)
antlr		Antlr (Mathieu Clabaut)
asm68k		68000 Assembly (Steve Wall)
automake	Automake (John Williams)
ayacc		Ayacc (Mathieu Clabaut)
b		B (Mathieu Clabaut)
bindzone	BIND zone (glory hump)
blank		Blank (Rafal Sulejman)
cfg		Configure files (Igor Prischepoff)
changelog	ChangeLog (Gediminas Paulauskas)
cl		Clever (Phil Uren)
crontab		Crontab (John Hoelzel)
csc		Essbase script (Raul Segura Acevedo)
cynlib		Cynlib(C++) (Phil Derrick)
cynpp		Cyn++ (Phil Derrick)
debchangelog	Debian Changelog (Wichert Akkerman)
debcontrol	Debian Control (Wichert Akkerman)
dns		DNS zone file (Jehsom)
dtml		Zope's DTML (Jean Jordaan)
dylan		Dylan, Dylan-intr and Dylan-lid (Brent Fulgham)
ecd		Embedix Component Description (John Beppu)
fgl		Informix 4GL (Rafal Sulejman)
foxpro		FoxPro (Powing Tse)
gsp		GNU Server Pages (Nathaniel Harward)
gtkrc		GTK rc (David Necas)
hercules	Hercules (Avant! Corporation) (Dana Edwards)
htmlos		HTML/OS by Aestiva (Jason Rust)
inittab		SysV process control (David Necas)
iss		Inno Setup (Dominique Stephan)
jam		Jam (Ralf Lemke)
jess		Jess (Paul Baleme)
lprolog		LambdaProlog (Markus Mottl)
ia64		Intel Itanium (parth malwankar)
kix		Kixtart (Nigel Gibbs)
mgp		MaGic Point (Gerfried Fuchs)
mason		Mason (HTML with Perl) (Andrew Smith)
mma		Mathematica (Wolfgang Waltenberger)
nqc		Not Quite C (Stefan Scherer)
omnimark	Omnimark (Paul Terray)
openroad	OpenROAD (Luis Moreno Serrano)
named		BIND configuration (glory hump)
papp		PApp (Marc Lehmann)
pfmain		Postfix main config (Peter Kelemen)
pic		PIC assembly (Aleksandar Veselinovic)
ppwiz		PPWizard (Stefan Schwarzer)
progress	Progress (Phil Uren)
psf		Product Specification File (Rex Barzee)
r		R (Tom Payne)
registry	MS-Windows registry (Dominique Stephan)
robots		Robots.txt (Dominique Stephan)
rtf		Rich Text Format (Dominique Stephan)
setl		SETL (Alex Poylisher)
sgmldecl	SGML Declarations (Daniel A. Molina W.)
sinda		Sinda input (Adrian Nagle)
sindacmp	Sinda compare (Adrian Nagle)
sindaout	Sinda output (Adrian Nagle)
smith		SMITH (Rafal Sulejman)
snobol4		Snobol 4 (Rafal Sulejman)
strace		Strace (David Necas)
tak		TAK input (Adrian Nagle)
takcmp		TAK compare (Adrian Nagle)
takout		TAK output (Adrian Nagle)
tasm		Turbo assembly (FooLman)
texmf		TeX configuration (David Necas)
trasys		Trasys input (Adrian Nagle)
tssgm		TSS Geometry (Adrian Nagle)
tssop		TSS Optics (Adrian Nagle)
tsscl		TSS Command line (Adrian Nagle)
virata		Virata Configuration Script (Manuel M.H. Stol)
vsejcl		VSE JCL (David Ondrejko)
wdiff		Wordwise diff (Gerfried Fuchs)
wsh		Windows Scripting Host (Paul Moore)
xkb		X Keyboard Extension (David Necas)

Renamed php3 to php, it now also supports php4 (Lutz Eymers)

Patch 5.7.015
Problem:    Syntax files for Vim 6.0 can't be used with 5.x.
Solution:   Add the "default" argument to the ":highlight" command: Ignore the
	    command if highlighting was already specified.
Files:	    src/syntax.c

Generate the Syntax menu with makemenu.vim, so that it doesn't have to be done
when Vim is starting up.  Reduces the startup time of the GUI.


Fixed							*fixed-5.8*
-----

Conversion of docs to HTML didn't convert "|tag|s" to a hyperlink.

Fixed compiling under NeXT. (Jeroen C.M. Goudswaard)

optwin.vim gave an error when used in Vi compatible mode ('cpo' contains 'C').

Tcl interpreter: "buffer" command didn't check for presence of an argument.
(Dave Bodenstab)

dosinst.c: Added checks for too long file name.

Amiga: a file name starting with a colon was considered absolute but it isn't.
Amiga: ":pwd" added a slash when in the root of a drive.

Macintosh: Warnings for unused variables. (Bernhard Pruemmer)

Unix: When catching a deadly signal, handle it in such a way that it's
unlikely that Vim will hang.  Call _exit() instead of exit() in case of a
severe problem.

Setting the window title from nothing to something didn't work after patch 29.

Check for ownership of .exrc and .vimrc was done with stat().  Use lstat() as
well for extra security.

Win32 GUI: Printing a file with 'fileformat' "unix" didn't work.  Set
'fileformat' to "dos" before writing the temp file.

Unix: Could start waiting for a character when checking for a CTRL-C typed
when an X event is received.

Could not use Perl and Python at the same time on FreeBSD, because Perl used
"-lc" and Python used the threaded C library.

Win32: The Mingw compiler gave a few warning messages.

When using "ZZ" and an autocommand for writing uses an abbreviation it didn't
work.  Don't stuff the ":x" command but execute it directly. (Mikael Berthe)

VMS doesn't always have lstat(), added an #ifdef around it.

Added a few corrections for the Macintosh. (Axel Kielhorn)

Win32: GvimExt could not edit more than a few files at once, the length of the
argument was fixed.


Previously released patches for Vim 5.7:

Patch 5.7.001
Problem:    When the current buffer is encrypted, and another modified buffer
	    isn't, ":wall" will encrypt the other buffer.
Solution:   In buf_write() use "buf" instead of "curbuf" to check for the
	    crypt key.
Files:	    src/fileio.c

Patch 5.7.002
Problem:    When 'showmode' is set, using "CTRL-O :r file" waits three seconds
	    before displaying the read text. (Wichert Akkerman)
Solution:   Set "keep_msg" to the file message so that the screen is redrawn
	    before the three seconds wait for displaying the mode message.
Files:	    src/fileio.c

Patch 5.7.003
Problem:    Searching for "[[:cntrl:]]" doesn't work.
Solution:   Exclude NUL from the matching characters, it terminates the list.
Files:	    src/regexp.c

Patch 5.7.004
Problem:    GTK: When selecting a new font, Vim can crash.
Solution:   In gui_mch_init_font() unreference the old font, not the new one.
Files:	    src/gui_gtk_x11.c

Patch 5.7.005
Problem:    Multibyte: Inserting a wrapped line corrupts kterm screen.
	    Pasting TEXT/COMPOUND_TEXT into Vim does not work.
	    On Motif no XIM status line is displayed even though it is
	    available.
Solution:   Don't use xterm trick for wrapping lines for multibyte mode.
	    Correct a missing "break", added TEXT/COMPOUND_TEXT selection
	    request.
	    Add XIMStatusArea fallback code.
	    (Katsuhito Nagano)
Files:	    src/gui_gtk_x11.c, src/multbyte.c, src/screen.c, src/ui.c

Patch 5.7.006
Problem:    GUI: redrawing the non-Visual selection is wrong when the window
	    is unobscured. (Jean-Pierre Etienne)
Solution:   Redraw the selection properly and don't clear it.  Added "len"
	    argument to clip_may_redraw_selection().
Files:	    src/gui.c, src/ui.c, src/proto/ui.pro

Patch 5.7.007
Problem:    Python: Crash when using the current buffer twice.
Solution:   Increase the reference count for buffer and window objects.
	    (Johannes Zellner)
Files:	    src/if_python.c

Patch 5.7.008
Problem:    In Ex mode, backspacing over the first TAB doesn't work properly.
	    (Wichert Akkerman)
Solution:   Switch the cursor on before printing the newline.
Files:	    src/ex_getln.c

Patch 5.7.009 (extra)
Problem:    Mac: Crash when using a long file.
Solution:   Don't redefine malloc() and free(), because it will break using
	    realloc().
Files:	    src/os_mac.h

Patch 5.7.010
Problem:    When using CTRL-A on a very long number Vim can crash.  (Michael
	    Naumann)
Solution:   Truncate the length of the new number to avoid a buffer overflow.
Files:	    src/ops.c

Patch 5.7.011 (extra)
Problem:    Win32 GUI on NT 5 and Win98: Displaying Hebrew is reversed.
Solution:   Output each character separately, to avoid that Windows reverses
	    the text for some fonts. (Ron Aaron)
Files:	    src/gui_w32.c

Patch 5.7.012
Problem:    When using "-complete=buffer" for ":command" the user command
	    fails.
Solution:   In a user command don't replace the buffer name with a count for
	    the  buffer number.
Files:	    src/ex_docmd.c

Patch 5.7.013
Problem:    "gD" didn't always find a match in the first line, depending on
	    the column the search started at.
Solution:   Reset the column to zero before starting to search.
Files:	    src/normal.c

Patch 5.7.014
Problem:    Rot13 encoding was done on characters with accents, which is
	    wrong. (Sven Gottwald)
Solution:   Only do rot13 encoding on ASCII characters.
Files:	    src/ops.c

Patch 5.7.016
Problem:    When hitting 'n' for a ":s///c" command, the ignore-case flag was
	    not restored, some matches were skipped. (Daniel Blaustein)
Solution:   Restore the reg_ic variable when 'n' was hit.
Files:	    src/ex_cmds.c

Patch 5.7.017
Problem:    When using a Vim script for Vim 6.0 with <SID> before a function
	    name, it produces an error message even when inside an "if version
	    >= 600".  (Charles Campbell)
Solution:   Ignore errors in the function name when the function is not going
	    to be defined.
Files:	    src/eval.c

Patch 5.7.018
Problem:    When running "rvim" or "vim -Z" it was still possible to execute a
	    shell command with system() and backtick-expansion. (Antonios A.
	    Kavarnos)
Solution:   Disallow executing a shell command in get_cmd_output() and
	    mch_expand_wildcards().
Files:	    src/misc1.c, src/os_unix.c

Patch 5.7.019
Problem:    Multibyte: In a substitute string, a multibyte character isn't
	    skipped properly, can be a problem when the second byte is a
	    backslash.
Solution:   Skip an extra byte for a double-byte character. (Muraoka Taro)
Files:	    src/ex_cmds.c

Patch 5.7.020
Problem:    Compilation doesn't work on MacOS-X.
Solution:   Add a couple of #ifdefs. (Jamie Curmi)
Files:	    src/regexp.c, src/ctags/general.h

Patch 5.7.021
Problem:    Vim sometimes produces a beep when started in an xterm.  Only
	    happens when compiled without mouse support.
Solution:   Requesting the xterm version results in a K_IGNORE.  This wasn't
	    handled when mouse support is disabled.  Accept K_IGNORE always.
Files:	    src/normal.c

Patch 5.7.022
Problem:    %v in 'statusline' is not displayed when it's equal to %c.
Solution:   Check if %V or %v is used and handle them differently.
Files:	    src/screen.c

Patch 5.7.023
Problem:    Crash when a WinLeave autocommand deletes the buffer in the other
	    window.
Solution:   Check that after executing the WinLeave autocommands there still
	    is a window to be closed.  Also update the test that was supposed
	    to check for this problem.
Files:	    src/window.c, testdir/test13.in, testdir/test13.ok

Patch 5.7.024
Problem:    Evaluating an expression for 'statusline' can have side effects.
Solution:   Evaluate the expression in a sandbox.
Files:	    src/edit.c, src/eval.c, src/proto/eval.pro, src/ex_cmds.c,
	    src/ex_cmds.h, src/ex_docmd.c, src/globals.h, src/option.c,
	    src/screen.c, src/undo.c

Patch 5.7.025 (fixed)
Problem:    Creating a temp file has a race condition.
Solution:   Create a private directory to write the temp files in.
Files:	    src/fileio.c, src/misc1.c, src/proto/misc1.pro,
	    src/proto/fileio.pro, src/memline.c, src/os_unix.h

Patch 5.7.026 (extra)
Problem:    Creating a temp file has a race condition.
Solution:   Create a private directory to write the temp files in.
	    This is the extra part of patch 5.7.025.
Files:	    src/os_msdos.h

Patch 5.7.027
Problem:    Starting to edit a file can cause a crash.  For example when in
	    Insert mode, using CTRL-O :help abbr<Tab> to scroll the screen and
	    then <CR>, which edits a help file. (Robert Bogomip)
Solution:   Check if keep_msg is NULL before copying it.
Files:	    src/fileio.c

Patch 5.7.028
Problem:    Creating a backup or swap file could fail in rare situations.
Solution:   Use O_EXCL for open().
Files:	    src/fileio.c, src/memfile.c

Patch 5.7.029
Problem:    Editing a file with an extremely long name crashed Vim.
Solution:   Check for length of the name when setting the window title.
Files:	    src/buffer.c

Patch 5.7.030
Problem:    A ":make" or ":grep" command with a very long argument could cause
	    a crash.
Solution:   Allocate the buffer for the shell command.
Files:	    src/ex_docmd.c


 vim:tw=78:ts=8:noet:ft=help:norl:
