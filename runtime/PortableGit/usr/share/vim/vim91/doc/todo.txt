*todo.txt*      For Vim version 9.1.  Last change: 2025 Apr 24


		  VIM REFERENCE MANUAL	  by <PERSON>


			      TODO list for Vim		*todo*

This is a veeeery long list of known bugs, current work and desired
improvements.  To make it a little bit accessible, the older items are grouped
by subject.  In the first column of the line a classification is used to be
able to look for "the next thing to do":

Priority classification:
9   next point release
8   next release
7   as soon as possible
6   soon
5   should be included
4   nice to have
3   consider including
2   maybe not
1   probably not
-   unclassified

						    *votes-for-changes*
See |develop.txt| for development plans.  You can vote for which items should
be worked on, but only if you sponsor Vim development.  See |sponsor|.

Issues can also be entered online: https://github.com/vim/vim/issues
Only use this for bug reports, not for questions!  Those belong on the
maillist.  Updates will be forwarded to the |vim_dev| maillist.  Issues
entered there will not be repeated below, unless there is extra information.

The #1234 numbers refer to an issue or pull request on github.  To see it in a
browser use: https://github.com/vim/vim/issues/1234
(replace 1234 with the issue/pull number)
							*known-bugs*
-------------------- Known bugs and current work -----------------------

Mapping with modifier is not recognized after a partial mapping.  Probably
because the typeahead was simplified when looking for a matching mapping.
Need to somehow undo the simplification.  #12002

Windows scroll when using the autocmd window.  #12085
in restore_snapshot_rec() restore more values from the snapshot, instead of
calling frame_new_height() and frame_new_width(), especially w_topline and
w_skipcol.

Check places that source "path/*.vim" to not match other extensions, e.g.
.vim9, on MS-Windows (short file name match, gets expanded to long file name).
E.g. for startup files, plugins, packs, etc.

When a help item can't be found, then open 'helpfile'.  Search for the tag in
that file and gtive E149 only when not found.  Helps for a tiny Vim installed
without all the help files.

Virtual text problems:
-  Virtual text aligned "above": Wrong indentation when using tabs  (Issue
   #12232)
-  truncated Virtual text below an empty line causes display error #12493

When 'virtualedit' is "all" and 'cursorcolumn' is set, the wrong column may be
highlighted. (van-de-bugger, 2018 Jan 23, #2576)

With 'smoothscroll' set and "lastline" in 'display', moving the cursor to a
wrapping line that makes the display scroll up may scroll much more than
needed, thus jump-scrolling. (part of issue 12411)
Errors when running tests with valgrind:
- test_codestyle.vim:  e.g.:
    command line..script /home/<USER>/vim/vim91/src/testdir/runtest.vim[569]..function RunTheTest[52]..Test_test_files line 6: keycode_check.vim: space before tab: Expected 0 but got 7
    command line..script /home/<USER>/vim/vim91/src/testdir/runtest.vim[569]..function RunTheTest[52]..Test_test_files line 10: setup.vim: trailing white space: Expected 0 but got 23
- test_gui.vim:
    Found errors in Test_gui_mouse_event():

When every block in if/elseif ends in "throw" or "return" code following after
"endif" should give an "unreachable code" error.

Upcoming larger works:
- Make spell checking work with recent .dic/.aff files, e.g. French.  #4916
    Make Vim understand the format somehow?   Search for "spell" below.
    Make sure suggestions are speedy, also with composed words (German).
- Make syntax highlighting faster and better.
  Add a generic mechanism to test a syntax plugin: An input file for the
  filetype and a screendump of expected result.  With a way to specify the
  setup (global variables) and another dump file from that.
  Discuss alternatives for using other grammars (treesitter, TextMate).
   - Possibly conversion to Vim syntax rules.
   - Other mechanism than group and cluster to nest syntax items, to be used
     for grammars.
   - Possibly keeping the parsed syntax tree and incremental updates.
   - tree-sitter doesn't handle incorrect syntax (while typing) properly.
   - NeoVim uses treesitter, what can we learn from that?
   - Vscode is asked to switch to treesitter:
      https://github.com/microsoft/vscode/issues/50140
   - Vscode uses TextMate.  #9087 - Other people don't like it.
      https://github.com/icedman/vim-textmate
   - sublime grammar?  Hugo mentions it's a moving target  #9087
   - Make clear how it relates to LSP.
   - example plugin: https://github.com/uga-rosa/dps-vsctm.vim


Further Vim9 improvements:
- Classes and Interfaces. See |vim9-classes|
  - Getting member of variable with "any" type should be handled at runtime.
    Remove temporary solution from #12096 / patch 9.0.1375.
  - "obj.Method()" does not always work in a compiled function, assumes "obj"
    is a dictionary.  #12196  Issue #12024 might be the same problem.
    Issue #11822: any.Func() can be a dict or an object call, need to handle
    this at runtime.  Also see #12198 for an example.
    Possibly issue #11981 can be fixed at the same time (has two examples).
  - Forward declaration of a class?  E.g. for Clone() function.
	Email lifepillar 2023 Mar 26
  - how about lock/unlock?
  - For chaining, allow using the class name as type for function return
    value.
  - Implement "specifies" interface
  - Implement generics
  - Add "assignable" (class or child)?
  - More efficient way for interface member index than iterating over list?
  - a variant of type() that returns a different type for each class?
      list<number> and list<string> should also differ.
- Promise class, could be used to wait on a popup close callback?
- class local to a function
- Use Vim9 for more runtime files.
- Inline call to map() and filter(), better type checking.
- When evaluating constants for script variables, some functions could work:
    has(featureName), len(someString)
- Implement as part of an expression: ++expr, --expr, expr++, expr--.
- The use of the literal value "null" and the type specific "null_xxx"
  values is confusing (#13433, #11770).

Information missing in terminfo:
- Codes used for focus gained and lost termcodes are hard-coded in
  set_termname(), not named.
- t_fe	enable focus-event tracking
- t_fd	disable focus-event tracking
- Accept "hyper" and "meta" modifiers (16 and 32) from Kitty like Meta?
    8 is actually "super".
- t_RV	request terminal version string; xterm:	"\033[>c"
    change in terminfo for "RV" uses the wrong escape sequence 7 - 14 Jan only
- Codes for <PasteStart> t_PS and <PasteEnd> t_PE; with bracketed paste:
    t_BE and t_BD.
Modifiers for various keys
- flag to indicate "xterm compatible modifiers" ?
Underline and similar:
- t_AU - Set underline color: like "AF" and "AB" entries.
- t_Ce	undercurl and underline end
- t_Cs	undercurl (curly underline) mode
- t_Us	double underline mode
- t_ds	dotted underline mode
- t_Ds	dashed underline mode
- t_Te	strikethrough end
- t_Ts	strikethrough mode
Cursor codes: |termcap-cursor-shape|
- t_RC	request terminal cursor blinking
- t_RS	request terminal cursor style
- t_VS	cursor normally visible (no blink)
- t_SI	start insert mode (bar cursor shape)
- t_SR	start replace mode (underline cursor shape)
- t_EI	end insert or replace mode (block cursor shape)
- t_SC	set cursor color start
- t_EC	set cursor color end
- t_SH	set cursor shape
State requests:
- t_RF	request terminal foreground color
- t_RB	request terminal background color
Less important (not needed for regular editing):
- t_IS	set icon text start
- t_IE	set icon text end
- t_ST	save window title to stack
- t_RT	restore window title from stack
- t_Si	save icon text to stack
- t_Ri	restore icon text from stack
- t_WP	set window position (Y, X) in pixels
- t_GP	get window position (Y, X) in pixels
- t_WS	set window size (height, width in cells)
Vim internal, but there should be a terminfo entry for these:
- t_8f	set foreground color (R, G, B) in printf() format
- t_8b	set background color (R, G, B) in printf() format
- t_8u	set underline color (R, G, B) in printf() format
Probably Vim internal, not in terminfo:
- t_TE	end of "raw" mode
- t_TI	put terminal into "raw" mode
- t_RK	request terminal keyboard protocol state; sent after |t_TI|
Already working, not properly documented:
- t_u7	request cursor position

Popup windows:
- Add a function to redraw a specific popup window.  Esp. to be used when
  editing the command line, when screen updating doesn't happen. (Shougo)
  #10210  Example that shows the need on the issue.
  Probably need to update all popup windows (they may overlap)
  If the display is scrolled need to redraw everything later.
- Add a flag to make a popup window focusable?
    CTRL-W P cycle over any preview window or focusable popup, end up back in
	     current window.
    ? - switch between current window and all popup windows
    Esc in popup window goes back to previous current window
- Add a termcap entry for changing the cursor when it goes under the popup and
  back.  like t_SI and t_EI  (t_SU and t_EU, where "U" means under?)
- With terminal in popup, allow for popup_hide() to temporarily hide it.?
- Make it possible to scroll horizontally when 'wrap' is off.  Does this
  require an ascii scrollbar, like with the vertical one?
- Fire some autocommand event after a new popup window was created and
  positioned?  PopupNew?  Could be used to set some options or move it out of
  the way. (#5737)
  However, it may also cause trouble, changing the popup of another plugin.
- Should popup_getoptions() also return the mask?  #7774
- Add a way to use popup_menu() synchronously: instead of invoking the
  callback, return the choice. (Ben Jackson, #6534)
- When using a popup for the info of a completion menu, and there is not
  enough space, let the popup overlap with the menu. (#4544)
- Implement flip option.
- Make redrawing more efficient and avoid flicker:
    - put popup menu also in popup_mask?
- Match does not have right BG color if line length equals popup width.
  (#5658)
- Figure out the size and position better if wrapping inserts indent

'incsearch' with :s:
- :s/foo  using CTRL-G moves to another line, should not happen, or use the
  correct line (it uses the last but one line) (Lifepillar, Aug 18, #3345)
- Also support range: :/foo/,/bar/delete
- Also support for user command, e.g. Cfilter
- :%s/foo should take the first match below the cursor line, unless there
  isn't one?
  Then :%s?foo should take the first match above the cursor line.

Prompt buffer:
- Add a command line history, using up/down keys.  issue #5010
- delay next prompt until plugin gives OK?
- add prompt_addtext({buf}, {expr})	none	add text to a prompt buffer

Terminal debugger:
- Add a mechanism for configuration.  Instead of several global variables use
  a dictionary.  Use callbacks for some things, e.g. opening a debug window in
  a certain position.
- Make prompt-buffer variant work better.
- Add option to not open the program window.  It's not used when attaching to
  an already running program. (M. Kelly)
- Use the optional token on requests, match the result with it. #10300
- When only gdb window exists, on "quit" edit another buffer.
- Termdebug does not work when Vim was built with mzscheme: gdb hangs just
  after "run".  Everything else works, including communication channel.  Not
  initializing mzscheme avoid the problem, thus it's not some #ifdef.
- Add support for lldb?  issue #3565
- Could we do something similar for debugging Vim scripts?  At least see the
  script while stepping through it.  Simple version would use an extra window.
  More complete solution would actually run Vim in a Terminal and control it
  with another Vim instance.

Terminal emulator window:
- Can escape a terminal popup with win_gotoid(), should be an error
- No support for underline color, t_8u.
- When in terminal-Normal mode when the job finishes, the cursor jumps to the
  end but the window is not updated.  This only happens when typing "a".
      :term bash -c "for V in {0..5}; do echo $V; sleep 1; done"
- GUI: cursor color is not updated when going to Terminal-Job mode.  #12328
- GUI: cursor color is not updated when switching between tabs with a key.
  #12329
- GUI: cursor color is not updated when switching between tabs with a mouse
  click.  #12330
- When started with ":terminal ++close" and the shell exits but there is a
  background process, the window remains open, because the channel still
  exists (and output still shows).  Perhaps close the window when an explicit
  ++close was used? (#5931)
- Using "CTRL-W :confirm quite" and selecting "yes" should work like ":quit!".
    (Harm te Hennepe, #6129)
- When the job in the terminal doesn't use mouse events, let the scroll wheel
  scroll the scrollback, like a terminal does at the shell prompt. #2490
  A bit like using CTRL-W N first.
  Jump back like with "a" when any key is typed.
  And use modeless selection.  #2962
- Use CTRL-W CTRL-K to enter a digraph? #5371
- When Vim runs in the terminal and changes the title, the statusline needs to
  be redrawn.
- Allow for specifying the directory, with ++cwd={dir}.
- When pasting should call vterm_keyboard_start_paste(), e.g. when using
  K_MIDDLEMOUSE, calling insert_reg().
- Users expect parsing the :term argument like a shell does, also support
  single quotes.  E.g. with:  :term grep 'alice says "hello"'  (#1999)
- Win32: Redirecting input does not work, half of Test_terminal_redir_file()
  is disabled.
- Win32: Redirecting output works but includes escape sequences.
- Win32: Make terminal used for :!cmd in the GUI work better.  Allow for
  redirection.
- When the job only outputs lines, we could handle resizing the terminal
  better: store lines separated by line breaks, instead of screen lines,
  then when the window is resized redraw those lines.
- Redrawing is slow with Motif. (Ramel Eshed)
- For the GUI fill termios with default values, perhaps like pangoterm:
  http://bazaar.launchpad.net/~leonerd/pangoterm/trunk/view/head:/main.c#L134
- When 'encoding' is not utf-8, or the job is using another encoding, setup
  conversions.

Patch adds showcmd() function  #11708

Cursor is after the end of the line:  #12137.

Crash when a variable is removed while listing variables (Issue #11435)

Problem with Visual highlight when 'linebreak' and 'showbreak' are set.
#11272

'cindent': compound literal indented wrong.  Check for " = " before "{"?
#12491

GUI Scroll test fails on FreeBSD when using Motif.  See FIXME in
Test_scrollbars in src/test_gui.vim

Support dark mode for MS-Windows: #12282

Remote command escapes single quote with backslash, should be doubling the
single quote in vim_strsave_escaped_ext()  #12202.

Can deref_func_name() and deref_function_name() be merged?

Using :global with a pattern containing \zs doesn't use the line where \zs
matches but the start of the pattern.  #3695  If there is a useful application
for this, it can be made to work by changing the call to ml_setmarked():
		ml_setmarked(lnum + regmatch.startpos[0].lnum);

After patch 8.2.4915 w_botline is computed much more often. Can this be
reduced?

When 'delcombine' is set a put after "x" includes the base character and all
combining characters. (Ron Aaron, 2023 Apr 10)

Add BufDeletePost.  #11041

Add winid arg to col() and charcol()  #11466 (request #11461)

'switchbuf' set to "newtab" does not work for ":cfirst" when in the quickfix
window.  #12436

When :argument has a non-number argument, use it like :buffer to find the
argument by name.  #12272

Fold unexpectedly closed when inserting fold marker with CTRL-V (Issue #12320)

Can we make 'noendofline' and 'endoffile' visible?  Should show by default,
since it's an unusual situation.
- Show 'noendofline' when it would be used for writing ('fileformat' "dos")
  with an upside down exclamation mark?  NonText highlighting.
- Show 'endoffile' when it would be used for writing ('fileformat' "dos") with
  "CTRL-Z", NonText highlighting.
- Add 'fillchars' items to change this, default behavior like:
	noeol:¡
	eof:CTRL-Z

Test property disappears when using CR twice in a row.  OK when some text was
entered. (#11151)

Add 'keywordprg' to various ftplugin files:
https://github.com/vim/vim/pull/5566

PR #11579 to add visualtext(), return Visually selected text.

PR #11860: Add more info to 'colorcolumn': display a character and highlight
for each separate entry.  Disadvantage: option value gets very complicated
with multiple entries, e.g. every 8 columns.

Stray characters in the shell #11719, caused by requesting a response for:
- XT key sequences
- Whether modifyOtherKeys is active
- Whether kitty keyboard protocol is active
Can we not request XT key sequences, or reduce them drastically?

Issue #10512: Dynamic loading broken with Perl 5.36
Damien has a patch (2022 Dec 4)

Request #11965: Allow several "%=" items in 'statusline', makes it possible
to have text in the center.

Add some kind of ":whathappend" command and functions to make visible what the
last few typed keys and executed commands are.  To be used when the user
wonders what went wrong.  Could also be used for statistics #12046.
- typed keys - Normal mode command - like what is recorded in a register and
  displayed by 'showcmd'.
- register used - #12063
- executed command lines
- with more verbosity: what scripts/functions/autocommands were executed

NFA regexp does not handle composing characters well: #10286
    [ɔ̃] matches both ɔ and ɔ̃
    \(ɔ\|ɔ̃\) matches ɔ and not ɔ̃

Is there a way to make 'autowriteall' make a clean exit when the xterm is
closed? (Dennis Nazic says files are preserved, okt 28).  Perhaps handle TERM
like HUP?

Better terminal emulator support:
  > Somehow request the terminfo entry from the terminal itself.  The $TERM
    value then is only relevant for whether this feature is supported or not.
    Replaces the xterm mechanism to request each entry separately. #6609
    Multiplexers (screen, tmux) can request it to the underlying terminal, and
    pass it on with modifications.
    How to get all the text quickly (also over ssh)? Can we use a side channel?
  > When xterm supports sending an Escape sequence for the Esc key, should
    have a way to request this state.  That could be an XTGETTCAP entry, e.g.
    "k!".  Add "esc_sends_sequence" flag.
    If we know this state, then do not pretend going out of Insert mode in
    vgetorpeek(), where kitty_protocol_state is checked.
  > If a response ends up in a shell command, one way to avoid this is by
    sending t_RV last and delay starting a shell command until the response
    has been seen.
  > Can we use the req_more_codes_from_term() mechanism with more terminals?
    Should we repeat it after executing a shell command?
    Can also add this to the 'keyprotocol' option: "mok2+tcap"
  > In the table of terminal names pointing to the list of termcap entries,
    add an optional additional one.  So that "xterm-kitty" can first load
    "xterm" and then add "kitty" entries.

Using "A" and "o" in manually created fold (in empty buffer) does not behave
consistently (James McCoy, #10698)

In a timer callback, when using ":echo" and then input() the message is
overwritten.  Could use ":echowin" and call redraw_cmd() in get_user_input().
#11299

Syntax include problem: #11277.  Related to Patch 8.2.2761

To avoid flicker: add an option that when a screen clear is requested, instead
of clearing it draws everything and uses "clear to end of line" for every line.
Resetting 't_ut' already causes this?

Instead of prefixing "INTERNAL" to internal messages, add a message in iemsg()
and siemsg() and translate it.  Messages only given to them don't need
translation.

When scheme can't be found by configure there is no clear "not found" message:
    configure:5769: checking MzScheme install prefix
    configure:5781: result:

Can "CSI nr X" be used instead of outputting spaces?  Is it faster?  #8002

Typed keys invisible after calling interrupt() from a timer. #10631

With a window height of 6 and 'scrolloff' set to 3, using "j" does not scroll
evenly. (#10545)  Need to handle this in scroll_cursor_bot().

Idea: when typing ":e /some/dir/" and "dir" does not exist, highlight in red.

":set shellpipe&" and ":set shellredir&" should use the logic from
initialization to figure out the default value from 'shell'.  Add a test for
this.

Add a diff() function to use the built-in diff support in a script.
#12321   Is the returned value in the right form now?

Support translations for plugins: #11637  PR: #12447
- Need a tool like xgettext for Vim script, generates a .pot file.
  Need the equivalent of _() and N_(), perhaps TR() and TRN().
- Instructions for how to create .po files and translate.
- Script or Makefile to generate .mo files.
- Instructions and perhaps a script to install the .mo files in the right
  place.
- Add variant of gettext() that takes a package name.

With concealed text mouse click doesn't put the cursor in the right position.
(Herb Sitz)  Fix by Christian Brabandt, 2011 Jun 16.  Doesn't work properly,
need to make the change in where RET_WIN_BUF_CHARTABSIZE() is called.
IDEA: when drawing the text, store the text byte index in ScreenLinesIdx[].
When converting screen column to text position use this.
The line number can be obtained from win->w_lines[].

test_arglist func Test_all_not_allowed_from_cmdwin() hangs on MS-Windows.

Can we add highlighting to ":echowindow"?

Information for a specific terminal (e.g. gnome, tmux, konsole, alacritty) is
spread out.  Make a section with copy/paste examples of script and pointers to
more information.

Request to use "." for the cursor column in search pattern \%<.c and \%<.v.
(#8179)

":filter set termcap" only filters terminal codes, not keys. (#9297)

Add an option to restrict 'incsearch' to not scroll the view. (Tavis Ormandy)

Remove SPACE_IN_FILENAME ? It is only used for completion.

When 'term' starts with "foot" then default t_TI and t_TE to the values used
for the builtin xterm termcap.

Adding "10" to 'spellsuggest' causes spell suggestions to become very slow.
(#4087)  Did patch 8.2.2379 help?
Also, z= in German on a long word can take a very long time, but CTRL-C to
interrupt does not work. Where to add ui_breakcheck()?
New English spell files also have very slow suggestions.

When 'spelloptions' is "camel" then zG doesn't work on some words.
(Gary Johnson, 17 Oct 2022)

SpellCap doesn't show below a closed fold. #12420

'cdpath' problems:
- Adding "~" to 'cdpath' doesn't work for completion?  (Davido, 2013 Aug 19)
- Problem with 'cdpath' on MS-Windows when a directory is equal to $HOME.
  (2006 Jul 26, Gary Johnson)

Make "g>" and "g<" in Visual mode move the text right or left.
Also for a block selection.  #8558

When using dictionary insert completion with 'ignorecase', the leading capital
in a word should be preserved.

Add optional argument to virtcol() that specifies "start", "cursor" or "end"
to tell which value from getvvcol() should be used. (#7964)
Value returned by virtcol() changes depending on how lines wrap.  This is
inconsistent with the documentation.

When 'wildignore' has an entry ending in "/*" this means nothing matching the
path before it will be added.  When encountering a directory check this and if
there is a match do not scan the directory (possibly speeds up :find a lot).
#12482

globpath() does not use 'wildignorecase' at all? (related to #8350)

mksession uses :buffer instead of :edit in one place but not another. #10629

Add 'termguiattr' option, use "gui=" attributes in the terminal?  Would work
with 'termguicolors'. #1740

Patch for blockwise paste reporting changes: #6660.  Asked for a PR.

Missing filetype test for bashrc, PKGBUILD, etc.

Add an option to not fetch terminal codes in xterm, to avoid flicker when t_Co
changes.

Add ??= operator, "a ??= b" works like "a = a ?? b". #10343

When ":redir" is used while already active, the previous one is ended.  But
when redirecting to a local variable (function or script) storing the value
won't work.  At least give an error.  Is there a way to make it work?
#10616

Completion for ":runtime" should show valid values, not what's in the current
directory. (#11447)

Add a "description" property to mappings.  #12205

Add an option to start_timer() to return from the input loop with K_IGNORE.
This is useful e.g. when a popup was created that disables mappings, we need
to return from vgetc() to make this happen.  #7011

Expanding <mods> should put the tab number from cmdmod.tab before "tab".
Any way to convert "$" back by using a special value? (#6901)

Can we detect true color support?  https://gist.github.com/XVilka/8346728
Try setting a color then request the current color, like using t_u7.

Add a v:register_used variable, which has the name of the register used for
the last command, e.g. put.  #12003

Make the jumplist behave like a tag stack. (#7738)  Should there be a more
time bound navigation, like with undo?

For testing, make a copy of ml_line_ptr instead of pointing it into the data
block, so that valgrind can do out of bounds check.  Set ML_LINE_DIRTY flag or
add ML_LINE_ALLOCED.

Changing a capturing group to non-capturing changes the result: #7607
    :echo matchstr('aaa bbb', '\(.\{-1,}\>\)\|.*')
    aaa
    :echo matchstr('aaa bbb', '\%(.\{-1,}\>\)\|.*')
    aaa bbb
Should also work without any group:
    :echo matchstr('aaa bbb', '.\{-1,}\>\|.*')
    aaa bbb  (should be aaa)

Should add a match/str/list/pos method that also returns the test and position
of submatches.  #8355

Check out PR #543 (Roland Puntaier).
Patch for multibyte characters in langmap and applying a mapping on them.
(Christian Brabandt, 2015 Jun 12, update July 25)
Is this the right solution?  Need to cleanup langmap behavior:
- in vgetorpeek() apply langmap to the typeahead buffer and put the result in
  a copy-buffer, only when langmap is appropriate for the current mode. Then
  check for mapping and let gotchars() work on the copy-buffer.
- Remove LANGMAP_ADJUST() in other parts of the code.  Make sure the mode is
  covered by the above change.
So that replaying the register doesn't use keymap/langmap and still does the
same thing.
Also see #737: langmap not applied to replaying recording.
Perhaps also related: #5147

Matchparen doesn't remove highlight after undo. (#7054)
Is OK when syntax HL is active.

Currently Del can be used to delete the last character of a typed count.
Can it also be used to delete an incomplete Normal mode command? (#7096)
After an operator: should work.  After "a" or "i" for text objects: should
work.

Using "au!" after "filetype on" is a bit slow.  Can the matching of
autocommands be made faster?  (#7056)

Using a search pattern containing "\%V" and 'hlsearch' set keeps old matches
when the Visual area changes.  #11694.  Do a redraw when starting a Visual
selection?

Append in Visual block mode inserts the wrong character.
Test_visual_block_mode() already has the proper check, which is commented out.
(#8288)

Add the <=> (spaceship) operator and "cond ?< expr ?= expr ?> expr"
    replace this:
	let left = GetLeftFunc()
	let right = GetRightFunc()
	let res = left < right ? lower : left == right ? equal : upper
    by:
	let res = GetLeftFunc() <=> GetRightFunc() ?< lower ?= equal ?> upper
Patch to make :q work with local arglist. (Christian Brabandt, #6286)

Why does Test_invalid_sid() not work in the GUI?

":pedit" ignores the local working directory when 'pvp' is set (#7267)

Lua: updating wrong buffer when using newly created, unloaded buffer.
(#6539)

File marks merging has duplicates since 7.4.1925. (Ingo Karkat, #5733)

A syntax plugin cannot use autocommands, it could be sourced from setting
'syntax' in a modeline.  Add a function that indicates whether "secure"
and/or "sandbox" are set.

Problem with auto-formatting - inserting space and putting cursor before added
character. (#6154)
Auto-formatting comments joins a following non-comment line when the comment
ends in a space. (Adam Levy, 18 Oct 2022)

When 'lazyredraw' is set sometimes the title is not updated.
(Jason Franklin, 2020 Feb 3)  Looks like a race condition.

With bash ":make" does not set v:shell_error.  Possible solution: set
'shellpipe' to "2>&1| tee %s; exit ${PIPESTATUS[0]}"  #5994

Using mode() when "/pat" is used in Visual mode returns "v" instead of "c",
which is not useful.  Return "c/v" instead.  And "c/o" when using "d/pat".
#6127

Add a cterm attribute for "dimmed" or "faint" text. (#8269)

When 'fileignorecase' is set ":e testfile.c" works to edit TestFile.c, but
":find testfile.c" does not ignore case.
Might be related to #6088.

Error for reverse range when using :vimgrep in file "[id-01] file.txt".
(#6919)

When changing the crypt key the buffer should be considered modified.
Like when changing 'fileformat'.  Save the old key in save_file_ff().
(Ninu-Ciprian Marginean)

Strange sequence of BufWipeout and BufNew events while doing omni-complete.
(Paul Jolly, #5656)
Get BufDelete without preceding BufNew. (Paul Jolly, #5694)
    Later more requests for what to track.
    Should we add new events that don't allow any buffer manipulation?
    Really only for dealing with appearing and disappearing buffers, load and
    unload.
BufWinenter event not fired when saving unnamed buffer. (Paul Jolly, #5655)
Another spurious BufDelete. (Dani Dickstein, #5701)

Wrong error when using local arglist. (Harm te Hennepe, #6133)

Test loose_clipboard() by selecting text before suspending.

:unmap <c-n> gives error but does remove the mapping. (Antony Scriven, 2019
Dec 19)

Patch to add an option to enable/disable VTP.  (Nobuhiro Takasaki, #5344)
Should have three values: empty, "off", "on".  Name it 'winterm'?

Patch to fix session file when using multiple tab pages. (Jason Franklin, 2019
May 20)
Also put :argadd commands at the start for all buffers, so that their order
remains equal?  Then %argdel to clean it up.  Do try this with 'hidden' set.
Also #5326: netrw buffers are not restored.

When 'backupdir' has a path ending in double slash (meaning: use full path of
the file) combined with 'patchmode' the file name is wrong. (#5791)

Completion mixes results from the current buffer with tags and other files.
Happens when typing CTRL-N while still searching for results.  E.g., type "b_"
in terminal.c and then CTRL-N twice.
Should do current file first and not split it up when more results are found.
(Also #1890)

Help for ":argadd fname" says that if "fname" is already in the argument list
that entry is used.  But instead it's always added. (#6210)
Add flag AL_FIND_ADD, if there is one argument find it in the list.

Statusline highlighting error, off by one. (#5599)

":find" with 'path' set to "data*" does not find files, while completion does
find them. (Max Kukartsev, #6218)

Enable 'termbidi' if $VTE_VERSION >= 5703 ?

"--cleanFOO" does not result in an error. (#5537)

Output from assert_equalfile() doesn't give a hint about what's different.
Assuming the files are text, print the line with the difference.

Result of synID() sometimes wrong in help files. (#5252)

When a help file is opened that doesn't have "ft=help" in the modeline then
the FileType is first set to "text" before it is set to "help". (#8099)

Problem showing a line if the number column width changes when using "o".
(Mateusz Morusiewicz, #4245)

When using :packadd for a replacement language plugin, it is loaded after the
default one.  #4698

When using :packadd files under "later" are not used, which is inconsistent
with packages under "start". (xtal8, #1994)

Patch to add new motion ]( and ]{.  (Yasuhiro Matsumoto, #5320)
Better: use the "z" prefix.  or ]t) and [t(.

Visual highlight not removed when 'display' is "lastline" and line doesn't
fit. (Kevin Lawler, #4457)

Current position in the changelist should be local to the buffer. (#2173)

Does not build with MinGW out of the box:
- _stat64 is not defined, need to use "struct stat" in vim.h
- WINVER conflict, should use 0x0600 by default?
- INT_MAX not defined: need to include <limits.h> in vim.h

Display messed up with matchparen, wrapping and scrolling. (#5638)
Screen update bug related to matchparen.  (Chris Heath, 2017 Mar 4, #1532)

When getting a focus event halfway a mapping this aborts the mapping.  E.g.
when "qq" is mapped and after the first "q" the mouse is moved outside of the
gvim window (with focus follows mouse), then the K_FOCUSLOST key is put in the
input buffer. (#5302)

Check_external_diff() is used too often. (Daniel Hahler, #4800)

Win32: after "[I" showing matches, scroll wheel messes up screen. (Tsakiridis,
2007 Feb 18)
Patch by Alex Dobrynin, 2007 Jun 3.  Also fixes other scroll wheel problems.

Add a WindowScrolled event.  Trigger around the same time as CursorMoved.
Can be used to update highlighting. #3127  #5181

Incorrect formatting with autoindent. (Sebastian Gniazdowski, #4909)

Patch to add the :bvimgrep command.  (Christian Brabandt, 2014 Nov 12)
Updated 2016 Jun 10, #858  Update 2017 Mar 28: use <buffer>.
Better use ":bufgrep" ?

Improve fallback for menu translations, to avoid having to create lots of
files that source the actual file.  E.g. menu_da_de -> menu_da
Include part of #3242?

Patch for different behavior of text objects with quotes: #11976
Is this actually better?

Patch to have text objects defined by arbitrary single characters. (Daniel
Thau, 2013 Nov 20, 2014 Jan 29, 2014 Jan 31)
Added tests (James McCoy, 2016 Aug 3, #958).  Still needs more work.

Would be nice to set tab-local values for 'diffexpr' and 'diffopt'.  Use
t:diffexpr_option t:diffopt_option? (#4782)
Also make 'scrollopt' tab-local, remove "hor" only for the current tab page.

Internal diff doesn't handle binary file like external diff does. (Mike
Williams, 2018 Oct 30)

'[ mark in wrong column after put. (#4776)

Problem with :tlmenu: Detach item added with all modes?  Issue #3563.

Add an argument to expandcmd() to expand like ":next" does.

When both "a" and "l" is in 'formatoptions' then auto-formatting also happens
in a long line. #5189

The quoting of the [command] argument of :terminal is not clearly documented.
Give a few examples. (#4288)

Opening a file with --remote-tab-silent that matches 'wildignore' does not
work, results in "E479: No match". (#4610)

7   Add an option to add one pixel column to the character width?  Lucida
    Console italic is wider than the normal font ("d" overlaps with next char).
    Opposite of 'linespace': 'columnspace'.
Patch for this (Tristan Konolige, #1011, only added the option, no implem.)

Bug: script written with "-W scriptout" contains Key codes, while the script
read with "-s scriptin" expects escape codes.  Probably "scriptout" needs to
be adjusted. (Daniel Steinberg, 2019 Feb 24, #4041)

Window size changes after closing a tab. (#4741)

Problem with colors in terminal window. (Jason Franklin, 2019 May 12)

Color schemes:
NOTE: modernizing the default colorschemes _AND_ introducing new ones is now
a project in its own right: https://github.com/vim/colorschemes. Feel free to
reach out if you want to lend a hand.
- Lifepillar: Updated/cleaned up color schemes:
  https://github.com/lifepillar/vim8-colorschemes.
- Include a few color schemes, based on popularity:
  - http://www.vim.org/scripts/script_search_results.php?keywords=&script_type=color+scheme&order_by=rating&direction=descending&search=search
http://vimawesome.com/?q=tag:color-scheme
- Use names that indicate their appearance (Christian Brabandt, 2017 Aug 3)
  - monokai - Xia Crusoe (2017 Aug 4)
  - seoul256 - Christian Brabandt (2017 Aug 3)
  - gruvbox -  Christian Brabandt (2017 Aug 3) (simplified version from
        Lifepillar, 2018 Jan 22, #2573)
  - janah - Marco Hinz (2017 Aug 4)
  - apprentice - Romain Lafourcade (2017 Aug 6)  remarks about help file #1964
- Suggested by Hiroki Kokubun:
  - [Iceberg](https://github.com/cocopon/iceberg.vim) (my one)
  - [hybrid](https://github.com/w0ng/vim-hybrid)
- Include solarized color scheme?, it does not support termguicolors.
  - Sanitized version of pablo (Lifepillar, 2017 Nov 21)

Bug: "vipgw" does not put cursor back where it belongs. (Jason Franklin, 2019
Mar 5)

Some composing characters actually add a cell width to the character they are
on top off, making the whole thing two characters wide. (#4526)

Should we include some part of pull request #4505, not increment changedtick
in some cases?  E.g. for ":write" when the changed flag was already off, the
buffer didn't change at all.

When using a timer callback vgetc_busy is reset, allowing for using input().
But in a channel callback this does not happen.  We need to do something
similar to check_due_timer().  Also see #3809.

C syntax: {} inside () causes following {} to be highlighted as error.
(Michalis Giannakidis, 2006 Jun 1)

Check: __attribute__((format(printf, on semsg() and siemsg().  Where was this
added?

Add test for urxvt mouse codes.  Also test that mouse coordinates can be
negative. (see #4326)

'cmdheight' has a tab-local value, but it cannot be obtained with
`:echo gettabwinvar(2, 1, '&cmdheight')` returns the value for the _current_
tab page. (Ingo Karkat, #4324)
:call settabwinvar(1, 1, '&cmdheight', 2) also doesn't work well.

When opening a file, allow for specifying the initial column position:
    vim +12:5 file.txt   line 12 column 5
    :edit +12:5 file.txt
Should probably use the column as the character index.

This modeline throws unexpected errors: (#4165)
    vim: syn=nosyntax

Make balloon_show() work outside of 'balloonexpr'?  Users expect it to work:
#2948. (related to #1512?)
Also see #2352, want better control over balloon, perhaps set the position.
Should also be possible to add highlighting, like in the status line?
balloonexpr() on MS-Windows GUI doesn't handle accented chars? (nivaemail,
2018 Sep 14)

More warnings from static analysis:
https://lgtm.com/projects/g/vim/vim/alerts/?mode=list

Not existing directory in CDPATH leads to two shell calls. (#4525)

Use dict_iterate_start() / dict_iterate_next() instead of relying on the
internals of the dict structure.

nvo-mode mapping works on Windows, not on Linux. (#3678)

Redo only remembers the last change.  Could use "{count}g." to redo an older
change.  How does the user know which change?  At least have a way to list
them: ":repeats".  Add to history, like search history and command line history.

When 'confirm' is set a "silent q" doesn't show the prompt.  It should in this
case. (Nate Peterson, 2019 Jan 31, #3892)
For "silent! q" it should not prompt and just fail.

Add <aevent>, which expands to the currently triggered autocommand event name.
(Daniel Hahler, #4232)  Or add it to v:event (easier to use but slightly more
expensive).

Some xterm responses are not properly handled: (Markus Gömmel, 2019 Apr 1)
    DCS 0 $ r Pt ST    should be ignored.
    DCS 0 + r/Pt/  ST  already ignored?

Using CTRL-L to add a character to the search string that contains \v,
punctuation is repeated. (Smylers, 2018 Nov 17, #3621)

Using single wide base character with double wide composing character gives
drawing errors.  Fill up the base character?  (Dominique, #4328)

When 'sidescrolloff' is set, using "zl" to go to the end of the line, suddenly
scrolls back.  Should allow for this scrolling, like 'scrolloff' does when
using CTRL-E. (Yee Cheng Chin, #3721)

When splitting a window with few text lines, the relative cursor position is
kept, which means part of the text isn't displayed.  Better show all the text
when possible. (Dylan Lloyd, #3973)

Make ":interactive !cmd" stop termcap mode, also when used in an autocommand.
(#3692)

Add something like 'fillchars' local to window, but allow for specifying a
highlight name.  Esp. for the statusline.
And "extends" and "precedes" are also useful without 'list' set.  Also in
'fillchars' or another option?

Sourceforge Vim pages still have content, make them empty, keep redirect.
Check for PHP errors. (Wayne Davison, 2018 Oct 26)

Problem with Visual yank when 'linebreak' and 'showbreak' are set.
Patch with tests, but it's not clear how it is supposed to work. (tommm, 2018
Nov 17)  Asked about this, Dec 22. Christian will have a look.

Update for xim-input-style help (Tony Mechelynck, 2019 Jan 10).
Feedback from someone who uses this?

Only output t_Cs when t_Ce is also set.  do not use Cs and Ce termcap entries.  (Daniel Hahler, 2018 Sep 25)
Add t_cS and t_cR for cursor color select and reset.  Use Cs and Cr terminfo
values.

Further xdiff changes:
- More options, e.g. different kind of whitespace diff.
- when editing text, update the surrounding diff blocks.
- omit diff.exe from distribution
- Can we make this show differences within a line?
- add option to use external diff above a certain size.

Difference between two regexp engines: #3373

When the last line wraps, selecting with the mouse below that line only
includes the first screen line. (2018 Aug 23, #3368)

Refactored HTML indent file. (Michael Lee, #1821)
Asked to write a test.

Merge checking for 'cursorline' and 'concealcursor', see neovim #9492.

Add a windowID argument to placing a sign, so that it only shows up in one
window for the buffer.

Compiler warning (geeknik, 2017 Oct 26):
- undefined left shift in eval_string(), before hex2nr() (#2250)
  Use unsigned for "nr".

Add Native language protocol server (LSP) support. (Yegappan Lakshmanan, 2018
Oct 28)

Patch to be able to use hex numbers with :digraph. (Lcd, 2015 Sep 6)
Update Sep 7.  Update by Christian Brabandt, 2015 Sep 8, 2016 Feb 1.
Patch to be able to disable default digraphs (incomplete) (Eric Pruitt, 2018
Nov 22).

Patch to list user digraphs. (Christian Brabandt, 2012 Apr 14)

Setting 'columns' in a BufEnter autocommand causes a second tab width to
behave strangely, as if there is a gap and a vertical window separator.
(Michael Soyka, 2018 Sep 23, #3477)

Add an option similar to 'lazyredraw' to skip redrawing while executing a
script or function.

Using a menu item while the "more" prompt is displayed doesn't work well.
E.g. after using help->version.  Have a key that ends the "more" prompt and
does nothing otherwise?

MS-Windows: write may fail if another program is reading the file.
If 'readonly' is not set but the file appears to be readonly later, try again
(wait a little while).
CreateFile() returns ERROR_SHARING_VIOLATION (Linwei, 2018 May 5)

Using --remote to open a file in which a # appears does not work on
MS-Windows.  Perhaps in \#  the \ is seen as a path separator. (Axel Bender,
2017 Feb 9)  Can we expand wildcards first and send the path literally to the
receiving Vim?  Or make an exception for #, it's not useful remotely.

Column number is wrong when using 'linebreak' and 'wrap'. (Keith Smiley, 2018
Jan 15, #2555)

Add Makefiles to the runtime/spell directory tree, since nobody uses Aap.
Will have to explain the manual steps (downloading the .aff and .dic files,
applying the diff, etc.

User dictionary ~/.vim/spell/lang.utf-8.add not used for spell checking until a
word is re-added to it. (Matej Cepl, 2018 Feb 6)

Fold at end of the buffer behaves inconsistently. (James McCoy, 2017 Oct 9)

Implement option_save() and option_restore():
option_restore({list})					*option_restore()*
		Restore options previously saved by option_save().
		When buffer-local options have been saved, this function must
		be called when the same buffer is the current buffer.
		When window-local options have been saved, this function must
		be called when the same window is the current window.
		When in the wrong buffer and/or window an error is given and
		the local options won't be restored.

option_save({list})					*option_save()*
		Saves the options named in {list}.  The returned value can be
		passed to option_restore().  Example: >
			let s:saved_options = option_save([
			    \ 'ignorecase',
			    \ 'iskeyword',
			    \ ])
			 au <buffer> BufLeave *
			    \ call option_restore(s:saved_options)
<		The advantage over using `:let` is that global and local
		values are handled and the script ID is restored, so that
		`:verbose set` will show where the option was originally set,
		not where it was restored.
  Alternatively: save and restore ALL options.  Implementation needs to use
	copy-on-write.  Return an ID from option_save(), when
	option_restore(ID) is called give an error if another option_save()
	was called in the meantime, they must be balanced.

"gvim --remote" from a directory with non-word characters changes the current
directory (Paulo Marcel Coelho Arabic, 2017 Oct 30, #2266)
Also see #1689.

No profile information for function that executes ":quit". (Daniel Hahler,
2017 Dec 26, #2501)

A function on a dictionary is not profiled. (ZyX, 2010 Dec 25)

Add script number to profile?  (#3330 breaks tests).

A function defined locally and lambda's are not easily recognized.
Mention where they were defined somewhere.

ml_get errors with buggy script. (Dominique, 2017 Apr 30)

Error in emsg with buggy script. (Dominique, 2017 Apr 30)

Join truncates xml comment. (Dmitrii Tcyganok, 2017 Dec 24, #2494)
Requires 'formatoptions' to include "j". (Gary Johnson, 2017 Dec 24)

Patch to support hunspell. (Matej Cepl, Jan 2018, #2500) Based on older patch
in #846)
Doesn't work on Windows yet.  Not ready to included, hard coded paths.

When a timer is running and typing CTRL-R on the command line, it is not
redrawn properly. (xtal8, 2017 Oct 23, #2241)

In an optional package the "after" directory is not scanned?
(Renato Fabbri, 2018 Feb 22)

Patch for Neovim concerning restoring when closing help window. (glacambre
neovim #7431)

Patch for improving detecting Ruby on Mac in configure. (Ilya Mikhaltsou, 2017
Nov 21)

When t_Co is changed from termresponse, the OptionSet autocommand event isn't
triggered.  Use the code from the end of set_num_option() in
set_color_count().

When using command line window, CmdlineLeave is triggered without
CmdlineEnter.  (xtal8, 2017 Oct 30, #2263)
Add some way to get the nested state.  Although CmdwinEnter is obviously
always nested.

matchit hasn't been maintained for a long time.  #955.

Problem with 'delcombine'. (agguser, 2017 Nov 10, #2313)

'delcombine' does not work for the command line. (Tony Mechelynck, 2009 Jul
20)

MS-Windows: buffer completion doesn't work when using backslash (or slash)
for a path separator. (xtal8, #2201)

Test more runtime files.

Window not closed when deleting buffer. (Harm te Hennepe, 2017 Aug 27, #2029)

Add options_default() / options_restore() to set several options to Vim
defaults for a plugin. Comments from Zyx, 2017 May 10.
Perhaps use a vimcontext / endvimcontext command block.

After using :noautocmd CursorMoved may still trigger. (Andy Stewart, 2017 Sep
13, #2084).  Set old position after the command.

When bracketed paste is used, pasting at the ":append" prompt does not get the
line breaks. (Ken Takata, 2017 Aug 22)

Cannot copy modeless selection when cursor is inside it. (lkintact, #2300)

Test_writefile_fails_conversion failure on Solaris because if different iconv
behavior.  Skip when "uname" returns "SunOS"? (Pavel Heimlich, #1872)

'tagrelative' is broken in specific situation. (xaizek, 2017 Oct 19, #2221)

The ++ options for the :edit command are also useful on the Vim command line.

Overlong utf-8 sequence is displayed wrong. (Harm te Hennepe, 2017 Sep 14,
#2089)  Patch with possible solution by Björn Linse.

X11: Putting more than about 262040 characters of text on the clipboard and
pasting it in another Vim doesn't work.  (Dominique Pelle, 2008 Aug 21-23)
clip_x11_request_selection_cb() is called with zero value and length.
Also: Get an error message from free() in the process that owns the selection.
Seems to happen when the selection is requested the second time, but before
clip_x11_convert_selection_cb() is invoked, thus in X library code.
Kazunobu Kuriyama is working on a proper fix. (2017 Jul 25)

Problem with three-piece comment. (Michael Lee, 2017 May 11, #1696)

Creating a partial with an autoload function is confused about the "self"
attribute of the function.  For an unknown function assume "self" and make
that optional? (Bjorn Linse, 2017 Aug 5)

Cindent: returning a structure has more indent for the second item.
(Sam Pagenkopf, 2017 Sep 14, #2090)

Patch from Christian Brabandt to preserve upper case marks when wiping out a
buffer. (2013 Dec 9)
Also fixes #2166?

Profile of a dict function is lost when the dict is deleted.  Would it be
possible to collect this?  (Daniel Hahler, #2350)

When checking if a bufref is valid, also check the buffer number, to catch the
case of :bwipe followed by :new.

Patch to skip writing a temp file for diffing if the buffer is equal to the
existing file. (Akria Sheng, 2017 Jul 22)
Could also skip writing lines that are the same.

MS-Windows: Opening same file in a second gvim hangs. (Sven Bruggemann, 2017
Jul 4)

Setting 'clipboard' to "unnamed" makes a global command very slow (Daniel
Drucker, 2017 May 8).
This was supposed to be fixed, did it break again somehow?
Christian cannot reproduce it.

Using composing char in mapping does not work properly. maparg() shows the
wrong thing. (Nikolai Pavlov, 2017 Jul 8, #1827)
Or is this not an actual problem?

Better TeX indent file. (Christian Brabandt, 2017 May 3)

Use gvimext.dll from the nightly build? (Issue #249)

'synmaxcol' works with bytes instead of screen cells. (Llandon, 2017 May 31,
#1736)

Problem with using :cd when remotely editing a file. (Gerd Wachsmuth, 2017 May
8, #1690)

Memory leak in test97?  The string is actually freed.  Weird.

assert_fails() can only check for the first error.  Make it possible to have
it catch multiple errors and check all of them.

Add a toolbar in the terminal.  Can be global, above all windows, or specific
for one window.

Make maparg() also return the raw rhs, so that it doesn't depend on 'cpo'.
(Brett Stahlman, 2017 May 23)
Even better: add a way to disable a mapping temporarily and re-enable it
later.  This is for a sub-mode that is active for a short while (one buffer).
Still need maplist() to find the mappings.  What can we use to identify a
mapping?  Something unique would be better than the LHS.
Perhaps simpler: actually delete the mappings.  Use maplist() to list matching
mappings (with a lhs prefix, like maparg()), mapdelete() to delete,
maprestore() to restore (using the output of maplist()).

Add an argument to :mkvimrc (or add another command) to skip mappings from
plugins (source is a Vim script).  No need to put these in a .vimrc, they will
be defined when the plugin is loaded.

Use tb_set(winid, [{'text': 'stop', 'cb': callback, 'hi': 'Green'}])
    tb_highlight(winid, 'ToolBar')
    tb_get(winid)

json_encode(): should convert to utf-8. (Nikolai Pavlov, 2016 Jan 23)
What if there is an invalid character?

Json string with trailing \u should be an error. (Lcd)

import can't be used in define option when include matches too.
(Romain Lafourcade, 2017 Jun 18, #1519)

Wrong diff highlighting with three files. (2016 Oct 20, #1186)
Also get E749 on exit.
Another example in #1309

Suggestion to improve pt-br spell checking. (Marcelo D Montu, 2016 Dec 15,
#1330)

Error in test_startup_utf8 on Solaris. (Danek Duvall, 2016 Aug 17)

Rule to use "^" for statusline does not work if a space is defined with
highlighting for both stl and stlnc.  Patch by Ken Hamada (itchyny, 2016 Dec 11)

Using CTRL-G_U in InsertCharPre causes trouble for redo. (Israel Chauca
Fuentes, 2017 Feb 12, #1470)

Add a "keytrans()" function, which turns the internal byte representation of a
key into a form that can be used for :map.  E.g.
    let xx = "\<C-Home>"
    echo keytrans(xx)
    <C-Home>

Check for errors E704 and E705 only does VAR_FUNC, should also do VAR_PARTIAL.
(Nikolai Pavlov, 2017 Mar 13, #1557)
Make a function to check for function-like type?

Implement named arguments for functions with optional arguments:
    func Foo(start, count = 1, all = 1)
    call Foo(12, all = 0)

Add a command to take a range of lines, filter them and put the output
somewhere else.  :{range}copy {dest} !cmd

The TermResponse event is not triggered when a plugin has set 'eventignore' to
"all".  Netrw does this. (Gary Johnson, 2017 Jan 24)
Postpone the event until 'eventignore' is reset.

Expanding /**/ is slow.  Idea by Luc Hermitte, 2017 Apr 14.

Once .exe with updated installer is available: Add remark to download page
about /S and /D options (Ken Takata, 2016 Apr 13)
Or point to nightly builds: https://github.com/vim/vim-win32-installer/releases

":sbr" docs state it respects 'switchbuf', but "vsplit" does not cause a
vertical split. (Haldean Brown, 2017 Mar 1)

Use ADDR_OTHER instead of ADDR_LINES for many more commands.
E.g. all the location list commands use a count.
Add tests for using number larger than number of lines in buffer.

Might be useful to have isreadonly(), like we have islocked().
Avoids exceptions, e.g. when using the b: namespace as a dict.

Patch to make v:shell_error writable. (Christian Brabandt, 2016 Sep 27)
Useful to restore it.  Is there another solution?

Patch for wrong cursor position on wrapped line, involving breakindent.
(Ozaki Kiichi, 2016 Nov 25)

Patch for 'cursorlinenr' option. (Ozaki Kiichi, 2016 Nov 30)

Window resizing with 'winfixheight': With a vertical split the height changes
anyway. (Tommy allen, 2017 Feb 21, #1502)

Invalid behavior with NULL list. (Nikolai Pavlov, #768)
E.g. deepcopy(test_null_list())

Patch to make it possible to extend a list with itself.
(Nikolai Pavlov, 2016 Sep 23)

Patch to add Zstandard compressed file support. (Nick Terrell, 2016 Oct 24)

On Windows buffer completion sees backslash as escape char instead of path
separator. (Toffanim, 2016 Nov 24, #1274)

Should :vmap in matchit.vim be :xmap?  (Tony Mechelynck)

Problem with whitespace in errorformat. (Gerd Wachsmuth, 2016 May 15, #807)

Support sort(l, 'F'), convert strings to float. (#7857)

sort() is not stable when using numeric/float sort (Nikolay Pavlov, 2016 Sep
4#1038)

sort() does not use 'smartcase' for the skip pattern, even though 'ignorecase'
is used. (Filipe Brandenburger, #7322)

+channel:
- Add a in_cb, invoked when the write buffer has become empty. (Matteo Landi)
- Add ch_readlines(): for a channel in NL mode, reads as many lines as are
  available.  Should be more efficient than looping over ch_read() with
  ch_status() to check for more.
- If buffer contents is changed in a callback, set w_redr_status so that it
  gets redrawn in redraw_after_callback(). #6120
- Add a separate timeout for opening a socket.  Currently it's fixed at 50
  msec, which is too small for a remote connection. (tverniquet, #2130)
- Writing raw mode to a buffer should still handle NL characters as line
  breaks. (Dmitry Zotikov, 2017 Aug 16)
- When out_cb executes :sleep, the close_cb may be invoked. (Daniel Hahler,
  2016 Dec 11, #1320)
- Implement |job-term| ?
- Calling a function when receiving a "call" on a channel, using feedkeys()
  does not work.  It does work from a timer. (Qiming Zhao, #3852)
- Channel test fails with Motif.  Sometimes kills the X11 server.
- When a message in the queue but there is no callback, drop it after a while?
  Add timestamp to queued messages and callbacks with ID, remove after a
  minute.  Option to set the droptime.
- Add an option to drop text of very long lines?  Default to 1 Mbyte.
- Add remark about undo sync, is there a way to force it?
- When starting a job, have an option to open the server socket, so we know
  the port, and pass it to the command with --socket-fd {nr}. (Olaf Dabrunz,
  Feb 9)  How to do this on MS-Windows?
- For connection to server, a "keep open" flag would be useful.  Retry
  connecting in the main loop with zero timeout.
- job_start(): run job in a newly opened terminal (not a terminal window).
    With xterm could use -S{pty}.
    Although user could use "xterm -e 'cmd arg'".

Regexp problems:
- NFA engine can be slow for some patterns.  Dominique found out that most
  time is spent in addstate_here() copying the threads.  Instead of copying,
  let each thread point to the next one (by offset, the list is reallocated).
  (Dominique Pelle, 2019 Feb 18)
- Old engine: using 'incsearch' /\Zabc does not highlight the "c" if it has a
  composing character.  New engine is OK. (Tony Mechelynck, 2019 May 5)
- When search pattern has the base character both with and without combining
  character, search fails.  E.g. "รรีบ" in "การรีบรักใคร". (agguser, #2312)
- [:space:] only matches ASCII spaces.  Add [:white:] for all space-like
  characters, esp. including 0xa0.  Use character class zero.
- Since 7.4.704 the old regex engine fails to match [[:print:]] in 0xf6.
  (Manuel Ortega, 2016 Apr 24)
  Test fails on Mac.  Avoid using isalpha(), isalnum(), etc?  Depends on
  LC_CTYPE
- The old engine does not find a match for "/\%#=1\(\)\{80}", the new engine
  matches everywhere.
- Using win_linetabsize() can still be slow. Cache the result, store col and
  vcol. Reset them when moving to another line.
- Very slow with a long line and Ruby highlighting. (John Whitley, 2014 Dec 4)
- Bug with pattern: '\vblock (\d+)\.\n.*\d+%(\1)@<!\.$'
  (Lech Lorens, 2014 Feb 3)
- Issue 164: freeze on regexp search.
- Ignorecase not handled properly for multibyte characters. (Axel Bender,
  2013 Dec 11)
- Using \@> and \?. (Brett Stahlman, 2013 Dec 21) Remark from Marcin
  Szamotulski; Remark from Brett 2014 Jan 6 and 7.
- NFA regexp doesn't handle \%<v correctly. (Ingo Karkat, 2014 May 12)
- Does not work with NFA regexp engine:
  \%u, \%x, \%o, \%d followed by a composing character
- Search for \%d0\+ may fail with E363. (Christian Brabandt, 2016 Oct 4)
- \%'[ does not work.  '%'] does work.  (Masaaki Nakamura, 2016 Apr 4)
- Bug relating to back references. (Ingo Karkat, 2014 Jul 24)
- New RE does not give an error for empty group: "\(\)\{2}" (Dominique Pelle,
  2015 Feb 7)
- Using back reference before the capturing group sometimes works with the old
  engine, can we do this with the new engine?  E.g. with
  "/\%(<\1>\)\@<=.*\%(<\/\(\w\+\)>\)\@=" matching text inside HTML tags.
  This problem is probably the same: "\%(^\1.*$\n\)\@<=\(\d\+\).*$".
  (guotuofeng, 2015 Jun 22)
- Strange matching with "\(Hello\n\)\@<=A". (Anas Syed, 2015 Feb 12)
- Problem with \v(A)@<=b+\1c. (Issue 334)
- Diff highlighting can be very slow. (Issue 309)
- Using %> for a virtual column has a check based on 'tabsize'.  Better would
  be to cache the result of win_linetabsize(col), storing both col and vcol,
  and use them to decide whether win_linetabsize() needs to be called.  Reset
  col and vcol when moving to another line.
- this doesn't work: "syntax match ErrorMsg /.\%9l\%>20c\&\%<28c/".  Leaving
  out the \& works.  Seems any column check after \& fails.
- Difference between two engines: ".*\zs\/\@>\/" on text "///"
  (Chris Paul, 2016 Nov 13)  New engine not greedy enough?
  Another one: echom matchstr(" sdfsfsf\n sfdsdfsdf",'[^\n]*')
  (2017 May 15, #1252)

Idea from Sven: record sequence of keys.  Useful to show others what they are
doing (look over the shoulder), and also to see what happened.
Probably list of keystrokes, with some annotations for mode changes.
Could store in logfile to be able to analyse it with an external command.
E.g. to see when's the last time a plugin command was used.

cmap using execute() has side effects. (Killthemule, 2016 Aug 17, #983)

:map X may print invalid data. (Nikolay Pavlov, 2017 Jul 3, #1816)

Patch to order results from taglist(). (Duncan McDougall, 2016 Oct 25)

ml_get errors when reloading file. (Chris Desjardins, 2016 Apr 19)
Also with latest version.

Completion for input() does not expand environment variables. (chdiza, 2016
Jul 25, #948)

Patch to add 'systemencoding', convert between 'encoding' and this for file
names, shell commands and the like.  (Kikuchan, 2010 Oct 14)
Assume the system converts between the actual encoding of the filesystem to
the system encoding (usually utf-8).

MS-Windows: use WS_HIDE instead of SW_SHOWMINNOACTIVE in os_win32.c?
Otherwise task flickers in taskbar.

Second problem in #966: ins_compl_add_tv() uses get_dict_string() multiple
times, overwrites the one buffer. (Nikolay Pavlov, 2016 Aug 5)

Patch to improve map documentation. Issue #799.

We can use '. to go to the last change in the current buffer, but how about
the last change in any buffer?  Can we use ', (, is next to .)?

Ramel Eshed: system() is much slower than job_start(), why? (Aug 26)

When generating the Unicode tables with runtime/tools/unicode.vim the
emoji_width table has only one entry.

It's possible to add ",," to 'wildignore', an empty entry.  Causes problems.
Reject the value? #710.

When doing "vi buf.md" a BufNew autocommand for *.md is not triggered.
Because of using the initial buffer? (Dun Peal, 2016 May 12)

Neovim patch for utfc_ptr2char_len() https://github.com/neovim/neovim/pull/4574
No test, needs some work to include.

Patch to improve indenting for C++ constructor with initializer list.
(Hirohito Higashi, 2016 Mar 31)

Zero-out crypt key information when no longer in use. (Ben Fritz, 2017 May 15)

Add stronger encryption.  Could use libsodium (NaCl).
https://github.com/jedisct1/libsodium/
Possibly include the needed code so that it can be built everywhere.

Add a way to restart a timer.  It's similar to timer_stop() and timer_start(),
but the reference remains valid.

Need to try out instructions in INSTALLpc.txt about how to install all
interfaces and how to build Vim with them.
Appveyor build with self-installing executable, includes getting most
interfaces: https://github.com/k-takata/vim/tree/chrisbra-appveyor-build
result: https://ci.appveyor.com/project/k-takata/vim/history

Problem using ":try" inside ":execute". (ZyX, 2013 Sep 15)

Patch to make tests pass with EBCDIC. (Owen Leibman, 2016 Apr 10)

Add ":read :command", to insert the output of an Ex command?
Can already do it with ":$put =execute('command')".

exists(":tearoff") does not tell you if the command is implemented. (Tony
Mechelynck)  Perhaps use exists("::tearoff") to check?

Use vim.vim syntax highlighting for help file examples, but without ":" in
'iskeyword' for syntax.

Installation of .desktop files does not work everywhere.
It's now fixed, but the target directory probably isn't right.
Add configure check?
Should use /usr/local/share/applications or /usr/share/applications.
Or use $XDG_DATA_DIRS.
Also need to run update-desktop-database (Kuriyama Kazunobu, 2015 Nov 4)

Test object i{ and it do not behave the same. #1379
Do not include the linebreak at the start?

Feature request: add the "al" text object, to manipulate a screen line.
Especially useful when using 'linebreak'

Patch to avoid redrawing tabline when the popup menu is visible.
(Christian Brabandt, 2016 Jan 28)

When the CursorMovedI event triggers, and CTRL-X was typed, a script cannot
restore the mode properly. (Andrew Stewart, 2016 Apr 20)
Do not trigger the event?

Patch to make the behavior of "w" more straightforward, but not Vi compatible.
With a 'cpo' flag.  (Christian Brabandt, 2016 Feb 8)

Patch to add optionproperties(). (Anton Lindqvist, 2016 Mar 27, update Apr 13)

Patch to add TagNotFound autocommand. (Anton Lindqvist, 2016 Feb 3)

Patch to add Error autocommand. (Anton Lindqvist, 2016 Feb 17)
Only remembers one error.

GVim: when both Tab and CTRL-I are mapped, use CTRL-I not for Tab.

Unexpected delay when using CTRL-O u.  It's not timeoutlen.
(Gary Johnson, 2015 Aug 28)

Instead of separately uploading patches to the ftp site, we can get them from
github with a URL like this:
   https://github.com/vim/vim/compare/v7.4.920%5E...v7.4.920.diff
Diff for version.c contains more context, can't skip a patch.

Comparing nested structures with "==" uses a different comparator than when
comparing individual items.

Using uninitialized memory. (Dominique Pelle, 2015 Nov 4)

MS-Windows: When editing a file with a leading space, writing it uses the
wrong name. (Aram, 2014 Nov 7)  Vim 7.4.

Can't recognize the $ProgramFiles(x86) environment variable.  Recognize it
specifically?  First try with the parens, then without.

Patch to add :mapgroup, put mappings in a group like augroup.
(Yasuhiro Matsumoto, 2016 Feb 19)

Can we cache the syntax attributes, so that updates for 'relativenumber' and
'cursorline'/'cursorcolumn' are a lot faster?  Thus store the attributes
before combining them.

C highlighting: modern C allows: /* comment */ #ifdef
and also line continuation after #include.
I can't recommend it though.

To support Thai (and other languages) word boundaries, include the ICU
library:  http://userguide.icu-project.org/boundaryanalysis

Patch to use two highlight groups for relative numbers. (Shaun Brady, 2016 Jan
30)

MS-Windows: Crash opening very long file name starting with "\\".
(Christian Brock, 2012 Jun 29)

The OptionSet autocommand event is not always triggered. (Rick Howe, 2015 Sep
24): :diffthis, :diffoff.

":set all&" still does not handle all side effects.  Centralize handling side
effects for when set by the user, on init and when reset to default.

":tag" does not jump to the right entry of a :tselect. (James Speros, 2015 Oct
9)

The argument for "-S" is not taken literally, the ":so" command expands
wildcards.  Add a ":nowild" command modifier?  (ZyX, 2015 March 4)

Proposal to make options.txt easier to read. (Arnaud Decara, 2015 Aug 5)
Update Aug 14.

When using --remote-tab on MS-Windows 'encoding' hasn't been initialized yet,
the file name ends up encoded wrong. (Raul Coronado, 2015 Dec 21)

Example in editing.txt uses $HOME with the expectation that it ends in a
slash.  For me it does, but perhaps not for everybody.  Add a function that
inserts a slash when needed?  pathconcat(dir, path) (Thilo Six, 2015 Aug 12)

ml_updatechunk() is slow when retrying for another encoding. (John Little,
2014 Sep 11)

Patch to fix checking global option value when not using it.
(Arnaud Decara, 2015 Jul 23)

When 'showbreak' is set repeating a Visual operation counts the size of the
'showbreak' text as part of the operation. (Axel Bender, 2015 Jul 20)

Patch to add grepfile(). (Scott Prager, 2015 May 26)
Work in progress.

Would be useful to have a treemap() or deepmap() function.  Like map() but
when an item is a list or dict would recurse into it.

Patch for global-local options consistency. (Arnaud Decara, 2015 Jul 22)
Is this right?

Patch to make getregtype() return the right size for non-linux systems.
(Yasuhiro Matsumoto, 2014 Jul 8)
Breaks test_eval.  Inefficient, can we only compute y_width when needed?

Patch to use different terminal mode settings for system(). (Hayaki Saito)
Does this work for everybody?

Patch for man.vim. (SungHyun Nam, 2015 May 20)
Doesn't work completely (Dominique Orban)

Patch to add a "literal" argument to bufnr().  (Olaf Dabrunz, 2015 Aug 4)

Extended file attributes lost on write (backupcopy=no).  Issue 306.
Would require reading attributes from the original file with listxattr() and
getxattr() and adding them to the new file.

Patch to add :lockjumps. (Carlo Baldassi, 2015 May 25)
OK to not block marks?

Patch on Issue 72: 'autochdir' causes problems for :vimgrep.

When two SIGWINCH arrive very quickly, the second one may be lost.
(Josh Triplett, 2015 Sep 17)

Make comments in the test Makefile silent. (Kartik Agaram, 2014 Sep 24)

Result of systemlist() does not show whether text ended in line break.
(Bjorn Linse, 2014 Nov 27)

When in 'comments' "n:x" follows after three-part comment directly it repeats
any one-character from the previous line. (Kartik Agaram, 2014 Sep 19)

Patch: Let rare word highlighting overrule good word highlighting.
(Jakson A. Aquino, 2010 Jul 30, again 2011 Jul 2)

Patch to add digits argument to round(). (Yasuhiro Matsumoto, 2015 Apr 26)

Can assign to s:type when a function s:type has been defined.
Also the other way around: define a function while a variable with that name
was already defined.
(Yasuhiro Matsumoto, 2014 Nov 3)

Patch for ordered dict. (Ozaki Kiichi, 2015 May 7)

Patch for building a 32bit Vim with 64bit MingW compiler.
(Michael Soyka, 2014 Oct 15)

Patch: On MS-Windows shellescape() may have to triple double quotes.
(Ingo Karkat, 2015 Jan 16)

Patch for glob(), adding slash to normal files. (Ingo Karkat, 2014 Dec 22)

When entering and leaving the preview window autocommands are triggered, but
these may not work well.  Perhaps set a flag to indicate that the preview
window is involved? (John Otter, 2015 Oct 27)

Using "." to repeat an Ex command puts that command in history.  Probably
should not happen.  If the command is the result of a mapping it's not put in
history either. (Jacob Niehus, 2014 Nov 2)
Patch from Jacob, Nov 2.

"hi link" does not respect groups with GUI settings only. (Mark Lodato, 2014
Jun 8)

Bug: Autocompleting ":tag/pat" replaces "/pat" with a match but does not
insert a space. (Micha Mos, 2014 Nov 7)

No error for missing endwhile. (ZyX, 2014 Mar 20)

Patch to make extend() fail early when it might fail at some point.
(Olaf Dabrunz, 2015 May 2)  Makes extend() slower, do we still want it?
Perhaps only the checks that can be done without looping over the dict or
arguments.

Problem with transparent and matchgroup.  Issue #475

Idea: For a window in the middle (has window above and below it), use
right-mouse-drag on the status line to move a window up/down without changing
its height?  It's like dragging the status bar above it at the same time.

Patch to add a :domodeline command. (Christian Brabandt, 2014 Oct 21)

This does not give an error: (Andre Sihera, 2014 Mar 21)
    vim -u NONE 1 2 3 -c 'bufdo if 1 | echo 1'
This neither: (ZyX)
    vim -u NONE 1 2 3 -c 'bufdo while 1 | echo 1'

'viewdir' default on MS-Windows is not a good choice, it's a system directory.
Change 'viewdir' to "$HOME/vimfiles/view" and use 'viewdiralt' to also read
from?

Include a plugin manager with Vim? vim-plug seems to be the best currently:
https://github.com/junegunn/vim-plug.
Also Vundle: https://github.com/gmarik/vundle
Or minpac: https://github.com/k-takata/minpac, since it leverages the builtin
package feature.
Long message about this from ZyX, 2014 Mar 23.  And following replies.
Also see (link seems dead):
http://vim-wiki.mawercer.de/wiki/topic/vim%20plugin%20managment.html
User view:
- Support multiple sources, basically any http:// URL. Or a central place that
  will work for everybody (github?  redirects from vim.org?).
  Be able to look into the files before deciding to install.
- Be able to try out a plugin and remove it again with (almost) no traces.
- Each plugin needs a "manifest" file that has the version, dependencies
  (including Vim version and features), conflicts, list of files, etc.
  Updater uses that to decide what/how to update.
  Dependencies can use a URL for specific versions, or short name for scripts
  on vim.org.
- Once a plugin is installed it remembers where it came from, updater checks
  there.  Can manually update when really needed.
- Must be possible to install for one user.  Also system wide?
- Can edit plugin config with Vim. Can temporarily disable a plugin.
- Run the update manually, find latest version and install.
- Be able to download without special tools, must work for 95% of users.
Implementation:
- Avoid the 'runtimepath' getting long.  Need some other way to keep each
  plugin separate.
- When installing or updating, first figure out what needs to be done. This
  may involve recursively fetching manifest files for dependencies.  Then show
  the user what's going to change and ask for OK.
- Scripts on Vim.org must be able to consist of several files.  Is zip format
  sufficient?  Upload the manifest?  Or refer to a site that has the manifest?
- Best is to fetch individual files or use a Vimball. Reduces dependency on
  tools that might be missing and allows inspection of the files before
  installing.
Out of scope:
- Overview of plugins, ratings, comments, etc.  That's another world.
- Development work on plugins (although diff with distributed version would be
  useful).

When typing the first character of a command, e.g. "f", then using a menu, the
menu item doesn't work.  Clear typeahead when using a menu?

Editing an ascii file as ucs-2 or ucs-4 causes display errors.
(ZyX, 2014 Mar 30)

":Next 1 some-arg" does not complain about trailing argument.  Also for
various other commands.  (ZyX, 2014 Mar 30)

Patch to skip sort if no line matches the expression.
(Christian Brabandt, 2014 Jun 25)

VMS: Select() doesn't work properly, typing ESC may hang Vim.  Use sys$qiow
instead. (Samuel Ferencik, 2013 Sep 28)

Patch for XDG base directory support. (Jean François Bignolles, 2014 Mar 4)
Remark on the docs.  Should not be a compile time feature.  But then what?
Also see #2034.

Patch to define macros for hardcoded values. (Elias Diem, 2013 Dec 14)

Updated spec ftplugin. (Matěj Cepl, 2013 Oct 16)

Patch to handle integer overflow. (Aaron Burrow, 2013 Dec 12)

7   Windows XP: When using "ClearType" for text smoothing, a column of yellow
    pixels remains when typing spaces in front of a "D" ('guifont' set to
    "lucida_console:h8").
Patch by Thomas Tuegel, also for GTK, 2013 Nov 24

:help gives example for z?, but it does not work.  m? and t? do work.

Discussion about canonicalization of Hebrew. (Ron Aaron, 2011 April 10)

Checking runtime scripts: Thilo Six, 2012 Jun 6.

When evaluating expression in backticks, autoload doesn't work.
(Andy Wokula, 2013 Dec 14)

Using <nr>ifoobar<esc> can slow down Vim.  Patch by Christian Brabandt, 2013
Dec 13.  Only helps a bit, 10000ii<Esc> is still too slow.

Javascript file where indent gets stuck on: GalaxyMaster, 2012 May 3.

The BufUnload event is triggered when re-using the empty buffer.
(Pokey Rule, 2013 Jul 22)
Patch by Marcin Szamotulski, 2013 Jul 22.

Patch to allow more types in remote_expr(). (Lech Lorens, 2014 Jan 5)
Doesn't work for string in list.  Other way to pass all types of variables
reliably?

Patch to add {lhs} to :mapclear: clear all maps starting with {lhs}.
(Christian Brabandt, 2013 Dec 9)

Exception caused by argument of return is not caught by try/catch.
(David Barnett, 2013 Nov 19)
Bug in try/catch: return with invalid compare throws error that isn't caught.
(ZyX, 2011 Jan 26)
try/catch not working for argument of return. (Matt Wozniski, 2008 Sep 15)
try/catch not working when inside a for loop. (ZyX, 2011 Jan 25)

Patch to fix that 'cedit' is recognized after :normal. (Christian Brabandt,
2013 Mar 19, later message)

Patch to view coverage of the tests. (Nazri Ramliy, 2013 Feb 15)

Patch to add "Q" and "A" responses to interactive :substitute. They are
carried over when using :global. (Christian Brabandt, 2013 Jun 19)

Bug with 'cursorline' in diff mode.  Line being scrolled into view gets
highlighted as the cursor line.  (Alessandro Ivaldi, 2013 Jun 4)

Two highlighting bugs. (ZyX, 2013 Aug 18)

Patch to support 'u' in interactive substitute. (Christian Brabandt, 2012 Sep
28)  With tests: Oct 9.

Dialog is too big on Linux too. (David Fishburn, 2013 Sep 2)

-   Add regex for 'paragraphs' and 'sections': 'parare' and 'sectre'.  Combine
    the two into a regex for searching. (Ned Konz)
Patch by Christian Brabandt, 2013 Apr 20, unfinished.

Bug: findfile("any", "file:///tmp;") does not work.

Patch to add getsid(). (Tyru, 2011 Oct 2)  Do we want this?  Update Oct 4.
Or use expand('<sid>')?

Patch to make confirm() display colors. (Christian Brabandt, 2012 Nov 9)

Problem with refresh:always in completion. (Tyler Wade, 2013 Mar 17)

b:undo_ftplugin cannot call a script-local function. (Boris Danilov, 2013 Jan
7)

Patch for :tabcloseleft, after closing a tab go to left tab. (William Bowers,
2012 Aug 4)

Patch to improve equivalence classes in regexp patterns.
(Christian Brabandt, 2013 Jan 16, update Jan 17)

Patch with suggestions for starting.txt. (Tony Mechelynck, 2012 Oct 24)
But use Gnome instead of GTK?

Should be possible to enable/disable matchparen per window or buffer.
Add a check for b:no_match_paren in Highlight_matching_Pair() (Marcin
Szamotulski, 2012 Nov 8)

'iminsert' global value set when using ":setlocal iminsert"? (Wu, 2012 Jun 23)

Patch to append regexp to tag commands to make it possible to select one out
of many matches. (Cody Cutler, 2013 Mar 28)

The input map for CTRL-O in mswin.vim causes problems after CTRL-X CTRL-O.
Suggestion for another map. (Philip Mat, 2012 Jun 18)
But use "gi" instead of "a".  Or use CTRL-\ CTRL-O.

When there are no command line arguments ":next" and ":argu" give E163, which
is confusing.  Should say "the argument list is empty".

URXVT:
- will get stuck if byte sequence does not contain the expected semicolon.
- Use urxvt mouse support also in xterm.  Explanations:
  http://www.midnight-commander.org/ticket/2662

Patch to add tests for if_xcmdsrv.c., Jul 8, need some more work. (Brian Burns)
New tests Jul 13.   Update Jul 17.  Discussion Jul 18.

Patch for input method status. (Hirohito Higashi, 2012 Apr 18)

Update Vim app icon (for Gnome). (Jakub Steiner, 2013 Dec 6)

Patch to use .png icons for the toolbar on MS-Windows. (Martin Gieseking, 2013
Apr 18)

Patch for has('unnamedplus') docs. (Tony Mechelynck, 2011 Sep 27)
And one for gui_x11.txt.

":cd" doesn't work when current directory path contains "**".
finddir() has the same problem.  (Yukihiro Nakadaira, 2012 Jan 10)
Requires a rewrite of the file_file_in_path code.

Should use has("browsefilter") in ftplugins.  Requires patch 7.3.593.

Update for vim2html.pl. (Tyru, 2013 Feb 22)

Patch to sort functions starting with '<' after others.  Omit dict functions,
they can't be called. (Yasuhiro Matsumoto, 2011 Oct 11)

Patch to pass list to or(), and() and xor(). (Yasuhiro Matsumoto, 2012 Feb 8)

Patch to improve "it" and "at" text object matching. (Christian Brabandt, 2011
Nov 20)

Patch to improve GUI find/replace dialog. (Christian Brabandt, 2012 May 26)
Update Jun 2.

`] moves to character after insert, instead of the last inserted character.
(Yukihiro Nakadaira, 2011 Dec 9)

Plugin for Modeleasy. (Massimiliano Tripoli, 2011 Nov 29)

BufWinLeave triggers too late when quitting last window in a tab page. (Lech
Lorens, 2012 Feb 21)

Patch for 'transparency' option. (Sergiu Dotenco, 2011 Sep 17)
Only for MS-Windows.  No documentation.  Do we want this?

Patch to support cursor shape in Cygwin console. (Ben bgold, 2011 Dec 27)

On MS-Windows a temp dir with a & init causes system() to fail. (Ben Fritz,
2012 Jun 19)

'cursorline' is displayed too short when there are concealed characters and
'list' is set.  (Dennis Preiser)
Patch 7.3.116 was the wrong solution.
Christian Brabandt has another incomplete patch. (2011 Jul 13)

Win32: Patch to use task dialogs when available. (Sergiu Dotenco, 2011 Sep 17)
New feature, requires testing.  Made some remarks.

Win32: Patch for alpha-blended icons and toolbar height. (Sergiu Dotenco, 2011
Sep 17)  Asked for feedback from others.

Win32: Cannot cd into a directory that starts with a space. (Andy Wokula, 2012
Jan 19)

Need to escape $HOME on Windows for fnameescape()?  (ZyX, 2011 Jul 21,
discussion 2013 Jul 4) Can't simply use a backslash, \$HOME has a different
meaning already.  Would be possible to use $$HOME where $HOME is to be used.

"2" in 'formatoptions' not working in comments. (Christian Corneliussen, 2011
Oct 26)

Bug in repeating Visual "u". (Lawrence Kesteloot, 2010 Dec 20)

Windows keys not set properly on Windows 7?  (cncyber, 2010 Aug 26)

When using a Vim server, a # in the path causes an error message.
(Jeff Lanzarotta, 2011 Feb 17)

When there is a ">" in a line that "gq" wraps to the start of the next line,
then the following line will pick it up as a leader.  Should get the leader
from the first line, not a wrapped line. (Matt Ackeret, 2012 Feb 27)

Using ":break" or something else that stops executing commands inside a
":finally" does not rethrow a previously uncaught exception. (ZyX, 2010 Oct
15)

Vim using lots of memory when joining lines. (John Little, 2010 Dec 3)

BT regexp engine: After trying a \@> match and failing, submatches are not
cleared.  See test64.

On 64 bit MS-Windows "long" is only 32 bits, but we sometimes need to store a
64 bits value.  Change all number options to use nropt_T and define it to the
right type.

string() can't parse back "inf" and "nan".  Fix documentation or fix code?
(ZyX, 2010 Aug 23)

When doing "redir => s:foo" in a script and then "redir END" somewhere else
(e.g. in a function) it can't find s:foo.
When a script contains "redir => s:foo" but doesn't end redirection, a
following "redir" command gives an error for not being able to access s:foo.
(ZyX, 2011 Mar 27)

When setqflist() uses a filename that triggers a BufReadCmd autocommand Vim
doesn't jump to the correct line with :cfirst. (ZyX,  2011 Sep 18)

Behavior of i" and a" text objects isn't logical. (Ben Fritz, 2013 Nov 19)

When setting a local option value from the global value, add a script ID that
indicates this, so that ":verbose set" can give a hint.  Check with options in
the help file.

After patch 7.3.097 still get E15. (Yukihiro Nakadaira, 2011 Jan 18)
Also for another example (ZyX, 2011 Jan 24)

"0g@$" puts '] on last byte of multibyte. (ZyX, 2011 Jan 22)

Patch for :tabrecently. (Hirokazu Yoshida, 2012 Jan 30)

Problem with "syn sync grouphere". (Gustavo Niemeyer, 2011 Jan 27)

Loading autoload script even when usage is inside "if 0". (Christian Brabandt,
2010 Dec 18)

With a filler line in diff mode, it isn't displayed in the column with line
number, but it is in the sign column.  Doesn't look right. (ZyX 2011 Jun 5)
Patch by Christian Brabandt, 2011 Jun 5.  Introduces new problems.

Add jump() function. (Marcin Szamotulski, 2013 Aug 29)
Is this needed?  CTRL-O and CTRL-I do the same, just more difficult to use.

8   Add a command to jump to the next character highlighted with "Error".
Patch by Christian Brabandt, uses ]e [e ]t and [t. 2011 Aug 9.

Add event for when the text scrolls.  A bit like CursorMoved.  Also a similar
one for insert mode.  Use the event in matchparen to update the highlight if
the match scrolls into view.

7   Use "++--", "+++--" for different levels instead of "+---" "+----".
Patch by Christian Brabandt, 2011 Jul 27.
Update by Ben Fritz, with fix for TOhtml. (2011 Jul 30)

9   Add %F to 'errorformat': file name without spaces.  Useful on Unix to
    avoid matching something up to a time 11:22:33.
Patch by Christian Brabandt, 2011 Jul 27.

Patch to add up to 99 match groups. (Christian Brabandt, 2010 Dec 22)
Also add named groups: \%{name}(re)  and \%{name}g

In the sandbox it's not allowed to do many things, but it's possible to change
or set variables.  Add a way to prevent variables from being changed in the
sandbox?  E.g.: ":protect g:restore_settings".

Win32: tear-off menu does not work when menu language is German. (Markus
Bossler, 2011 Mar 2)  Fixed by 7.3.095?

Wish for NetBeans commands:
    - make it possible to have 'defineAnnoType' also handle terminal colors.

7.3.014 changed how backslash at end of line works, but still get a NUL when
there is one backslash. (Ray Frush, 2010 Nov 18)  What does the original ex
do?

New esperanto spell file can't be processed. (Dominique Pelle, 2011 Jan 30)
- move compflags to separate growarray?
- instead of a regexp use a hashtable.  Expand '?', '*', '+'.  What would be
  the maximum repeat for * and +?

"L'Italie" noted as a spell error at start of the sentence. (Dominique Pelle,
2011 Feb 27)

Editing a file with a ^M with 'ff' set to "mac", opening a help file, then the
^M is displayed as ^J sometimes.  Getting 'ff' value from wrong window/buffer?

When Vim is put in the background (SIGTSTP) and then gets a SIGHUP it doesn't
exit.  It exists as soon as back in the foreground. (Stephen Liang, 2011 Jan
9)  Caused by vim_handle_signal(SIGNAL_BLOCK); in ui.c.

g` not working correctly when using :edit.  It works OK when editing a file on
the command line. (Ingo Karkat, 2011 Jan 25)

Since patch 7.2.46 Yankring plugin has become very slow, eventually make Vim
crash? (Raiwil, 2010 Nov 17)

Regexp engine performance:
- Profiling:
	./vim -u NONE -s ~/vim/test/ruby.vim
	./vim -u NONE -s ~/vim/test/loop.vim
	./vim -u NONE -s ~/vim/test/alsa.vim
	./vim -s ~/vim/test/todo.vim
	./vim -s ~/vim/test/xml.vim
    Dominique Pelle:  xmlSyncDT is particularly slow (Jun 7)
- More test files from the src/pkg/regexp/testdata directory in the Go repo.
- Performance tests:
  - Using asciidoc syntax. (Marek Schimara, 2013 Jun 6)
  - ~/vim/text/FeiqCfg.xml (file from Netjune)
  - ~/vim/text/edl.svg  (also XML)
  - glts has five tests. (May 25)
  - ~/vim/test/slowsearch
  - ~/vim/test/rgb.vim
  - search for  a.*e*exn  in the vim executable.  Go to last line to use
    'hlsearch'.
  - Slow combination of folding and PHP syntax highlighting.  Script to
    reproduce it.  Caused by "syntax sync fromstart" in combination with patch
    7.2.274.  (Christian Brabandt, 2010 May 27) Generally, folding with
    'foldmethod' set to "syntax" is slow.  Do profiling to find out why.

Problem producing tags file when hebrew.frx is present.  It has a BOM.
Results in E670. (Tony Mechelynck, 2010 May 2)

'beval' option should be global-local.

Ruby: ":ruby print $buffer.number" returns zero.

setpos() does not restore cursor position after :normal. (Tyru, 2010 Aug 11)

With "tw=55 fo+=a" typing space before ) doesn't work well. (Scott Mcdermott,
2010 Oct 24)

Messages in message.txt are highlighted as examples.

When using cp850 the NBSP (0xff) is not drawn correctly. (Brett Stahlman, 2010
Oct 22)  'isprint' is set to "@,161-255".

":echo "\x85" =~# '[\u0085]'" returns 1 instead of 0. (ZyX, 2010 Oct 3)

'cindent' not correct when 'list' is set. (Zdravi Korusef, 2010 Apr 15)

C-indenting: A matching { in a comment is ignored, but intermediate { are not
checked to be in a comment.  Implement FM_SKIPCOMM flag of findmatchlimit().
Issue 46.

Using CompilerSet doesn't record where an option was set from.  E.g., in the
gcc compiler plugin. (Gary Johnson, 2010 Dec 13)

":helpgrep" does not put the cursor in the correct column when preceded by
accented character. (Tony Mechelynck, 2010 Apr 15)

Don't call check_restricted() for histadd(), setbufvar(), settabvar(),
setwinvar().

Patch for gVimExt to show an icon. (Dominik Riebeling, 2010 Nov 7)

When 'lines' is 25 and 'scrolloff' is 12, "j" scrolls zero or two lines
instead of one. (Constantin Pan, 2010 Sep 10)

Gui menu edit/paste in block mode insert only inserts in one line (Bjorn
Winckler, 2011 May 11)
Requires a map mode for Insert mode started from blockwise Visual mode.

Problem with cursor in the wrong column. (SungHyun Nam, 2010 Mar 11)
Additional info by Dominique Pelle. (also on 2010 Apr 10)

CreateFile and CreateFileW are used without sharing, filewritable() fails when
the file was already open (e.g. script is being sourced).  Add FILE_SHARE_READ|
FILE_SHARE_WRITE in mch_access()? (Philippe Vaucher, 2010 Nov 2)

Is ~/bin (literally) in $PATH supposed to work?  (Paul, 2010 March 29)
Looks like only bash can do it. (Yakov Lerner)

Cscope "cs add" stopped working somewhat before 7.2.438. (Gary Johnson, 2010
Jun 29)  Caused by 7.2.433?

Jumplist doesn't work properly in Insert mode? (Jean Johner, 2010 Mar 20)

Problem with transparent cmdline.  Also: Terminal title is wrong with
non-ASCII character. (Lily White, 2010 Mar 7)

iconv() doesn't fail on an illegal character, as documented. (Yongwei Wu, 2009
Nov 15, example Nov 26)  Add argument to specify whether iconv() should fail
or replace with a character and continue?

Add local time at start of --startuptime output.
Requires configure check for localtime().
Use format year-month-day hr:min:sec.

Patch to make ":hi link" also take arguments. (Nate Soares, 2012 Dec 4)

Shell not recognized properly if it ends in "csh -f". (James Vega, 2009 Nov 3)
Find tail?  Might have a / in argument.  Find space?  Might have space in
path.

Test 51 fails when language set to German. (Marco, 2011 Jan 9)
Dominique can't reproduce it.

'ambiwidth' should be global-local.

":function f(x) keepjumps" creates a function where every command is executed
like it has ":keepjumps" before it.

Coverity: Check if there are new reported defects:
https://scan.coverity.com/projects/241

Problem with editing file in binary mode. (Ingo Krabbe, 2009 Oct 8)

Display error when 'tabline' that includes a file name with double-width
characters. (2010 Aug 14, bootleq)

Problem with stop directory in findfile(). (Adam Simpkins, 2009 Aug 26)

Using ']' as the end of a range in a pattern requires double escaping:
     /[@-\\]]  (Andy Wokula, 2011 Jun 28)

For running gvim on a USB stick: avoid the OLE registration.  Use a command
line argument -noregister.

When using an expression in 'statusline' leading white space sometimes goes
missing (but not always). (ZyX, 2010 Nov 1)

When a mapping exists both for insert mode and lang-insert mode, the last one
doesn't work. (Tyru, 2010 May 6)  Or is this intended?

Still a problem with ":make" in the wrong directory.  Caused by ":bufdo".
(Ajit Thakkar, 2009 Jul 1) More information Jul 9, Jul 15.
Caused by "doautoall syntaxset BufEnter *" in syntax/nosyntax.vim ?
There also is a BufLeave/BufEnter aucmd to save/restore view.
Does the patch to save/restore globaldir work?

":bufdo normal gg" while 'hidden' is set leaves buffers without syntax
highlighting.  Don't disable Syntax autocommands then?  Or add a flag/modifier
to avoid changing 'eventignore'?

Patch for displaying 0x200c and 0x200d. (Ali Gholami Rudi, 2009 May 6)
Probably needs a bit of work.

List of encoding aliases. (Takao Fujiwara, 2009 Jul 18)
Are they all OK?  Update Jul 22.

Win32: Improved Makefile for MSVC. (Leonardo Valeri Manera, 2010 Aug 18)

Win32: Expanding 'path' runs into a maximum size limit. (bgold12, 2009 Nov 15)

Win32: Patch for using .png files for icons. (Charles Peacech, 2012 Feb 5)

Putting a Visual block while 'visualedit' is "all" does not leave the cursor
on the first character. (John Beckett, 2010 Aug 7)

Setting 'tags' to "tagsdir/*" does not find "tagsdir/tags". (Steven K. Wong,
2009 Jul 18)

Patch to add "focusonly" to 'scrollopt', so that scrollbind also applies in
window that doesn't have focus. (Jonathon Mah, 2009 Jan 12)
Needs more work.

Problem with <script> mappings (Andy Wokula, 2009 Mar 8)

When starting Vim with "gvim -f -u non_existent_file > foo.txt" there are a
few control characters in the output. (Dale Wiles, 2009 May 28)

'cmdwinheight' is only used in last window when 'winheight' is a large value.
(Tony Mechelynck, 2009 Apr 15)

Status line containing winnr() isn't updated when splitting the window (Clark
J.  Wang, 2009 Mar 31)

When $VIMRUNTIME is set in .vimrc, need to reload lang files.  Already done
for GTK, how about others? (Ron Aaron, 2010 Apr 10)

":tab split fname" doesn't set the alternate file in the original window,
because win_valid() always returns FALSE.  Below win_new_tabpage() in
ex_docmd.c.

Space before comma in function definition not allowed: "function x(a , b)"
Give a more appropriate error message.  Add a remark to the docs.

string_convert() should be able to convert between utf-8 and utf-16le.  Used
for GTK clipboard.  Avoid requirement for iconv.

Now that colnr_T is int instead of unsigned, more type casts can be removed.

Don't load macmap.vim on startup, turn it into a plugin. (Ron Aaron,
2009 Apr 7)  Reminder Apr 14.

Add "no_hlsearch" to winsaveview().

Bug: When reloading a buffer changed outside of Vim, BufRead autocommands
are applied to the wrong buffer/window. (Ben Fritz, 2009 Apr 2, May 11)
Ignore window options when not in the right window?
Perhaps we need to use a hidden window for applying autocommands to a buffer
that doesn't have a window.

When using "ab foo bar" and mapping <Tab> to <Esc>, pressing <Tab> after foo
doesn't trigger the abbreviation like <Esc> would. (Ramana Kumar, 2009 Sep 6)

getbufvar() to get a window-local option value for a buffer that's not
displayed in a window should return the value that's stored for that buffer.

":he ctrl_u" can be auto-corrected to ":he ctrl-u".

Diff mode out of sync. (Gary Johnson, 2010 Aug 4)

Win32: completion of file name ":e c:\!test" results in ":e c:\\!test", which
does not work. (Nieko Maatjes, 2009 Jan 8, Ingo Karkat, 2009 Jan 22)

Using ~ works OK on 'a' with composing char, but not on 0x0418  with composing
char 0x0301. (Tony Mechelynck, 2009 Mar 4)

This does not work yet: "a\(%C\)" (get composing characters into a submatch).

Inconsistent: starting with $LANG set to es_ES.utf-8 gives Spanish
messages, even though locale is not supported.  But ":lang messages
es_ES.utf-8" gives an error and doesn't switch messages. (Dominique Pelle,
2009 Jan 26)

When $HOME contains special characters, such as a comma, escape them when used
in an option. (Michael Hordijk, 2009 May 5)
Turn "esc" argument of expand_env_esc() into string of chars to be escaped.

Should make 'ignorecase' global-local, so that it makes sense setting it from
a modeline.

Add cscope target to Makefile. (Tony Mechelynck, 2009 Jun 18, replies by
Sergey Khorev)

Completion for ":buf" doesn't work properly on Win32 when 'shellslash' is off.
(Henrik Ohman, 2009, Jan 29)

shellescape() depends on 'shellslash' for quoting.  That doesn't work when
'shellslash' is set but using cmd.exe. (Ben Fritz)
Use a different option or let it depend on whether 'shell' looks like a
unix-like shell?

Bug: in Ex mode (after "Q") backslash before line break, when yanked into a
register and executed, results in <Nul>: instead of line break.
(Konrad Schwarz, 2010 Apr 16)

Have a look at patch for utf-8 line breaking. (Yongwei Wu, 2008 Mar 1, Mar 23)
Now at: http://vimgadgets.sourceforge.net/liblinebreak/

Greek sigma character should be lower cased depending on the context.  Can we
make this work?  (Dominique Pelle, 2009 Sep 24)

When changing 'encoding' convert all the swap file names, so that we can
still delete them.  Also convert all buffer file names?

"gqip" in Insert mode has an off-by-one error, causing it to reflow text.
(Raul Coronado, 2009 Nov 2)

MS-Windows: editing the first, empty buffer, 'ffs' set to "unix,dos", ":enew"
doesn't set 'ff' to "unix".  (Ben Fritz, 2008 Dec 5) Reusing the old buffer
probably causes this.

'scrollbind' is not respected when deleting lines or undo. (Milan Vancura,
2009 Jan 16)

Having "Syntax" in 'eventignore' for :bufdo may cause problems, e.g. for
":bufdo e" when buffers are open in windows.  ex_listdo(eap) could set the
option only for when jumping to another buffer, not when the command argument
is executed.

":pedit %" with a BufReadPre autocommand causes the cursor to move to the
first line. (Ingo Karkat, 2008 Jul 1)  Ian Kelling is working on this.
Similar problem with ":e". (Marc Montu, 2014 Apr 22)

Cursor line moves in other window when using CTRL-W J that doesn't change
anything.  (Dasn, 2009 Apr 7)

On Unix "glob('does not exist~')" returns the string.  Without the "~" it
doesn't. (John Little, 2008 Nov 9)
Shell expansion returns unexpanded string?
Don't use shell when "~" is not at the start?

When using ":e ++enc=foo file" and the file is already loaded with
'fileencoding' set to "bar", then do_ecmd() uses that buffer, even though the
fileencoding differs.  Reload the buffer in this situation?  Need to check for
the buffer to be unmodified.
Unfinished patch by Ian Kelling, 2008 Jul 11.  Followup Jul 14, need to have
another look at it.

c.vim: XXX in a comment is colored yellow, but not when it's after "#if 0".
(Ilya Dogolazky, 2009 Aug 7)

You can type ":w ++bad=x fname", but the ++bad argument is ignored.  Give an
error message?  Or is this easy to implement?  (Nathan Stratton Treadway, 2008
Aug 20)  This is in ucs2bytes(), search for 0xBF.  Using the ++bad argument is
at the other match for 0xBF.

When adding "-complete=file" to a user command this also changes how the
argument is processed for <f-args>. (Ivan Tishchenko, 2008 Aug 19)

Win32: associating a type with Vim doesn't take care of space after a
backslash? (Robert Vibrant, 2008 Jun 5)

When 'rightleft' is set, cursorcolumn isn't highlighted after the end of a
line.  It's also wrong in folds. (Dominique Pelle, 2010 Aug 21)

":help s/~" jumps to *s/\~*, while ":help s/\~" doesn't find anything. (Tim
Chase)  Fix by Ian Kelling, 2008 Jul 14.

When mapping : to ; and ; to :, @; doesn't work like @: and @: doesn't work
either.  Matt Wozniski:  nv_at() calls do_execreg() which uses
put_in_typebuf().  Char mapped twice?

Despite adding save_subexpr() this still doesn't work properly:
Regexp: matchlist('12a4aaa', '^\(.\{-}\)\(\%5c\@<=a\+\)\(.\+\)\?')
Returns ['12a4', 'aaa', '4aaa'], should be ['12a4', 'aaa', '']
Backreference not cleared when retrying after \@<= fails?
(Brett Stahlman, 2008 March 8)

Problem with remote_send(). (Charles Campbell, 2008 Aug 12)

ftplugin for help file should set 'isk' to help file value.

Win32: remote editing fails when the current directory name contains "[".
(Ivan Tishchenko, Liu Yubao)  Suggested patch by Chris Lubinski: Avoid
escaping characters where the backslash is not removed later.  Asked Chris for
an alternate solution, also for src/ex_getln.c.
This also fails when the file or directory name contains "%". (Thoml, 2008
July 7)
Using --remote-silent while the current directory has a # in the name does not
work, the # needs to be escaped. (Tramblay Bruno, 2012 Sep 15)

When using remote-silent the -R flag is not passed on. (Axel Bender, 2012 May
31)

Win32: A --remote command that has a directory name starting with a ( doesn't
work, the backslash is removed, assuming that it escapes the (. (Valery
Kondakoff, 2009 May 13)

Win32: Using "gvim --remote-tab-silent elŝuti.txt" doesn't work, the
multibyte character isn't passed and edits elsuti.txt.
(Raúl Núñez de Arenas Coronado, 2015 Dec 18)

Problem with CTRL-F. (Charles Campbell, 2008 March 21)
Only happens with "gvim -geometry "160x26+4+27" -u NONE -U NONE prop.c".
'lines' is 54. (2008 March 27)

Problem with pointer wrapping around in getvcol(). (Wolfgang Kroworsch, 2008
Oct 19)  Check for "col" being "MAXCOL" separately?

Unexpectedly inserting a double quote. (Anton Woellert, 2008 Mar 23)
Works OK when 'cmdheight' is 2.

8   Use a mechanism similar to omni completion to figure out the kind of tab
    for CTRL-] and jump to the appropriate matching tag (if there are
    several).

The utf class table is missing some entries:
	0x2212, minus sign
	0x2217, star
	0x2500, bar
	0x26ab, circle

Visual line mode doesn't highlight properly when 'showbreak' is used and the
line doesn't fit. (Dasn, 2008 May 1)

GUI: In Normal mode can't yank the modeless selection.  Make "gy" do this?
Works like CTRL-Y in Command line mode.

C't: On utf-8 system, editing file with umlaut through Gnome results in URL
with %nn%nn, which is taken as two characters instead of one.
Try to reproduce at work.

Patch for default choice in file changed dialog. (Bjorn Winckler, 2008 Oct 19)
Is there a way to list all the files first?

Fail to edit file after failed register access.  Error flag remains set?
(Lech Lorens, 2010 Aug 30)

Problem with 'ts' set to 9 and 'showbreak' to ">>>". (Matthew Winn, 2007 Oct
1)

":tab help" always opens a new tab, while ":help" re-uses an existing window.
Would be more consistent when an existing tab is re-used. (Tony Mechelynck)

Using Aap to build Vim: add remarks about how to set personal preferences.
Example on http://www.calmar.ws/tmp/aap.html (link seems dead)

When 'diffopt' has "context:0" a single deleted line causes two folds to merge
and mess up syncing. (Austin Jennings, 2008 Jan 31)

Gnome improvements: Edward Catmur, 2007 Jan 7
    Also use Save/Discard for other GUIs

New PHP syntax file, use it? (Peter Hodge)

":echoe" in catch block stops processing, while this doesn't happen outside of
a catch block. (ZyX, 2011 Jun 2)

Test 54 uses shell commands, that doesn't work on non-Unix systems.  Use some
other way to test buffer-local autocommands.

The documentation mentions the priority for ":2match" and ":3match", but it
appears the last one wins. (John Beckett, 2008 Jul 22)  Caused by adding
matchadd()?  Suggested patch by John, 2008 Jul 24.

When 'encoding' is utf-8 the command line is redrawn as a whole on every
character typed. (Tyler Spivey, 2008 Sep 3)  Only redraw cmdline for
'arabicshape' when there is a character on the command line for which
(ARABIC_CHAR(u8c)) is TRUE.

Cheng Fang made javacomplete. (2007 Aug 11)
Asked about latest version: 0.77.1 is on www.vim.org.

More AmigaOS4 patches. (Peter Bengtsson, Nov 9)

Amiga patches with vbcc. (Adrien Destugues, 2010 Aug 30)
(link seems dead): http://pulkomandy.ath.cx/drop/vim73_vbcc_amiga.diff

Problem with compound words?  (Bert, 2008 May 6)
No warning for when flags are defined after they are used in an affix.

Screen redrawing when continuously updating the buffer and resizing the
terminal. (Yakov Lerner, 2006 Sept 7)

Add option settings to help ftplugin. (David Eggum, 2006 Dec 18)

Autoconf problem: when checking for iconv library we may add -L/usr/local/lib,
but when compiling further tests -liconv is added without the -L argument,
that may fail (e.g., sizeof(int)). (Blaine, 2007 Aug 21)

Problem with ".add" files when using two languages and restarting Vim. (Raul
Coronado, 2008 Oct 30)

Popup menu redraw: Instead of first redrawing the text and then drawing the
popup menu over it, first draw the new popup menu, remember its position and
size and then redraw the text, skipping the characters under the popup menu.
This should avoid flicker.  Other solution by A.Politz, 2007 Aug 22.

When the file name has parenthesis, e.g., "foo (bar).txt", ":!ls '%'" has the
parenthesis escaped but not the space.  That's inconsistent.  Either escape
neither or both.  No escaping might be best, because it doesn't depend on
particularities of the shell. (Zvi Har'El, 2007 Nov 10) (Teemu Likonen, 2008
Jun 3)
However, for backwards compatibility escaping might be necessary.  Check if
the user put quotes around the expanded item?

A throw in a function causes missing an endif below the call. (Spiros
Bousbouras, 2011 May 16)

Error E324 can be given when a cron script has wiped out our temp directory.
Give a clear error message about this (and tell them not to wipe out /tmp).

Color for cUserLabel should differ from case label, so that a mistake in a
switch list is noticed:
    switch (i)
    {
    case 1:
    foobar:
    }

Look at http://www.gtk-server.org/ .  It has a Vim script implementation.

Netbeans problem.  Use "nc -l 127.0.0.1 55555" for the server, then run gvim
with "gvim -nb:localhost:55555:foo".  From nc do: '1:editFile!0 "foo"'.  Then
go to Insert mode and add a few lines.  Then backspacing every other time
moves the cursor instead of deleting. (Chris Kaiser, 2007 Sep 25)

Windows installer could add a "open in new tab of existing Vim" menu entry.
GvimExt: patch to add "Edit with single Vim &tabbed" menu entry.
Just have two choices, always using one Vim and selecting between using an
argument list or opening each file in a separate tab.
(Erik Falor, 2008 May 21, 2008 Jun 26)

Windows installer: licence text should not use indent, causes bad word wrap.
(Benjamin Fritz, 2010 Aug 16)

Changes for Win32 makefile. (Mike Williams, 2007 Jan 22, Alexei Alexandrov,
2007 Feb 8)

Win32: Can't complete shell command names.  Why is setting xp_context in
set_one_cmd_context() inside #ifndef BACKSLASH_IN_FILENAME?

Win32: Patch for cscope external command. (Mike Williams, 2007 Aug 7)

Win32: XPM support only works with path without spaces.  Patch by Mathias
Michaelis, 2006 Jun 9.  Another patch for more path names, 2006 May 31.
New version (link seems dead):
http://members.tcnet.ch/michaelis/vim/patches.zip
(also for other patches by Mathias, see mail Feb 22)

Win32: compiling with normal features and OLE fails.  Patch by Mathias
Michaelis, 2006 Jun 4.

Win32: using CTRL-S in Insert mode doesn't remove the "+" from the tab pages
label.  (Tsakiridis, 2007 Feb 18)  Patch from Ian Kelling, 2008 Aug 6.

Win32: using "gvim --remote-tab-silent fname" sometimes gives an empty screen
with the more prompt.  Caused by setting the guitablabel?  (Thomas Michael
Engelke, 2007 Dec 20 - 2008 Jan 17)

Win32: patch for fullscreen mode. (Liushaolin, 2008 April 17)

Win32: When 'shell' is bash shellescape() doesn't always do the right thing.
Depends on 'shellslash', 'shellquote' and 'shellxquote', but shellescape()
only takes 'shellslash' into account.

Menu item that does "xxd -r" doesn't work when 'fileencoding' is utf-16.
Check for this and use iconv?  (Edward L. Fox, 2007 Sep 12)
Does the conversion in the other direction work when 'fileencodings' is set
properly?

Cursor displayed in the wrong position when using 'numberwidth'. (James Vega,
2007 Jun 21)

When $VAR contains a backslash expand('$VAR') removes it. (Teemu Likonen, 2008
Jun 18)

C++ indenting wrong with "=". (James Kanze, 2007 Jan 26)

":lockvar" should use copyID to avoid endless loop.

When using --remote-silent and the file name matches 'wildignore' get an E479
error.  without --remote-silent it works fine. (Ben Fritz, 2008 Jun 20)

GVim: dialog for closing Vim should check if Vim is busy writing a file.  Then
use a different dialog: "busy saving, really quit? yes / no".

Check other interfaces for changing curbuf in a wrong way.  Patch like for
if_ruby.c.

":helpgrep" should use the directory from 'helpfile'.

The need_fileinfo flag is messy.  Instead make the message right away and put
it in keep_msg?

Editing a file remotely that matches 'wildignore' results in a "no match"
error.  Should only happen when there are wildcards, not when giving the file
name literally, and esp. if there is only one name.

Test 61 fails sometimes.  This is a timing problem: "sleep 2" sometimes takes
longer than 2 seconds.

Using ":au CursorMoved * cmd" invokes mch_FullName(), which can be slow.
Can this be avoided? (Thomas Waba, 2008 Aug 24)
Also for ":w" without a file name.
The buffer has the full path in ffname, should pass this to the autocommand.

"vim -C" often has 'nocompatible', because it's set in some startup script.
Set 'compatible' after startup is done?  Patch by James Vega, 2008 Feb 7.

VMS: while editing a file found in complex, Vim will save file into the first
directory of the path and not to the original location of the file.
(Zoltan Arpadffy)

VMS: VFC files are in some cases truncated during reading (Zoltan Arpadffy)

input() completion should not insert a backslash to escape a space in a file
name?

Ruby completion is insecure.  Can this be fixed?

When 'backupskip' is set from $TEMP special characters need to be escaped.
(patch by Grembowietz, 2007 Feb 26, not quite right)
Another problem is that file_pat_to_reg_pat() doesn't recognize "\\", so "\\("
will be seen as a path separator plus "\(".

gvim d:\path\path\(FILE).xml should not remove the \ before the (.
This also fails with --remote.

When doing ":quit" the Netbeans "killed" event isn't sent.  (Xavier de Gaye,
2008 Nov 10)  call netbeans_file_closed() at the end of buf_freeall(), or in
all places where buf_freeall() is called?

aucmd_prepbuf() should also use a window in another tab page.

When unloading a buffer in a BufHidden autocommand the hidden flag is reset?
(Bob Hiestand, 2008 Aug 26, Aug 27)

Substituting an area with a line break with almost the same area does change
the Visual area.  Can this be fixed? (James Vega, 2006 Sept 15)

GUI: When combining fg en bg make sure they are not equal.

Use different pt_br dictionary for spell checking. (Jackson A. Aquino, 2006
Jun 5)

Use different romanian dictionary for spell checking. (Andrei Popescu, Nov
2008)  Use http://downloads.sourceforge.net/rospell/ro_RO.3.2.zip
Or the hunspell-ro.3.2.tar.gz file, it also has a iso-8859-2 list.

In a C file with spell checking, in "% integer" "nteger" is seen as an error,
but "]s" doesn't find it.  "nteger" by itself is found. (Ralf Wildenhues, 2008
Jul 22)

There should be something about spell checking in the user manual.

Add an option to specify the character to use when a double-width character is
moved to the next line.  Default '>', set to a space to blank it out.  Check
that char is single width when it's set (compare with 'listchars').

The generated vim.bat can avoid the loop for NT. (Carl Zmola, 2006 Sep 3)

When showing a diff between a non-existent file and an existing one, with the
cursor in the empty buffer, the other buffer only shows the last line.  Change
the "insert" into a change from one line to many? (Yakov Lerner, 2008 May 27)

These two abbreviations don't give the same result:
	let asdfasdf = "xyz\<Left>"
	cabbr XXX <C-R>=asdfasdf<CR>
	cabbr YYY xyz<Left>

Michael Dietrich: maximized gvim sometimes displays output of external command
partly. (2006 Dec 7)

In FileChangedShell command it's no longer allowed to switch to another
buffer.  But the changed buffer may differ from the current buffer, how to
reload it then?

For Aap: include a config.arg.example file with hints how to use config.arg.

Default for 'background' is wrong when using xterm with 256 colors.
Table with estimates from Matteo Cavalleri, 2014 Jan 10.

Setting 'background' resets the Normal background color:
   highlight Normal ctermbg=DarkGray
   set background=dark
This is undesired, 'background' is supposed to tell Vim what the background
color is, not reset it.

Completion menu: For a wrapping line, completing a long file name, only the
start of the path is shown in the menu.  Should move the menu to the right to
show more text of the completions.  Shorten the items that don't fit in the
middle?

Accessing file#var in a function should not need the g: prepended.

When exiting detects a modified buffer, instead of opening the buffer in the
current tab, use an existing tab, if possible.  Like finding a window where
the buffer is displayed. (Antonios Tsakiridis)

When ":cn" moves to an error in the same line the message isn't shortened.
Only skip shortening for ":cc"?

Problem with ":call" and dictionary function. Hari Krishna Dara, Charles
Campbell 2006 Jul 06.

A custom completion function in a ":command" cannot be a Funcref. (Andy
Wokula, 2007 Aug 25)

Problem with using :redir in user command completion function? (Hari Krishna
Dara, 2006 June 21)

Another resizing problem when setting 'columns' and 'lines' to a very large
number. (Tony Mechelynck, 2007 Feb 6)

After starting Vim, using '0 to jump somewhere in a file, ":sp" doesn't center
the cursor line.  It works OK after some other commands.

Win32: Is it possible to have both postscript and Win32 printing?

Using UTF-8 character with ":command" does not work properly. (Matt Wozniski,
2008 Sep 29)

In the Netbeans interface add a "vimeval" function, so that the other side can
check the result of has("patch13").

Cursor line at bottom of window instead of halfway after saving view and
restoring.  Only with 'nowrap'. (Robert Webb, 2008 Aug 25)

Netrw has trouble executing autocommands only for a directory.  Add <isdir>
and <notisdir> to autocommand patterns?  Also <isfile>?

Add command modifier that skips wildcard expansion, so that you don't need to
put backslashes before special chars, only for white space.

In mswin.vim: Instead of mapping <C-V> for Insert mode in a complicated way,
can it be done like ":imap <C-V> <MiddleMouse>" without negative side effects?

When right after "vim file", "M" then CTRL-W v the windows are scrolled
differently and unexpectedly.  Caused by patch 7.2.398?

The magic clipboard format "VimClipboard2" appears in several places.  Should
be only one.

Win32, NTFS: When editing a specific infostream directly and 'backupcopy' is
"auto" should detect this situation and work like 'backupcopy' is "yes".  File
name is something like "c:\path\foo.txt:bar", includes a colon.  (Alex
Jakushev, 2008 Feb 1)

Small problem displaying diff filler line when opening windows with a script.
(David Luyer, 2007 Mar 1 ~/Mail/oldmail/mool/in.15872 )

Is it allowed that 'backupext' is empty?  Problems when backup is in same dir
as original file?  If it's OK don't compare with 'patchmode'. (Thierry Closen)

Patch for adding ":lscscope". (Navdeep Parhar, 2007 Apr 26; update 2008 Apr
23)

":mkview" isn't called with the right buffer argument.  Happens when using
tabs and the autocommand "autocmd BufWinLeave * mkview".  (James Vega, 2007
Jun 18)

When completing from another file that uses a different encoding completion
text has the wrong encoding.  E.g., when 'encoding' is utf-8 and file is
latin1.  Example from Gombault Damien, 2007 Mar 24.

In gvim the backspace key produces a backspace character, but on Linux the
VERASE key is Delete.  Set VERASE to Backspace? (patch by Stephane Chazelas,
2007 Oct 16)

TermResponse autocommand isn't always triggered when using vimdiff. (Aron
Griffis, 2007 Sep 19)

Create a gvimtutor.1 file and change Makefiles to install it.

When 'encoding' is utf-8 typing text at the end of the line causes previously
typed characters to be redrawn.  Caused by patch 7.1.329. (Tyler Spivey, 2008
Sep 3, 11)

":vimgrep" does not recognize a recursive symlink.  Is it possible to detect
this, at least for Unix (using device/inode)?

When switching between windows the cursor is often put in the middle.
Remember the relative position and restore that, just like lnum and col are
restored. (Luc St-Louis)

Add an option for a minimal text length before inserting a line break for
'textwidth'.  Avoids very short lines when a very long word follows.
(Kartik Agaram)


Better plugin support (not plugin manager, see elsewhere for that):
- Avoid use of feedkeys, add eval functions where needed:
  - manipulating the Visual selection?
- Add createmark(): add a mark like mM, but return a unique ID.  Need some way
  to clean them up again...  Use a name + the script ID.
  Add createmark( , 'c') to track inserts/deletes before the column.
- Plugins need to make a lot of effort, lots of mappings, to know what
  happened before pressing the key that triggers a plugin action.  How about
  keeping the last N pressed keys, so that they do not need to be mapped?
- equivalent of netbeans_beval_cb().  With an autocommand?
- Add something to enable debugging when a remote message is received.


More patches:
-   Another patch for Javascript indenting. (Hari Kumar, 2010 Jul 11)
    Needs a few tests.
-   Add 'cscopeignorecase' option. (Liang Wenzhi, 2006 Sept 3)
-   Extra argument to strtrans() to translate special keys to their name (Eric
    Arnold, 2006 May 22)
-   Mac: indicate whether a buffer was modified. (Nicolas Weber, 2006 Jun 30)
-   Allow negative 'nrwidth' for left aligning. (Nathan Laredo, 2006 Aug 16)
-   ml_append_string(): efficiently append to an existing line. (Brad
    Beveridge, 2006 Aug 26)  Use in some situations, e.g., when pasting a
    character at a time?
-   recognize hex numbers better. (Mark Manning, 2006 Sep 13)
-   Add <AbbrExpand> key, to expand an abbreviation in a mapping. (Kana
    Natsuno, 2008 Jul 17)
-   Add 'wspara' option, also accept blank lines like empty lines for "{" and
    "}". (Mark Lundquist, 2008 Jul 18)
-   Patch to add CTRL-T to delete part of a path on cmdline. (Adek, 2008 Jul
    21)
-   Instead of creating a copy of the tutor in all the shell scripts, do it in
    vimtutor.vim. (Jan Minar, 2008 Jul 20)
-   When fsync() fails there is no hint about what went wrong.  Patch by Ben
    Schmidt, 2008 Jul 22.
-   testdir/Make_dos_sh.mak for running tests with MingW. (Bill McCarthy, 2008
    Sep 13)
-   Replace ccomplete.vim by cppcomplete.vim from www.vim.org?  script 1520 by
    Vissale Neang.  (Martin Stubenschrott) Asked Vissale to make the scripts
    more friendly for the Vim distribution.
    New version received 2008 Jan 6.
    No maintenance in two years...
-   Patch to open dropped files in new tabs. (Michael Trim, 2010 Aug 3)

Awaiting updated patches:
9   Mac unicode patch (Da Woon Jung, Eckehard Berns):
    8   Add patch from Muraoka Taro (Mar 16) to support input method on Mac?
	New patch 2004 Jun 16
    - selecting proportional font breaks display
    - UTF-8 text causes display problems.  Font replacement causes this.
    - Command-key mappings do not work. (Alan Schmitt)
    - With 'nopaste' pasting is wrong, with 'paste' Command-V doesn't work.
      (Alan Schmitt)
    - remove 'macatsui' option when this has been fixed.
    - when 'macatsui' is off should we always convert to "macroman" and ignore
      'termencoding'?
9   HTML indenting can be slow.  Caused by using searchpair().  Can search()
    be used instead?  A.Politz is looking into a solution.
8   Win32: Add minidump generation. (George Reilly, 2006 Apr 24)
7   Completion of network shares, patch by Yasuhiro Matsumoto.
    Update 2004 Sep 6.
    How does this work?  Missing comments.
8   Add a few more command names to the menus.  Patch from Jiri Brezina
    (28 feb 2002).  Will mess the translations...
7   ATTENTION dialog choices are more logical when "Delete it" appears
    before "Quit".  Patch by Robert Webb, 2004 May 3.
-   Include flipcase patch: ~/vim/patches/wall.flipcase2 ?  Make it work
    for multibyte characters.
-   Win32: add options to print dialog.  Patch from Vipin Aravind.
-   Patch to add highlighting for whitespace. (Tom Schumm, 2003 Jul 5)
    use the patch that keeps using HLF_8 if HLF_WS has not
    been given values.
    Add section in help files for these highlight groups?
7   Add "DefaultFG" and "DefaultBG" for the colors of the menu. (Marcin
    Dalecki has a patch for Motif and Carbon)
-   Add possibility to highlight specific columns (for Fortran).  Or put a
    line in between columns (e.g., for 'textwidth').
    Patch to add 'hlcolumn' from Vit Stradal, 2004 May 20.
    confirm()		add "flags" argument, with 'v' for vertical
			    layout and 'c' for console dialog. (Haegg)
			    Flemming Madsen has a patch for the 'c' flag
			    (2003 May 13)
    raisewin()		raise gvim window (see HierAssist patch for
			    Tcl implementation ~/vim/HierAssist/ )
    taglist()		add argument to specify maximum number of matches.
			useful for interactive things or completion.
    col('^')            column of first non-white character.
			Can use "len(substitute(getline('.'), '\S.*', '', ''))
			+ 1", but that's ugly.
7   Add patch from Benoit Cerrina to integrate Vim and Perl functions
    better.  Now also works for Ruby (2001 Nov 10)
7   When 'rightleft' is set, the search pattern should be displayed right
    to left as well?  See patch of Dec 26. (Nadim Shaikli)
8   Option to lock all used memory so that it doesn't get swapped to disk
    (unencrypted).  Patch by Jason Holt, 2003 May 23.  Uses mlock.
7   Add ! register, for shell commands. (patch from Grenie)
8   In the gzip plugin, also recognize *.gz.orig, *.gz.bak, etc.  Like it's
    done for filetype detection.  Patch from Walter Briscoe, 2003 Jul 1.
7   Add a "-@ filelist" argument: read file names from a file. (David
    Kotchan has a patch for it)
7   Add ":justify" command.  Patch from Vit Stradal 2002 Nov 25.
-   findmatch() should be adjusted for Lisp.  See remark at
    get_lisp_indent().  Esp. \( and \) should be skipped. (Dorai Sitaram,
    incomplete patch Mar 18)
-   For GUI Find/Replace dialog support using a regexp.  Patch for Motif
    and GTK by degreneir (nov 10 and nov 18).
-   Patch for "paranoid mode" by Kevin Collins, March 7.  Needs much more work.
-   Patch for redo register. (Ben Schmidt, 2007 Oct 19)
    Await response to question to make the register writable.


Better 'rightleft' or BIDI support:
- Minimal Vi with bidi support: https://github.com/aligrudi/neatvi
  By Ali Gholami Rudi, also worked on arabic.c


Spell checking:
-   List of common misspellings in English:
    https://en.wikipedia.org/wiki/Wikipedia:Lists_of_common_misspellings/For_machines
    German:
    https://de.wikipedia.org/wiki/Wikipedia:Liste_von_Tippfehlern/F%C3%BCr_Maschinen
    There are other languages.
-   [s does not find missing capital at start of the line.  #10838
    Probably because the dot at the end of the previous line isn't seen.
-   When 'cursorline' is set and the first word should have SpellCap
    highlighting, redrawing the line removes it when moving the cursor away
    from the line. (#7085)  Would need to inspect the end of the previous line
    and update "capcol_lnum" and "cap_col".
-   Mixup of highlighting when there is a match and SpellBad. (ZyX, 2015 Jan 1)
-   Spell files use a latin single quote. Unicode also has another single
    quote: 0x2019.  (Ron Aaron, 2014 Apr 4)
    New OpenOffice spell files support this with ICONV.  But they are not
    compatible with Vim spell files.  The old files can no longer be
    downloaded.
-   Add a feature to only consider two spaces after a dot to start a new
    sentence.  Don't give the capitalization error when there is one space.
-   Add a way to specify punctuation characters.  Add the superscript numbers
    by default: 0x2070, 0xb9, 0xb2, 0xb3, 0x2074 - 0x2079.
-   In popup menu: If the only problem is the case of the first character,
    don't offer "ignore" and "add to word list".
-   Spell menu: When using the Popup menu to select a replacement word,
    ":spellrepeat" doesn't work.  SpellReplace() uses setline().  Can it use
    "z=" somehow?  Or use a new function.


Quickfix/Location List:
-   Window size is wrong when using quickfix window. (Lifepillar, 2018 Aug 24,
    #2999)
-   When using CTRL-W CR in the quickfix window, the jumplist in the opened
    window is cleared, to avoid going back to the list of errors buffer (would
    have two windows with it).  Can we just remove the jump list entries for
    the quickfix buffer?
-   When an item in the quickfix list has a file name that does not exist,
    behave like the item was not a match for :cnext.
-   When adding an item to a new quickfix list make ":cnext" jump to that
    item.  Make a difference being at the first item and not having used
    :cnext at all.  (Afanasiy Fet, 2017 Jan 3)
-   When opening quickfix window, disable spell checking?
-   Patch for supporting count before CR in quickfix window. (AOYAMA Shotaro,
    2007 Jan 1)
-   Patch from Herculano de Lima Einloft Neto for better formatting of the
    quickfix window (2004 dec 2)
-   Amiga: When using quickfix with the Manx compiler we only get the first 25
    errors.  How do we get the rest?
8   The quickfix file is read without conversion, thus in 'encoding'.  Add an
    option to specify the encoding of the errorfile and convert it.  Also for
    ":grep" and ":helpgrep".
    More generic solution: support a filter (e.g., by calling a function).
7   Add a command that goes back to the position from before jumping to the
    first quickfix location.


Python Interface:
-   Python 3.8 doesn't work. (Antonios Hadjigeorgalis, #5509)
-   Python: ":py raw_input('prompt')" doesn't work. (Manu Hack)
-   Build with Python on Mac does not always use the right library.
    (Kazunobu Kuriyama, 2015 Mar 28)
-   Win32: The Python interface only works with one version of Python,
    selected at compile time.  Can this be made to work with version 2.1 and
    2.2 dynamically?
-   Be able to define a Python function that can be called directly from Vim
    script.  Requires converting the arguments and return value, like with
    vim.bindeval().


Vi incompatibility:
-   Try new POSIX tests, made after my comments. (Geoff Clare, 2005 April 7)
    Version 1.5 is in ~/src/posix/1.5. (Lynne Canal)
8   With undo/redo only marks in the changed lines should be changed.  Other
    marks should be kept.  Vi keeps each mark at the same text, even when it
    is deleted or restored. (Webb)
    Also: A mark is lost after: make change, undo, redo and undo.
    Example: "{d''" then "u" then "d''": deletes an extra line, because the ''
    position is one line down. (Veselinovic)
8   When stdin is not a tty, and Vim reads commands from it, an error should
    make Vim exit.
7   Unix Vim (not gvim): Typing CTRL-C in Ex mode should finish the line
    (currently you can continue typing, but it's truncated later anyway).
    Requires a way to make CTRL-C interrupt select() when in cooked input.
8   When loading a file in the .exrc, Vi loads the argument anyway.  Vim skips
    loading the argument if there is a file already.  When no file argument
    given, Vi starts with an empty buffer, Vim keeps the loaded file. (Bearded)
6   In Insert mode, when using <BS> or <Del>, don't wipe out the text, but
    only move back the cursor.	Behaves like '$' in 'cpoptions'.  Use a flag
    in 'cpoptions' to switch this on/off.
8   When editing a file which is a symbolic link, and then opening another
    symbolic link on the same file, Vim uses the name of the first one.
    Adjust the file name in the buffer to the last one used?  Use several file
    names in one buffer???
    Also: When first editing file "test", which is symlink to "test2", and
    then editing "test2", you end up editing buffer "test" again.  It's not
    logical that the name that was first used sticks with the buffer.
7   The ":undo" command works differently in Ex mode.  Edit a file, make some
    changes, "Q", "undo" and _all_ changes are undone, like the ":visual"
    command was one command.
    On the other hand, an ":undo" command in an Ex script only undoes the last
    change (e.g., use two :append commands, then :undo).
7   The ":map" command output overwrites the command.  Perhaps it should keep
    the ":map" when it's used without arguments?
7   CTRL-L is not the end of a section?  It is for Posix!  Make it an option.
7   Add a way to send an email for a crashed edit session.  Create a file when
    making changes (containing name of the swap file), delete it when writing
    the file.  Supply a program that can check for crashed sessions (either
    all, for a system startup, or for one user, for in a .login file).
7   Vi doesn't do autoindenting when input is not from a tty (in Ex mode).
7   "z3<CR>" should still use the whole window, but only redisplay 3 lines.
7   ":tag xx" should move the cursor to the first non-blank.  Or should it go
    to the match with the tag?	Option?
7   Implement 'autoprint'/'ap' option.
7   Add flag in 'cpoptions' that makes <BS> after a count work like <Del>
    (Sayre).
7   Add flag in 'cpoptions' that makes operator (yank, filter) not move the
    cursor, at least when cancelled. (default Vi compatible).
7   This Vi-trick doesn't work: "Q" to go to Ex mode, then "g/pattern/visual".
    In Vi you can edit in visual mode, and when doing "Q" you jump to the next
    match.  Nvi can do it too.
7   Support '\' for line continuation in Ex mode for these commands: (Luebking)
	g/./a\		    g/pattern1/ s/pattern2/rep1\\
	line 1\		    line 2\\
	line 2\		    line 3\\
	.		    line4/
6   ":e /tmp/$tty" doesn't work.  ":e $uid" does.  Is $tty not set because of
    the way the shell is started?
6   Vi compatibility (optional): make "ia<CR><ESC>10." do the same strange
    thing.  (only repeat insert for the first line).


GTK+ GUI:
9   Crash with X command server over ssh. (Ciaran McCreesh, 2006 Feb 6)
8   GTK 2: Combining UTF-8 characters not displayed properly in menus (Mikolaj
    Machowski)  They are displayed as separate characters.  Problem in
    creating a label?
8   GTK 2: Combining UTF-8 characters are sometimes not drawn properly.
    Depends on the font size, "monospace 13" has the problem.  Vim seems to do
    everything right, must be a GTK bug.  Is there a way to work around it?
9   Can't paste a Visual selection from GTK-gvim to vim in xterm or Motif gvim
    when it is longer than 4000 characters.  Works OK from gvim to gvim and
    vim to vim.  Pasting through xterm (using the shift key) also works.
    It starts working after GTK gvim loses the selection and gains it again.
-   Gnome2: When moving the toolbar out of the dock, so that it becomes
    floating, it can no longer be moved.  Therefore making it float has been
    blocked for now.
-   Mapping with partial match not executed properly in GTK. (Ingo Karkat,
    #7082)
-   Add more testing of the GTK GUI.
    - gtk_test_widget_click() can be used to simulate a click in a widget.
-   Tests failing for "make testgui" with GTK:
    - Test_setbufvar_options()
    - Test_exit_callback_interval()
-   When adding a timer from 'balloonexpr' it won't fire, because
    g_main_context_iteration() doesn't return.  Need to trigger an event when
    the timer expires.
-   problem with 'L' in 'guioptions' changing the window width.  (Aaron
    Cornelius, 2012 Feb 6)
-   Patch to invert characters differently in GTK. (Yukihiro Nakadaira, 2013
    May 5)
-   drawing a double-width combining character over single-width characters
    doesn't look right. (Dominique Pelle, 2010 Aug 8)
-   tear-off menu does not work. (Kurt Sonnenmoser, 2010 Oct 25)
-   Patch for GTK buttons X1Mouse and X2Mouse. (Christian J. Robinson, 2010
    Aug 9)
-   When setting 'columns' in a startup script and doing ":vertical diffsplit"
    the window isn't redrawn properly, see two vertical bars.
-   when the Tab pages bar appears or disappears while the window is maximized
    the window is no longer maximized.  Patch that has some idea but doesn't
    work from Geoffrey Antos, 2008 May 5.  Also: the window may no longer fit
    on the screen, thus the command line is not visible.
-   Patch to support horizontal scroll wheel in GTK. Untested. (Bjorn
    Winckler, 2010 Jun 30)


Win32 GUI known bugs:
-   Win32: tearoff menu window should have a scrollbar when it's taller than
    the screen.
8   The -P argument doesn't work very well with many MDI applications.
    The last argument of CreateWindowEx() should be used, see MSDN docs.
    Tutorial (link seems dead): http://win32assembly.online.fr/tut32.html
6   Win32 GUI: With "-u NONE -U NONE" and doing "CTRL-W v" "CTRL-W o", the ":"
    of ":only" is highlighted like the cursor.  (Lipelis)
8   When 'encoding' is "utf-8", should use 'guifont' for both normal and wide
    characters to make Asian languages work.  Win32 fonts contain both
    type of characters.
7   When font smoothing is enabled, redrawing can become very slow.  The reason
    appears to be drawing with a transparent background.  Would it be possible
    to use an opaque background in most places?
7   The cursor color indicating IME mode doesn't work properly. (Shizhu Pan,
    2004 May 9)
8   Win32: When clicking on the gvim title bar, which gives it focus, produces
    a file-changed dialog, after clicking on a button in that dialog the gvim
    window follows the mouse.  The button-up event is lost.  Only with
    MS-Windows 98?
    Try this: ":set sw ts", get enter-prompt, then change the file in a
    console, go back to Vim and click "reload" in the dialog for the changed
    file: Window moves with the cursor!
    Put focus event in input buffer and let generic Vim code handle it?
8   When Vim is minimized and when maximizing it a file-changed dialog pops
    up, Vim isn't maximized.  It should be done before the dialog, so that it
    appears in the right position. (Webb)
9   When selecting at the more-prompt or hit-enter-prompt, the right mouse
    button doesn't give popup menu.
    At the hit-enter prompt CTRL-Y doesn't work to copy the modeless
    selection.
    On the command line, don't get a popup menu for the right mouse button.
    Let the middle button paste selected text (not the clipboard but the
    non-Visual selection)?  Otherwise CTRL-Y has to be used to copy the text.
8   When 'grepprg' doesn't execute, the error only flashes by, the
    user can hardly see what is wrong. (Moore)
    Could use vimrun with an "-nowait" argument to only wait when an error
    occurs, but "command.com" doesn't return an error code.
8   When the 'shell' cannot be executed, should give an appropriate error msg.
    Esp. for a filter command, currently it only complains the file could not
    be read.
7   At the hit-enter prompt scrolling now no longer works.  Need to use the
    keyboard to get around this.  Pretend <CR> was hit when the user tries to
    scroll?
7   Scrollbar width doesn't change when selecting other windows appearance.
    Also background color of Toolbar and rectangle below vert. scrollbar.
6   Drawing text transparently doesn't seem to work (when drawing part cursor).
8   CTRL key doesn't always work in combination with ALT key.  It does work
    for function keys, not for alphabetic characters.  Perhaps this is because
    CTRL-ALT is used by Windows as AltGr?
8   CTRL-- doesn't work for AZERTY, because it's CTRL-[ for QWERTY.  How do we
    know which keyboard is being used?
7   When scrolling, and a background color is dithered, the dither pattern
    doesn't always join correctly between the scrolled area and the new drawn
    area (Koloseike).
8   When gui_init_font() is called with "*", p_guifont is freed while it might
    still be used somewhere.  This is too tricky, do the font selection first,
    then set the new font by name (requires putting all logfont parameters in
    the font name).
-   When running a fast timer, the cursor no longer blinks.  Was reported:
    cursor blinks in terminal on widows with a timer. (xtal8, #2142)
-   Last message from startup doesn't show up when there is an echoerr
    command. (Cyril Slobin, 2009 Mar 13)


Motif:
6   New Motif toolbar button from Marcin Dalecki:
    - When the mouse pointer is over an Agide button the red becomes black.
      Something with the way colors are specified in the .xpm file.
    - The pixmap is two pixels smaller than it should be.  The gap is filled
      with grey instead of the current toolbar background color.
9   Can configure be changed to disable netbeans if the Xpm library is
    required and it's missing?
8   When using the resource "Vim*borderwidth 2" the widgets are positioned
    wrong.
9   XIM is disabled by default for SGI/IRIX.  Fix XIM so that 'imdisable' can
    be off by default.
9   XIM doesn't work properly for Motif. (Yasuhiro Matsumoto)  For now,
    keep XIM active at all times when the input method has the preediting
    flag.
8   X11: A menu that contains an umlaut is truncated at that character.
    Happens when the locale is "C", which uses ASCII instead of IS0-8859-1.
    Is there a way to use latin1 by default?  Gnome_init() seems to do this.
8   Perhaps use fontsets for everything?
6   When starting in English and switching the language to Japanese, setting
    the locale with ":lang", 'guifontset' and "hi menu font=", deleting all
    menus and setting them again, the menus don't use the new font.  Most of
    the tooltips work though...
7   Motif: when using a file selection dialog, the specified file name is not
    always used (when specifying a filter or another directory).
8   When 'encoding' is different from the current locale (e.g., utf-8) the
    menu strings don't work.  Requires conversion from 'encoding' to the
    current locale.  Workaround: set 'langmenu'.


Motif GUI:
-   gui_mch_browsedir() is missing, browsedir() doesn't work nicely.
7   Use XmStringCreateLocalized() instead of XmStringCreateSimple()?
    David Harrison says it's OK (it exists in Motif 1.2).
8   Lesstif: When deleting a menu that's torn off, the torn off menu becomes
    very small instead of disappearing.  When closing it, Vim crashes.
    (Phillipps)


GUI:
9   On Solaris, creating the popup menu causes the right mouse button no
    longer to work for extending the selection. (Halevy)
9   When running an external program, it can't always be killed with CTRL-C.
    e.g., on Solaris 5.5, when using "K" (Keech).  Other 'guipty' problems on
    Solaris 2.6. (Marley)
9   On Solaris: Using a "-geometry" argument, bigger than the window where Vim
    is started from, causes empty lines below the cmdline. (raf)
8   When setting 'langmenu', it should be effective immediately.  Store both
    the English and the translated text in the menu structure.  Re-generate
    the translation when 'langmenu' has changed.
8   Basic flaw in the GUI code: NextScreen is updated before calling
    gui_write(), but the GUI code relies on NextScreen to represent the state
    of where it is processing the output.
    Need better separation of Vim core and GUI code.
8   When fontset support is enabled, setting 'guifont' to a single font
    doesn't work.
8   Menu priority for sub-menus for: Amiga.
8   When translating menus ignore the part after the Tab, the shortcut.  So
    that the same menu item with a different shortcut (e.g., for the Mac) are
    still translated.
8   Add menu separators for Amiga.
8   Add way to specify the file filter for the browse dialog.  At least for
    browse().
8   Add dialog for search/replace to other GUIs?  Tk has something for this,
    use that code?  Or use console dialog.
8   When selecting a font with the font dialog and the font is invalid, the
    error message disappears too quick.
7   More features in the find/replace dialog:
    - regexp on/off
    - search in selection/buffer/all buffers/directory
       when all buffers/directory is used:
	- filter for file name
       when directory is used:
	- subdirectory on/off
	- top directory browser
8   gui_check_colors() is not called at the right moment.  Do it much later,
    to avoid problems.
8   gui_update_cursor() is called for a cursor shape change, even when there
    are mappings to be processed.  Only do something when going to wait for
    input.  Or maybe every 100 ms?
8   X11: When the window size is reduced to fit on screen, there are blank
    lines below the text and bottom scrollbar.  "gvim -geometry 80x78+0+0".
    When the "+0+0" is omitted it works.
8   When starting an external command, and 'guipty' set, BS and DEL are mixed
    up.  Set erase character somehow?
8   The compose key doesn't work properly (Cepas).  Both for Win32 and X11.
7   The cursor in an inactive window should be hollow.  Currently it's not
    visible.
7   GUI on Solaris 2.5.1, using /usr/dt/..: When gvim starts, cursor is
    hollow, after window lowered/raised it's OK. (Godfrey)
7   When starting GUI with ":gui", and window is made smaller because it
    doesn't fit on the screen, there is an extra redraw.
8   When setting font with .Xdefaults, there is an extra empty line at the
    bottom, which disappears when using ":set guifont=<Tab>". (Chadzelek)
8   When font shape changes, but not the size, doing ":set font=" does not
    redraw the screen with the new font.  Also for Win32.
    When the size changes, on Solaris 2.5 there isn't a redraw for the
    remaining part of the window (Phillipps).
-   Flashes really badly in certain cases when running remotely from a Sun.
4   Re-write the code so that the highlighting isn't changed multiple times
    when doing a ":hi clear".  The color changes happen three or more times
    currently.  This is very obvious on a 66Mhz 486.


Win32 console:
8   Should $USERPROFILE be preferred above $HOMEDRIVE/$HOMEPATH?  No, but it's
    a good fallback, thus use:
	    $HOME
	    $HOMEDRIVE$HOMEPATH
	    SHGetSpecialFolderPath(NULL, lpzsPath, CSIDL_APPDATA, FALSE);
	    $USERPROFILE
	    SHGetSpecialFolderPath(NULL, lpzsPath, CSIDL_COMMON_APPDATA, FALSE);
	    $ALLUSERSPROFILE
	    $SYSTEMDRIVE\
	    C:\
8   Win32 console: <M-Up> and <M-Down> don't work. (Geddes)  We don't have
    special keys for these.  Should use modifier + key.
8   Win32 console: caps-lock makes non-alpha keys work like with shift.
    Should work like in the GUI version.
8   Environment variables in DOS are not case sensitive.  Make a define for
    STRCMP_ENV(), and use it when comparing environment var names.
8   Setting 'shellslash' has no immediate effect.  Change all file names when
    it is set/reset?  Or only use it when actually executing a shell command?
8   When editing a file on a Samba server, case might matter.  ":e file"
    followed by ":e FILE" will edit "file" again, even though "FILE" might be
    another one.  Set last used name in buflist_new()?  Fix do_ecmd(), etc.
8   When a buffer is editing a file like "ftp://mach/file", which is not going
    to be used like a normal file name, don't change the slashes to
    backslashes. (Ronald Hoellwarth)


Win32 console:
9   When editing a file by its short file name, it should be expanded into its
    long file name, to avoid problems like these: (Mccollister)
     1) Create a file called ".bashrc" using some other editor.
     2) Drag that file onto a shortcut or the actual executable.
     3) Note that the file name is something like BASHRC~1
     4) Go to File->Save As menu item and type ".bashrc" as the file name.
     5) Press "Yes" to indicate that I want to overwrite the file.
     6) Note that the message "File exists (add ! to override)" is displayed
	and the file is not saved.
    Use FindFirstFile() to expand a file name and directory in the path to its
    long name.
8   Also implement 'conskey' option for the Win32 console version?  Look at
    how Xvi does console I/O under Windows NT.
7   Re-install the use of $TERM and support the use of different terminals,
    besides the console.
8   Use of <altgr> modifier doesn't work?  5.3 was OK. (Garcia-Suarez/Guckes)
9   Mapping <C-S-Tab> doesn't work correctly.  How to see the difference with
    <C-S-i>?
9   tmpnam() uses file in root of file system: "\asdf".  That doesn't work on
    a Netware network drive.  Use same function as for Win32 GUI?
8   In os_win32.h, HAVE_STRICMP and HAVE_STRNICMP are defined only if __GNUC__
    is not defined.  Shouldn't that be the other way around?


Amiga:
8   In mch_inchar() should use convert_input_safe() to handle incomplete byte
    sequences.
9   In mch_expandpath() a "*" is to be expanded, but "\*" isn't.  Remove
    backslashes in result.
8   Executing a shell, only one option for 'shell' is separated.  Should do
    all options, using white space separation.


Macintosh:
-   GUI: gui_mch_browsedir() is missing.
7   Loading the Perl library only works on OS/X 10.2 or 10.3, never on both.
    Load the Perl library dynamically see Python sources file dynload_mac
    (Jack)
    dynamic linking: http://developer.apple.com/technotes/tn2002/tn2064.html
8   inputdialog() doesn't resize when giving more text lines. (David Fishburn,
    2006 Sept 28)
8   Define vim_mkdir() for Macintosh.
8   Define mch_writable() for Macintosh.
9   When DiskLock is running, using a swap file causes a crash.  Appears to be
    a problem with writing a file that starts with a dot. (Giacalone)
9   In mac_expandpath() check that handling of backslashes is done properly.
-   Build problem with small features on Mac OS X 10.6. (Rainer, 2011 Jan 24)
-   Version of netbeans.c for use with MacVim. (Kazuki Sakamoto, 2010 Nov 18)
-   Mac with X11: clipboard doesn't work properly. (Raf, 2010 Aug 16)
-   Move Carbon todo items to os_mac.txt.  Note that this version is frozen,
    try the Cocoa version.
-   After a ":vsplit" the left scrollbar doesn't appear until 'columns' is
    changed or the window is resized.
-   Patch for configure: remove arch from ruby link args. (Knezevic, 2008 Mar
    5)  Alternative: Kazuki Sakamoto, Mar 7.
-   trouble compiling with Motif, requires --disable-darwin. (Raf, 2008 Aug 1)
    Reply by Ben Schmidt.
-   Using gvim: netrw window disappears. (Nick Lo, 2006 Jun 21)


"Small" problems:
-   When using e_secure in do_one_cmd() mention the command being executed,
    otherwise it's not clear where it comes from.
9   For Turkish vim_tolower() and vim_toupper() also need to use utf_
    functions for characters below 0x80. (Sertacyildiz)
9   When the last edited file is a help file, using '0 in a new Vim doesn't
    edit the file as a help file.  'filetype' is OK, but 'iskeyword' isn't,
    file isn't readonly, etc.
8   When an ":edit" is inside a try command and the ATTENTION prompt is used,
    the :catch commands are always executed, also when the file is edited
    normally.  Should reset did_emsg and undo side effects.  Also make sure
    the ATTENTION message shows up.  Servatius Brandt works on this.
7   Vimtutor leaves escape sequence in terminal. This is the xterm response to
    requesting the version number.  (Yasuhiro Matsumoto)
8   When redirecting and using ":silent" the current column for displaying and
    redirection can be different.  Use a separate variable to hold the column
    for redirection.
7   The messages for "vim --help" and "vim --version" don't use
    'termencoding'.
-   Could the hit-enter prompt be avoided when a message only overlaps the
    'showcmd' area?  Clear that area when the next cmd is typed.
8   When 'scrollbind' is set, a window won't scroll horizontally if the cursor
    line is too short.  Add a word in 'scrollopt' to allow moving the cursor
    to longer line that is visible.  A similar thing is done for the GUI when
    using the horizontal scrollbar.
8   When giving a ":bwipeout" command a file-changed dialog may popup for this
    buffer, which is pointless.  (Mike Williams)
8   On MS-Windows ":make" doesn't show output while it is working.  Use the
    tee.exe from  http://unxutils.sourceforge.net/ ?  About 16 Kbyte in the
    UnxUtils.zip archive.
    Is it better than what we have in src/tee?
8   When doing Insert mode completion a mapping cannot recursively call
    edit(), because the completion information is global.  Put everything in
    an allocated structure?
7   mb_off2cells() doesn't work correctly on the tail byte of a double-byte
    character. (Yasuhiro Matsumoto)  It should return 1 when used on a tail
    byte, like for utf-8.  Store second byte of double-byte in ScreenLines2[]
    (like for DBCS_JPNU) and put a zero in the second byte (like for UTF-8).
7   Inside a function with "perl <<EOF" a line with "$i++" is recognized as an
    ":insert" command, causing the following "endfunction" not to be found.
    Add skipping this perl construction inside function definitions.
7   When 'ttimeoutlen' is 10 and 'timeoutlen' is 1000, there is a keycode
    "<Esc>a" and a mapping <Esc>x", when typing "<Esc>a" with half a second
    delay should not be interpreted as a keycode. (Hans Ginzel)
7   ":botright 1 new" twice causes all window heights to be changed.  Make the
    bottom window only bigger as much as needed.
7   The Cygwin and MingW makefiles define "PC", but it's not used anywhere.
    Remove? (Dan Sharp)
9   User commands use the context of the script they were defined in.  This
    causes a "s:var" argument to unexpectedly use a variable in the defining
    script, not the calling script.  Add an argument to ":command":
    "-keepcontext".  Do replace <SID>, so that a function in the defining
    script can be called.
8   The Japanese message translations for MS-Windows are called ja.sjis.po,
    but they use encoding cp932.  Rename the file and check that it still
    works.
8   A very long message in confirm() can't be quit.  Make this possible with
    CTRL-C.
8   "gf" always excludes trailing punctuation characters.  file_name_in_line()
    is currently fixed to use ".,:;!".  Add an option to make this
    configurable?
8   'hkmap' should probably be global-local.
8   Using ":s" in a function changes the previous replacement string.  Save
    "old_sub" in save_search_patterns()?
8   Should allow multibyte characters for the delimiter: ":s+a+b+" where "+"
    is a multibyte character.
8   When appending to a file and 'patchmode' isn't empty, a backup file is
    always written, even when the original file already exists.
9   When getting focus while writing a large file, could warn for this file
    being changed outside of Vim.  Avoid checking this while the file is being
    written.
7   The message in bt_dontwrite_msg() could be clearer.
8   The script ID that is stored with an option and displayed with ":verbose
    set" isn't reset when the option is set internally.  For example when
    'foldlevel' is set from 'foldlevelstart'.
8   Also store the line number with the script ID and use it for ":verbose",
    so that "set nocompatible" is found when it changes other option values.
    When an option is set indirectly mention the command?  E.g. when
    ":diffsplit" sets 'foldmethod'.
8   In the fileformat dialog, "Cancel" isn't translated.  Add a global
    variable for this. (Eduardo Fernandez)
9   When editing a file with 'readonly' set, there is no check for an existing
    swap file.  Then using ":write" (without making any changes) doesn't give
    a warning either.  Should check for an existing swap file without creating
    one. Unfinished patch by Ian Kelling, 2008 July 14.
7   When 'showbreak' is set, the amount of space a Tab occupies changes.
    Should work like 'showbreak' is inserted without changing the Tabs.
7   When 'mousefocus' is set and switching to another window with a typed
    command, the mouse pointer may be moved to a part of the window that's
    covered by another window and we lose focus.  Only move in the y
    direction, not horizontally?
8   ":hardcopy":
    - Using the cterm_color[] table is wrong when t_colors is > 16.
    - Need to handle unprintable characters.
    - Win32: On a B&W printer syntax highlighting isn't visible.  Perform
      dithering to make grey text?
    - Add a flag in 'printoptions' to add an empty page to make the total
      number even.  "addempty"? (Mike Williams)
    - Respect 'linebreak'.  Perhaps also 'showbreak'?
    - Should interpret CTRL-L as a page break.
    - Grey line numbers are not always readable.  Add field in 'printoptions'.
      Default to black when no syntax highlighting.
    - Be able to print a window in diff mode.
    - Be able to specify a colorscheme to use for printing.  And a separate
      one for B&W printing (if that can be detected).
8   When 'virtualedit' is "block,insert" and encoding is "utf-8", selecting a
    block of one double-wide character, then "d" deletes only half of it.
8   When 'virtualedit' is set, should "I" in blockwise visual mode also insert
    in lines that don't extend into the block?
8   With 'virtualedit' set, in Insert mode just after the end of line, CTRL-O
    yh does not yank the last character of the line. (Pavel Papushev)
    Doing "hl" first appears to make it work.
8   With 'virtualedit' set it's possible to move into the blank area from
    'linebreak'.
8   With 'virtualedit' set and 'selection' "exclusive", a Visual selection
    that ends in or after a tab, "d" doesn't delete (part of) the tab.
    (Helmut Stiegler)
9   When jumping to a tag, the search pattern is put in the history.  When
    'magic' is on, the pattern may not work.  Translate the pattern depending
    on p_magic when putting it in the history?  Alternative: Store value of
    'magic' in history.  (Margo)
9   optwin.vim: Restoring a mapping for <Space> or <CR> is not correct for
    ":noremap".  Add "mapcmd({string}, {mode})?  Use code from ":mkexrc".
9   term_console is used before it is set (msdos, Amiga).
9   Get out-of-memory for ":g/^/,$s//@/" on 1000 lines, this is not handled
    correctly.  Get many error messages while redrawing the screen, which
    cause another redraw, etc.
8   [<C-I> doesn't work when '*' is in 'iskeyword'.  find_pattern_in_path()
    must escape special characters in the pattern.
8   Vim can overwrite a read-only file with ":w!".  ":w" can't overwrite an
    existing file, "w!" can, but perhaps not a read-only file?  Then use
    ":w!!" for that.
    Or ask for permission to overwrite it (if file can be made writable) and
    restore file to readonly afterwards.
    Overwriting a file for which a swap file exists is similar issue.
7   When compiled with "xterm_clipboard", startup can be slower and might get
    error message for invalid $DISPLAY.  Try connecting to the X server in the
    background (forked), so that Vim starts up quicker?  Connect as soon as
    the clipboard is to be used (Visual select mode starts, paste from
    clipboard)
7   X11: Some people prefer to use CLIPBOARD instead of PRIMARY for the normal
    selection.  Add an "xclipboard" argument to the 'clipboard' option? (Mark
    Waggoner)
6   When the xterm reports the number of colors, a redraw occurs.  This is
    annoying on a slow connection.  Wait for the xterm to report the number of
    colors before drawing the screen.  With a timeout.
8   When the builtin xterm termcap contains codes that are not wanted, need a
    way to avoid using the builtin termcap.
8   Xterm sends ^[[H for <Home> and ^[[F for <End> in some mode.  Also
    recognize these keys?  Mostly useful for xterm simulators, like gnometerm.
    See https://invisible-island.net/xterm/xterm.faq.html#xterm_pc_style
8   '[ and '] should be set to start/end of line when using a linewise operator
    (e.g., ":w").
8   CTRL-A can't handle big "long" numbers, they become negative.  Check for
    "-" character, if not present, use unsigned long.
8   Add suspending with CTRL-Z at the "more" prompt, and when executing a long
    script in do_cmdline().
8   When using 'hidden', many swap files will be open.  When Vim runs into the
    maximum number of open files, error messages will appear.  Detect that
    this problem is present, and close any hidden files that don't have
    changes.
8   Core dump within signal function: gdb doesn't show stack backtrace!  Option
    to skip catch_signals()?
9   Repeating a "cw" with "." doesn't work if the text was pasted from the
    clipboard. (Thomas Jones)  It's because the menu/toolbar item exits Insert
    mode and uses "gP".  How to fix this without breaking inserting a block of
    text?
8   In Replace mode pasting from the clipboard (using menu or toolbar) inserts
    all the text.  Add ":rmenu"?
8   Pasting with the mouse in Replace mode inserts the text, instead of
    overwriting, when it is more than one line.  Same for using <C-R>.
9   CTRL-E and CTRL-Y don't work in small window when 'so' is 4 and lines are
    wrapping (Acevedo/in.226).  E.g., when using CTRL-E, window height 7,
    window might actually scroll down when last line of buffer is displayed.
    --> Remember if the previous command was "cursor follows screen" or
    "screen follow cursor" and use this in cursupdate().
7   tilde_replace() can only handle "~/", should also do "~user/".
    Get the list of home directories (from /etc/passwd?  Use getpwent()) and
    use some clever algorithm to match a path with that.  Find common strings
    in the list?
8   When dragging status line with mouse, sometimes a jump when first clicking
    on the status line (caused by 'winheight').  Select window on button up,
    instead of on button down.
8   Dragging the status line doesn't scroll but redraw.
8   When performing incremental search, should abort searching as soon as a
    character is typed.
8   How to set VIMRC_FILE to \"something\" for configure?  Why does this not
    work: CFLAGS='-DVIMRC_FILE=\"/mydir/myfile\"' ./configure
8   The temporary file is sometimes not writable.  Check for this, and use an
    alternate name when it isn't.  Or add the 'temptemplate' option: template
    for the temp file name ":set temptemplate=/usr/tmp/?????.tmp".
    Also: Win32 version uses Windows temp directory, which might not work for
    cygwin bash.
7   Get error "*, \+ or \( operand could be empty" for pattern "\(.\)\1\{3}".
    Remember flags for backreferences.
7   When switching to Daylight Saving Time, Vim complains that a file has been
    changed since last read.  Can we use a function that uses GMT?
7   When completing an environment variable after a '$', check for file names
    that contain a '$' after all have been found.
8   When "cm" termcap entry is missing, starting gvim shouldn't complain about
    it. (Lohner)  Try out with "vt100" entry, cm replaced with cX.
7   When an include file starts with "../", the check for already visiting
    this file doesn't work.  Need to simplify the file name.
7   The names and comments for the arguments of do_browse() are confusing.
    "dflt" isn't the default file name when "initdir" is not NULL and
    "initdir" is the default path to be used.
7   When 'scrolloff' is exactly half the window height, "j" causes a scroll of
    two lines at a time.  "k" doesn't do this. (Cory T. Echols)


I can't reproduce these (if you can, let me know how!):
9   NT 4.0 on NTFS file system: Editing ".bashrc" (drag and drop), file
    disappears.  Editing ".xyz" is OK.  Also, drag&drop only works for three
    files. (McCollister)


Problems that will (probably) not be solved:
-   GTK: when using the popup menu with spelling suggestions and releasing the
    right mouse button before the menu appears selecting an item with the
    right mouse button has no effect.  GTK does not produce an event for this.
-   GTK 2: Cannot use the file selector.  When using it many things become
    slow.  This is caused by some code in GTK that writes
    ~/.recently-used.xbel every time an event is handled.  It assumes the main
    loop is never quit, which is a wrong assumption.  Also, it overwrites the
    file with different file permissions, which is a privacy issue.  This
    needs to be fixed in GTK.  A solution in Vim would be really complicated.
    (2008 Jul 31)  This appears to be fixed in Vim 7.3.
-   xterm title: The following scenario may occur (esp. when running the Vim
    test script): Vim 1 sets the title to "file1", then restores the title to
    "xterm" with an ESC sequence when exiting.  Vim 2 obtains the old title
    with an X library call, this may result in "file1", because the window
    manager hasn't processed the "xterm" title yet.  Can apparently only be
    worked around with a delay.
-   In a terminal with 'mouse' set such that the mouse is active when entering
    a command line, after executing a shell command that scrolls up the
    display and then pressing ":": Selecting text with the mouse works like
    the display wasn't scrolled.  Vim doesn't know how much the external
    command scrolled up the display.  Use Shift to select text.
-   X windows: When $DISPLAY points to a X server where there is no access
    permission, trying to connect to the X server causes an error message.
    XtOpenDisplay() prints this directly, there is no way to avoid it.
-   X windows: Setting 'guifontset' to an illegal value sometimes crashes Vim.
    This is caused by a fault in a X library function, can't be solved in Vim.
-   Win32 tcl: has("tcl") hangs when the tcl84.dll is from cygwin.
-   Motif: When adding a menu item "Find this &Symbol", the "s" in "this" will
    be underlined, instead of in "Symbol".  Motif doesn't let us specify which
    character gets the highlighting.
-   Moving the cursor removes color in color-xterm.  This is a color-xterm
    problem!  color-xterm ver. 6.1 beta 3 and later work properly.
-   In zsh, "gvim&" changes the terminal settings.  This is a zsh problem.
    (Jennings)
-   Problem with HPterm under X: old contents of window is lost (Cosentino).
-   Amiga: The ":cq" command does not always abort the Manx compiler.  Why?
-   Linux: A file with protection r--rw-rw- is seen readonly for others.  The
    access() function in GNU libc is probably wrong.
-   When doing a CTRL-Z and typing a command for the shell, while Vim is busy
    (e.g. writing a file), the command for the shell is sometimes eaten by Vim,
    because the terminal mode is changed from RAW to CBREAK.
-   An old version of GNU tgoto can't handle the terminfo code for "AF".  The
    "%p1" is interpreted as "%p" and "1", causing color not to be working.
    Fix: Change the "%p1" in the "AF" and "AB" terminfo entries to "%p".
    (Benzinger).
-   When running an external command from the GUI, typeahead is going to that
    program, not to Vim.  It looks like the shell eats the characters, Vim
    can't get back what the external command didn't use.
-   Win32 GUI: Error code from external command not returned in shell_error.
    It appears that cmd.exe and command.com don't return an error code.
-   Win32 GUI: The Toolbar is a bit too high when the flat style is being
    used.  We don't have control over the height of the Toolbar.
-   Win32: All files created on the day of switching from winter to summer
    time cause "changed since editing started" messages.  It goes away when
    the file is written again the next day, or the timezone is adjusted.
    DJGPP version is OK. (Zaimi)  Looks like a problem with the Win32 library.
    Rebooting doesn't help.  Time stamps look OK in directory. (Penn)
    Is this on FAT (stores wall clock time) or NTFS (stores UTS)?
-   Win32, MS-Windows XP: $HOME uses the wrong drive when the user profiles
    are not on the boot disk.  This is caused by a wrong value of $HOMEDRIVE.
    This is a bug in XP, see MSKB article 818134.
-   Win32, MS-Windows: expanding plugin/**/*.vim also picks up
    dir/ctags.vim,v.  This is because the short file name is something like
    "ctags~1.vim" and that matches the pattern.
-   SunOS 5.5.1 with Motif: The file open dialog does not have a horizontal
    scroll bar for the "files" selection.  This is a problem in the Motif
    libraries, get a patch from Sun.
-   Solaris 2.6 with GTK and Perl: gvim crashes when started.  Problem with X
    input method called from GDK code.  Without Perl it doesn't crash.
-   VMS: Vimdiff doesn't work with the VMS diff, because the output looks
    different.  This makes test 47 fail.  Install a Unix-compatible diff.
-   GTK with Gnome: Produces an error message when starting up:
	Gdk-WARNING **: locale not supported by C library
    This is caused by the gnome library gnome_init() setting $LC_CTYPE to
    "en_US".  Not all systems support this locale name, thus causing the
    error.  Hopefully a newer version of GTK/Gnome fixes this problem.
-   GTK 2: With this mapping the hit-enter prompt is _sometimes_ below the
    screen, at other times there is a grey area below the command line:
	:nmap <F11> :if &guioptions=~'m' \| set guioptions-=m \| else \| set guioptions+=m \| endif<cr>
-   GTK: When pasting a selection from Vim to xclipboard gvim crashes with a
    ABRT signal.  Probably an error in the file gdkselection.c, the assert
    always fails when XmbTextListToTextProperty() fails. (Tom Allard)
-   GTK 2: gives an assertion error for every non-builtin icon in the toolbar.
    This is a GTK 2.4.x bug, fixed in GTK 2.4.2. (Thomas de Grenier de Latour)
-   When using an xterm that supports the termresponse feature, and the 't_Co'
    termcap option was wrong when Vim started, it will be corrected when the
    termresponse is received.  Since the number of colors changes, the
    highlighting needs to be initialized again.  This may cause colors defined
    in the vimrc file to be lost.
-   On Windows NT 4.0 the number of files passed to Vim with drag&drop and
    "Edit with Vim" is limited.  The maximum command line length is 255 chars.

---------------------  extensions and improvements ----------------------
						    *extensions-improvements*

Most interesting new features to be added when all bugs have been fixed:
-   Using ":exe edit fname" has escaping problems.  Use ":edit ++(fname)".
    Thus use "++=" to give arguments as expressions, comma-separated as if
    calling a function.
    With options: ":edit ++(['!', '++enc=abc'], ['+/pat'], fname)".
    Alternative: Make a function for Ex commands: cmd_edit().
-   Add COLUMN NUMBERS to ":" commands ":line1,line2[col1,col2]cmd".  Block
    can be selected with CTRL-V.  Allow '$' (end of line) for col2.
    (issue #3292)
-   ECLIPSE plugin.  Problem is: the interface is very complicated.  Need to
    implement part in Java and then connect to Vim.  Some hints from Alexandru
    Roman, 2004 Dec 15.  Should then also work with Oracle Jdeveloper, see JSR
    198 standard http://www.jcp.org/en/jsr/detail?id=198.
    Eclim does it: http://eclim.sourceforge.net/  (Eric Van Dewoestine)
    Plugin that uses a terminal emulator: http://vimplugin.sf.net
    And another one: http://www.satokar.com/viplugin/index.php
-   STICKY CURSOR: Add a way of scrolling that leaves the cursor where it is.
    Especially when using the scrollbar.  Typing a cursor-movement command
    scrolls back to where the cursor is.
-   Scroll commands by screen line. g CTRL-E and g CTRL-Y ?  Requires the
    first line to be able to start halfway.
8   Add a command to jump to a certain kind of tag.  Allow the user to specify
    values for the optional fields.  E.g., ":tag size type=m".
    Also allow specifying the file and command, so that the result of
    taglist() can be used.
-   X11: Make it possible to run Vim inside a window of another program.
    This can be done with XReparentWindow().  But how exactly?


Documentation:
8   List of Vim runtime directories.  dotvim.txt from Charles Campbell, 2007
    Feb 20.
8   The GUI help should explain the Find and Find/Replace dialogs.  Add a link
    to it from ":promptrepl" and ":promptfind".
8   List of options should mention whether environment variables are expanded
    or not.
8   Extend usr_27.txt a bit. (Adam Seyfarth)
9   Make the Reference Manual more precise.  For each command mention:
    - change to cursor position and curswant
    - if it can be undone (u/CTRL-R) and redone (.)
    - how it works for folded lines
    - how it works with multibyte characters
8   Spread the windows commands over the other files.  For example, ":stag"
    should be with ":tag".  Cross-link with tags to avoid too much double
    text.
8   Add tags for all features, e.g. "gui_running".
7   MS-Windows: When a wrong command is typed with an ALT key, give a hint to
    look at the help for 'winaltkeys'.
7   Add a help.vim plugin that maps <Tab> to jump to the next tag in || and
    <C-Tab> (and <S-Tab>) to the previous tag.
    Patch by Balazs Kezes, 2007 Dec 30.  Remark from A. Politz.
-   Check text editor compendium for vi and Vim remarks.


Help:
-   First try using the ":help" argument literally, before using it as a
    pattern.  And then match it as part of a tag.
-   When a help item has multiple matches make it possible to use ":tn" to go
    to the other matches.
-   Support a way to view (and edit) .info files.
-   Implement a "sticky" help window, some help text lines that are always
    displayed in a window with fixed height. (Guckes)  Use "~/.vimhelp" file,
    user can edit it to insert favorite commands, new account can contain a
    default contents.
-   Make 'winminheight' a local option, so that the user can set a minimal
    height for the help window (and other windows).
-   ":help :s^I" should expand to ":help :substitute".
-   Make the help key (<F1>) context sensitive?
-   Learn mode: show short help while typing commands.


User Friendlier:
8   Windows install with install.exe: Use .exe instead of .bat files for
    links, so that command line arguments are passed on unmodified? (Walter
    Briscoe)
8   Windows install: Be able to associate Vim with a selection of file types?
8   Windows uninstall: Have uninstal.c delete the vimfiles directories that
    dosinst.c creates.  List the contents of the directory (recursively) if
    the user asks for it.  Requires an implementation of "rm -rf".
8   Remember the name of the vimrc file that was used (~/.vimrc, $VIM/_vimrc,
    $HOME/_vimrc, etc.) and add "edit vimrc" to the File menu.
-   Add a way to save local settings and mappings into a new plugin file.
    ":mkplugin <file>"?
-   Add mappings local to a window: ":map <window> ..."  #9339
9   Add buffer-local menu.  Should offer a choice between removing the menu or
    disabling it.  Be careful that tear-offs don't disappear (keep one empty
    item?).
    Alternative: use BufEnter and BufLeave autocommands.
8   make a vimtutor script for Amiga and other systems.
7   When Vim detects a file is being edited elsewhere and it's a gvim session
    of the same user it should offer a "Raise" button, so that the other gvim
    window can be displayed. (Eduard)
8   Support saving and restoring session for X windows?  It should work to do
    ":mksession" and use "-S fname" for the restart command.  The
    gui_x11_wm_protocol_handler() already takes care of the rest.
    global_event_filter() for GTK.


Tab pages:
9   GUI implementation for the tab pages line for other systems.
7   GUI: Control over the appearance of the text in the labels (bold, color,
    font, etc.)
8   Make GUI menu in tab pages line configurable.  Like the popup menu.
8   balloons for the tab page labels that are shortened to show the full path.
7   :tabdup	 duplicate the tab with all its windows.
7   Option to put tab line at the left or right?  Need an option to specify
    its width.  It's like a separate window with ":tabs" output.
8   Add local options for each tab page?  E.g., 'diffopt' could differ between
    tab pages.
7   Add local highlighting for each tab page?


Spell checking:
-   Support more regions? Caolan McNamara argues it's needed for es_XX.
    https://bugzilla.redhat.com/bugzilla/show_bug.cgi?id=219777
-   Unicode defines another quote character: 0x2019.  Use it as an equivalent
    of a single quote, thus use it as a word character like a quote and match
    with words, replacing the curly quote with a single quote.
-   Could filter &eacute; things for HTML before doing spell checking.
    Similarly for TeX.
-   The Hungarian spell file uses four extra characters in the FOL/UPP/LOW
    items than other spell files with the ISO-8859-2 encoding, that causes
    problem when changing 'spelllang'.  There is no obvious way to fix this.
-   Considering Hunspell 1.1.4:
    What does MAXNGRAMSUGS do?
    Is COMPLEXPREFIXES necessary when we have flags for affixes?
-   There is no Finnish spell checking file.  For openoffice Voikko is now
    used, which is based on Malaga (link seems dead):
    http://home.arcor.de/bjoern-beutel/malaga/ (Teemu Likonen)
8   ":mkspell" still takes much too long in Hungarian dictionary from
    hunspell.  Only solution appears to be to postpone secondary suffixes.
8   Handle postponed prefix with COMPOUNDPERMITFLAG or COMPOUNDFORBIDFLAG.
    WFP_COMPPERMIT and WFP_COMPFORBID
8   implement use of <compoptions> in .spl file:
    implement CHECKCOMPOUNDREP: when a compound word seems to be OK apply REP
    items and check if the result is a valid word.
    implement CHECKCOMPOUNDDUP
    implement CHECKCOMPOUNDTRIPLE
    Add CHECKCOMPOUNDCASE: when compounding make leading capital lower case.
    How is it supposed to work?
-   Add a command the repeats ]s and z=, showing the misspelled word in its
    context.  Thus to spell-check a whole file.
-   suggestion for "KG" to "kg" when it's keepcase.
-   For flags on affixes: Use a "AFFCOMPSET" flag; means the compound flags of
    the word are not used.
-   Support breakpoint character ? 0xb7 and ignore it?  Makes it possible to
    use same wordlist for hyphenation.
-   Compound word is accepted if nr of words is <= COMPOUNDWORDMAX OR nr of
    syllables <= COMPOUNDSYLMAX.  Specify using AND in the affix file?
-   NEEDCOMPOUND also used for affix?  Or is this called ONLYINCOMPOUND now?
    Or is ONLYINCOMPOUND only for inside a compound, not at start or end?
-   Do we need a flag for the rule that when compounding is done the following
    word doesn't have a capital after a word character, even for Onecap words?
-   New hunspell home page: http://hunspell.sourceforge.net/
    - Version 1.1.0 is out now, look into that.
    - Lots of code depends on LANG, that isn't right.  Enable each mechanism
      in the affix file separately.
    - Example with compounding dash is bad, gets in the way of setting
      COMPOUNDMIN and COMPOUNDWORDMAX to a reasonable value.
    - PSEUDOROOT == NEEDAFFIX
    - COMPOUNDROOT -> COMPOUNDED?  For a word that already is a compound word
	    Or use COMPOUNDED2, COMPOUNDED3, etc.
-   CIRCUMFIX: when a word uses a prefix marked with the CIRCUMFIX flag, then
    the word must also have a suffix marked with the CIRCUMFIX flag.  It's a
    bit primitive, since only one flag is used, which doesn't allow matching
    specific prefixes with suffixes.
    Alternative:
	PSFX {flag} {pchop} {padd} {pcond} {schop} {sadd}[/flags] {scond}
    We might not need this at all, you can use the NEEDAFFIX flag and the
    affix which is required.
-   When a suffix has more than one syllable, it may count as a word for
    COMPOUNDWORDMAX.
-   Add flags to count extra syllables in a word.  SYLLABLEADD1 SYLLABLEADD2,
    etc.?  Or make it possible to specify the syllable count of a word
    directly, e.g., after another slash: /abc/3
-   MORPHO item in affix file: ignore TAB and morphological field after
    word/flags and affix.
-   Implement multiple flags for compound words and CMP item?
    Await comments from other spell checking authors.
-   Also see tklspell: http://tkltrans.sourceforge.net/
8   Charles Campbell asks for method to add "contained" groups to existing
    syntax items (to add @Spell).
    Add ":syntax contains {pattern} add=@Spell" command?  A bit like ":syn
    cluster" but change the contains list directly for matching syntax items.
-   References: MySpell library (in OpenOffice.org), (links seem dead):
	http://spellchecker.mozdev.org/source.html
	http://whiteboard.openoffice.org/source/browse/whiteboard/lingucomponent/source/spellcheck/myspell/
      author: Kevin Hendricks <<EMAIL>>
8   It is currently not possible to mark "can not" as rare, because "can" and
    "not" are good words.  Find a way to let "rare" overrule "good"?
8   Make "en-rare" spell file?  Ask Charles Campbell.
8   The English dictionaries for different regions are not consistent in their
    use of words with a dash.
7   Insert mode completion mechanism that uses the spell word lists.
8   Add hl groups to 'spelllang'?
	:set spelllang=en_us,en-rare/SpellRare,en-math/SpellMath
    More complicated: Regions with different languages?  E.g., comments
    in English, strings in German (po file).


Diff mode:
9   When making small changes, e.g. deleting a character, update the diff.
    Possibly without running diff.
8   Also show difference with the file when editing started?  Should show what
    can be undone. (Tom Popovich)

Folding:
    (commands still available: zI zJ zK zp zP zq zQ zV zy zY;
    secondary: zB zS zT zZ, z=)
-   Patch to make closed folds line up. (Charles Campbell, 2014 Sep 12)
    Remark from Roland Eggner: does it cause crashes? (2014 Dec 12)
    Updated patch by Roland Eggner, Dec 16
    Updated patch from Charles, 2016 Jul 2
-   Patch to make fold updates much faster. (Christian Brabandt, 2012 Dec)
-   Patch to have the fold and sign column and at the last line of the buffer.
    (Marco Hinz, 2014 Sep 25)
    Alternate suggestion: let all columns continue, also the number column.
-   Patch to add FoldedLineNr highlighting: different highlighting for the
    line number of a closed fold. (eXerigumo Clanjor, 2013 Jul 15)
-   Patch to use 'foldnestmax' also for "marker" foldmethod. (Arnaud Lacombe,
    2011 Jan 7)
-   'foldcolumn' in modeline applied to wrong window when using a session.
    (Teemu Likonen, March 19)
-   With foldmethod=syntax and nofoldenable comment highlighting isn't
    removed.  (Marcin Szewczyk, 2017 Apr 26)
-   The ":move" command does not honor closed folds. (Ryan Lue, #2351)
-   When completion inserts the first match, it may trigger the line to be
    folded.  Disable updating folds while completion is active? (Peter Odding,
    2010 Jun 9)
-   When 'foldmethod' is "indent", adding an empty line below a fold and then
    indented text, creates a new fold instead of joining it with the previous
    one.  (Evan Laforge, 2009 Oct 17)
-   Add ":nofold".  Range will apply without expanding to closed fold.
8   Vertical folds: looks like vertically split windows, but the cursor moves
    through the vertical separator, separator moves when scrolling.
8   Add "z/" and "z?" for searching in not folded text only.  Or use a regexp
    item, so that it can be used in any pattern.
8   When a closed fold is displayed open because of 'foldminlines', the
    behavior of commands is still like the fold is closed.  How to make the
    user aware of this?
8   Add an option 'foldskip' with values like 'foldopen' that specifies which
    commands skip over a closed fold.
8   "H" and "L" count buffer lines instead of window lines. (Servatius Brandt)
8   Add a way to add fold-plugins.  Johannes Zellner has one for VB.
7   When using manual folding, the undo command should also restore folds.
-   Allow completely hiding a closed fold.  E.g., by setting 'foldtext' to an
    empty string.  Require showing a character in 'foldcolumn' to avoid the
    missing line goes unnoticed.
    How to implement this?
-   When pressing the down arrow of a scrollbar, a closed fold doesn't scroll
    until after a long time.  How to make scrolling with closed folds
    smoother?
-   When creating a session, also store folds for buffers in the buffer list,
    using the wininfo in wi_folds.
-   When currently editing the first file in the argument list the session
    file can contain:
	args version.c main.c
	edit version.c
    Can editing version.c twice be avoided?
-   'foldmethod' "textobject": fold on sections and paragraph text objects.
-   "zuf": undo change in manual fold. "zUf" redo change in manual fold.  How
    to implement this?
-   "zJ" command: add the line or fold below the fold in the fold under the
    cursor.
-   'foldmethod' "syntax": "fold=3" argument: set fold level for a region or
    match.
-   Apply a new foldlevel to a range of lines. (Steve Litt)

Multi-byte characters:
-   When editing a file with both utf-8 and latin1 text Vim always falls back
    to latin1.  Add a command to convert the latin1 characters to utf-8?
	:unmix utf-8,latin1 filename
    Would only work when 'encoding' is utf-8.
9   When the tail byte of a double-byte character is illegal (e.g., a CR), the
    display is messed up (Yasuhiro Matsumoto).  Should check for illegal
    double-byte characters and display them differently (display each single
    byte).
9   'fenc' in modeline problem: add option to reload the file when 'fenc' is
    set to a different value in a modeline?  Option can be default on.  Could
    it be done with an autocommand?
8   Add an item in 'fileencodings' to check the first lines of a file for
    the encoding.  See Python PEP: http://www.python.org/peps/pep-0263.html.
    To avoid getting a wrong encoding only accept something Emacs-like:
    "-*- coding: enc-na_me.foo -*-" and "-*- coding= enc-na_me.foo -*-"
    Match with "-\*-\s*coding[:=]\s*\([::word::-_.]\+\)\s*-\*-" and use first
    item.
8   Add an item in 'fileencodings' to check the first line of an XML file for
    the encoding.  <?xml version="1.0" encoding="UTF-8"?>  Or "charset=UTF-8"?
    For HTML look for "charset=utf-8".
8   When a file was converted from 'fileencoding' to 'encoding', a tag search
    should also do this on the search pattern. (Andrzej M. Ostruszka)
8   When filtering changes the encoding 'fileencoding' may not work.  E.g.,
    when using xxd and 'fileencoding' is "utf-16".  Add an option to set a
    different fileencoding for filter output?
7   When converting a file fails, mention which byte could not be converted,
    so that the user can fix the problem.
8   Add configure option to be able to disable using the iconv library. (Udo
    Schweigert)
9   'aleph' should be set to 1488 for Unicode. (Zvi Har'El)
8   Should add test for using various commands with multibyte characters.
8   'infercase' doesn't work with multibyte characters.
8   toupper() function doesn't handle byte count changes.
7   Searching and composing characters:
    When searching, should order of composing characters be ignored?
    Add a special item to match with a composing character, so that composing
    characters can be manipulated.
8   Should implement 'delcombine' for command line editing.
8   Detect overlong UTF-8 sequences and handle them like illegal bytes.
8   ":s/x/\u\1/" doesn't work, making uppercase isn't done for multibyte
    characters.
8   UTF-8: "r" in Visual mode doesn't take composing characters.
8   UTF-8: When there is a precomposed character in the font, use it instead
    of a character and a composing character.  See xterm for an example.
7   When a character can't be displayed, display its digraph instead.
    'display' option to specify this.
7   Use ideas for nl_langinfo() from Markus Kuhn in enc_default():
    (www.cl.cam.ac.uk/~mgk25/ucs/langinfo.c)
-   GTK and Win32: Allow selecting fonts for 'guifontset' with the
    fontselector somehow.
-   GTK and Win32: make it possible to set the font for the menu to make it
    possible to have 'encoding' different from the current locale.
-   dbcs_class() only works for Japanese and Korean.  Implement this for
    other encodings.  The "euc-jp" and "euc-kr" choices might be wrong.
-   Find some way to automatically select the right GUI font or fontset,
    depending on the default value of 'encoding'.
    Irrelevant in the GTK+ 2 GUI so long as UTF-8 is used.
    For Windows, the charset_pairs[] table could be used.  But how do we know
    if a font exists?
-   Do keyboard conversion from 'termencoding' to 'encoding' with
    convert_input() for Mac GUI.
-   Add mnemonics from RFC1345 longer than two characters.
    Support CTRL-K _{mnemonic}_
-   Make 'breakat' accept multibyte characters.  Problem: can't use a lookup
    table anymore (breakat_flags[]).
    Simplistic solution: when 'formatoptions' contains "m" also break a line
    at a multibyte character >= 0x100.
    Making breakat support multibyte characters (Yasuhiro Matsumoto, #6598)
    Scroll doesn't work correctly, why?
-   Add the possibility to enter mappings which are used whenever normal text
    could be entered.  E.g., for "f" command.  But not in Normal mode.  Sort
    of opposite of 'langmap'.  Use ":amap" command?
-   When breaking a line, take properties of multibyte characters into
    account.  The "linebreak" program from Bruno Haible can do it, this
    is now part of gnulib module unilbrk:
    https://www.gnu.org/software/gnulib/MODULES.html
    But it's very complicated...
-   Problem with 'langmap' being used on the rhs of a mapping. (Nikolai
    Weibull, 2008 May 14).
    Possibly related problem: Alexey Muranov, 2015 Apr 2


Printing:
7   Implement "undercurl" for printing.
-   Add "page width" to wrap long lines.
-   Win32: use a font dialog for setting 'printfont'.  Can reuse the code for
    the 'guifont' dialog, put the common code in a separate function.
-   Add the file timestamp to the page header (with an option). (George
    Reilly)
-   Win32: when 'printfont' is empty use 'guifont'.
-   Unix: Use some dialog box to do the obvious settings (paper size, printer
    name, portrait/landscape, etc).
-   PostScript: Only works for text that can be converted to an 8-bit
    character set.  How to support Unicode fully?
-   Allow specifying the paper size, instead of using a standard size.  Same
    units as for the margins.
-   Support right-to-left text?
8   Make the foreground color darkening function preserve the hue of the
    color.


Syntax highlighting:
8   Make ":syn off" use 'runtimepath' instead of $VIMRUNTIME. (Gary Johnson)
    Should do the same for ":syn on" and ":syn manual".
8   Support "containedin" argument for ":syn include", so that the defined
    cluster can be added to existing syntax items.
8   C syntax: Don't highlight {} as errors inside () when used like this:
    "({ something })", often used in GCC code.
7   Add a "startgroup" to a region.  Used like "nextgroup" inside the region,
    preferred item at the start of the region. (Charles Campbell)
8   When editing a new file without a name and giving it a name (by writing
    it) and 'filetype' is not set, detect the filetype.  Avoid doing it for
    ":wq file".
7   For "nextgroup" we have skipwhite, skipnl and skipempty.  It would be
    really nice to be able to skip with a pattern.  Or skip with a syntax
    group. (Nikolai Weibull, 2007 Feb 27)
8   Make conversion to HTML faster (Write it in C or pre-compile the script).
9   There is still a redraw bug somewhere.  Probably because a cached state is
    used in a wrong way.  I can't reproduce it...
7   Be able to change only the background highlighting.  Useful for Diff* and
    Search highlighting.
7   When 'number' is set highlight the number of the current line.
    Must be enabled with an option, because it slows down display updating.
8   Allow the user to add items to the Syntax menu sorted, without having to
    change this for each release.
8   Add a "matchcontains" for regions: items contained in the start or end
    pattern, but not in the body.
8   Add a "keepend-contained" argument: Don't change the end of an item this
    one is contained in.  Like "keepend" but specified on the contained item,
    instead of the containing item.
8   cpp.vim: In C++ it's allowed to use {} inside ().
8   Some syntax files set 'iskeyword', they should use "syn iskeyword".
    Also need a separate 'iskeyword' for the command line, e.g., in a help
    window ":e /asdf/asdf/" CTRL-W works different.
8   Add specific syntax item to match with parens/braces that don't have a
    "%" match.  :syntax nomatch cMatchError (,{,[,),},] [contained]
8   Highlight the text between two matching parens (e.g., with a grey
    background) when on one of the parens or in between them.
    Option for the matchparen plugin?
8   When using a cterm, and no ctermfg or ctermbg are defined, use start/stop
    sequences.	Add remark in docs that :if 'term' == "term-name" should be
    used.
8   Add @spell cluster to String and Comment groups for many languages.  Will
    allow spell checking. (Fleiner)
8   When listing syntax items, try to sort the keywords alphabetically.  And
    re-insert the [] if possible.
8   Make it possible to use color of text for Visual highlight group (like for
    the Cursor).
8   It would be useful to make the highlight group name an expression.  Then
    when there is a match, the expression would be evaluated to find out what
    highlight group to use.  Could be used to check if the shell used in a
    password file appears in /etc/shells. (Nikolai Weibull)
	syn match =s:checkShell(v:match) contained 'pattern'
8   Make it possible to only highlight a sub-expression of a match.  Like
    using "\1" in a ":s" command.
8   Support for deleting syntax items:
    :syn keyword cTodo remove this
    :syn match cTodo remove "pattern"
    :syn region cString remove start="this" end="that"
8   Add possibility to sync on something else, when the syncing in one way
    doesn't find match.  For HTML: When no {script} is found, try looking for
    a '<'.  (Fleiner)
7   Replace the synchronizing method with a state machine specification?
    Should be able to start at any line in the file, search forwards or
    backwards, and use the result of matching a pattern.
7   Use parsing like awk, so that e.g., a ( without a matching ) can be
    detected.
8   Make it possible to use "inverted" highlighting, invert the original
    character.  For Visual mode.  (xterm-selection already does this).
8   Highlight non-printable characters with "SpecialChar", linked to
    "Special".  Display them with the digraph characters, if possible.
8   Highlight the clipboard-selection with a highlight group.
8   Be able to reset highlighting to its original (default) values.
7   Be able to write current highlighting to a file as commands, similar to
    ":mkvimrc".
8   Improve c.vim:
    - Add check for unterminated strings, with a variable to switch it on:
      "c_strict_ansi".
    - Detect unbalanced "#endif".  Requires looking back a long way...
8   Add an option to restrict the updating of syntax highlighting to the
    current line while in Insert mode.
8   When guessing value of 'background', the syntax file has already been
    loaded (from the .gvimrc).	After changing 'background', load it again?
8   Add ":syn resync" command, to re-parse the whole file until the current
    display position.
8   Should support "me" offset for a region start pattern.  To be used to
    allow searching for the end pattern inside the match of the end pattern.
    Example: syn region pikeXX start="([^{]" end=")" should work on "()".
8   When using a regexp for "contains=", should delay matching with it until
    redrawing happens.  Set a flag when a group is added, check this flag when
    highlighting starts.
7   It's possible for an item to be transparent, so that the colors of an item
    lower on the stack is used.  Also do this with highlighting, so that the
    user can set transparent highlighting?  E.g. a number in a C comment would
    get the color of a comment, a number in an assignment Normal. (Nikolai
    Weibull)
7   Add "semitrans": Add highlighting.  E.g., make the text bold, but keep the
    colors.  And add colors, so that Green+Red becomes Yellow.
    E.g. for this html:
	<B> bold text <I> italic+bold text </B> italic text </I>
7   CTRL-] checks the highlight group for finding out what the tag is.
7   Add an explanation how a list of words can be used to highlight misspelled
    words.
7   Should find a better way to parse the :syntax and :highlight commands.
    Use tables or lists that can be shared by parsing for execution and
    completion?
8   Add ColorSchemePost autocommand event, so that scripts can set up their
    highlighting. (Salman Halim)
7   Add a few sets of colors (e.g. Borland Turbo C one).  With a menu to
    select one of the sets.
8   Add offsets to sub-matches: "\(a*\) *"he=e1-1
    'e' is end of match 'e1' is end of sub-match 1, 's2' is start of submatch
    2, etc.
8   In Insert mode, when there are typeahead characters, postpone the
    highlighting (for "." command).
8   Syncing on comments isn't 100% correct when / / lines mix with / * and * /.
    For example: What about a line that starts with / / and contains * /?
8   Ignore / * and  * / inside strings, when syncing.
7   Build a few more syntax files from the file "/usr/share/misc/vgrindefs":
    ISP, LDL, Icon, ratfor.  And check "nedit/source/highlight.c".
6   Add possibility to have background color continue until the right edge of
    the window.  Useful for comment blocks and function headings. (Rogall)
-   Make it possible to add "contains" items for all items in a group.	Useful
    when extending an already existing syntax file.
-   Add line-continuation pattern for non-syncing items too?
-   Add possibility to highlight the whole line, including the right margin
    (for comment blocks).
-   Add 'hlmatch' option: List of flags:
    'c': highlight match for character under the cursor.
    'b': highlight the previous (, and its match.
    'a': highlight all text from the previous ( until its match.
	 Also for {}, <>, etc.?
    'e': highlight all braces without a match (slow?)
    OR: add an argument "cursor" to the syntax command, which means that the
    region/match/keyword is only highlighted when the cursor is on it.
    (Campbell)
    Or do it like Elvis: define text objects and how to highlight them around
    the cursor. (Iain Truskett)
7   Make it possible to use all words in the tags files as Keyword.
    Can also be done with a script (but it's slow).
7   Make it possible to call a ":" command when a match is found.  Should
    allow for adding keywords from the text (e.g. variables that are set).
    And allows for sections with different highlighting.
7   Add highlight group for commandline: "Commandline".  Make sure it
    highlights the command line while typing a command, and any output from
    messages.  And external commands?
8   Make a version that works like less, but with highlighting: read stdin for
    text, exit at end of file, don't allow editing, etc.  moreim?  lessim?
7   SpecialKey highlighting overrules syntax highlighting.  Can't give an
    unprintable char another color.  Would be useful for ^M at end of line.
-   Syntax highlight for a region does not work with a "nextgroup" if the
    start match is empty. #8449
-   The :syntax cchar value can only be a single character.  It would be
    useful to support combining characters. (Charles Campbell)  Also #4687
-   Syntax highlighting slow (hangs) in SASS file. (Niek Bosch, 2013 Aug 21)
-   Several syntax file match "^\s*" which may get underlined if that's in the
    highlight group.  Add a "\zs" after it?
-   patch to add "combine" flag to  syntax commands. (so8res, 2012 Dec 6)
    Patch to add "combine" to :syntax, combines highlight attributes. (Nate
    Soares, 2012 Dec 3)
-   Syntax update problem in one buffer opened in two windows, bottom window
    is not correctly updated. (Paul Harris, 2012 Feb 27)
-   Syntax region with 'concealends' and a 'cchar' value, 'conceallevel' set
    to 2, only one of the two ends gets the cchar displayed. (Brett Stahlman,
    2010 Aug 21, Ben Fritz, 2010 Sep 14)
-   Using "syn sync breaklines=2" works for when text is changed, but not when
    scrolling or redrawing.  Should start search for syntax patterns above the
    first drawn line. (#8103)
-   Syntax priority problem. (Charles Campbell, 2011 Sep 15)
-   Syntax highlighting wrong for transparent region. (Doug Kearns, 2007 Feb
    26)
-   Bug in using a transparent syntax region. (Hanlen in vim-dev maillist,
    2007 Jul 31)
-   Syntax HL error caused by "containedin". (Peter Hodge, 2006 Oct 6)
-   Open two windows on the same C code, delete a ")" in one window, resulting
    in highlighted "{" in that window, not in the other.
-   When using "nextgroup" and the group has an empty match, there is no
    search at that position for another match. (Lukas Mai, 2008 April 11)


Vim script language:
8   Make the filename and line number available to script functions, so that
    they can give useful debugging info.  The whole call stack would be ideal.
    At least use this for error messages.
7   Execute a function with standard option values.  No need to save and
    restore option values.  Especially useful for new options.  Problem: how
    to avoid a performance penalty (esp. for string options)?
-   range for ":exec", pass it on to the executed command.  (Webb)
7   ":include" command: just like ":source" but doesn't start a new scriptID?
    Will be tricky for the list of script names.
8   Have a look at VSEL.  Would it be useful to include? (Bigham)
8   Have a prefix for a function to make it unique.  When using packages it
    can be the plugin name.
    Perhaps also have a way to remove everything that the package added?
    including autocommands.
7   Pre-parse or compile Vim scripts into a bytecode, like :def functions.
    Possibilities:
    1. Put the bytecode with the original script, with an ":if
       has('bytecode-1234')" around it, so that it's only used with a Vim that
       supports the version.  Update the code with a command, can be used in
       an autocommand.
    2. Use a ".vic" file (like Python use .pyc).  Create it when writing a
       .vim file.  Problem: distribution, non-writable directory, etc.
    3. Use a cache directory for each user.  Disadvantage: cache lookup may
       cost more time than bytecode wins.
7   Add argument to winwidth() to subtract the space taken by 'foldcolumn',
    signs and/or 'number'.
6   Add ++ and -- operators?  They only work on variables (lvals), how to
    implement this?
8   Add functions:
	has(":command")		Check if ":command" works.  compare function
				with "ex_ni".  E.g. for ":simalt".
	escape()		Add argument to specify what to escape with.
	modestack()		Instead of just the current mode return the
				stack of Insert / CTRL-O / :normal things.
	realname()		Get user name (first, last, full)
				user_fullname() patch by Nikolai Weibull, Nov
				3 2002
				Only add this when also implemented for
				non-Unix systems, otherwise a shell cmd could
				be used.
				get_user_name() gets login name.
	menuprop({name}, {idx}, {what})
				Get menu property of menu {name} item {idx}.
				menuprop("", 1, "name") returns "File".
				menuprop("File", 1, "n") returns "nmenu
				File.Open..." argument.
				Patch by Ilya Sher, 2004 Apr 22
				Return a list of menus and/or a dictionary
				with properties instead.
	mapname({idx}, mode)	return the name of the idx'th mapping.
				Patch by Ilya Sher, 2004 Mar 4.
				Return a list instead.
	char2hex()		convert char string to hex string.
	crypt()			encrypt string
	decrypt()		decrypt string
	attributes()		return file protection flags "drwxrwxrwx"
	shorten(fname)		shorten a file name, like home_replace()
	perl(cmd)		call Perl and return string
	inputrl()		like input() but right-to-left
	typed()			return the characters typed and consumed (to
				find out what happened)
	virtualmode()		add argument to obtain whether "$" was used in
				Visual block mode.
	getacp()		Win32: get codepage (Glenn Maynard)
	libcall()		Allow more than one argument.
	libcallext()		Like libcall(), but using a callback function
				to allow the library to execute a command or
				evaluate an expression.
7   Make bufname("'0") return the buffer name from mark '0.  How to get the
    column and line number?  col("'0") currently returns zero.
8   argc() returns 0 when using "vim -t tag".  How to detect that no file was
    specified in any way?  To be able to jump to the last edited file.
8   Pass the command line arguments to Vim scripts in some way.  As v:args
    List?  Or extra parameter to argv()?
8   Add command arguments with three dashes, passed on to Vim scripts.
6   User functions: Functions local to buffer "b:func()"?
8   For Strings add ":let var[{expr}] = {expr}".  When past the end of "var"
    just ignore.
8   The "= register should be writable, if followed by the name of a variable,
    option or environment variable.
8   ":let &option" should list the value of the option.
8   ":let Func().foo = value" should work, also when "foo" doesn't exist.
    Also: ":let Func()[foo] = value" should work.  Same for a List.
7   Add synIDlist(), making the whole list of syntax items on the syntax stack
    available as a List.
8   Add autocommand-event for when a variable is changed:
	:au VarChanged {varname} {commands}
8   Add "has("gui_capable")", to check if the GUI can be started.
8   Add possibility to use variables like registers: characterwise (default),
    linewise (when ending in '\n'), blockwise (when ending in '\001').	reg0,
    rega, reg%, etc.  Add functions linewise({expr}), blockwise({expr}) and
    charwise({expr}).
7   Make it possible to do any command on a string variable (make a buffer
    with one line, containing the string).  Maybe add an (invisible) scratch
    buffer for this?
	result = scratch(string, command)
	result = apply(string, command)
	result = execute(string, command)
    "command" would use <> notation.
    Does scratch buffer have a number?  Or re-use same number?
7   Add function to generate unique number (date in milliseconds).


Robustness:
6   Add file locking.  Lock a file when starting to edit it with flock() or
    fcntl().  This patch has advisory file locking while reading/writing
    the file for Vim 5.4: ~/vim/patches/kahn_file_locking .
    The patch is incomplete (needs support for more systems, autoconf).
    Andy doesn't have time to work on it.
    Disadvantage: Need to find ways to gracefully handle failure to obtain a
    lock.  When to release a lock: When buffer is unloaded?


Performance:
7   For string variables up to 3 bytes don't allocate memory, use v_list
    itself as a character array.  Use VAR_SSTRING (short string).
7   Add 'lazysize' option: Above this size Vim doesn't load everything before
    starting to edit a file.  Things like 'fileencodings' only work up to this
    size, modelines only work at the top.  Useful for large log files where
    you only want to look at the first few pages.  Use zero to disable it.
8   move_lines() copies every line into allocated memory, making reloading a
    buffer a lot slower than re-editing the file.  Can the memline be locked
    so that we don't need to make a copy?  Or avoid invoking ml_updatechunk(),
    that is taking a lot of time.  (Ralf Wildenhues, 2008 Jul 7)
    With a patch, but does it work?
8   Turn b_syn_ic and b_syn_containedin into b_syn_flags.
9   Loading menu.vim still takes quite a bit of time.  How to make it faster?
8   in_id_list() takes much time for syntax highlighting.  Cache the result?
7   setpcmark() shifts the jumplist, this takes quite a bit of time when
    jumping around.  Instead use an index for the start?
8   When displaying a space with only foreground highlighting, it's the same
    as a space without attributes.  Avoid displaying spaces for the "~" lines
    when starting up in a color terminal.
8   Avoid alloc() for scratch buffer use, esp. in syntax.c.  It's very slow on
    Win16.
8   Profiling shows that in_id_list() is used very often for C code.  Can this
    function be improved?
8   For an existing file, the page size of the swap file is always the
    default, instead of using the block size of the device, because the swap
    file is created only after setting the block size in mf_open().  How can
    this be improved?
8   Set default for 'ttyscroll' to half a screen height?  Should speed up
    MS-DOS version. (Negri)
7   C syntax highlighting gets a lot slower after ":set foldmethod=syntax".
    (Charles Campbell)  Inserting a "{" is very slow. (dman)
7   HTML syntax highlighting is slow for long lines.  Try displaying
    http://www.theregister.co.uk/content/4/22908.html. (Andre Pang)
7   Check how performance of loading the wordlist can be improved (adding a
    lot of abbreviations).
7   Compile Ex commands to byte codes.  Store byte codes in a vim script file
    at the end, after "compiled:.  Make it look like a single comment line
    for old Vim versions.  Insert first line "Vim script compiled <timestamp>.
    Only used compiled code when timestamp matches the file stat.
    Add command to compile a vim script and add it to the file in-place.
    Split Ex command executing into a parsing and executing phase.
    Use compiled code for functions, while loops, etc.
8   When defining autocommands (e.g., from $VIMRUNTIME/filetype.vim), need to
    compare each pattern with all existing patterns.  Use a hash code to avoid
    using strcmp() too often?
7   Include turbo_loader patches, speeding up reading a file?
    Speed up reading a file by reading it into a fixed-size buffer, creating
    the list of indexes in another buffer, and then copying the result into a
    memfile block with two copies.  Then read the next block into another
    fixed-size buffer, create the second list of indexes and copy text from
    the two blocks to the memfile block.
7   do_cmdline(): Avoid that the command line is copied to allocated memory
    and freed again later all the time.  For while loops, and for when called
    with an argument that can be messed with.
    Generic solution: Make a struct that contains a pointer and a flag that
    indicates if the pointer should be freed when replaced.
7   Check that the file size is not more than "sizeof(long)".
-   Further improve finding mappings in maphash[] in vgetorpeek()
8   Syntax highlighting is slow when deleting lines.  Try in
    $VIMRUNTIME/filetype.vim.
-   "out of memory" after deleting (1,$d) and changing (:%s/^/> /) a lot of
    lines (27000) a few times.  Memory fragmentation?
-   Have a look at how pdksh does memory allocation (alloc.c). (Dalecki)
-   Do profiling on:
    - :g/pat/normal cmd
    - deleting 10Mbyte worth of lines (netscape binary)
    - "[i" and "[d" (Yegappan Lakshmanan)
    - ":g/^/m0" on a 450Kbyte file.  And the "u".
    - highlighting "~/vim/test/longline.tex", "~/vim/test/scwoop.tcl" and
      "~/vim/test/lockup.pl".
    - loading a syntax file to highlight all words not from a dictionary.
    - editing a Vim script with syntax highlighting on (loading vim.vim).
7   Screen updating can be further improved by only redrawing lines that were
    changed (and lines after them, when syntax highlighting was used, and it
    changed).
    - On each change, remember start and end of the change.
    - When inserting/deleting lines, remember begin, end, and line count.
-   Use macros/duarte/capicua for profiling.  Nvi 1.71 is the fastest!
-   When using a file with one long line (1Mbyte), then do "$hhhh", is still
    very slow.  Avoid calling getvcol() for each "h"?
-   Executing a register, e.g. "10000@@" is slow, because ins_typebuf has to
    move the previous commands forward each time.  Pass count from
    normal_cmd() down to do_execreg().
-   Avoid calls to plines() for cursor line, use w_cline_height.
-   After ":set nowrap" remove superfluous redraw with wrong hor. offset if
    cursor is right of the screen.
8   Make CTRL-C on Unix generate a signal, avoid using select() to check for a
    CTRL-C (it's slow).


Code size:
8   GUI: When NO_CONSOLE is defined, more code can be excluded.
-   Put getline() and cookie in a struct, so only one argument has to be
    passed to do_cmdline() and other functions.
8   Make a GUI-only version for Unix?
8   In buf_write _() isn't needed when setting errmsg, do it once when using
    it.
7   When compiling with a GUI-only version, the code for cterm colors can be
    left out.
8   When compiled with a GUI-only version, the termcap entries for terminals
    can be removed.


Messages:
8   When using ":q" in a changed file, the error says to "add !".  Add the
    command so that beginners understand it: "use :q!".
8   For 'verbose' level 12 prints commands from source'ed files.  How to skip
    lines that aren't executed?  Perhaps move the echoing to do_cmdline()?
8   Use 'report' for ":bdel"?  (Krishna)  To avoid these messages when using a
    script.
-   Delete message after new command has been entered and have waited for key.
    Perhaps after ten seconds?
-   Make message history available in "msg" variables: msg1, msg2, .. msg9.
9   Check handling of overwriting of messages and delays:
    Very wrong: errors while redrawing cause endless loop.
    When switching to another file and screen scrolls because of the long
    message and return must be typed, don't scroll the screen back before
    redrawing.
8   When address range is wrong you only get "Invalid range".  Be a bit more
    specific: Negative, beyond last line, reverse range?  Include the text.
8   Make it possible to ignore errors for a moment ('errorignore'?).  Another
    option to switch off giving error messages ('errorquiet'?).  Also an option
    not to give any messages ('quiet')?  Or ":quiet on", ":quiet off".
    Careful: For a severe error (out of memory), and when the user starts
    typing, error messages must be switched back on.
    Also a flag to ignore error messages for shell commands (for mappings).
-   Option to set time for emsg() sleep.  Interrupt sleep when key is typed?
    Sleep before second message?
8   In Ex silent mode or when reading commands from a file, what exactly is
    not printed and what is?  Check ":print", ":set all", ":args", ":vers",
    etc.  At least there should be no prompt. (Smulders)  And don't clear the
    screen when reading commands from stdin. (Kendall)
    --> Make a difference between informative messages, prompts, etc. and
	error messages, printing text, etc.
8   Window should be redrawn when resizing at the hit-enter prompt.
    Also at the ":tselect" prompt.  Find a generic solution for redrawing when
    a prompt is present (with a callback function?).


Screen updating:
-   screen_line():
    - insert/delete character stuff.
    - improve delete rest of line (spaces at end of line).
-   When moving or resizing window, try to avoid a complete redraw (esp. when
    dragging the status line with the mouse).
-   When 'lazyredraw' set, don't echo :ex commands?  Need a flag to redraw when
    waiting for a character.
8   Add a ":refresh [winnr]" command, to force updating a window.  Useful from
    an event handler where ":normal" can't be used.  Also useful when
    'lazyredraw' is set in a mapping.

Scrolling:
8   Add "zy" command: scroll horizontally to put the cursor in the middle.
6   Add option to set the overlap for CTRL-F and CTRL-B. (Garhi)
-   extend 'scrollbind' option: 'scrollopt' words "search", "relative", etc..
    Also 'e'xecute some commands (search, vertical movements) in all bound
    windows.
7   Add 'scrollbind' feature to make the offset of one window with the next
    one equal to the window height.  When editing one file in both windows it
    looks like each window displays a page of the buffer.
-   Allow scrolling by dragging with the mouse (grab a character and move it
    up/down).  Like the "hand" in Acrobat reader.  Use Alt-LeftMouse for this?
    (Goldfarb)
-   Add command to execute some commands (search, vertical movements) in all
    bound windows.
-   Add 'search' option to 'scrollopt' to allow 'scrollbind' windows to
    be bound by regexp searches
-   Add "z>" and "z<": scroll sideways one screenful. (Campbell)
-   Add option to set the number of lines when not to scroll, instead of the
    fixed number used now (for terminals that scroll slow with a large number
    of lines but not with a single line).


Autoconf:
8   Should use acconfig.h to define prototypes that are used by autoheader.
8   Some compilers don't give an error for "-OPT:Olimit" but a warning. (Webb)
    Add a check for the warning, so that "Olimit" can be added automatically?
-   Autoconf: Use @datadir@ for the system independent files.  Make sure the
    system dependent and system independent files are separated. (Leitner).
-   Add autoconf check for waitpid()/wait4().
-   Remove fcntl() from autoconf, all systems have it?
-   Set default for 'dictionary', add search for dictionary to autoconf.


Perl interface:
8   Rename typemap file to something else?
7   Make buffers accessed as Perl arrays. (Clark)
7   Make it possible to compile with non-ANSI C?
6   Tcl/Tk has the "load" command: load a shared library (.so or .dll).


Shared libraries:
8   libcall() can keep the library around instead of always calling dlclose().
    (Jason Felice, 2018 Mar 20)
6   Add support for loading shared libraries, and calling functions in it.
	:libload internal-name libname
	:libunload internal-name
	:liblist
	:libcall internal-name function(arg1, arg2, ...)
	:libcall function(arg1, arg2, ...)
    libcall() can have only one integer or String argument at the moment.
6   Have a look on how Perl handles loading dynamic libraries.


Tags:
9   With ":set tags=./tags,../tags" and a tag appears in both tags files it is
    added twice.  Requires figuring out the actual file name for each found
    match.  Remove tag_fname from the match and combine it with the fname in
    the match (without expanding or other things that take time).  When
    'tagrelative' is off tag_fname isn't needed at all.
8   For 'tags' wildcard in the file name is not supported, only in the path.
    This is due to it using |file-searching|. Suboptimal solution would be to
    make the filename or the whole option use |wildcards| globing, better
    would be to merge the 2 kinds of globing. originally (Erik Falor, 2008
    April 18), updated (Ian Kelling, 2008 July 4)
7   Can CTRL-] (jump to tag) include a following "." and "->" to restrict the
    number of possible matches? Check tags file for an item that has members.
    (Flemming Madsen)
8   Scope arguments for ":tag", e.g.: ":tag class:cPage open", like Elvis.
8   When output of ":tselect" is long, getting the more-prompt, should be able
    to type the tag number directly.
7   Add the possibility to use the "-t {tag}" argument multiple times.  Open a
    window for each tag.
7   Make output of ":tselect" a bit nicer.  Use highlighting?
7   Highlight the "tag 1 of >2" message.  New highlight group, or same as "hit
    bottom" search message.
7   When using ":tag" at the top of the tag stack, should add another entry,
    so CTRL-T can bring you back to where you are now AND to where you were
    before the previous ":tag" command. (Webb)
-   When doing "[^I" or "[^D" add position to tag stack.
-   Add command to put current position to tag stack: ":tpush".
-   Add functions to save and restore the tag stack?  Or a command to switch
    to another tag stack?  So that you can do something else and come back to
    what you were working on.
7   When using CTRL-] on someClass::someMethod, separate class from method and
    use ":ta class:someClass someMethod".
    Include C++ tags changes (Bertin).	Change "class::func" tag into "func"
    with "class=class"?  Docs in oldmail/bertin/in.xxx.
7   Add ":tagargs", to set values for fields:
	:tagargs class:someclass file:version.c
	:tagargs clear
    These are then the default values (changes the order of priority in tag
    matching).
7   Support for "gtags" and "global"?  With ":rtag" command?
    There is an example for how to do this in Nvi.
    Or do it like Elvis: 'tagprg' and 'tagprgonce' options. (Yamaguchi)
    The Elvis method is far more flexible, do it that way.
7   Support "col:99" extra field, to position the cursor in that column.  With
    a flag in 'cpoptions' to switch it off again.
7   Better support for jumping to where a function or variable is used.  Use
    the id-utils, with a connection to "gid" (Emacs can do it too).  Add
    ":idselect", which uses an "ID" database (made by "mkid") like "tselect".


Win32 GUI:
8   Make debug mode work while starting up (vim -D).  Open console window for
    the message and input?
8   When using "Edit with Vim" for one file it changes directory, when several
    files are selected and using "Edit with single Vim" the directory isn't
    changed.  At least change directory when the path is the same for all
    files.  Perhaps just use the path of the first file or use the longest
    common part of the path.
8   Add font argument to set the lfCharSet. (Bobcik)
8   Somehow automatically detect the system language and set $LANG, so that
    gettext and menus work.
8   Could keep console open to run multiple commands, to avoid the need to hit
    return in every console.
    Also: Look at how Emacs does run external commands:
	http://www.cs.washington.edu/homes/voelker/ntemacs.html.
8   Need a separate PopUp menu for modeless selection.  Need two new commands:
    Copy selection to clipboard, Paste selection (as typed text).
8   Support copy/paste for other file formats.  At least HTML, perhaps RTF.
    Add "copy special" and "paste special" commands?
7   Use different default colors, to match the current Windows color scheme.
    Sys_WindowText, Sys_Window, etc. (Lionel Schaffhauser)
7   Use <C-Tab> to cycle through open windows (e.g., the find dialog).
7   <Esc> should close a dialog.
7   Keep the console for external commands open.  Don't wait for a key to be
    hit.  Re-open it when the user has closed it anyway.  Or use a prepended
    command: ":nowait {cmd}", or ":quiet", which executes {cmd} without any
    prompts.
7   Should be able to set an option so that when you double click a file that
    is associated with Vim, you can either get a new instance of Vim, or have
    the file added into an already running Vim.
7   The "-P" argument only works for the current codepage.  Use wide
    functions to find the window title.


GUI:
7   Implement ":popup" for other systems than Windows.
8   Implement ":tearoff" for other systems than Win32 GUI.
6   Implement ":untearoff": hide a torn-off menu.
8   When using the scrollbar to scroll, don't move the cursor position.  When
    moving the cursor: scroll to the cursor position.
9   Make <S-Insert> paste from the clipboard by default. (Kunze)
7   Menu local to a buffer, like mappings.  Or local to a filetype?
8   In Buffers menu, add a choice whether selecting a buffer opens it in the
    current window, splits the window or uses ":hide".
8   Dragging the mouse pointer outside of a Vim Window should make the text
    scroll.  Return a value from gui_send_mouse_event() to the machine
    specific code to indicate the time in which the event should be repeated.
8   Make it possible to ignore a mouse click when it's used to give Vim (gvim)
    window focus.  Also when a mouse click is used to bring a window to front.
8   Make the split into system independent code and system specific code more
    explicit.  There are too many #ifdefs in gui.c.
    If possible, separate the Vim code completely from the GUI code, to allow
    running them in separate processes.
7   X11: Support cursorColor resource and "-cr" argument.
8   X11 (and others): CTRL-; is not different from ';'.  Set the modifier mask
    to include CTRL for keys where CTRL produces the same ASCII code.
7   Add some code to handle proportional fonts on more systems?  Need to draw
    each character separately (like xterm).  Also for when a double-width font
    is not exactly double-width. (Maeda)
8   Should take font from xterm where gvim was started (if no other default).
8   Selecting font names in X11 is difficult, make a script or something to
    select one.
8   Visual highlighting should keep the same font (bold, italic, etc.).
8   Add flag to 'guioptions' to not put anything in the clipboard at all?
8   Should support a way to use keys that we don't recognize yet.  Add a
    command that adds entries to special_keys somehow.	How do we make this
    portable (X11, Win32, ..)?
7   Add a flag to 'guioptions' that tells not to remove inactive menu items.
    For systems where greying-out or removing menu items is very slow.  The
    menu items would remain visibly normally, but not do anything.
7   Add ":minimize" and ":maximize", which iconize the window and back.
    Useful when using gvim to run a script (e.g. 2html.vim).
7   X11: Is it possible to free allocated colors, so that other programs can
    use them again?  Otherwise, allow disabling allocating the default colors.
    Or allocate an own colormap (check UAE).  With an option to use it.  For
    the commandline, "-install" is mostly used for X11 programs.
7   Should support multi-column menus.
-   Should add option for where to put the "Help" menu: like Motif at the far
    right, or with the other menus (but still at the right).
-   Add menu item to "Keep Insert mode".
8   ":mkgvimrc" command, that includes menus.
6   Big change: Move GUI to separate program "vimgui", to make startup of vim a
    lot faster, but still be able to do "vim -g" or ":gui".
7   More explicit mouse button binding instead of 'mousemodel'?
7   Add option to set the position of the window on the screen.  'windowpos',
    which has a value of "123,456": <x>,<y>.
    Or add a command, like ":winsize"?
7   Add toolbar for more GUIs.
8   Make it possible to use "amenu icon=BuiltIn##", so that the toolbar item
    name can be chosen free.
7   Make it possible to put the toolbar on top, left, right and/or bottom of
    the window?  Allows for softkey-like use.
6   Separate the part of Vim that does the editing from the part that runs the
    GUI.  Communicate through a pseudo-tty.  Vim starts up, creates a
    pty that is connected to the terminal.  When the GUI starts, the pty is
    reconnected to the GUI process.  When the GUI stops, it is connected to
    the terminal again.  Also use the pty for external processes, it looks
    like a vt100 terminal to them.  Vim uses extra commands to communicate GUI
    things.
7   Motif: For a confirm() dialog <Enter> should be ignored when no default
    button selected, <Esc> should close the dialog.
7   When using a pseudo-tty Vim should behave like some terminal (vt52 looks
    simple enough).  Terminal codes to/from shell should be translated.
-   Would it be useful to be able to quit the GUI and go back to the terminal
    where it was started from?
7   Support "-visual <type>" command line argument.


Autocommands:
9   When triggering WinNew provide the window ID somehow.  #10633
9   Rework the code from FEAT_OSFILETYPE for autocmd-osfiletypes to use
    'filetype'.  Only for when the current buffer is known.
-   Put autocommand event names in a hashtable for faster lookup?
8   When the SwapExists event is triggered, provide information about the
    swap file, e.g., whether the process is running, file was modified, etc.
    Must be possible to check the situation that it's probably OK to delete
    the swap file. (Marc Merlin)
8   When all the patterns for an event are "*" there is no need to expand
    buffer names to a full path.  This can be slow for NFS.
7   For autocommand events that trigger multiple times per buffer (e.g.,
    CursorHold), go through the list once and cache the result for a specific
    buffer.  Invalidate the cache when adding/deleting autocommands or
    changing the buffer name.
7   Add TagJump event: do something after jumping to a tag.
8   Add "TagJumpFile" autocommand: When jumping to another file for a tag.
    Can be used to open "main.c.gz" when "main.c" isn't found.
8   Use another option than 'updatetime' for the CursorHold event.  The two
    things are unrelated for the user (but the implementation is more
    difficult).
7   Add autocommand event for when a buffer cannot be abandoned.  So that the
    user can define the action taking (autowrite, dialog, fail) based on the
    kind of file. (Yakov Lerner)  Or is BufLeave sufficient?
8   Autocommand for when modified files have been found, when getting input
    focus again (e.g., FileChangedFocus).
    Check when: getting focus, jumping to another buffer, ...
8   Autocommands should not change registers.  And marks?  And the jumplist?
    And anything else?  Add a command to save and restore these things.
8   Add autocommands, user functions and user commands to ":mkvimrc".
6   Add KeymapChanged event, so that the effects of a different keymap can be
    handled (e.g., other font) (Ron Aaron)
7   When trying to open a directory, trigger an OpenDirectory event.
7   Add file type in front of file pattern: <d> for directory, <l> for link,
    <x> for executable, etc.  With commas to separate alternatives.  The
    autocommand is only executed when both the file type AND the file pattern
    match. (Leonard)
5   Add option that specifies extensions which are to be discarded from the
    file name.  E.g. 'ausuffix', with ".gz,.orig".  Such that file.c.gz will
    trigger the "*.c" autocommands.  (Belabas)
7   Add something to break the autocommands for the current event, and for
    what follows.  Useful for a "BufWritePre" that wants to avoid writing the
    file.
8   When editing "tt.gz", which is in DOS format, 'fileformat' stays at
    "unix", thus writing the file changes it.  Somehow detect that the read
    command used dos fileformat.  Same for 'fileencoding'.
-   Add events to autocommands:
    Error	    - When an error happens
    VimLeaveCheck    - Before Vim decides to exit, so that it can be cancelled
		      when exiting isn't a good idea.
    CursorHoldC     - CursorHold while command-line editing
    WinMoved	    - when windows have been moved around, e.g, ":wincmd J"
    SearchPost	    - After doing a search command (e.g. to do "M")
    ShutDown	    - when the system is about to shut down
    InsertCharPost  - user typed a character in Insert mode, after inserting
		      the char.
    BufModified	    - When a buffer becomes modified, or unmodified (for
		      putting a [+] in the window title or checking out the
		      file from CVS).
    BufFirstChange  - When making a change, when 'modified' is set.  Can be
		      used to do a :preserve for remote files.
    BufChange	    - after a change was made.  Set some variables to indicate
		      the position and number of inserted/deleted lines, so
		      that marks can be updated.  HierAssist has patch to add
		      BufChangePre, BufChangePost and RevertBuf. (Shah)
    ViewChanged	    - triggered when the text scrolls and when the window size
		      changes.
    QuickfixList    - when any entry in the current list changes or another
		      list is selected
    QuickfixPosition - when selecting another entry in the current quickfix
		       list

-   Write the file now and then ('autosave'):
				  *'autosave'* *'as'* *'noautosave'* *'noas'*
    'autosave' 'as' number  (default 0)
	    Automatically write the current buffer to file N seconds after the
	    last change has been made and when |'modified'| is still set.
	    Default: 0 = do not autosave the buffer.
    Alternative: have 'autosave' use 'updatetime' and 'updatecount' but make
    them save the file itself besides the swapfile.
-   Buffer autocommands are a bit inconsistent.  Add a separate set of
    autocommands for the buffer lifecycle:
        BufIsCreated  (after buffer ID exists)
        BufIsLoaded   (after buffer ID has content)
        BufIsUnloaded (after buffer ID no longer has)
        BufIsWiped    (after buffer ID was wiped)
        BufIsRenamed  (after buffer ID gets another name)
    The buffer list and windows are locked, no changes possible


Omni completion:
-   Add a flag to 'complete' to be able to do omni completion with CTRL-N (and
    mix it with other kinds of completion).
-   Ideas from the Vim 7 BOF at SANE:
	- For interpreted languages, use the interpreter to obtain information.
	  Should work for Java (Eclipse does this), Python, Tcl, etc.
	  Richard Emberson mentioned working on an interface to Java.
	- Check Readline for its completion interface.
-   Ideas from others:
	http://www.wholetomato.com/
	http://www.vim.org/scripts/script.php?script_id=747
	    http://sourceforge.net/projects/insenvim
		or http://insenvim.sourceforge.net
	    Java, XML, HTML, C++, JSP, SQL, C#
	    MS-Windows only, lots of dependencies (e.g. Perl, Internet
		explorer), uses .dll shared libraries.
	    For C++ uses $INCLUDE environment var.
	    Uses Perl for C++.
	    Uses ctags to find the info:
		ctags -f $allTagsFile --fields=+aiKmnsSz --language-force=C++ --C++-kinds=+cefgmnpsut-dlux -u $files
	www.vim.org script 1213 (Java Development Environment) (Fuchuan Wang)
	IComplete: http://www.vim.org/scripts/script.php?script_id=1265
	http://cedet.sourceforge.net/intellisense.shtml (for Emacs)
	Ivan Villanueva has something for Java.
	Emacs (link seems dead):
	http://www.xref-tech.com/xrefactory/more_c_completion.html
	Completion in .NET framework SharpDevelop: http://www.icsharpcode.net
-   Pre-expand abbreviations, show which abbrevs would match?


Insert mode completion/expansion:
-   Is it possible to keep the complete menu open when calling complete()?
    (Prabir Shrestha, 2017 May 19, #1713)
-   When complete() first argument is before where insert started and
    'backspace' is Vi compatible, the completion fails.
    (Hirohito Higashi, 2015 Feb 19)
-   When a:base in 'completefunc' starts with a number it's passed as a
    number, not a string. (Sean Ma)  Need to add flag to call_func_retlist()
    to force a string value.
-   When editing the text and pressing CTRL-N again goes back to originally
    completed text, edited text is gone. (Peng Yu, 2008 Jul 24)
    Suggestion by Ben Schmidt, 2008 Aug 6.
-   GUI implementation of the popup menu.
7   When searching in other files the name flash by, too fast to read.  Only
    display a name every second or so, like with ":vimgrep".
7   When expanding file names with an environment variable, add the match with
    the unexpanded var.  So $HOME/tm expands to "/home/<USER>/tmp" and
    "$HOME/tmp"
9   ^X^L completion doesn't repeat correctly.  It uses the first match with
    the last added line, instead of continuing where the last match ended.
    (Webb)
8   Add option 'istagword': characters used for CTRL-]. like 'isexpand'
8   Add a command to undo the completion, go back to the original text.
7   Completion of an abbreviation: Can leave letters out, like what Instant
    text does: www.textware.com
8   Use the class information in the tags file to do context-sensitive
    completion.  After "foo." complete all member functions/variables of
    "foo".  Need to search backwards for the class definition of foo.
    Should work for C++ and Java.
    Even more context would be nice: "import java.^N" -> "io", "lang", etc.
7   When expanding $HOME/dir with ^X^F keep the $HOME (with an option?).
7   Add CTRL-X command in Insert mode like CTRL-X CTRL-N, that completes WORDS
    instead of words.
8   Add CTRL-X CTRL-R: complete words from register contents.
8   Add completion of previously inserted texts (like what CTRL-A does).
    Requires remembering a number of insertions.
8   Add 'f' flag to 'complete': Expand file names.
    Also apply 'complete' to whole line completion.
-   Add a flag to 'complete' to only scan local header files, not system
    header files. (Andri Moell)
-   Make it possible to search include files in several places.  Use the
    'path' option?  Can this be done with the dictionary completion (use
    wildcards in the file name)?
-   Make CTRL-X CTRL-K do a binary search in the dictionary (if it's sorted).
-   Speed up CTRL-X CTRL-K dictionary searching (don't use a regexp?).
-   Set a mark at the position where the match was found (file mark, could
    be in another file).
-   Add CTRL-A command in CTRL-X mode: show all matches.
-   Make CTRL-X CTRL-L use the 'complete' option?
-   Add command in CTRL-X mode to add following words to the completed string
    (e.g. to complete "Pointer->element" with CTRL-X CTRL-P CTRL-W CTRL-W)
-   CTRL-X CTRL-F: Use 'path' to find completions.
-   CTRL-X CTRL-F: Option to use forward slashes on MS-Windows?
-   CTRL-X CTRL-F: Don't replace "$VIM" with the actual value. (Kelly)
-   Allow listing all matches in some way (and picking one from the list).


Command line editing:
7   Add commands (keys) to delete from the cursor to the end of the command
    line.
8   Custom completion of user commands can't use the standard completion
    functions.  Add a hook to invoke a user function that returns the type of
    completion to be done: "file", "tag", "custom", etc.
-   Add flags to 'whichwrap' for command line editing (cursor right at end of
    lines wraps to start of line).
-   Make editing the command line work like Insert mode in a single-line view
    on a buffer that contains the command line history.  But this has many
    disadvantages, only implement it when these can be solved.  Elvis has run
    into these, see remarks from Steve (~/Mail/oldmail/kirkendall/in.00012).
    - Going back in history and editing a line there would change the history.
      Would still need to keep a copy of the history elsewhere.  Like the
      cmdwin does now already.
    - Use CTRL-O to execute one Normal mode command.  How to switch to normal
      mode for more commands?  <Esc> should cancel the command line.  CTRL-T?
    - To allow "/" and "= need to recursively call getcmdline(), overwrite the
      cmdline.  But then we are editing a command-line again.  How to avoid
      that the user gets confused by the stack of command lines?
    - Use edit() for normal cmdline editing?  Would have to integrate
      getcmdline() into edit().  Need to solve conflicts between Insert mode
      and Command-line mode commands.  Make it work like Korn shell and tcsh.
      Problems:
	- Insert: completion with 'wildchar'
	- Insert: use cmdline abbreviations
	- Insert: CTRL-D deletes indent instead of listing matches
	- Normal: no CTRL-W commands
	- Normal: no ":" commands?
	- Normal: allow Visual mode only within one line.
    - where to show insert/normal mode message?  Change highlighting of
      character in first column?
    - Implementation ideas:
      - Set "curwin" and "curbuf" to the command line window and buffer.
      - curwin->w_topline is always equal to curwin->w_cursor.lnum.
      - never set 'number', no folding, etc.  No status line.
      - sync undo after entering a command line?
      - use NV_NOCL flag for commands that are not allowed in Command-line
	Mode.


Command line completion:
-   Feature request: Complete members of a dictionary.
    (Luc Hermitte, 2017 Jan 4, #1350)
-   Completion of ":e" is ":earlier", should be ":edit".  Complete to the
    matching command instead of doing this alphabetically. (Mikel Jorgensen)
8   Change expand_interactively into a flag that is passed as an argument.
8   With command line completion after '%' and '#', expand current/alternate
    file name, so it can be edited.  Also with modifiers, such as "%:h".
8   When completing command names, either sort them on the long name, or list
    them with the optional part inside [].
8   Add an option to ignore case when doing interactive completion.  So that
    ":e file<Tab>" also lists "Filelist" (sorted after matching case matches).
7   Completion of ":map x ": fill in the current mapping, so that it can be
    edited. (Sven Guckes)
-   For 'wildmenu': Simplify "../bar" when possible.
-   When using <Up> in wildmenu mode for a submenu, should go back to the
    current menu, not the first one.  E.g., ":emenu File.Save<Up>".
8   When using backtick expansion, the external command may write a greeting
    message.  Add an option or commands to remove lines that match a regexp?
7   When listing matches of files, display the common path separately from the
    file names, if this makes the listing shorter. (Webb)
-   Add command line completion for ":ilist" and friends, show matching
    identifiers (Webb).
8   Add command line completion for "old value" of a command.  ":args <key>"
    would result in the current list of arguments, which you can then edit.
7   Add command line completion with CTRL-X, just like Insert mode completion.
    Useful for ":s/word/xx/".
-   Add command to go back to the text as it was before completion started.
    Also to be used for <Up> in the command line.
-   Add 'wildlongest' option: Key to use to find longest common match for
    command line completion (default CTRL-L), like 'wildchar'. (Cregut)
    Also: when there are several matches, show them line a CTRL-D.
-   With 'wildmode' set to "longest:full,full" and pressing Tab once the first
    entry in wildmenu is highlighted, that shouldn't happen. (Yuki Watanabe,
    2011 Feb 12)
-   After using <Tab> for command line completion after ":ta blah" and getting
    E33 (no tags file), further editing the command to e.g., ":echo 'blah'",
    the command is not executed.  Fix by Ian Kelling?
-   Command line completion: Scanning for tags doesn't check for typed key now
    and then?  Hangs for about 5 seconds.  Appears to be caused by finding
    include files with "foo/**" in 'path'.  (Kalisiak, 2006 July 15)
    Additional info: When using the |wildcards| ** globing, vim hangs
    indefinitely on lots of directories. The |file-searching| globing, like in
    ":set path=/**" does not hang as often as with globing with |wildcards|,
    like in ":1find /**/file".  This is for files that unix "find" can find
    very quickly. Merging the 2 kinds of globing might make this an easier
    fix. (Ian Kelling, 2008 July 4)
-   Command line completion when 'cmdheight' is maximum and 'wildmenu' is set,
    only one buffer line displayed, causes display errors.
-   Completing with 'wildmenu' and using <Up> and <Down> to move through
    directory tree stops unexpectedly when using ":cd " and entering a
    directory that doesn't contain other directories.
8   Command line completion: buffers "foo.txt" and "../b/foo.txt", completing
    ":buf foo<Tab>" doesn't find the second one. (George V. Reilly)
8   Add more command line completion for :syntax.
8   Add more command line completion for :highlight.
-   Wildmenu not deleted: "gvim -u NONE", ":set nocp wildmenu cmdheight=3
    laststatus=2", CTRL-D CTRL-H CTRL-H CTRL-H. (A.Politz, 2008 April 1) Works
    OK with Vim in an xterm.
-   If the variable "g:x#y#z" exists completion after ":echo g:x#" doesn't
    work.


Command line history:
-   Add "KeyWasTyped" flag: It's reset before each command and set when a
    character from the keyboard is consumed. Value is used to decide to put a
    command line in history or not. Put line in history if it didn't
    completely result from one mapping.
-   When using ":browse", also put the resulting edit command in the history,
    so that it can be repeated. (Demirel)


Insert mode:
9   When 'autoindent' is set, hitting <CR> twice, while there is text after
    the cursor, doesn't delete the autoindent in the resulting blank line.
    (Rich Wales) This is Vi compatible, but it looks like a bug.
8   When using CTRL-O in Insert mode, then executing an insert command
    "a" or "i", should we return to Insert mode after <Esc>? (Eggink)
    Perhaps it can be allowed a single time, to be able to do
    "<C-O>10axyz<Esc>".  Nesting this further is confusing.
    ":map <F2> 5aabc<Esc>" works only once from Insert mode.
8   When using CTRL-G CTRL-O do like CTRL-\ CTRL-O, but when returning with
    the cursor in the same position and the text didn't change continue the
    same change, so that "." repeats the whole insert.
7   Use CTRL-G <count> to repeat what follows.  Useful for inserting a
    character multiple times or repeating CTRL-Y.
-   Make 'revins' work in Replace mode.
7   Use 'matchpairs' for 'showmatch': When inserting a character check if it
    appears in the rhs of 'matchpairs'.
-   In Insert mode (and command line editing?): Allow undo of the last typed
    character.	This is useful for CTRL-U, CTRL-W, delete and backspace, and
    also for characters that wrap to the next line.
    Also: be able to undo CTRL-R (insert register).
    Possibly use 'backspace'="whole" for a mode where at least a <CR> that
    inserts autoindent is undone by a single <BS>.
-   Use CTRL-G in Insert mode for an extra range of commands, like "g" in
    Normal mode.
-   Make 'paste' work without resetting other options, but override their
    value.  Avoids problems when changing files and modelines or autocommands
    are used.
-   When typing CTRL-V and a digit higher than 2, only expect two digits.
-   Insert binary numbers with CTRL-V b.
-   Make it possible to undo <BS>, <C-W> and <C-U>.  Bash uses CTRL-Y.


'cindent', 'smartindent':
9   Wrapping a variable initialization should have extra indent:
	char * veryLongName =
		"very long string"
    Also check if "cino=+10" is used correctly.
8   Lisp indenting: "\\" confuses the indenter. (Dorai Sitaram, 2006 May 17)
8   Why are continuation lines outside of a {} block not indented?  E.g.:
	long_type foo =
	value;
8   Java: Inside an anonymous class, after an "else" or "try" the indent is
    too small. (Vincent Bergbauer)
    Problem of using {} inside (), 'cindent' doesn't work then.
8   In C++ it's possible to have {} inside (): (Kirshna)
		func(
			new String[] {
			    "asdf",
			    "asdf"
			}
		    );
8   In C++ a function isn't recognized inside a namespace:
    (Chow Loong Jin)
	namespace {
	    int
		 func(int arg) {
		 }
	}
6   Add 'cino' flag for this function argument layout: (Spencer Collyer)
	    func( arg1
	        , arg2
		, arg3
		);
7   Add separate "(0" option into inside/outside a function (Zellner):
	func(
	   int x)	// indent like "(4"
	{
	   if (a
	       && b)	// indent like "(0"
9   Using "{" in a comment: (Helmut Stiegler)
    if (a)
    {
	    if (b)
	    {
		    // {
	    }
	    } <-- this is indented incorrect
    Problem is that find_start_brace() checks for the matching brace to be in
    a comment, but not braces in between.  Requires adding a comment check to
    findmatchlimit().
-   Make smartindenting configurable.  Add 'sioptions', e.g. '#' setting the
    indent to 0 should be switched on/off.
7   Support ANSI style function header, with each argument on its own line.
-   "[p" and "]p" should use 'cindent' code if it's on (only for the first
    line).
-   Add option to 'cindent' to set indent for comments outside of {}?
-   Make a command to line up a comment after a code line with a previous
    comment after a code line.	Can 'cindent' do this automatically?
-   When 'cindent'ing a '}', showmatch is done before fixing the indent.  It
    looks better when the indent is fixed before the showmatch. (Webb)
-   Add option to make indenting work in comments too (for commented-out
    code), unless the line starts with "*".
-   Don't use 'cindent' when doing formatting with "gq"?
-   When formatting a comment after some text, insert the '*' for the new line
    (indent is correct if 'cindent' is set, but '*' doesn't get inserted).
8   When 'comments' has both "s1:/*,mb:*,ex:*/" and "s1:(*,mb:*,ex:*)", the
    'x' flag always uses the first match.  Need to continue looking for more
    matches of "*" and remember all characters that could end the comment.
-   For smartindent: When typing 'else' line it up with matching 'if'.
-   'smartindent': allow patterns in 'cinwords', for e.g. TeX files, where
    lines start with "\item".
-   Support this style of comments (with an option): (Brown)
	/* here is a comment that
	is just autoindented, and
	nothing else */
-   Add words to 'cinwords' to reduce the indent, e.g., "end" or "fi".
7   Use Tabs for the indent of starting lines, pad with spaces for
    continuation lines.  Allows changing 'tabstop' without messing up the
    indents.
    Patch by Lech Lorens, 2010 Mar.  Update by James McCoy, 2014 Mar 15.


Java:
8   Can have {} constructs inside parens.  Include changes from Steve
    Odendahl?
8   Recognize "import java.util.Vector" and use $CLASSPATH to find files for
    "[i" commands and friends.
-   For files found with 'include': handle "*" in included name, for Java.
    (Jason)
-   How to make a "package java.util" cause all classes in the package to be
    searched?  Also for "import java.util.*". (Mark Brophy)


'comments':
8   When formatting C comments that are after code, the "*" isn't repeated
    like it's done when there is no code.  And there is no automatic wrapping.
    Recognize comments that come after code.  Should insert the comment leader
    when it's "#" or "//".
    Other way around: when a C command starts with "* 4" the "*" is repeated
    while it should not.  Use syntax HL comment recognition?
7   When using "comments=fg:--", Vim inserts three spaces for a new line.
    When hitting a TAB, these spaces could be removed.
7   The 'n'esting flag doesn't do the indenting of the last (rightmost) item.
6   Make strings in 'comments' option a RE, to be able to match more
    complicated things. (Phillipps)  Use a special flag to indicate that a
    regexp is used.
8   Make the 'comments' option with "/* * */" lines only repeat the "*" line
    when there is a "/*" before it?  Or include this in 'cindent'?


Virtual edit:
8   Make the horizontal scrollbar work to move the text further left.
7   Allow specifying it separately for Tabs and beyond end-of-line?


Text objects:
8   Add text object for fold, so that it can be yanked when it's open.
8   Add test script for text object commands "aw", "iW", etc.
8   Add text object for part of a CamelHumpedWord and under_scored_word.
    (Scott Graham)  "ac" and "au"?
8   Add a text object for any kind of quoting, also with multibyte
    characters.  Option to specify what quotes are recognized (default: all)
    use "aq" and "iq".  Use 'quotepairs' to define pairs of quotes, like
    'matchpairs'?
8   Add text object for any kind of parens, also multibyte ones.
8   Add a way to make an ":omap" for a user-defined text object.  Requires
    changing the starting position in oap->start.
8   Add "gp" and "gP" commands: insert text and make sure there is a single
    space before it, unless at the start of the line, and after it, unless at
    the end of the line or before a ".".
7   Add objects with backwards extension?  Use "I" and "A".  Thus "2dAs"
    deletes the current and previous sentence. (Jens Paulus)
7   Add "g{" and "g}" to move to the first/last character of a paragraph
    (instead of the line just before/after a paragraph as with "{" and "}").
6   Ignore comment leaders for objects.  Make "das" work in reply-email.
5   Make it possible to use syntax group matches as a text object.  For
    example, define a "ccItem" group, then do "da<ccItem>" to delete one.
    Or, maybe just define "dai", delete-an-item, to delete the syntax item the
    cursor is on.


Select mode:
8   In blockwise mode, typed characters are inserted in front of the block,
    backspace deletes a column before the block. (Steve Hall)
7   Alt-leftmouse starts block mode selection in MS Word.
    See http://vim.wikia.com/wiki/Use_Alt-Mouse_to_select_blockwise.
7   Add Cmdline-select mode.  Like Select mode, but used on the command line.
    - Change gui_send_mouse_event() to pass on mouse events when 'mouse'
      contains 'C' or 'A'.
    - Catch mouse events in ex_getln.c.  Also shift-cursor, etc., like in
      normal_cmd().
    - remember start and end of selection in cmdline_info.
    - Typing text replaces the selection.


Visual mode:
8   Support using "." in Visual mode. Use the operator applied to the Visual
    selection, if possible.
-   When dragging the Visual selection with the mouse and 'scrolloff' is zero,
    behave like 'scrolloff' is one, so that the text scrolls when the pointer
    is in the top line.
-   Displaying size of Visual area: use 24-33 column display.
    When selecting multiple lines, up to about a screenful, also count the
    characters.
8   When using "I" or "A" in Visual block mode, short lines do not get the new
    text.  Make it possible to add the text to short lines too, with padding
    where needed.
7   With a Visual block selected, "2x" deletes a block of double the width,
    "3y" yanks a block of triple width, etc.
7   When selecting linewise, using "itext" should insert "text" at the start
    of each selected line.
8   What is "R" supposed to do in Visual mode?
8   Make Visual mode local to the buffer.  Allow changing to another buffer.
    When starting a new Visual selection, remove the Visual selection in any
    other buffer. (Ron Aaron)
8   Support dragging the Visual area to drop it somewhere else. (Ron Aaron,
    Ben Godfrey)
7   Support dragging the Visual area to drop it in another program, and
    receive dropped text from another program. (Ben Godfrey)
7   With blockwise Visual mode and "c", "C", "I", "A", etc., allow the use of
    a <CR>.  The entered lines are repeated over the Visual area.
7   Filtering a block should only apply to the block, not to the whole lines.
    When the number of lines is increased, add lines. When decreased, pad with
    spaces or delete? Use ":`<,`>" on the command line.
8   After filtering the Visual area, make "gv" select the filtered text?
    Currently "gv" only selects a single line, not useful.
7   Don't move the cursor when scrolling?  Needed when the selection should
    stay the same.  Scroll to the cursor at any movement command.  With an
    option!
7   In Visual block mode, need to be able to define a corner on a position
    that doesn't have text?  Also: when using the mouse, be able to select
    part of a TAB.  Even more: Add a mode where the cursor can be on a screen
    position where there is no text.  When typing, add spaces to fill the gap.
    Other solution: Always use curswant, so that you can move the cursor to
    the right column, and then use up/down movements to select the line,
    without changing the column.
6   ":left" and ":right" should work in Visual block mode.
7   CTRL-I and CTRL-O should work in Visual mode, but only jump to marks in the
    current buffer.
6   In non-Block mode, "I" should insert the same text in front of each line,
    before the first non-blank, "gI" in column 1.
6   In non-Block mode, "A" should append the same text after each line.
6   When in blockwise visual selection (CTRL-V), allow cursor to be placed
    right of the line.  Could also allow cursor to be placed anywhere on a TAB
    or other special character.
6   Add commands to move selected text, without deselecting.


More advanced repeating commands:
-   Add "." command for visual mode: redo last visual command (e.g. ":fmt").
-   Add command to repeat last movement.  Including count.
-   Add "." command after operator: repeat last command of same operator.  E.g.
    "c." will repeat last change, also when "x" used since then (Webb).
    "y." will repeat last yank.
    "c2." will repeat the last but one change?
    Also: keep history of Normal mode commands, add command to list the history
    and/or pick an older command.
-   History stack for . command?  Use "g." command.


Mappings and Abbreviations:
8   When "0" is mapped (it is a movement command) this mapping should not be
    used after typing another number, e.g. "20l". (Charles Campbell)
    Is this possible without disabling the mapping of the following command?
8   Should mapping <C-A> and <C-S-A> both work?
7   ":abbr b byte", append "b " to an existing word still expands to "byte".
    This is Vi compatible, but can we avoid it anyway?
8   To make a mapping work with a prepended "x to select a register, store the
    last _typed_ register name and access it with "&.
8   Add ":amap", like ":amenu".
7   Add a mapping that works always, for remapping the keyboard.
8   Add ":cab!", abbreviations that only apply to Command-line mode and not to
    entering search strings.
8   Add a flag to ":abbrev" to eat the character that triggers the
    abbreviation.  Thus "abb ab xxx" and typing "ab<Space>" inserts "xxx" and
    not the <Space>.
8   Give a warning when using CTRL-C in the lhs of a mapping.  It will never
    (?) work.
7   Add <0x8f> (hex), <0o33> (octal) and <123> (decimal) to <> notation?
7   When someone tries to unmap with a trailing space, and it fails, try
    unmapping without the trailing space.  Helps for ":unmap xx | unmap yy".
6   Context-sensitive abbreviations: Specify syntax group(s) in which the
    abbreviations are to be used.
-   Add mappings that take arguments.  Could work like the ":s" command.  For
    example, for a mouse escape sequence:
	:mapexp  <Esc>{\([0-9]*\),\([0-9]*\);	H\1j\2l
-   Add optional <Number> argument for mappings:
    :map <Number>q	     ^W^W<Number>G
    :map <Number>q<Number>t  ^W^W<Number1-1>G<Number2>l
    :map q<Char>	    :s/<Char>/\u\0/g
    Or implicit:
    :map q			<Register>d<Number>$
-   Add command to repeat a whole mapping ("." only repeats the last change in
    a mapping).  Also: Repeat a whole insert command, including any mappings
    that it included.  Sort-of automatic recording?
-   Include an option (or flag to 'cpoptions') that makes errors in mappings
    not flush the rest of the mapping (like nvi does).
-   Use context sensitiveness of completion to switch abbreviations and
    mappings off for :unab and :unmap.
6   When using mappings in Insert mode, insert characters for incomplete
    mappings first, then remove them again when a mapping matches.  Avoids
    that characters that are the start of some mapping are not shown until you
    hit another character.
-   Add mappings for replace mode: ":rmap".  How do we then enter mappings for
    non-replace Insert mode?
-   Add separate mappings for Visual-character/block/line mode?
-   Add 'mapstop' command, to stop recursive mappings.
-   List mappings that have a raw escape sequence both with the name of the key
    for that escape sequence (if there is one) and the sequence itself.
-   List mappings: Once with special keys listed as <>, once with meta chars as
    <M-a>, once with the byte values (octal?).  Sort of "spell mapping" command?
-   When entering mappings: Add the possibility to enter meta keys like they
    are displayed, within <>: <M-a>, <~@> or <|a>.
-   Allow multiple arguments to :unmap.
-   Command to show keys that are not used and available for mapping
    ":freekeys".
-   Allow any character except white space in abbreviations lhs (Riehm).


Incsearch:
-   Wrong scrolling when using incsearch.  Patch by Christian Brabandt, 2014
    Dec 4.  Is this a good solution?
-   Temporarily open folds to show where the search ends up.  Restore the
    folds when going to another line.
    Patch to open folds for 'incsearch'. (Christian Brabandt, 2015 Jan 6)
-   Bug with 'incsearch' going to wrong line. (Wolfram Kresse, 2009 Aug 17)
    Only with "vim -u NONE".
-   When no match is found and the user types more, the screen is redrawn
    anyway.  Could skip that.  Esp. if the line wraps and the text is scrolled
    up every time.
-   When incsearch used and hitting return, no need to search again in many
    cases, saves a lot of time in big files. (Slootman wants to work on this?)
    When not using special characters, can continue search from the last match
    (or not at all, when there was no match).  See oldmail/webb/in.872.
9   incsearch is incorrect for "/that/<Return>/this/;//" (last search pattern
    isn't updated).


Searching:
9   Should have an option for :vimgrep to find lines without a match.
8   Add "g/" and "gb" to search for a pattern in the Visually selected text?
    "g?" is already used for rot13.
    The vis.vim script has a ":S" command that does something like this.
    Can use "g/" in Normal mode, uses the '< to '> area.
    Use "&/" for searching the text in the Visual area?
9   Add "v" offset: "/pat/v": search for pattern and start Visual mode on the
    matching text.
8   Add a modifier to interpret a space like "\_s\+" to make it much easier to
    search for a phrase.
8   Add a mechanism for recursiveness: "\@(([^()]*\@g[^()]*)\)".  \@g stands
    for "go recursive here" and \@( \) marks the recursive part.
    Perl does it this way:
	    $paren = qr/ \(( [^()] | (??{ $paren }) )* \) /x;
    Here $paren is evaluated when it's encountered.  This is like a regexp
    inside a regexp.  In the above terms it would be:
	    \@((\([^()]\|\@g\)*)\)
8   Show the progress every second.  Could use the code that checks for CTRL-C
    to find out how much time has passed.  Or use SIGALRM.  Where to show the
    number?
7   Support for approximate-regexps to find similar words (agrep
    http://www.tgries.de/agrep/ tre: http://laurikari.net/tre/index.html).
8   Add an item for a big character range, so that one can search for a
    chinese character: \z[234-1234]  or \z[XX-YY] or \z[0x23-0x234].
7   Add an item stack to allow matching ().  One side is "push X on
    the stack if previous atom matched".  Other side is "match with top of
    stack, pop it when it matches".  Use "\@pX" and "\@m"?
	Example: \((\@p).\{-}\@m\)*
7   Add a flag to "/pat/" to discard an error.  Useful to continue a mapping
    when a search fails.  Could be "/pat/E" (e is already used for end offset).
7   Add pattern item to use properties of Unicode characters.  In Perl it's
    "\p{L}" for a letter.  See Regular Expression Pocket Reference.
8   Would it be possible to allow ":23,45/pat/flags" to search for "pat" in
    lines 23 to 45?  Or does this conflict with Ex range syntax?
8   Allow identical pairs in 'matchpairs'.  Restrict the search to the current
    line.
7   Allow longer pairs in 'matchpairs'.  Use matchit.vim as an
    example.
8   Make it possible to define the character that "%" checks for in
    #if/#endif.  For nmake it's !if/!endif.
-   For "%" command: set hierarchy for which things include other things that
    should be ignored (like "*/" or "#endif" inside /* */).
    Also: use "%" to jump from start to end of syntax region and back.
    Alternative: use matchit.vim
8   A pattern like "\([^a]\+\)\+" takes an awful long time.  Recognize that
    the recursive "\+" is meaningless and optimize for it.
    This one is also very slow on "/* some comment */": "^\/\*\(.*[^/]\)*$".
7   Recognize "[a-z]", "[0-9]", etc. and replace them with the faster "\l" and
    "\d".
7   Add a way to specify characters in <C-M> or <Key> form.  Could be
    \%<C-M>.
8   Add an argument after ":s/pat/str/" for a range of matches.  For example,
    ":s/pat/str/#3-4" to replace only the third and fourth "pat" in a line.
8   When 'iskeyword' is changed the matches from 'hlsearch' may change. (Benji
    Fisher)  redraw if some options are set while 'hlsearch' is set?
8   Add an option not to use 'hlsearch' highlighting for ":s" and ":g"
    commands. (Kahn)  It would work like ":noh" is used after that command.
    Also: An extra flag to do this once, and a flag to keep the existing
    search pattern.
-   Make 'hlsearch' a local/global option, so that it can be disabled in some
    of the windows.
-   Add \%h{group-name}; to search for a specific highlight group.
    Add \%s{syntax-group}; to search for a specific syntax group.
-   Support Perl regexp.  Use PCRE (Perl Compatible RE) package. (Shade)
    Or translate the pattern to a Vim one.
    Don't switch on with an option for typed commands/mappings/functions, it's
    too confusing.  Use "\@@" in the pattern, to avoid incompatibilities.
8   Add a way to access the last substitute text, what is used for ":s//~/".
    Can't use the ~ register, it's already used for drag & drop.
-   Remember flags for backreferenced items, so that "*" can be used after it.
    Check with "\(\S\)\1\{3}". (Hemmerling)
8   Flags that apply to the whole pattern.
    This works for all places where a regexp is used.
    Add "\q" to not store this pattern as the last search pattern?
-   Add flags to search command (also for ":s"?):
    i	ignore case
    I	use case
    p	use Perl regexp syntax (or POSIX?)
    v	use Vi regexp syntax
    f	forget pattern, don't keep it for "n" command
    F   remember pattern, keep it for "n" command
    Perl uses these too:
    e	evaluate the right side as an expression (Perl only)
    m	multiple line expression (we don't need it)
    o	compile only once (Perl only)
    s	single line expression (we don't need it)
    x	extended regexp (we don't need it)
    When used after ":g" command, backslash needed to avoid confusion with the
    following command.
    Add 'searchflags' for default flags (replaces 'gdefault').
-   Add command to display the last used substitute pattern and last used
    pattern. (Margo)  Maybe make it accessible through a register (like "/
    for search string)?
7   Use T-search algorithm, to speed up searching for strings without special
    characters.  See C't article, August 1997.
-   Add 'fuzzycase' option, so that case doesn't matter, and '-' and '_' are
    equivalent (for Unix filenames).
-   Add 'v' flag to search command: enter Visual mode, with the matching text
    as Visual area. (variation on idea from Bertin)
-   Searching: "/this//that/" should find "that" after "this".
-   Add global search commands: Instead of wrapping at the end of the buffer,
    they continue in another buffer.  Use flag after search pattern:
    a	for the next file in the argument list
    f	for file in the buffer list
    w	for file edited in a window.
    e.g. "/pat/f".  Then "n" and "N" work through files too.  "f" flag also for
    ":s/pat/foo/f"???  Then when 'autowrite' and 'hidden' are both not set, ask
    before saving files: "Save modified buffer "/path/file"? (Yes/Hide/No
    Save-all/hide-All/Quit) ".
-   ":s/pat/foo/3": find 3rd match of "pat", like sed. (Thomas Koehler)
7   When searching with 'n' give message when getting back where the search
    first started.  Remember start of search in '/ mark.
7   Add option that scrolls screen to put cursor in middle of screen after
    search always/when off-screen/never.  And after a ":tag" command.  Maybe
    specify how many lines below the screen causes a redraw with the cursor in
    the middle (default would be half a screen, zero means always).
6   Support multiple search buffers, so macros can be made without side
    effects.
7   From xvim: Allow a newline in search patterns (also for :s, can delete
    newline).  Add BOW, EOW, NEWL, NLORANY, NLBUTANY, magic 'n' and 'r', etc.
    [not in xvim:] Add option to switch on matches crossing ONE line boundary.
7   Add ":iselect", a combination of ":ilist" and ":tselect". (Aaron) (Zellner)
    Also ":dselect".
-   Searching for \%'> does not find anything when using line Visual
    selection.  Probably because it's using MAXCOL.  #8238
-   Regexp to search for duplicate lines does not work correctly:
    /\(^.*\n\)\1  (Chris Morgan, #6239)
-   Problem with upwards search on Windows (works OK on Linux). (Brett
    Stahlman, 2014 Jun 8)
-   Searching mixed with Visual mode doesn't redraw properly. (James Vega,
    2010 Nov 22)
-   Searching for composing char works, but not when inside []. (ZyX, Benjamin
    R. Haskell, 2010 Aug 24)
-   When 'smartcase' is set and using CTRL-L to add to the search pattern it
    may result in no matches.  Convert chars to lower case? (Erik Wognsen,
    2009 Apr 16)


Undo:
9   ":gundo" command: global undo.  Undoes changes spread over multiple files
    in the order they were made.  Also ":gredo".  Both with a count.  Useful
    when tests fail after making changes and you forgot in which files.
9   After undo/redo, in the message show whether the buffer is modified or
    not.
8   Search for pattern in undo tree, showing when it happened and the text
    state, so that you can jump to it.
8   Undo tree: visually show the tree somehow (Damian Conway)
    Show only the leaves, indicating how many changed from the branch and the
    timestamp?
    Put branch with most recent change on the left, older changes get more
    indent?
-   Make it possible to undo all the commands from a mapping, including a
    trailing unfinished command, e.g. for ":map K iX^[r".
-   When accidentally hitting "R" instead of Ctrl-R, further Ctrl-R is not
    possible, even when typing <Esc> immediately. (Grahn)  Also for "i", "a",
    etc.  Postpone saving for undo until something is really inserted?
8   When Inserting a lot of text, it can only be undone as a whole.  Make undo
    sync points at every line or word.  Could recognize the start of a new
    word (white space and then non-white space) and backspacing.
    Can already use CTRL-G u, but that requires remapping a lot of things.
8   Make undo more memory-efficient: Compare text before and after change,
    only remember the lines that really changed.
7   Add undo for a range of lines.  Can change these back to a previous
    version without changing the rest of the file.  Stop doing this when a
    change includes only some of these lines and changes the line count.  Need
    to store these undo actions as a separate change that can be undone.
-   For u_save() include the column number. This can be used to set '[ and '].
    And in the future the undo can be made more efficient (Webb).
-   In out-of-memory situations: Free allocated space in undo, and reduce the
    number of undo levels (with confirmation).
-   Instead of [+], give the number of changes since the last write: [+123].
    When undoing to before the last write, change this to a negative number:
    [-99].
-   With undo with simple line delete/insert: optimize screen updating.
-   When executing macro's: Save each line for undo only once.
-   When doing a global substitute, causing almost all lines to be changed,
    undo info becomes very big.  Put undo info in swap file??
-   MS-Windows: when writing undo file the infostreams are copied in
    mch_copy_file_attribute(), that seems unnecessary. (#7925) Add a flag to
    only copy attributes?
-   undo result wrong: Masato Nishihata, #4798
-   After recovering from a swap file the undofile should not be used, it
    causes corruption.  (#6631)
-   When the computer crashes while writing the undofile, the contents may be
    lost.  Write to a temp file, fsync and rename. (#8879)
-   Undo puts cursor in wrong line after "cG<Esc>" undo.
-   Undo history wrong when ":next file" re-uses a buffer. (#5426) ex_next()
    should pass flag to do_argfile(), then to do_ecmd().  Is there a test for
    this?
-   Undo problem: "g-" doesn't go back, gets stuck. (Björn Linse, 2016 Jul 18)
-   Undo message is not always properly displayed.  Patch by Ken Takata, 2013
    oct 3.  Doesn't work properly according to Yukihiro Nakadaira.
    Also see #1635.
-   When using ":diffput" through a mapping, undo in the target buffer isn't
    synced.  (Ryan Carney, 2016 Sep 14)
-   The undo file name can get too long. (Issue #346)
    For the path use a hash instead of dir%dir%dir%name  hash%name.
    Check both for some time for backwards compatibility.
    Alternatively: create the directory structure under 'undodir'.
-   Patch to add ":undorecover", get as much text out of the undo file as
    possible. (Christian Brabandt, 2014 Mar 12, update Aug 22)
-   Patch to support :undo absolute jump to file save number. (Christian
    Brabandt, 2010 Nov 5)
-   There should be a way after an abbreviation has expanded to go back to
    what was typed.  CTRL-G h ?  Would also undo last word or line break
    inserted perhaps.  And undo CTRL-W.  CTRL-G l would redo.
-   When using 'cryptmethod' xchaha20 the undo file is not encrypted.
    Need to handle extra bytes.


Buffer list:
7   Command to execute a command in another buffer: ":inbuf {bufname} {cmd}".
    Also for other windows: ":inwin {winnr} {cmd}".  How to make sure that
    this works properly for all commands, and still be able to return to the
    current buffer/window?  E.g.: ":inbuf xxx only".
8   Add File.{recent-files} menu entries: Recently edited files.
    Ron Aaron has a plugin for this: mru.vim.
8   Unix: Check all uses of fnamecmp() and fnamencmp() if they should check
    inode too.
7   Add another number for a buffer, which is visible for the user.  When
    creating a new buffer, use the lowest number not in use (or the highest
    number in use plus one?).
7   Offer some buffer selection from the command line?  Like using ":ls" and
    asking for a buffer number. (Zachmann)
-   When starting to edit a file that is already in the buffer list, use the
    file name argument for the new short file name. (Webb)
-   Add an option to make ":bnext" and ":bprev" wrap around the end of the
    buffer list.  Also for ":next" and ":prev"?
7   Add argument to ":ls" which is a pattern for buffers to list.
    E.g. ":ls *.c". (Thompson)
7   Add expansion of buffer names, so that "*.c" is expanded to all buffer
    names.  Needed for ":bdel *.c", ":bunload *.c", etc.
8   Support for <afile> where a buffer name is expected.
7   Add an option to mostly use slashes in file names.  Separately for
    internal use and for when executing an external program?
8   Some file systems are case-sensitive, some are not.  Besides
    'wildignorecase' there might be more parts inside
    CASE_INSENSITIVE_FILENAME that are useful on Unix.
-   When using ":bwipe!" also get rid of references to be buffer, e.g. in the
    jumplist and alternate file.
-   ":bnext" in a help buffer is supposed to go to the next help buffer, but
    it goes to any buffer, and then :bnext skips help buffers, since they are
    unlisted. (#4478)
-   :buffer completion does not escape "+" properly and results in a regexp
    error.  (#5467)
-   Problem with two buffers with the same name a/b, if it didn't exist before
    and is created outside of Vim. (dskloetg, 2018 Jul 16, #3219)
-   Add an option with file patterns, to be used when unloading a buffer: If
    there is a match, remove entries for the buffer from marks, jumplist, etc.
    To be used for git temp files.


Swap (.swp) files:
8   If writing to the swap file fails, should try to open one in another
    directory from 'dir'.  Useful in case the file system is full and when
    there are short file name problems.
8   Also use the code to try using a short file name for the backup and swap
    file for the Win32 and Dos 32 bit versions.
8   When a file is edited by root, add $LOGNAME to know who did su.
8   When the edited file is a symlink, try to put the swap file in the same
    dir as the actual file.  Adjust FullName().  Avoids editing the same file
    twice (e.g. when using quickfix).  Also try to make the name of the backup
    file the same as the actual file?
    Use the code for resolve()?
7   When using 64 bit inode numbers, also store the top 32 bits.  Add another
    field for this, using part of bo_fname[], to keep it compatible.
7   When editing a file on removable media, should put swap file somewhere
    else.  Use something like 'r' flag in 'viminfo'.  'diravoid'?
    Also: Be able to specify minimum disk space, skip directory when not
    enough room.
7   Add a configure check for which directory should be used: /tmp, /var/tmp
    or /var/preserve.
-   Add an option to create a swap file only when making the first change to
    the buffer.  (Liang)  Or only when the buffer is not read-only.
-   Add option to set "umask" for backup files and swap files (Antwerpen).
    'backupumask' and 'swapumask'?  Or 'umaskbackup' and 'umaskswap'?
-   When editing a readonly file, don't use a swap file but read parts from the
    original file.  Also do this when the file is huge (>'maxmem').  We do
    need to load the file once to count the number of lines?  Perhaps keep a
    cached list of which line is where.
-   When editing a file with ":edit" the output of :swapname is relative,
    while editing it with "vim file" it is absolute. (#355) Which one should
    it be?
-   When recovering a file, put the swap file name in b:recovered_swapfile.
    Then a command can delete it.
-   In the ATTENTION message about an existing swap file, mention the name of
    the process that is running.  It might actually be some other program,
    e.g. after a reboot.
-   When running Vim in silent ex mode, an existing swapfile causes Vim to
    wait for a user action without a prompt. (Maarten Billemont, 2012 Feb 3)
    Do give the prompt? Quit with an error?
-   After doing "su" $HOME can be the old user's home, thus ~root/file is not
    correct.  Don't use it in the swap file.
-   In the swapfile dialog, add a H(elp) option that gives more info about
    what each choice does.  Similar to  ":help swap-exists-choices"


Viminfo:
7   Can probably remove the code that checks for a writable viminfo file,
    because we now do the chown() for root, and others can't overwrite someone
    else's viminfo file.
8   When there is no .viminfo file and someone does "su", runs Vim, a
    root-owned .viminfo file is created.  Is there a good way to avoid this?
    Perhaps check the owner of the directory.  Only when root?
8   Add argument to keep the list of buffers when Vim is started with a file
    name. (Schild)
8   Keep the last used directory of the file browser (File/Open menu).
8   Remember the last used register for "@@".
8   Remember the redo buffer, so that "." works after restarting.
8   Remember a list of last accessed files.  To be used in the
    "File.Open Recent" menu.  Default is to remember 10 files or so.
    Also remember which files have been read and written.  How to display
    this?
7   Also store the ". register (last inserted text).
7   Make it possible to store buffer names in viminfo file relative to some
    directory, to make them portable over a network. (Aaron)
6   Store a snapshot of the currently opened windows.  So that when quitting
    Vim, and then starting again (without a file name argument), you see the
    same files in the windows.  Use ":mksession" code?
-   Make marks present in .viminfo usable as file marks: Display a list of
    "last visited files" and select one to jump to.
-   Add the debug command line history to viminfo.
-   Using "wviminfo /tmp/viminfo" does not store file marks that Vim knows
    about, it only works when merging with an existing file.  (Shougo, 2017
    Jun 19, #1781)
-   Writing nested List and Dict in viminfo gives error message and can't be
    read back. (Yukihiro Nakadaira, 2010 Nov 13)
-   When a register contains illegal bytes, writing viminfo in utf-8 and
    reading it back doesn't result in utf-8. (Devin Bayer)
8   With 'viminfo' set such that the ".viminfo" file is written on a FAT
    filesystem, an illegal file name may be created: ".vim".
8   For each buffer that is opened, the viminfo file is opened and read to
    check for file marks.  This can be slow.
8   When write_viminfo() is used while there are many orphaned viminfo
    tempfiles writing the viminfo file fails.  Give a clear error message so
    that the user knows the files have to be deleted.


Modelines:
8   Before trying to execute a modeline, check that it looks like one (valid
    option names).  If it's very wrong, silently ignore it.
    Ignore a line that starts with "Subject: ".
-   Add an option to whitelist options that are allowed in a modeline.  This
    would allow careful users to use modelines, e.g., only allowing
    'shiftwidth'.
-   Add an option to let modelines only set local options, not global ones
    such as 'encoding'.
-   When an option value is coming from a modeline, do not carry it over to
    another edited file?  Would need to remember the value from before the
    modeline setting.
-   Allow setting a variable from a modeline?  Only allow using fixed strings,
    no function calls, to avoid a security problem.
-   Allow ":doauto BufRead x.cpp" in modelines, to execute autocommands for
    .cpp files.
-   Support the "abbreviate" command in modelines (Kearns).  Careful for
    characters after <Esc>, that is a security leak.
-   Add an option setting to ask the user if the modelines are to be executed
    or not.  Same for .exrc in local dir.


Sessions:
-   Session file contains absolute paths when "curdir" is removed form
    'sessionoptions', making it impossible to have a session with a relative
    path.  (#4450)
-   Session file only contains local option values for buffers that are in a
    window, not other buffers. (#7532)
-   Script generated by :mksession does not work well if there are windows
    with modified buffers:
      change "silent only" into "silent only!"
      change "edit fname" of first buffer to "hide edit fname"
      skip "badd fname" if "fname" is already in the buffer list
      remove remark about unloading buffers from documentation
-   When session file has name in argument list but the buffer was deleted,
    the buffer is not deleted when using the session file. (#1393) Should add
    the buffer in hidden state.
-   When a session file is created and there are "nofile" buffers, these are
    not filled.  Need to trigger BufReadCmd autocommands.  Also handle
    deleting the initial empty buffer better. (ZyX, 2015 March 8)
-   Setting the spell file in a session only reads the local additions, not
    the normal spell file. (Enno Nagel, 2014 Mar 29)
-   Directory wrong in session file, caused by ":lcd" in BufEnter autocommand.
    (Felix Kater, 2009 Mar 3)
-   Something wrong with session that has "cd" commands and "badd", in such a
    way that Vim doesn't find the edited file in the buffer list, causing the
    ATTENTION message?  (Tony Mechelynck, 2008 Dec 1) Also: swap files are in
    ~/tmp/  One has relative file name ".mozilla/...".
-   Session file creation: 'autochdir' causes trouble.  Keep it off until
    after loading all files.
-   Session file generates error upon loading, cause by --remote-silent-tab.
    (7tommm (ytommm) 2010 Nov 24)
8   DOS/Windows: ":mksession" generates a "cd" command where "aa\#bb" means
    directory "#bb" in "aa", but it's used as "aa#bb". (Ronald Hoellwarth)
7   When there is a "help.txt" window in a session file, restoring that
    session will not get the "LOCAL ADDITIONS" back.
8   With ":mksession" always store the 'sessionoptions' option, even when
    "options" isn't in it. (St-Amant)
8   When using ":mksession", also store a command to reset all options to
    their default value, before setting the options that are not at their
    default value.
7   With ":mksession" also store the tag stack and jump history. (Michal
    Malecki)


Options:
7   ":with option=value | command": temporarily set an option value and
    restore it after the command has executed.
8   Make "old" number options that really give a number of effects into string
    options that are a comma-separated list.  The old number values should
    also be supported.
8   Add commands to save and restore an option, which also preserves the flag
    that marks if the option was set.  Useful to keep the effect of setting
    'compatible' after ":syntax on" has been used.
7   There is 'titleold', why is there no 'iconold'? (Chazelas)


External commands:
8   When filtering text, redirect stderr so that it can't mess up the screen
    and Vim doesn't need to redraw it.  Also for ":r !cmd".
4   Set separate shell for ":sh", piping "range!filter", reading text "r !ls"
    and writing text "w !wc". (Deutsche)  Allow arguments for fast start (e.g.
    -f).
4   Allow direct execution, without using a shell.
4   Run an external command in the background.  But how about I/O in the GUI?
    Careful: don't turn Vim into a shell!
4   Add feature to disable using a shell or external commands.


Multiple Windows:
7   "vim -oO file ..." use both horizontal and vertical splits.
8   Add CTRL-W T: go to the top window in the column of the current window.
    And CTRL-W B: go to bottom window.
7   Use CTRL-W <Tab>, like alt-tab, to switch between buffers.  Repeat <Tab>
    to select another buffer (only loaded ones?), <BS> to go back, <Enter> to
    select buffer, <Esc> to go back to original buffer.
7   Make it possible to edit a new buffer in the preview window.  A script can
    then fill it with something.  ":popen"?
7   Add a 'tool' window: behaves like a preview window but there can be
    several.  Don't count it in only_one_window(). (Alexei Alexandrov)
6   Add an option to resize the shell when splitting and/or closing a window.
    ":vsp" would make the shell wider by as many columns as needed for the new
    window.  Specify a maximum size (or use the screen size).  ":close" would
    shrink the shell by as many columns as come available. (Demirel)
7   When starting Vim several times, instantiate a Vim server, that allows
    communication between the different Vims.  Feels like one Vim running with
    multiple top-level windows.  Esp. useful when Vim is started from an IDE
    too.  Requires some form of inter process communication.
-   Support a connection to an external viewer.  Could call the viewer
    automatically after some seconds of non-activity, or with a command.
    Allow some way of reporting scrolling and cursor positioning in the viewer
    to Vim, so that the link between the viewed and edited text can be made.


Marks:
8   Add ten marks for last changed files: ':0, ':1, etc.  One mark per file.
8   When cursor is first moved because of scrolling, set a mark at this
    position.  (Rimon Barr)  Use '-.
8   Add a command to jump to a mark and make the motion inclusive.  g'm and g`m?
8   The '" mark is set to the first line, even when doing ":next" a few times.
    Only set the '" mark when the cursor was really moved in a file.
8   Make `` and '', which would position the new cursor position in the middle
    of the window, restore the old topline (or relative position) from when
    the mark was set.
7   Make a list of file marks in a separate window.  For listing all buffers,
    matching tags, errors, etc.  Normal commands to move around.  Add commands
    to jump to the mark (in current window or new window).  Start it with
    ":browse marks"?
6   Add a menu that lists the Marks like ":marks". (Amerige)
7   For ":jumps", ":tags" and ":marks", for not loaded buffers, remember the
    text at the mark.  Highlight the column with the mark.
7   Highlight each mark in some way (With "Mark" highlight group).
    Or display marks in a separate column, like 'number' does.
7   Use d"m to delete rectangular area from cursor to mark m (like Vile's \m
    command).
7   Try to keep marks in the same position when:
    - replacing with a line break, like in ":s/pat/^M/", move marks after the
      line break column to the next line. (Acevedo)
    - inserting/deleting characters in a line.
5   Include marks for start/end of the current word and section.  Useful in
    mappings.
6   Add "unnamed mark" feature: Like marks for the ":g" command, but place and
    unplace them with commands before doing something with the lines.
    Highlight the marked lines somehow.


Digraphs:
7   Make "ga" show the keymap for a character, if it exists.
    Also show the code of the character after conversion to 'fileencoding'.
-   Use digraph table to tell Vim about the collating sequence of special
    characters?
8   Add command to remove one or more (all) digraphs. (Brown)
7   Support different sets of digraphs (depending on the character set?).  At
    least Latin1/Unicode, Latin-2, MS-DOS (esp. for Win32).


Writing files:
-   In vim_rename(), should lock "from" file when deleting "to" file for
    systems other than Amiga.  Avoids problems with unexpected longname to
    shortname conversion.
8   write mch_isdevice() for Amiga, Mac, VMS, etc.
8   When appending to a file, Vim should also make a backup and a 'patchmode'
    file.
8   'backupskip' doesn't write a backup file at all, a bit dangerous for some
    applications.  Add 'backupelsewhere' to write a backup file in another
    directory?  Or add a flag to 'backupdir'?
6   Add an option to write a new, numbered, backup file each time.  Like
    'patchmode', e.g., 'backupmode'.
6   Make it possible to write 'patchmode' files to a different directory.
    E.g., ":set patchmode=~/backups/*.orig". (Thomas)
6   Add an option to prepend something to the backup file name.  E.g., "#".
    Or maybe allow a function to modify the backup file name?
8   Only make a backup when overwriting a file for the first time.  Avoids
    losing the original when writing twice. (Slootman)
7   On non-Unix machines, also overwrite the original file in some situations
    (file system full, it's a link on an NFS partition).
7   When editing a file, check that it has been changed outside of Vim more
    often, not only when writing over it.  E.g., at the time the swap file is
    flushed.  Or every ten seconds or so (use the time of day, check it before
    waiting for a character to be typed).
8   When a file was changed since editing started, show this in the status
    line of the window, like "[time]".
    Make it easier to reload all outdated files that don't have changes.
    Automatic and/or with a command.


Substitute:
8   Substitute with hex/unicode number "\%xff" and "\%uabcd".  Just like
    "\%uabcd" in search pattern.
8   Make it easier to replace in all files in the argument list.  E.g.:
    ":argsub/oldword/newword/".  Works like ":argdo %s/oldword/newword/g|w".
-   :s///p prints the line after a substitution.
-   With :s///c replace \&, ~, etc. when showing the replacement pattern.
8   With :s///c allow scrolling horizontally when 'nowrap' is effective.
    Also allow a count before the scrolling keys.
-   Add number option to ":s//2": replace second occurrence of string?  Or:
    :s///N substitutes N times.
-   Add answers to ":substitute" with 'c' flag, used in a ":global", e.g.:
    ":g/pat1/s/pat2/pat3/cg": 'A' do all remaining replacements, 'Q' don't do
    any replacements, 'u' undo last substitution.
7   Substitute in a block of text.  Use {line}.{column} notation in an Ex
    range, e.g.: ":1.3,$.5s" means to substitute from line 1 column 3 to the
    last line column 5.
5   Add commands to bookmark lines, display bookmarks, remove bookmarks,
    operate on lines with bookmarks, etc.  Like ":global" but with the
    possibility to keep the bookmarks and use them with several commands.
    (Stanislav Sitar)


Mouse support:
8   Add 'o' flag to 'mouse'?
7   Be able to set a 'mouseshape' for the popup menu.
8   Add 'mouse' flag, which sets a behavior like Visual mode, but automatic
    yanking at the button-up event.  Or like Select mode, but typing gets you
    out of Select mode, instead of replacing the text. (Bhaskar)
-   Implement mouse support for the Amiga console.
-   Using right mouse button to extend a blockwise selection should attach to
    the nearest corner of the rectangle (four possible corners).
-   Precede mouse click by a number to simulate double clicks?!?
-   When mouse click after 'r' command, get character that was pointed to.


Argument list:
6   Add command to put all filenames from the tag files in the argument list.
    When given an argument, only use the files where that argument matches
    (like `grep -l ident`) and jump to the first match.
6   Add command to form an args list from all the buffers?


Registers:
8   Don't display empty registers with ":display". (Etienne)
8   Add put command that overwrites existing text.  Should also work for
    blocks.  Useful to move text around in a table.  Works like using "R ^R r"
    for every line.
-   When appending to a register, also report the total resulting number of
    lines.  Or just say "99 more lines yanked", add the "more".
-   When inserting a register in Insert mode with CTRL-R, don't insert comment
    leader when line wraps?
-   The ":@r" commands should take a range and execute the register for each
    line in the range.
-   Add "P" command to insert contents of unnamed register, move selected text
	to position of previous deleted (to swap foo and bar in " + foo")
8   Should be able to yank and delete into the "/ register.
    How to take care of the flags (offset, magic)?
-   In Select mode the deleted text always goes into the unnamed register.
    Use CTRL-R to specify the register to use. (#9531)
-   When "+ register is set then "" points to it.  If another Vim grabs the "+
    register, then "" doesn't contain anything.  Make it still follow "+.
    (#6435)
-   Pasting a register in Visual mode cannot be repeated. (Mahmoud Al-Qudsi,
    2018 Apr 26, #2849)
-   Patch to add option that tells whether small deletes go into the numbered
    registers. (Aryeh Leib Taurog, 2013 Nov 18)
-   With "unamedplus" in 'clipboard' pasting in Visual mode causes error for
    empty register. (Michael Seiwald, 2011 Jun 28)  I can't reproduce it.
-   Consider making YankRing or something else that keeps a list of yanked
    text part of standard Vim.  The "1 to "9 registers are not sufficient.
6   When yanking into the unnamed registers several times, somehow make the
    previous contents also available (like it's done for deleting).  What
    register names to use?  g"1, g"2, etc.?
    Also do this for the small delete register "-.


Debug mode:
8   Add breakpoints for setting an option
8   Add breakpoints for assigning to a variable.
7   Store the history from debug mode in viminfo.
7   Make the debug mode history available with histget() et al.
-   In debug mode, using CTRL-R = to evaluate a function causes stepping
    through the function. (Hari Krishna Dara, 2006 Jun 28)


Various improvements:
7   Add plugins for formatting?  Should be able to make a choice depending on
    the language of a file (English/Korean/Japanese/etc.).
    Setting the 'langformat' option to "chinese" would load the
    "format/chinese.vim" plugin.
    The plugin would set 'formatexpr' and define the function being called.
    Edward L. Fox explains how it should be done for most Asian languages.
    (2005 Nov 24)
    Alternative: patch for utf-8 line breaking. (Yongwei Wu, 2008 Feb 23)
7   [t to move to previous xml/html tag (like "vatov"), ]t to move to next
    ("vatv").
7   [< to move to previous xml/html tag, e.g., previous <li>. ]< to move to
    next <li>, ]< to next </li>, [< to previous </li>.
8   Add ":rename" command: rename the file of the current buffer and rename
    the buffer.  Buffer may be modified.
7   Instead of filtering errors with a shell script it should be possible to
    do this with Vim script.  A function that filters the raw text that comes
    from the 'makeprg'?
7   Allow a window not to have a statusline.  Makes it possible to use a
    window as a buffer-tab selection.
8   Allow non-active windows to have a different statusline. (Yakov Lerner)
7   Add an invisible buffer which can be edited.  For use in scripts that want
    to manipulate text without changing the window layout.
8   Add a command to revert to the saved version of file; undo or redo until
    all changes are gone.
6   "vim -q -" should read the list of errors from stdin. (Gautam Mudunuri)
8   Add "--remote-fail": When contacting the server fails, exit Vim.
    Add "--remote-self": When contacting the server fails, do it in this Vim.
    Overrules the default of "--remote-send" to fail and "--remote" to do it
    in this Vim.
8   When Vim was started without a server, make it possible to start one, as
    if the "--servername" argument was given.  ":startserver <name>"?
8   No address range can be used before the command modifiers.  This makes
    them difficult to use in a menu for Visual mode.  Accept the range and
    have it apply to the following command.
8   Add the possibility to set 'fileformats' to force a format and strip other
    CR characters.  For example, for "dos" files remove CR characters at the
    end of the line, so that a file with mixed line endings is cleaned up.
    To just not display the CR characters: Add a flag to 'display'?
7   Some compilers give error messages in which the file name does not have a
    path.  Be able to specify that 'path' is used for these files.
7   Xterm sends <Esc>O3F for <M-End>.  Similarly for other <M-Home>, <M-Left>,
    etc.  Combinations of Alt, Ctrl and Shift are also possible.  Recognize
    these to avoid inserting the raw byte sequence, handle like the key
    without modifier (unless mapped).
6   Add "gG": like what "gj" is to "j": go to the N'th window line.
8   Add command like ":normal" that accepts <Key> notation like ":map".
9   Support ACLs on more systems.
7   Add ModeMsgVisual, ModeMsgInsert, etc. so that each mode message can be
    highlighted differently.
7   Add a message area for the user.  Set some option to reserve space (above
    the command line?).  Use an ":echouser" command to display the message
    (truncated to fit in the space).
7   Add %s to 'keywordprg': replace with word under the cursor. (Zellner)
8   Support printing on Unix.  Can use "lpansi.c" as an example. (Bookout)
8   Add put command that replaces the text under it.  Esp. for blockwise
    Visual mode.
7   Enhance termresponse stuff: Add t_CV(?): pattern of term response, use
    regexp: "\e\[[>?][0-9;]*c", but only check just after sending t_RV.
7   Add "g|" command: move to N'th column from the left margin (after wrapping
    and applying 'leftcol').  Works as "|" like what "g0" is to "0".
7   Support setting 'equalprg' to a user function name.
7   Highlight the characters after the end-of-line differently.
7   When 'whichwrap' contains "l", "$dl" should join lines?
8   Add an argument to configure to use $CFLAGS and not modify it? (Mooney)
8   Enabling features is a mix of configure arguments and defines in
    feature.h.  How to make this consistent?  Feature.h is required for
    non-unix systems.  Perhaps let configure define CONF_XXX, and use #ifdef
    CONF_XXX in feature.h?  Then what should min-features and max-features do?
8   Add "g^E" and "g^Y", to scroll a screen-full line up and down.
8   Add ":confirm" handling in open_exfile(), for when file already exists.
8   When quitting with changed files, make the dialog list the changed file
    and allow "write all", "discard all", "write some".  The last one would
    then ask "write" or "discard" for each changed file.  Patch in HierAssist
    does something like this. (Shah)
7   Use growarray for replace stack.
7   Have a look at viH (Hellenic or Greek version of Vim).  But a solution
    outside of Vim might be satisfactory (Haritsis).
3   Make "2d%" work like "d%d%" instead of "d2%"?
7   "g CTRL-O" jumps back to last used buffer.	Skip CTRL-O jumps in the same
    buffer.  Make jumplist remember the last ten accessed buffers?
7   Make it possible to set the size of the jumplist (also to a smaller number
    than the default). (Nikolai Weibull)
-   Add code to disable the CAPS key when going from Insert to Normal mode.
-   Set date/protection/etc. of the patchfile the same as the original file.
-   Use growarray for termcodes[] in term.c
-   Add <window-99>, like <cword> but use filename of 99'th window.
7   Add a way to change an operator to always work characterwise-inclusive
    (like "v" makes the operator characterwise-exclusive).  "x" could be used.
-   Make a set of operations on list of names: expand wildcards, replace home
    dir, append a string, delete a string, etc.
-   Remove using mktemp() and use tmpname() only?  Ctags does this.
-   When replacing environment variables, and there is one that is not set,
    turn it into an empty string?  Only when expanding options? (Hiebert)
-   Option to set command to be executed instead of producing a beep (e.g. to
    call "play newbeep.au").
-   Add option to show the current function name in the status line.  More or
    less what you find with "[[k", like how 'cindent' recognizes a function.
    (Bhatt).
-   "[r" and "]r": like "p" and "P", but replace instead of insert (esp. for
    blockwise registers).
-   Add 'timecheck' option, on by default.  Makes it possible to switch off the
    timestamp warning and question. (Dodt).
-   Add an option to set the time after which Vim should check the timestamps
    of the files.  Only check when an event occurs (e.g., character typed,
    mouse moved).  Useful for non-GUI versions where keyboard focus isn't
    noticeable.
-   Make 'smartcase' work even though 'ic' isn't set (Webb).
7   When formatting text, allow to break the line at a number of characters.
    Use an option for this: 'breakchars'?  Useful for formatting Fortran code.
-   Add flag to 'formatoptions' to be able to format book-style paragraphs
    (first line of paragraph has larger indent, no empty lines between
    paragraphs).  Complements the '2' flag.  Use '>' flag when larger indent
    starts a new paragraph, use '<' flag when smaller indent starts a new
    paragraph.	Both start a new paragraph on any indent change.
8   The 'a' flag in 'formatoptions' is too dangerous.  In some way only do
    auto-formatting in specific regions, e.g. defined by syntax highlighting.
8   Allow using a trailing space to signal a paragraph that continues on the
    next line (MIME text/plain; format=flowed, RFC 2646).  Can be used for
    continuous formatting.  Could use 'autoformat' option, which specifies a
    regexp which triggers auto-formatting (for one line).
    ":set autoformat=\\s$".
-   Be able to redefine where a sentence stops.  Use a regexp pattern?
-   Support multibyte characters for sentences.  Example from Ben Peterson.
7   Add command "g)" to go to the end of a sentence, "g(" to go back to the
    end of a sentence. (Servatius Brandt)
-   Be able to redefine where a paragraph starts.  For "[[" where the '{' is
    not in column 1.
6   Add ":cdprev": go back to the previous directory.  Need to remember a
    stack of previous directories.  We also need ":cdnext".
7   Should ":cd" for MS-DOS go to $HOME, when it's defined?
-   Make "gq<CR>" work on the last line in the file.  Maybe for every operator?
-   Add more redirecting of Ex commands:
	:redir #>  bufname
	:redir #>> bufname   (append)
-   Give error message when starting :redir: twice or using END when no
    redirection was active.
-   Setting of options, specifically for a buffer or window, with
    ":set window.option" or ":set buffer.option=val".  Or use ":buffer.set".
    Also: "buffer.map <F1> quit".
6   Would it be possible to change the color of the cursor in the Win32
    console?  (Klaus Hast)
-   Add :delcr command:
			    *:delcr*
     :[range]delcr[!]	Check [range] lines (default: whole buffer) for lines
			ending in <CR>.  If all lines end in <CR>, or [!] is
			used, remove the <CR> at the end of lines in [range].
			A CTRL-Z at the end of the file is removed.  If
			[range] is omitted, or it is the whole file, and all
			lines end in <CR> 'textmode' is set.
-   Should integrate addstar() and file_pat_to_reg_pat().
-   When working over a serial line with 7 bit characters, remove meta
    characters from 'isprint'.
-   Use fchdir() in init_homedir(), like in FullName().
-   In win_update(), when the GUI is active, always use the scrolling area.
    Avoid that the last status line is deleted and needs to be redrawn.
-   That "cTx" fails when the cursor is just after 'x' is Vi compatible, but
    may not be what you expect.  Add a flag in 'cpoptions' for this?  More
    general: Add an option to allow "c" to work with a null motion.
-   Give better error messages by using errno (strerror()).
-   Give "Usage:" error message when command used with wrong arguments (like
    Nvi).
-   Make 'restorescreen' option also work for xterm (and others), replaces the
    SAVE_XTERM_SCREEN define.
7   Support for ":winpos" In xterm: report the current window position.
-   Give warning message when using ":set t_xx=asdf" for a termcap code that
    Vim doesn't know about.  Add flag in 'shortmess'?
6   Add ":che <file>", list all the include paths which lead to this file.
-   For a commandline that has several commands (:s, :d, etc.) summarize the
    changes all together instead of for each command (e.g. for the rot13
    macro).
-   Add command like "[I" that also shows the tree of included files.
-   ":set sm^L" results in ":set s", because short names of options are also
    expanded.  Is there a better way to do this?
-   Add ":@!" command, to ":@" like what ":source!" is to ":source".
8   Add ":@:!": repeat last command with forceit set.
-   Add 't_normal': Used whenever t_me, t_se, t_ue or t_Zr is empty.
-   ":cab map test ^V| je", ":cunab map" doesn't work.	This is vi compatible!
-   CTRL-W CTRL-E and CTRL-W CTRL-Y should move the current window up or down
    if it is not the first or last window.
-   Include-file-search commands should look in the loaded buffer of a file (if
    there is one) instead of the file itself.
7   Change 'nrformats' to include the leader for each format.  Example:
	nrformats=hex:$,binary:b,octal:0
    Add setting of 'nrformats' to syntax files.
-   'path' can become very long, don't use NameBuff for expansion.
-   When unhiding a hidden buffer, put the same line at top of the window as
    the one before hiding it.  Or: keep the same relative cursor position (so
    many percent down the windows).
-   Make it possible for the 'showbreak' to be displayed at the end of the
    line.  Use a comma to separate the part at the end and the start of the
    line?  #754  Highlight the linebreak characters, add flag in 'highlight'.
    Make 'showbreak' local to a window.
-   Some string options should be expanded if they have wildcards, e.g.
    'dictionary' when it is "*.h".
-   Use a specific type for number and boolean options, making it possible to
    change it for specific machines (e.g. when a long is 64 bit).
-   Add option for <Insert> in replace mode going to normal mode. (Nugent)
-   Add a next/previous possibility to "[^I" and friends.
-   Add possibility to change the HOME directory.  Use the directory from the
    passwd file? (Antwerpen)
8   Add commands to push and pop all or individual options. ":setpush tw",
    ":setpop tw", ":setpush all".  Maybe pushing/popping all options is
    sufficient.  ":setflush" resets the option stack?
    How to handle an aborted mapping?  Remember position in tag stack when
    mapping starts, restore it when an error aborts the mapping?
-   Change ":fixdel" into option 'fixdel', t_del will be adjusted each time
    t_bs is set? (Webb)
-   "gc": goto character, move absolute character positions forward, also
    counting newlines.  "gC" goes backwards (Weigert).
-   When doing CTRL-^, redraw buffer with the same topline. (Demirel)  Store
    cursor row and window height to redraw cursor at same percentage of window
    (Webb).
-   Besides remembering the last used line number of a file, also remember the
    column.  Use it with CTRL-^ et. al.
-   Check for non-digits when setting a number option (careful when entering
    hex codes like 0xff).
-   Add option to make "." redo the "@r" command, instead of the last command
    executed by it.  Also to make "." redo the whole mapping.  Basically: redo
    the last TYPED command.
-   Support URL links for ^X^F in Insert mode, like for "gf".
-   Support %name% expansion for "gf" on Windows.
-   Make "gf" work on "file://c:/path/name".  "file:/c:/" and "file:///c:/"
    should also work?
-   Add 'urlpath', used like 'path' for when "gf" used on a URL?
8   When using "gf" on an absolute file name, while editing a remote file
    (starts with scp:// or http://) should prepend the method and machine
    name.
-   When finding a URL or file name, and it doesn't exist, try removing a
    trailing '.'.
-   Add ":path" command modifier.  Should work for every command that takes a
    file name argument, to search for the file name in 'path'.	Use
    find_file_in_path().
-   Highlight control characters on the screen: Shows the difference between
    CTRL-X and "^" followed by "X" (Colon).
-   Integrate parsing of cmdline command and parsing for expansion.
-   Create a program that can translate a .swp file from any machine into a
    form usable by Vim on the current machine.
-   Add ":noro" command: Reset 'ro' flag for all buffers, except ones that have
    a readonly file.  ":noro!" will reset all 'ro' flags.
-   Add a variant of CTRL-V that stops interpretation of more than one
    character.	For entering mappings on the command line where a key contains
    several special characters, e.g. a trailing newline.
-   Make '2' option in 'formatoptions' also work inside comments.
-   Add 's' flag to 'formatoptions': Do not break when inside a string. (Dodt)
-   When window size changed (with the mouse) and made too small, set it back
    to the minimal size.
-   Add "]>" and "[<", shift comment at end of line (command;  /* comment */).
-   Should not call cursorcmd() for each vgetc() in getcmdline().
-   ":split file1 file2" adds two more windows (Webb).
-   Don't give message "Incomplete last line" when editing binary file.
-   Add ":a", ":i" for preloading of named buffers.
-   When entering text, keep other windows on same buffer updated (when a line
    entered)?
-   Check out how screen does output optimizing.  Apparently this is possible
    as an output filter.
-   In dosub() regexec is called twice for the same line.  Try to avoid this.
-   Window updating from memline.c: insert/delete/replace line.
-   Optimize ml_append() for speed, esp. for reading a file.
-   V..c should keep indent when 'ai' is set, just like [count]cc.
-   Updatescript() can be done faster with a string instead of a char.
-   Screen updating is inefficient with CTRL-F and CTRL-B when there are long
    lines.
-   Uppercase characters in Ex commands can be made lowercase?
8   Add option to show characters in text not as "|A" but as decimal ("^129"),
    hex ("\x81") or octal ("\201") or meta (M-x).  Nvi has the 'octal' option
    to switch from hex to octal.  Vile can show unprintable characters in hex
    or in octal.
7   Tighter integration with xxd to edit binary files.  Make it more
    easy/obvious to use.  Command line argument?
-   How does vi detect whether a filter has messed up the screen?  Check source.
    After ":w !command" a wait_return?
-   Improve screen updating code for doput() (use s_ins()).
-   With 'p' command on last line: scroll screen up (also for terminals without
    insert line command).
-   Use insert/delete char when terminal supports it.
-   Optimize screen redraw for slow terminals.
-   Optimize "dw" for long row of spaces (say, 30000).
-   Add "-d null" for editing from a script file without displaying.
-   In Insert mode: Remember the characters that were removed with backspace
    and re-insert them one at a time with <key1>, all together with <key2>.
-   Amiga: Add possibility to set a keymap.  The code in amiga.c does not work
    yet.
-   Implement 'redraw' option.
-   Add special code to 'sections' option to define something else but '{' or
    '}' as the start of a section (e.g. one shiftwidth to the right).
7   Allow using Vim in a pipe: "ls | vim -u xxx.vim - | yyy".  Only needs
    implementing ":w" to stdout in the buffer that was read from stdin.
    Perhaps writing to stdout will work, since stderr is used for the terminal
    I/O.
8   Allow opening an unnamed buffer with ":e !cmd" and ":sp !cmd".  Vile can
    do it.
-   Add commands like ]] and [[ that do not include the line jumped to.
-   When :unab without matching "from" part and several matching "to" parts,
    delete the entry that was used last, instead of the first in the list.
-   Add text justification option.
-   Set boolean options on/off with ":set paste=off", ":set paste=on".
-   After "inv"ing an option show the value: ":set invpaste" gives "paste is
    off".
-   Check handling of CTRL-V and '\' for ":" commands that do not have TRLBAR.
-   When a file cannot be opened but does exist, give error message.
-   Amiga: When 'r' protection bit is not set, file can still be opened but
    gives read errors.  Check protection before opening.
-   When writing check for file exists but no permission, "Permission denied".
-   If file does not exist, check if directory exists.
-   Settings edit mode: make file with ":set opt=xx", edit it, parse it as ex
    commands.
-   ":set -w all": list one option per line.
-   Amiga: test for 'w' flag when reading a file.
-   :table command (Webb)
-   Add new operator: clear, make area white (replace with spaces): "g ".
-   Add command to ":read" a file at a certain column (blockwise read?).
-   Add sort of replace mode where case is taken from the old text (Goldfarb).
-   Allow multiple arguments for ":read", read all the files.
-   Support for tabs in specific columns: ":set tabcol=8,20,34,56" (Demirel).
-   Add 'searchdir' option: Directories to search for file name being edited
    (Demirel).
-   Modifier for the put command: Change to linewise, charwise, blockwise, etc.
-   Add commands for saving and restoring options ":set save" "set restore",
    for use in macro's and the like.
-   Keep output from listings in a window, so you can have a look at it while
    working in another window.  Put cmdline in a separate window?
-   Add possibility to put output of Ex commands in a buffer or file, e.g. for
    ":set all".  ":r :set all"?
-   When the 'equalalways' option is set, creating a new window should not
    result in windows to become bigger.  Deleting a window should not result in
    a window to become smaller (Webb).
-   When resizing the whole Vim window, the windows inside should be resized
    proportionally (Webb).
-   Include options directly in option table, no indirect pointers.  Use
    mkopttab to make option table?
-   When doing ":w dir", where "dir" is a directory name, write the current
    file into that directory, with the current file name (without the path)?
-   Support for 'dictionary's that are sorted, makes access a lot faster
    (Haritsis).
-   Add "^Vrx" on the command line, replace with contents of register x.  Used
    instead of CTRL-R to make repeating possible. (Marinichev)
-   Add "^Vb" on the command line, replace with word before or under the
    cursor?
-   Support mapping for replace mode and "r" command (Vi doesn't do this)?
8   Sorting of filenames for completion is wrong on systems that ignore
    case of filenames.  Add 'ignorefncase' option.  When set, case in
    filenames is ignored for sorting them. Patch by Mike Williams:
    ~/vim/patches/ignorefncase.  Also change what matches?  Or use another
    option name.
8   Should be able to compile Vim in another directory, with $(srcdir) set to
    where the sources are. Add $(srcdir) in the Makefile in a lot of places.
    (Netherton)
6   Make it configurable when "J" inserts a space or not.  Should not add a
    space after "(", for example.
5   When inserting spaces after the end-of-line for 'virtualedit', use tabs
    when the user wants this (e.g., add a "tab" field to 'virtualedit').
    (Servatius Brandt)


From Elvis:
-   Use "instman.sh" to install manpages?
-   Add ":alias" command.
-   Search patterns:
      \@	match word under cursor.
    but do:
      \@w	match the word under the cursor?
      \@W	match the WORD under the cursor?
8   ":window" command:
    :win +	next window (up)
    :win ++	idem, wrapping
    :win -	previous window (down)
    :win --	idem, wrapping
    :win nr	to window number "nr"
    :win name	to window editing buffer "name"
7   ":cc" compiles a single file (default: current one).  'ccprg'   option is
    program to use with ":cc".  Use ":compile" instead of ":cc"?


From xvi:
-   CTRL-_ : swap 8th bit of character.
-   Add egrep-like regex type, like xvi (Ned Konz) or Perl (Emmanuel Mogenet)


From vile:
-   When horizontal scrolling, use '>' for lines continuing right of a window.
-   Support putting .swp files in /tmp: Command in rc.local to move .swp files
    from /tmp to some directory before deleting files.


Far future and "big" extensions:
-   Instead of using a Makefile and autoconf, use a simple shell script to
    find the C compiler and do everything with C code.  Translate something
    like an Aap recipe and configure.ac to C.  Avoids depending on Python,
    thus will work everywhere.  With batch file to find the C compiler it
    would also work on MS-Windows.
-   Make it easy to setup Vim for groups of users: novice vi users, novice
    Vim users, C programmers, xterm users, GUI users,...
-   Change layout of blocks in swap file: Text at the start, with '\n' in
    between lines (just load the file without changes, except for Mac).
    Indexes for lines are from the end of the block backwards.  It's the
    current layout mirrored.
-   Make it possible to edit a register, in a window, like a buffer.
-   Add stuff to syntax highlighting to change the text (upper-case keywords,
    set indent, define other highlighting, etc.).
-   Mode to keep C-code formatted all the time (sort of on-line indent).
-   Several top-level windows in one Vim session.  Be able to use a different
    font in each top-level window.
-   Allow editing above start and below end of buffer (flag in 'virtualedit').
-   Smart cut/paste: recognize words and adjust spaces before/after them.
-   Add open mode, use it when terminal has no cursor positioning.
-   Special "drawing mode": a line is drawn where the cursor is moved to.
    Backspace deletes along the line (from jvim).
-   Support for underlining (underscore-BS-char), bold (char-BS-char) and other
    standout modes switched on/off with , 'overstrike' option (Reiter).
-   Add vertical mode (Paul Jury, Demirel): "5vdw" deletes a word in five
    lines, "3vitextESC" will insert "text" in three lines, etc..
4   Recognize l, #, p as 'flags' to EX commands:
    :g/RE/#l shall print lines with line numbers and in list format.
    :g/RE/dp shall print lines that are deleted.
    POSIX: Commands where flags shall apply to all lines written: list,
    number, open, print, substitute, visual, &, z.  For other commands, flags
    shall apply to the current line after the command completes.  Examples:
    :7,10j #l Join the lines 7-10 and print the result in list
-   Allow two or more users to edit the same file at the same time.  Changes
    are reflected in each Vim immediately.  Could work with local files but
    also over the internet.  See http://www.codingmonkeys.de/subethaedit/.

vim:tw=78:sw=4:sts=4:ts=8:noet:ft=help:norl:fo+=n:
