*netbeans.txt*  For Vim version 9.1.  Last change: 2024 May 11


		  VIM REFERENCE MANUAL    by <PERSON> et al.


					*netbeans* *NetBeans* *netbeans-support*

Vim NetBeans Protocol: a socket interface for Vim integration into an IDE.

1.  Introduction				|netbeans-intro|
2.  Integration features			|netbeans-integration|
3.  Configuring Vim for NetBeans		|netbeans-configure|
4.  Error Messages				|netbeans-messages|
5.  Running Vim in NetBeans mode		|netbeans-run|
6.  NetBeans protocol				|netbeans-protocol|
7.  NetBeans commands				|netbeans-commands|
8.  Known problems				|netbeans-problems|
9.  Debugging NetBeans protocol			|netbeans-debugging|
10. NetBeans External Editor
    10.1. Downloading NetBeans			|netbeans-download|
    10.2. NetBeans Key Bindings			|netbeans-keybindings|
    10.3. Preparing NetBeans for Vim		|netbeans-preparation|
    10.4. Obtaining the External Editor Module	|obtaining-exted|
    10.5. Setting up NetBeans to run with Vim	|netbeans-setup|

{only available when compiled with the |+netbeans_intg| feature}

==============================================================================
1. Introduction						*netbeans-intro*

The NetBeans interface was initially developed to integrate Vim into the
NetBeans Java IDE, using the external editor plugin.  This NetBeans plugin no
longer exists for recent versions of NetBeans but the protocol was developed
in such a way that any IDE can use it to integrate Vim.

The NetBeans protocol of Vim is a text based communication protocol, over a
classical TCP socket. There is no dependency on Java or NetBeans. Any language
or environment providing a socket interface can control Vim using this
protocol. There are existing implementations in C, C++, Python and Java. The
name NetBeans is kept today for historical reasons.

Active project using the NetBeans protocol of Vim:
- Eclim, http://eclim.org/

VimIntegration, description of various projects doing Vim Integration:
	http://www.freehackers.org/VimIntegration

Projects using the NetBeans protocol of Vim are or were:
- Agide, an IDE for the AAP project, written in Python (now replaced by
  |:Termdebug|): http://www.a-a-p.org
- Clewn, a gdb integration into Vim, written in C:
	http://clewn.sourceforge.net/
- Pyclewn, a gdb integration into Vim, written in Python:
	http://pyclewn.sourceforge.net/
- VimWrapper, library to easy Vim integration into IDE:
	http://www.freehackers.org/VimWrapper
Outdated projects (links don't work):
- VimPlugin, integration of Vim inside Eclipse:
	http://vimplugin.sourceforge.net/wiki/pmwiki.php
- PIDA, IDE written in Python integrating Vim:
	http://pida.co.uk/

Check the specific project pages to see how to use Vim with these projects.

An alternative is to use a channel, see |channel|.

In the rest of this help page, we will use the term "Vim Controller" to
describe the program controlling Vim through the NetBeans socket interface.


About the NetBeans IDE ~

NetBeans is an open source Integrated Development Environment developed
jointly by Sun Microsystems, Inc. and the netbeans.org developer community.
Initially just a Java IDE, NetBeans has had C, C++, and Fortran support added
in recent releases.

For more information visit the main NetBeans web site http://www.netbeans.org.
The External Editor is now, unfortunately, declared obsolete.  See (link seems
dead): http://externaleditor.netbeans.org.

Sun Microsystems, Inc. also ships NetBeans under the name Sun ONE Studio.
Visit http://www.sun.com for more information regarding the Sun ONE Studio
product line.

Current releases of NetBeans provide full support for Java and limited support
for C, C++, and Fortran.  Current releases of Sun ONE Studio provide full
support for Java, C, C++, and Fortran.

==============================================================================
2. Integration features					*netbeans-integration*

The NetBeans socket interface of Vim allows to get information from Vim or to
ask Vim to perform specific actions:
- get information about buffer: buffer name, cursor position, buffer content,
  etc.
- be notified when buffers are open or closed
- be notified of how the buffer content is modified
- load and save files
- modify the buffer content
- installing special key bindings
- raise the window, control the window geometry

For sending key strokes to Vim or for evaluating functions in Vim, you must
use the |clientserver| interface.


==============================================================================
3. Configuring Vim for NetBeans			*netbeans-configure*

For more help about installing Vim, please read |usr_90.txt| in the Vim User
Manual.


On Unix:
--------

When running configure without arguments the NetBeans interface should be
included.  That is, if the configure check to find out if your system supports
the required features succeeds.

In case you do not want the NetBeans interface you can disable it by
uncommenting a line with "--disable-netbeans" in the Makefile.

Currently the NetBeans interface is supported by Vim running in a terminal and
by gvim when it is run with one of the following GUIs: GTK, GNOME, Windows
and Motif.

							*netbeans-xpm*
If Motif support is required the user must supply XPM libraries.
The XPM library is required to show images within Vim with Motif.
Without it the toolbar and signs will be disabled.

The XPM library is provided by Arnaud Le Hors of the French National Institute
for Research in Computer Science and Control.  It can be downloaded from
http://cgit.freedesktop.org/xorg/lib/libXpm.  The current release, as of this
writing, is xpm-3.4k-solaris.tgz, which is a gzip'ed tar file.  If you create
the directory /usr/local/xpm and untar the file there you can use the
uncommented lines in the Makefile without changing them.  If you use another
xpm directory you will need to change the XPM_DIR in src/Makefile.


On MS-Windows:
--------------

The Win32 support is now in beta stage.

To use XPM signs on Win32 (e.g. when using with NetBeans) you can compile
XPM by yourself or use precompiled libraries from (link seems dead):
http://iamphet.nm.ru/misc/ (for MS Visual C++) or
http://gnuwin32.sourceforge.net (for MinGW).

Enable debugging:
-----------------

To enable debugging of Vim and of the NetBeans protocol, the "NBDEBUG" macro
needs to be defined.  Search in the Makefile of the platform you are using for
"NBDEBUG" to see what line needs to be uncommented.  This effectively adds
"-DNBDEBUG" to the compile command.  Also see |netbeans-debugging|

==============================================================================
4. Error Messages					*netbeans-messages*

These error messages are specific to NetBeans socket protocol:

							*E463*
Region is guarded, cannot modify
		The Vim Controller has defined guarded areas in the text,
		which you cannot change.  Also sets the current buffer, if
		necessary.

							*E532*
The defineAnnoType highlighting color name is too long
		The maximum length of the "fg" or "bg" color argument in the
		defineAnnoType command is 32 characters.
		New in version 2.5.

							*E656*
Writes of unmodified buffers forbidden
		Writes of unmodified buffers that were opened from the
		Vim Controller are not possible.

							*E657*
Partial writes disallowed
		Partial writes for buffers that were opened from the
		Vim Controller are not allowed.

							*E658*
Connection lost for this buffer
		The Vim Controller has become confused about the state of
		this file.  Rather than risk data corruption, it has severed
		the connection for this file.  Vim will take over
		responsibility for saving changes to this file and the
		Vim Controller will no longer know of these changes.

							*E744*
Read-only file
		Vim normally allows changes to a read-only file and only
		enforces the read-only rule if you try to write the file.
		However, NetBeans does not let you make changes to a file
		which is read-only and becomes confused if Vim does this.
		So Vim does not allow modifications to files when run
		in NetBeans mode.

==============================================================================
5. Running Vim in NetBeans mode				*netbeans-run*

There are two different ways to run Vim in NetBeans mode:

	+ an IDE may start Vim with the |-nb| command line argument
	+ NetBeans can be started from within Vim with the |:nbstart| command

Vim uses a 3 second timeout on trying to make the connection.

							*netbeans-parameters*
Three forms can be used to setup the NetBeans connection parameters.
When started from the command line, the |-nb| command line argument may be:

	-nb={fname}				from a file
	-nb:{hostname}:{addr}:{password}	directly
	-nb					from a file or environment

When started from within Vim, the |:nbstart| optional argument may be:

	={fname}				from a file
	:{hostname}:{addr}:{password}	        directly
	<MISSING ARGUMENT>			from a file or environment

							*E660* *E668*
When NetBeans is started from the command line, for security reasons, the best
method is to write the information in a file readable only by the user.  The
name of the file can be passed with the "-nb={fname}" argument or, when "-nb"
is used without a parameter, the environment variable "__NETBEANS_CONINFO".
The file must contain these three lines, in any order:

	host={hostname}
	port={addr}
	auth={password}

Other lines are ignored.  The Vim Controller is responsible for deleting the
file afterwards.

{hostname} is the name of the machine where Vim Controller is running.  When
omitted the environment variable "__NETBEANS_HOST" is used or the default
"localhost".

{addr} is the port number for the NetBeans interface.  When omitted the
environment variable "__NETBEANS_SOCKET" is used or the default 3219.

{password} is the password for connecting to NetBeans.  When omitted the
environment variable "__NETBEANS_VIM_PASSWORD" is used or "changeme".

Vim will initiate a socket connection (client side) to the specified host and
port upon startup. The password will be sent with the AUTH event when the
connection has been established.


==============================================================================
6. NetBeans protocol					*netbeans-protocol*

The communication between the Vim Controller and Vim uses plain text
messages.  This protocol was first designed to work with the external editor
module of NetBeans.  Later it was extended to work with Agide (A-A-P GUI IDE,
see http://www.a-a-p.org) and then with other IDE. The extensions are marked
with "version 2.1".

Version 2.2 of the protocol has several minor changes which should only affect
NetBeans users (ie, not Agide users).  However, a bug was fixed which could
cause confusion.  The netbeans_saved() function sent a "save" protocol
command.  In protocol version 2.1 and earlier this was incorrectly interpreted
as a notification that a write had taken place.  In reality, it told NetBeans
to save the file so multiple writes were being done.  This caused various
problems and has been fixed in 2.2.  To decrease the likelihood of this
confusion happening again, netbeans_saved() has been renamed to
netbeans_save_buffer().

We are now at version 2.5.  For the differences between 2.4 and 2.5 search for
"2.5" below.

The messages are currently sent over a socket.  Since the messages are in
plain UTF-8 text this protocol could also be used with any other communication
mechanism.

Netbeans messages are processed when Vim is idle, waiting for user input.
When Vim is run in non-interactive mode, for example when running an automated
test case that sources a Vim script, the idle loop may not be called often
enough. In that case, insert |:sleep| commands in the Vim script. The |:sleep|
command does invoke Netbeans messages processing.

6.1 Kinds of messages		|nb-messages|
6.2 Terms			|nb-terms|
6.3 Commands			|nb-commands|
6.4 Functions and Replies	|nb-functions|
6.5 Events			|nb-events|
6.6 Special messages		|nb-special|
6.7 Protocol errors		|nb-protocol_errors|


6.1 Kinds of messages					*nb-messages*

There are four kinds of messages:

kind		direction	comment ~
Command		IDE -> editor	no reply necessary
Function	IDE -> editor	editor must send back a reply
Reply		editor -> IDE	only in response to a Function
Event		editor -> IDE	no reply necessary

The messages are sent as a single line with a terminating newline character.
Arguments are separated by a single space.  The first item of the message
depends on the kind of message:

kind		first item		example ~
Command		bufID:name!seqno	11:showBalloon!123 "text"
Function	bufID:name/seqno	11:getLength/123
Reply		seqno			123 5000
Event		bufID:name=seqno	11:keyCommand=123 "S-F2"



6.2 Terms						*nb-terms*

bufID		Buffer number.  A message may be either for a specific buffer
		or generic.  Generic messages use a bufID of zero.  NOTE: this
		buffer ID is assigned by the IDE, it is not Vim's buffer
		number.  The bufID must be a sequentially rising number,
		starting at one.  When the 'switchbuf' option is set to
		"usetab" and the "bufID" buffer is not found in the current
		tab page, the netbeans commands and functions that set this
		buffer as the current buffer will jump to the first open
		window that contains this buffer in other tab pages instead of
		replacing the buffer in the current window.

seqno		The IDE uses a sequence number for Commands and Functions.  A
		Reply must use the sequence number of the Function that it is
		associated with.  A zero sequence number can be used for
		Events (the seqno of the last received Command or Function can
		also be used).

string		Argument in double quotes.  Text is in UTF-8 encoding.  This
		means ASCII is passed as-is.  Special characters are
		represented with a backslash:
			\"	double quote
			\n	newline
			\r	carriage-return
			\t	tab (optional, also works literally)
			\\	backslash
		NUL bytes are not allowed!

boolean		Argument with two possible values:
			T	true
			F	false

number		Argument with a decimal number.

color		Argument with either a decimal number, "none" (without the
		quotes) or the name of a color (without the quotes) defined
		both in the color list in |highlight-ctermfg| and in the color
		list in |gui-colors|.
		New in version 2.5.

offset		A number argument that indicates a byte position in a buffer.
		The first byte has offset zero.  Line breaks are counted for
		how they appear in the file (CR/LF counts for two bytes).
		Note that a multibyte character is counted for the number of
		bytes it takes.

lnum/col	Argument with a line number and column number position.  The
		line number starts with one, the column is the byte position,
		starting with zero.  Note that a multibyte character counts
		for several columns.

pathname	String argument: file name with full path.


6.3 Commands						*nb-commands*

actionMenuItem	Not implemented.

actionSensitivity
		Not implemented.

addAnno serNum typeNum off len
		Place an annotation in this buffer.
		Arguments:
		   serNum	number	serial number of this placed
					annotation, used to be able to remove
					it
		   typeNum	number	sequence number of the annotation
					defined with defineAnnoType for this
					buffer
		   off		number	offset where annotation is to be placed
		   len		number	not used
		In version 2.1 "lnum/col" can be used instead of "off".

balloonResult text
		Not implemented.

close		Close the buffer.  This leaves us without current buffer, very
		dangerous to use!

create		Creates a buffer without a name.  Replaces the current buffer
		(it's hidden when it was changed).
		The Vim Controller should use this as the first command for a
		file that is being opened.  The sequence of commands could be:
			create
			setCaretListener	(ignored)
			setModified		(no effect)
			setContentType		(ignored)
			startDocumentListen
			setTitle
			setFullName

defineAnnoType typeNum typeName tooltip glyphFile fg bg
		Define a type of annotation for this buffer.
		Arguments:
		   typeNum	number	sequence number (not really used)
		   typeName	string	name that identifies this annotation
		   tooltip	string	not used
		   glyphFile	string	name of icon file
		   fg		color	foreground color for line highlighting
		   bg		color	background color for line highlighting
		Vim will define a sign for the annotation.
		When color is a number, this is the "#rrggbb" Red, Green and
		Blue values of the color (see |gui-colors|) and the
		highlighting is only defined for gVim.
		When color is a name, this color is defined both for Vim
		running in a color terminal and for gVim.
		When both "fg" and "bg" are "none" no line highlighting is
		used (new in version 2.1).
		When "glyphFile" is empty, no text sign is used (new in
		version 2.1).
		When "glyphFile" is one or two characters long, a text sign is
		defined (new in version 2.1).
		Note: the annotations will be defined in sequence, and the
		sequence number is later used with addAnno.

editFile pathname
		Set the name for the buffer and edit the file "pathname", a
		string argument.
		Normal way for the IDE to tell the editor to edit a file.

		You must set a bufId different of 0 with this command to
		assign a bufId to the buffer. It will trigger an event
		fileOpened with a bufId of 0 but the buffer has been assigned.

		If the IDE is going to pass the file text to the editor use
		these commands instead:
			setFullName
			insert
			initDone
		New in version 2.1.

enableBalloonEval
		Not implemented.

endAtomic	End an atomic operation.  The changes between "startAtomic"
		and "endAtomic" can be undone as one operation.  But it's not
		implemented yet.  Redraw when necessary.

guard off len
		Mark an area in the buffer as guarded.  This means it cannot
		be edited.  "off" and "len" are numbers and specify the text
		to be guarded.

initDone	Mark the buffer as ready for use.  Implicitly makes the buffer
		the current buffer.  Fires the BufReadPost autocommand event.

insertDone starteol readonly
		Sent by Vim Controller to tell Vim an initial file insert is
		done.  This triggers a read message being printed.  If
		"starteol" is "F" then the last line doesn't have a EOL. If
		"readonly" is "T" then the file is marked as readonly. Prior
		to version 2.3, no read messages were displayed after opening
		a file.  New in version 2.3.

moveAnnoToFront serNum
		Not implemented.

netbeansBuffer isNetbeansBuffer
		If "isNetbeansBuffer" is "T" then this buffer is "owned" by
		NetBeans.
		New in version 2.2.

putBufferNumber pathname
		Associate a buffer number with the Vim buffer by the name
		"pathname", a string argument.  To be used when the editor
		reported editing another file to the IDE and the IDE needs to
		tell the editor what buffer number it will use for this file.
		Also marks the buffer as initialized.
		New in version 2.1.

raise		Bring the editor to the foreground.
		Only when Vim is run with a GUI.
		New in version 2.1.

removeAnno serNum
		Remove a previously placed annotation for this buffer.
		"serNum" is the same number used in addAnno.

save		Save the buffer when it was modified.  The other side of the
		interface is expected to write the buffer and invoke
		"setModified" to reset the "changed" flag of the buffer.
		The writing is skipped when one of these conditions is true:
		- 'write' is not set
		- the buffer is read-only
		- the buffer does not have a file name
		- 'buftype' disallows writing
		New in version 2.2.

saveDone
		Sent by Vim Controller to tell Vim a save is done.  This
		triggers a save message being printed.  Prior to version 2.3,
		no save messages were displayed after a save.
		New in version 2.3.

setAsUser	Not implemented.

setBufferNumber pathname
		Associate a buffer number with Vim buffer by the name
		"pathname".  To be used when the editor reported editing
		another file to the IDE and the IDE needs to tell the editor
		what buffer number it will use for this file.
		Has the side effect of making the buffer the current buffer.
		See "putBufferNumber" for a more useful command.

setContentType
		Not implemented.

setDot off	Make the buffer the current buffer and set the cursor at the
		specified position.  If the buffer is open in another window
		than make that window the current window.
		If there are folds they are opened to make the cursor line
		visible.
		In version 2.1 "lnum/col" can be used instead of "off".

setExitDelay seconds
		Set the delay for exiting to "seconds", a number.
		This delay is used to give the IDE a chance to handle things
		before really exiting.  The default delay is two seconds.
		New in version 2.1.
		Obsolete in version 2.3.

setFullName pathname
		Set the file name to be used for a buffer to "pathname", a
		string argument.
		Used when the IDE wants to edit a file under control of the
		IDE.  This makes the buffer the current buffer, but does not
		read the file.  "insert" commands will be used next to set the
		contents.

setLocAndSize	Not implemented.

setMark		Not implemented.

setModified modified
		When the boolean argument "modified" is "T" mark the buffer as
		modified, when it is "F" mark it as unmodified.

setModtime time
		Update a buffers modification time after the file has been
		saved directly by the Vim Controller.
		New in version 2.3.

setReadOnly readonly
		When the boolean argument "readonly" is "T" for True, mark the
		buffer as readonly, when it is "F" for False, mark it as not
		readonly.  Implemented in version 2.3.

setStyle	Not implemented.

setTitle name
		Set the title for the buffer to "name", a string argument.
		The title is only used for the Vim Controller functions, not
		by Vim.

setVisible visible
		When the boolean argument "visible" is "T", goto the buffer.
		The "F" argument does nothing.

showBalloon text
		Show a balloon (popup window) at the mouse pointer position,
		containing "text", a string argument.  The balloon should
		disappear when the mouse is moved more than a few pixels.
		Only when Vim is run with a GUI.
		New in version 2.1.

specialKeys
		Map a set of keys (mostly function keys) to be passed back
		to the Vim Controller for processing.  This lets regular IDE
		hotkeys be used from Vim.
		Implemented in version 2.3.

startAtomic	Begin an atomic operation.  The screen will not be updated
		until "endAtomic" is given.

startCaretListen
		Not implemented.

startDocumentListen
		Mark the buffer to report changes to the IDE with the
		"insert" and "remove" events.  The default is to report
		changes.

stopCaretListen
		Not implemented.

stopDocumentListen
		Mark the buffer to stop reporting changes to the IDE.
		Opposite of startDocumentListen.
		NOTE: if "netbeansBuffer" was used to mark this buffer as a
		NetBeans buffer, then the buffer is deleted in Vim.  This is
		for compatibility with Sun Studio 10.

unguard off len
		Opposite of "guard", remove guarding for a text area.
		Also sets the current buffer, if necessary.

version		Not implemented.


6.4 Functions and Replies				*nb-functions*

getDot		Not implemented.

getCursor	Return the current buffer and cursor position.
		The reply is:
			seqno bufID lnum col off
		seqno = sequence number of the function
		bufID = buffer ID of the current buffer (if this is unknown -1
			is used)
		lnum  = line number of the cursor (first line is one)
		col   = column number of the cursor (in bytes, zero based)
		off   = offset of the cursor in the buffer (in bytes)
		New in version 2.1.

getLength	Return the length of the buffer in bytes.
		Reply example for a buffer with 5000 bytes:
			123 5000
		TODO: explain use of partial line.

getMark		Not implemented.

getAnno serNum
		Return the line number of the annotation in the buffer.
		Argument:
			serNum		serial number of this placed annotation
		The reply is:
			123 lnum	line number of the annotation
			123 0		invalid annotation serial number
		New in version 2.4.

getModified	When a buffer is specified: Return zero if the buffer does not
		have changes, one if it does have changes.
		When no buffer is specified (buffer number zero): Return the
		number of buffers with changes.  When the result is zero it's
		safe to tell Vim to exit.
		New in version 2.1.

getText		Return the contents of the buffer as a string.
		Reply example for a buffer with two lines
			123 "first line\nsecond line\n"
		NOTE: docs indicate an offset and length argument, but this is
		not implemented.

insert off text
		Insert "text" before position "off".  "text" is a string
		argument, "off" a number.
		"text" should have a "\n" (newline) at the end of each line.
		Or "\r\n" when 'fileformat' is "dos".  When using "insert" in
		an empty buffer Vim will set 'fileformat' accordingly.
		When "off" points to the start of a line the text is inserted
		above this line.  Thus when "off" is zero lines are inserted
		before the first line.
		When "off" points after the start of a line, possibly on the
		NUL at the end of a line, the first line of text is appended
		to this line.  Further lines come below it.
		Possible replies:
			123		no problem
			123 !message	failed
		Note that the message in the reply is not quoted.
		Also sets the current buffer, if necessary.
		Does not move the cursor to the changed text.
		Resets undo information.

remove off length
		Delete "length" bytes of text at position "off".  Both
		arguments are numbers.
		Possible replies:
			123		no problem
			123 !message	failed
		Note that the message in the reply is not quoted.
		Also sets the current buffer, if necessary.

saveAndExit	Perform the equivalent of closing Vim: ":confirm qall".
		If there are no changed files or the user does not cancel the
		operation Vim exits and no result is sent back.  The IDE can
		consider closing the connection as a successful result.
		If the user cancels the operation the number of modified
		buffers that remains is returned and Vim does not exit.
		New in version 2.1.


6.5 Events						*nb-events*

balloonEval off len type
		The mouse pointer rests on text for a short while.  When "len"
		is zero, there is no selection and the pointer is at position
		"off".  When "len" is non-zero the text from position "off" to
		"off" + "len" is selected.
		Only sent after "enableBalloonEval" was used for this buffer.
		"type" is not yet defined.
		Not implemented yet.

balloonText text
		Used when 'ballooneval' is set and the mouse pointer rests on
		some text for a moment.  "text" is a string, the text under
		the mouse pointer.
		Only when Vim is run with a GUI.
		New in version 2.1.

buttonRelease button lnum col
		Report which button was pressed and the location of the cursor
		at the time of the release.  Only for buffers that are owned
		by the Vim Controller.  This event is not sent if the button
		was released while the mouse was in the status line or in a
		separator line.  If col is less than 1 the button release was
		in the sign area.
		New in version 2.2.

disconnect
		Tell the Vim Controller that Vim is exiting and not to try and
		read or write more commands.
		New in version 2.3.

fileClosed	Not implemented.

fileModified	Not implemented.

fileOpened pathname open modified
		A file was opened by the user.
		Arguments:
		   pathname	string	  name of the file
		   open		boolean   always "T"
		   modified	boolean   always "F"

geometry cols rows x y
		Report the size and position of the editor window.
		Arguments:
		   cols		number	  number of text columns
		   rows		number	  number of text rows
		   x		number	  pixel position on screen
		   y		number	  pixel position on screen
		Only works for Motif.

insert off text
		Text "text" has been inserted in Vim at position "off".
		Only fired when enabled, see "startDocumentListen".

invokeAction	Not implemented.

keyCommand keyName
		Reports a special key being pressed with name "keyName", which
		is a string.
		Supported key names:
			F1		function key 1
			F2		function key 2
			...
			F12		function key 12

			' '		space (without the quotes)
			!		exclamation mark
			...		any other ASCII printable character
			~		tilde

			X		any unrecognized key

		The key may be prepended by "C", "S" and/or "M" for Control,
		Shift and Meta (Alt) modifiers.  If there is a modifier a dash
		is used to separate it from the key name.  For example:
		"C-F2".
		ASCII characters are new in version 2.1.

keyAtPos keyName lnum/col
		Like "keyCommand" and also report the line number and column
		of the cursor.
		New in version 2.1.

killed		A file was deleted or wiped out by the user and the buffer
		annotations have been removed.  The bufID number for this
		buffer has become invalid.  Only for files that have been
		assigned a bufID number by the IDE.

newDotAndMark off off
		Reports the position of the cursor being at "off" bytes into
		the buffer.  Only sent just before a "keyCommand" event.

quit		Not implemented.

remove off len
		Text was deleted in Vim at position "off" with byte length
		"len".
		Only fired when enabled, see "startDocumentListen".

revert		Not implemented.

save		The buffer has been saved and is now unmodified.
		Only fired when enabled, see "startDocumentListen".

startupDone	The editor has finished its startup work and is ready for
		editing files.
		New in version 2.1.

unmodified	The buffer is now unmodified.
		Only fired when enabled, see "startDocumentListen".

version vers	Report the version of the interface implementation.  Vim
		reports "2.4" (including the quotes).


6.6 Special messages					*nb-special*

These messages do not follow the style of the messages above.  They are
terminated by a newline character.

ACCEPT		Not used.

AUTH password	editor -> IDE: First message that the editor sends to the IDE.
		Must contain the password for the socket server, as specified
		with the |-nb| argument.  No quotes are used!

DISCONNECT	IDE -> editor: break the connection.  The editor will exit.
		The IDE must only send this message when there are no unsaved
		changes!

DETACH		IDE -> editor: break the connection without exiting the
		editor.  Used when the IDE exits without bringing down the
		editor as well.
		New in version 2.1.

REJECT		Not used.


6.7 Protocol errors					*nb-protocol_errors*

These errors occur when a message violates the protocol:
*E627* *E628* *E629* *E632* *E633* *E634* *E635* *E636*
*E637* *E638* *E639* *E640* *E641* *E642* *E643* *E644* *E645* *E646*
*E647* *E648* *E650* *E651* *E652*


==============================================================================
7. NetBeans commands					*netbeans-commands*

							*:nbstart* *E511* *E838*
:nbs[tart] {connection}	Start a new Netbeans session with {connection} as the
			socket connection parameters.  The format of
			{connection} is described in |netbeans-parameters|.
			At any time, one may check if the netbeans socket is
			connected by running the command:
			':echo has("netbeans_enabled")'

							*:nbclose*
:nbc[lose]		Close the current NetBeans session. Remove all placed
			signs.

							*:nbkey*
:nb[key] {key}		Pass the {key} to the Vim Controller for processing.
			When a hot-key has been installed with the specialKeys
			command, this command can be used to generate a hotkey
			message to the Vim Controller.
			This command can also be used to pass any text to the
			Vim  Controller. It is used by Pyclewn, for example,
			to build the complete set of gdb commands as Vim user
			commands.
			The events newDotAndMark, keyCommand and keyAtPos are
			generated (in this order).


==============================================================================
8. Known problems					*netbeans-problems*

NUL bytes are not possible.  For editor -> IDE they will appear as NL
characters.  For IDE -> editor they cannot be inserted.

A NetBeans session may be initiated with Vim running in a terminal, and
continued later in a GUI environment after running the |:gui| command. In this
case, the highlighting defined for the NetBeans annotations may be cleared
when the ":gui" command sources .gvimrc and this file loads a colorscheme
that runs the command ":highlight clear".
New in version 2.5.


==============================================================================
9. Debugging NetBeans protocol				*netbeans-debugging*

To debug the Vim protocol, you must first compile Vim with debugging support
and NetBeans debugging support.  See |netbeans-configure| for instructions
about Vim compiling and how to enable debug support.

When running Vim, set the following environment variables:

    export SPRO_GVIM_DEBUG=netbeans.log
    export SPRO_GVIM_DLEVEL=0xffffffff

Vim will then log all the incoming and outgoing messages of the NetBeans
protocol to the file netbeans.log .

The content of netbeans.log after a session looks like this:
Tue May 20 17:19:27 2008
EVT: 0:startupDone=0
CMD 1: (1) create
CMD 2: (1) setTitle "testfile1.txt"
CMD 3: (1) setFullName "testfile1.txt"
EVT(suppressed): 1:remove=3 0 -1
EVT: 1:fileOpened=0 "d:\\work\\vimWrapper\\vimWrapper2\\pyvimwrapper\\tests\\testfile1.txt" T F
CMD 4: (1) initDone
FUN 5: (0) getCursor
REP 5: 1 1 0 0
CMD 6: (2) create
CMD 7: (2) setTitle "testfile2.txt"
CMD 8: (2) setFullName "testfile2.txt"
EVT(suppressed): 2:remove=8 0 -1
EVT: 2:fileOpened=0 "d:\\work\\vimWrapper\\vimWrapper2\\pyvimwrapper\\tests\\testfile2.txt" T F
CMD 9: (2) initDone


==============================================================================
10. NetBeans External Editor

NOTE: This information is obsolete!  Only relevant if you are using an old
version of NetBeans.


10.1. Downloading NetBeans			*netbeans-download*

The NetBeans IDE is available for download from netbeans.org.  You can download
a released version, download sources, or use CVS to download the current
source tree.  If you choose to download sources, follow directions from
netbeans.org on building NetBeans.

Depending on the version of NetBeans you download, you may need to do further
work to get the required External Editor module.  This is the module which lets
NetBeans work with gvim (or xemacs :-).  See http://externaleditor.netbeans.org
for details on downloading this module if your NetBeans release does not have
it.

For C, C++, and Fortran support you will also need the cpp module.  See
http://cpp.netbeans.org (link seems dead) for information regarding this
module.

You can also download Sun ONE Studio from Sun Microsystems, Inc for a 30 day
free trial.  See http://www.sun.com for further details.


10.2. NetBeans Key Bindings			*netbeans-keybindings*

Vim understands a number of key bindings that execute NetBeans commands.
These are typically all the Function key combinations.  To execute a NetBeans
command, the user must press the Pause key followed by a NetBeans key binding.
For example, in order to compile a Java file, the NetBeans key binding is
"F9".  So, while in vim, press "Pause F9" to compile a java file.  To toggle a
breakpoint at the current line, press "Pause Shift F8".

The Pause key is Function key 21.  If you don't have a working Pause key and
want to use F8 instead, use: >

	:map <F8> <F21>

The External Editor module dynamically reads the NetBeans key bindings so vim
should always have the latest key bindings, even when NetBeans changes them.


10.3. Preparing NetBeans for Vim		*netbeans-preparation*

In order for NetBeans to work with vim, the NetBeans External Editor module
must be loaded and enabled.  If you have a Sun ONE Studio Enterprise Edition
then this module should be loaded and enabled.  If you have a NetBeans release
you may need to find another way of obtaining this open source module.

You can check if you have this module by opening the Tools->Options dialog
and drilling down to the "Modules" list (IDE Configuration->System->Modules).
If your Modules list has an entry for "External Editor" you must make sure
it is enabled (the "Enabled" property should have the value "True").  If your
Modules list has no External Editor see the next section on |obtaining-exted|.


10.4. Obtaining the External Editor Module	*obtaining-exted*

There are 2 ways of obtaining the External Editor module.  The easiest way
is to use the NetBeans Update Center to download and install the module.
Unfortunately, some versions do not have this module in their update
center.  If you cannot download via the update center you will need to
download sources and build the module.  I will try and get the module
available from the NetBeans Update Center so building will be unnecessary.
Also check http://externaleditor.netbeans.org for other availability options.

To download the External Editor sources via CVS and build your own module,
see http://externaleditor.netbeans.org and http://www.netbeans.org.
Unfortunately, this is not a trivial procedure.


10.5. Setting up NetBeans to run with Vim	*netbeans-setup*

Assuming you have loaded and enabled the NetBeans External Editor module
as described in |netbeans-preparation| all you need to do is verify that
the gvim command line is properly configured for your environment.

Open the Tools->Options dialog and open the Editing category.  Select the
External Editor.  The right hand pane should contain a Properties tab and
an Expert tab.  In the Properties tab make sure the "Editor Type" is set
to "Vim".  In the Expert tab make sure the "Vim Command" is correct.

You should be careful if you change the "Vim Command".  There are command
line options there which must be there for the connection to be properly
set up.  You can change the command name but that's about it.  If your gvim
can be found by your $PATH then the Vim Command can start with "gvim".  If
you don't want gvim searched from your $PATH then hard code in the full
Unix path name.  At this point you should get a gvim for any source file
you open in NetBeans.

If some files come up in gvim and others (with different file suffixes) come
up in the default NetBeans editor you should verify the MIME type in the
Expert tab MIME Type property.  NetBeans is MIME oriented and the External
Editor will only open MIME types specified in this property.


 vim:tw=78:ts=8:noet:ft=help:norl:
