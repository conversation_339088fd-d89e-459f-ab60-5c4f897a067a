*version4.txt*  For Vim version 9.1.  Last change: 2006 Apr 24


		  VIM REFERENCE MANUAL    by <PERSON>


This document lists the incompatible differences between Vim 3.0 and Vim 4.0.
Although 4.0 is mentioned here, this is also for version 4.1, 4.2, etc..

This file is important for everybody upgrading from Vim 3.0.  Read it
carefully to avoid unexpected problems.

'backup' option default changed			|backup-changed|
Extension for backup file changed		|backup-extension|
Structure of swap file changed			|swapfile-changed|
"-w scriptout" argument changed			|scriptout-changed|
Backspace and Delete keys			|backspace-delete|
Escape for | changed				|escape-bar|
Key codes changed				|key-codes-changed|
Terminal options changed			|termcap-changed|
'errorformat' option changed			|errorformat-changed|
'graphic' option gone				|graphic-option-gone|
'yankendofline' option gone			|ye-option-gone|
'icon' and 'title' default value changed	|icon-changed|
'highlight' option changed			|highlight-changed|
'tildeop' and 'weirdinvert' short names changed	|short-name-changed|
Use of "v", "V" and "CTRL-V" in Visual mode	|use-visual-cmds|
CTRL-B in Insert mode removed			|toggle-revins|


'backup' option default changed				*backup-changed*
-------------------------------

The default value for 'backup' used to be on.  This resulted in a backup file
being made when the original file was overwritten.

Now the default for 'backup' is off.  As soon as the writing of the file has
successfully finished, the backup file is deleted.  If you want to keep the
backup file, set 'backup' on in your vimrc.  The reason for this change is
that many people complained that leaving a backup file behind is not
Vi-compatible.						|'backup'|


Extension for backup file changed			*backup-extension*
---------------------------------

The extension for the backup file used to be ".bak".  Since other programs
also use this extension and some users make copies with this extension, it was
changed to the less obvious "~".  Another advantage is that this takes less
space, which is useful when working on a system with short file names.  For
example, on MS-DOS the backup files for "longfile.c" and "longfile.h" would
both become "longfile.bak"; now they will be "longfile.c~" and "longfile.h~".

If you prefer to use ".bak", you can set the 'backupext' option: >
	:set bex=.bak


Structure of swap file changed				*swapfile-changed*
------------------------------

The contents of the swap file were extended with several parameters.  Vim
stores the user name and other information about the edited file to make
recovery more easy and to be able to know where the swap file comes from.  The
first part of the swap file can now be understood on a machine with a
different byte order or sizeof(int).  When you try to recover a file on such a
machine, you will get an error message that this is not possible.

Because of this change, swap files cannot be exchanged between 3.0 and 4.0.
If you have a swap file from a crashed session with 3.0, use Vim 3.0 to
recover the file---don't use 4.0.			|swap-file|


"-w scriptout" argument changed				*scriptout-changed*
-------------------------------

"vim -w scriptout" used to append to the scriptout file.  Since this was
illogical, it now creates a new file.  An existing file is not overwritten
(to avoid destroying an existing file for those who rely on the appending).
[This was removed again later]					|-w|


Backspace and Delete keys				*backspace-delete*
-------------------------

In 3.0 both the delete key and the backspace key worked as a backspace in
insert mode; they deleted the character to the left of the cursor.  In 4.0 the
delete key has a new function: it deletes the character under the cursor, just
like it does on the command-line.  If the cursor is after the end of the line
and 'bs' is set, two lines are joined.			|<Del>| |i_<Del>|

In 3.0 the backspace key was always defined as CTRL-H and delete as CTRL-?.
In 4.0 the code for the backspace and delete key is obtained from termcap or
termlib, and adjusted for the "stty erase" value on Unix.  This helps people
who define the erase character according to the keyboard they are working on.
							|<BS>| |i_<BS>|

If you prefer backspace and delete in Insert mode to have the old behavior,
put this line in your vimrc:

	inoremap ^? ^H

And you may also want to add these, to fix the values for <BS> and <Del>:

	set t_kb=^H
	set t_kD=^?

(Enter ^H with CTRL-V CTRL-H and ^? with CTRL-V CTRL-? or <Del>.)

If the value for t_kb is correct, but the t_kD value is not, use the ":fixdel"
command.  It will set t_kD according to the value of t_kb.  This is useful if
you are using several different terminals.		|:fixdel|

When ^H is not recognized as <BS> or <Del>, it is used like a backspace.


Escape for | changed					*escape-bar*
--------------------

When the 'b' flag is present in 'cpoptions', the backslash cannot be used to
escape '|' in mapping and abbreviate commands, only CTRL-V can.  This is
Vi-compatible.  If you work in Vi-compatible mode and had used "\|" to include
a bar in a mapping, this needs to be replaced by "^V|".  See |:bar|.


Key codes changed					*key-codes-changed*
-----------------

The internal representation of key codes has changed dramatically.  In 3.0 a
one-byte code was used to represent a key.  This caused problems with
different characters sets that also used these codes.  In 4.0 a three-byte
code is used that cannot be confused with a character.		|key-notation|

If you have used the single-byte key codes in your vimrc for mappings, you
will have to replace them with the 4.0 codes.  Instead of using the three-byte
code directly, you should use the symbolic representation for this in <>.  See
the table below.  The table also lists the old name, as it was used in the 3.0
documentation.

The key names in <> can be used in mappings directly.  This makes it possible
to copy/paste examples or type them literally.  The <> notation has been
introduced for this |<>|.  The 'B' and '<' flags must not be present in
'cpoptions' to enable this to work |'cpoptions'|.

old name	new name	  old code	old MS-DOS code	~
				hex	dec	hex	dec	~
<ESC>		<Esc>
<TAB>		<Tab>
<LF>		<NL> <NewLine> <LineFeed>
<SPACE>		<Space>
<NUL>		<Nul>
<BELL>		<Bell>
<BS>		<BS> <BackSpace>
<INSERT>	<Insert>
<DEL>		<Del> <Delete>
<HOME>		<Home>
<END>		<End>
<PAGE_UP>	<PageUp>
<PAGE_DOWN>	<PageDown>

<C_UP>		<Up>		0x80	128	0xb0	176
<C_DOWN>	<Down>		0x81	129     0xb1	177
<C_LEFT>	<Left>		0x82	130     0xb2	178
<C_RIGHT>	<Right>		0x83	131     0xb3	179
<SC_UP>		<S-Up>		0x84	132     0xb4	180
<SC_DOWN>	<S-Down>	0x85	133     0xb5	181
<SC_LEFT>	<S-Left>	0x86	134     0xb6	182
<SC_RIGHT>	<S-Right>	0x87	135     0xb7	183

<F1>		<F1>		0x88	136     0xb8	184
<F2>		<F2>		0x89	137     0xb9	185
<F3>		<F3>		0x8a	138     0xba	186
<F4>		<F4>		0x8b	139     0xbb	187
<F5>		<F5>		0x8c	140     0xbc	188
<F6>		<F6>		0x8d	141     0xbd	189
<F7>		<F7>		0x8e	142     0xbe	190
<F8>		<F8>		0x8f	143     0xbf	191
<F9>		<F9>		0x90	144     0xc0	192
<F10>		<F10>		0x91	145     0xc1	193

<SF1>		<S-F1>		0x92	146     0xc2	194
<SF2>		<S-F2>		0x93	147     0xc3	195
<SF3>		<S-F3>		0x94	148     0xc4	196
<SF4>		<S-F4>		0x95	149     0xc5	197
<SF5>		<S-F5>		0x96	150     0xc6	198
<SF6>		<S-F6>		0x97	151     0xc7	199
<SF7>		<S-F7>		0x98	152     0xc8	200
<SF8>		<S-F8>		0x99	153     0xc9	201
<SF9>		<S-F9>		0x9a	154     0xca	202
<SF10>		<S-F10>		0x9b	155     0xcb	203

<HELP>		<Help>		0x9c	156     0xcc	204
<UNDO>		<Undo>		0x9d	157     0xcd	205

		(not used)	0x9e	158     0xce	206
		(not used)	0x9f	159     0xcf	207


Terminal options changed				*termcap-changed*
------------------------

The names of the terminal options have been changed to match the termcap names
of these options.  All terminal options now have the name t_xx, where xx is
the termcap name.  Normally these options are not used, unless you have a
termcap entry that is wrong or incomplete, or you have set the highlight
options to a different value.				|terminal-options|

Note that for some keys there is no termcap name.  Use the <> type of name
instead, which is a good idea anyway.

Note that "t_ti" has become "t_mr" (invert/reverse output) and "t_ts" has
become "t_ti" (init terminal mode).  Be careful when you use "t_ti"!

old name	new name	meaning		~
t_cdl		t_DL		delete number of lines		*t_cdl*
t_ci		t_vi		cursor invisible		*t_ci*
t_cil		t_AL		insert number of lines		*t_cil*
t_cm		t_cm		move cursor
t_cri		t_RI		cursor number of chars right	*t_cri*
t_cv		t_ve		cursor visible			*t_cv*
t_cvv		t_vs		cursor very visible		*t_cvv*
t_dl		t_dl		delete line
t_cs		t_cs		scroll region
t_ed		t_cl		clear display			*t_ed*
t_el		t_ce		clear line			*t_el*
t_il		t_al		insert line			*t_il*
		t_da		display may be retained above the screen
		t_db		display may be retained below the screen
t_ke		t_ke		put terminal out of keypad transmit mode
t_ks		t_ks		put terminal in keypad transmit mode
t_ms		t_ms		save to move cursor in highlight mode
t_se		t_se		normal mode (undo t_so)
t_so		t_so		shift out (standout) mode
t_ti		t_mr		reverse highlight
t_tb		t_md		bold mode			*t_tb*
t_tp		t_me		highlight end			*t_tp*
t_sr		t_sr		scroll reverse
t_te		t_te		out of termcap mode
t_ts		t_ti		into termcap mode		*t_ts_old*
t_vb		t_vb		visual bell
t_csc		t_CS		cursor is relative to scroll region *t_csc*

t_ku	t_ku	<Up>		arrow up
t_kd	t_kd	<Down>		arrow down
t_kr	t_kr	<Right>		arrow right
t_kl	t_kl	<Left>		arrow left
t_sku		<S-Up>		shifted arrow up		*t_sku*
t_skd		<S-Down>	shifted arrow down		*t_skd*
t_skr	t_%i	<S-Right>	shifted arrow right		*t_skr*
t_skl	t_#4	<S-Left>	shifted arrow left		*t_skl*
t_f1	t_k1	<F1>		function key 1			*t_f1*
t_f2	t_k2	<F2>		function key 2			*t_f2*
t_f3	t_k3	<F3>		function key 3			*t_f3*
t_f4	t_k4	<F4>		function key 4			*t_f4*
t_f5	t_k5	<F5>		function key 5			*t_f5*
t_f6	t_k6	<F6>		function key 6			*t_f6*
t_f7	t_k7	<F7>		function key 7			*t_f7*
t_f8	t_k8	<F8>		function key 8			*t_f8*
t_f9	t_k9	<F9>		function key 9			*t_f9*
t_f10	t_k;	<F10>		function key 10			*t_f10*
t_sf1		<S-F1>		shifted function key 1		*t_sf1*
t_sf2		<S-F2>		shifted function key 2		*t_sf2*
t_sf3		<S-F3>		shifted function key 3		*t_sf3*
t_sf4		<S-F4>		shifted function key 4		*t_sf4*
t_sf5		<S-F5>		shifted function key 5		*t_sf5*
t_sf6		<S-F6>		shifted function key 6		*t_sf6*
t_sf7		<S-F7>		shifted function key 7		*t_sf7*
t_sf8		<S-F8>		shifted function key 8		*t_sf8*
t_sf9		<S-F9>		shifted function key 9		*t_sf9*
t_sf10		<S-F10>		shifted function key 10		*t_sf10*
t_help	t_%1	<Help>		help key			*t_help*
t_undo	t_&8	<Undo>		undo key			*t_undo*


'errorformat' option changed				*errorformat-changed*
----------------------------

'errorformat' can now contain several formats, separated by commas.  The first
format that matches is used.  The default values have been adjusted to catch
the most common formats.				|errorformat|

If you have a format that contains a comma, it needs to be preceded with a
backslash.  Type two backslashes, because the ":set" command will eat one.


'graphic' option gone					*graphic-option-gone*
---------------------

The 'graphic' option was used to make the characters between <~> and 0xa0
display directly on the screen.  Now the 'isprint' option takes care of this
with many more possibilities.  The default setting is the same; you only need
to look into this if you previously set the 'graphic' option in your vimrc.
							|'isprint'|


'yankendofline' option gone				*ye-option-gone*
---------------------------

The 'yankendofline' option has been removed.  Instead you can just use
	:map Y y$


'icon' and 'title' default value changed		*icon-changed*
----------------------------------------

The 'title' option is now only set by default if the original title can be
restored.  Avoids "Thanks for flying Vim" titles.  If you want them anyway,
put ":set title" in your vimrc.				|'title'|

The default for 'icon' now depends on the possibility of restoring the
original value, just like 'title'.  If you don't like your icon titles to be
changed, add this line to your vimrc:			|'icon'|
	:set noicon


'highlight' option changed				*highlight-changed*
--------------------------

The 'i' flag now means italic highlighting, instead of invert.  The 'r' flag
is used for reverse highlighting, which is what 'i' used to be.  Normally you
won't see the difference, because italic mode is not supported on most
terminals and reverse mode is used as a fallback.	|'highlight'|

When an occasion is not present in 'highlight', use the mode from the default
value for 'highlight', instead of reverse mode.


'tildeop' and 'weirdinvert' short names changed		*short-name-changed*
-----------------------------------------------

Renamed 'to' (abbreviation for 'tildeop') to 'top'.	|'tildeop'|
Renamed 'wi' (abbreviation for 'weirdinvert') to 'wiv'.	|'weirdinvert'|

This was done because Vi uses 'wi' as the short name for 'window' and 'to' as
the short name for 'timeout'.  This means that if you try setting these
options, you won't get an error message, but the effect will be different.


Use of "v", "V" and "CTRL-V" in Visual mode		*use-visual-cmds*
-------------------------------------------

In Visual mode, "v", "V", and "CTRL-V" used to end Visual mode.  Now this
happens only if the Visual mode was in the corresponding type.  Otherwise the
type of Visual mode is changed.  Now only ESC can be used in all circumstances
to end Visual mode without doing anything.		|v_V|


CTRL-B in Insert mode removed				*toggle-revins*
-----------------------------

CTRL-B in Insert mode used to toggle the 'revins' option.  If you don't know
this and accidentally hit CTRL-B, it is very difficult to find out how to undo
it.  Since hardly anybody uses this feature, it is disabled by default.  If
you want to use it, define RIGHTLEFT in feature.h before compiling. |'revins'|

 vim:tw=78:ts=8:noet:ft=help:norl:
