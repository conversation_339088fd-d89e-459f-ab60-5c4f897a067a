*pi_getscript.txt*  For Vim version 9.1.  Last change: 2025 Apr 27
>
		GETSCRIPT REFERENCE MANUAL  by <PERSON>
<
Original Author:  <PERSON>  <<EMAIL>-NOSPAM>
	 (remove NOSPAM from the email address)
						*GetLatestVimScripts-copyright*
Copyright: (c) 2004-2012 by <PERSON>	*glvs-copyright*
	The VIM LICENSE (see |copyright|) applies to the files in this
	package, including getscriptPlugin.vim, getscript.vim,
	GetLatestVimScripts.dist, and pi_getscript.txt, except use "getscript"
	instead of "Vim".  Like anything else that's free, getscript and its
	associated files are provided *as is* and comes with no warranty of
	any kind, either expressed or implied.  No guarantees of
	merchantability.  No guarantees of suitability for any purpose.  By
	using this plugin, you agree that in no event will the copyright
	holder be liable for any damages resulting from the use of this
	software. Use at your own risk!

Getscript is a plugin that simplifies retrieval of the latest versions of the
scripts that you yourself use!  Typing |:GLVS| will invoke getscript; it will
then use the <GetLatestVimScripts.dat> (see |GetLatestVimScripts_dat|) file to
get the latest versions of scripts listed therein from https://www.vim.org/.

==============================================================================
1. Contents				*glvs-contents* *glvs* *getscript*
					*GetLatestVimScripts*

	1. Contents........................................: |glvs-contents|
	2. GetLatestVimScripts -- Getting Started..........: |glvs-install|
	3. GetLatestVimScripts Usage.......................: |glvs-usage|
	4. GetLatestVimScripts Data File...................: |glvs-data|
	5. GetLatestVimScripts Friendly Plugins............: |glvs-plugins|
	6. GetLatestVimScripts AutoInstall.................: |glvs-autoinstall|
	7. GetLatestViMScripts Options.....................: |glvs-options|
	8. GetLatestVimScripts Algorithm...................: |glvs-alg|
	9. GetLatestVimScripts History.....................: |glvs-hist|


==============================================================================
2. GetLatestVimScripts -- Getting Started		*getscript-start*
						*getlatestvimscripts-install*

	VERSION FROM VIM DISTRIBUTION			*glvs-dist-install*

Vim 7.0 does not include the GetLatestVimScripts.dist file which
serves as an example and a template.  So, you'll need to create
your own!  See |GetLatestVimScripts_dat|.

	VERSION FROM VIM SF NET				*glvs-install*

NOTE: The last step, that of renaming/moving the GetLatestVimScripts.dist
file, is for those who have just downloaded GetLatestVimScripts.tar.bz2 for
the first time.

The GetLatestVimScripts.dist file serves as an example and a template for your
own personal list.  Feel free to remove all the scripts mentioned within it;
the "important" part of it is the first two lines.

Your computer needs to have wget or curl for GetLatestVimScripts to do its work.

	1. if compressed:  gunzip getscript.vmb.gz
	2. Unix:
		vim getscript.vmb
		:so %
		:q
		cd ~/.vim/GetLatest
		mv GetLatestVimScripts.dist GetLatestVimScripts.dat
		(edit GetLatestVimScripts.dat to install your own personal
		list of desired plugins -- see |GetLatestVimScripts_dat|)

	3. Windows:
		vim getscript.vmb
		:so %
		:q
		cd **path-to-vimfiles**/GetLatest
		mv GetLatestVimScripts.dist GetLatestVimScripts.dat
		(edit GetLatestVimScripts.dat to install your own personal
		list of desired plugins -- see |GetLatestVimScripts_dat|)


==============================================================================
3. GetLatestVimScripts Usage				*glvs-usage* *:GLVS*

Unless it has been defined elsewhere, >

	:GLVS

will invoke GetLatestVimScripts().  If some other plugin has defined that
command, then you may type
>
	:GetLatestVimScripts
<
The script will attempt to update and, if permitted, will automatically
install scripts from http://vim.sourceforge.net/.  To do so it will peruse a
file,
>
	.vim/GetLatest/GetLatestVimScripts.dat                    (unix)
<
or >
	..wherever..\vimfiles\GetLatest\GetLatestVimScripts.dat   (windows)
(see |glvs-data|), and examine plugins in your [.vim|vimfiles]/plugin
directory (see |glvs-plugins|).

Scripts which have been downloaded will appear in the
~/.vim/GetLatest (unix) or ..wherever..\vimfiles\GetLatest (windows)
subdirectory.  GetLatestVimScripts will attempt to automatically
install them if you have the following line in your <.vimrc>: >

	let g:GetLatestVimScripts_allowautoinstall=1

The <GetLatestVimScripts.dat> file will be automatically be updated to
reflect the latest version of script(s) so downloaded.
(also see |glvs-options|)


==============================================================================
4. GetLatestVimScripts Data File		*getscript-data* *glvs-data*
						*:GetLatestVimScripts_dat*
The data file <GetLatestVimScripts.dat> must have for its first two lines
the following text:
>
	ScriptID SourceID Filename
	--------------------------
<
Following those two lines are three columns; the first two are numeric
followed by a text column.  The GetLatest/GetLatestVimScripts.dist file
contains an example of such a data file.  Anything following a #... is
ignored, so you may embed comments in the file.

The first number on each line gives the script's ScriptID.  When you're about
to use a web browser to look at scripts on http://vim.sf.net/, just before you
click on the script's link, you'll see a line resembling

	http://vim.sourceforge.net/scripts/script.php?script_id=40

The "40" happens to be a ScriptID that GetLatestVimScripts needs to
download the associated page, and is assigned by vim.sf.net itself
during initial uploading of the plugin.

The second number on each line gives the script's SourceID.  The SourceID
records the count of uploaded scripts as determined by vim.sf.net; hence it
serves to indicate "when" a script was uploaded.  Setting the SourceID to 1
insures that GetLatestVimScripts will assume that the script it has is
out-of-date.

The SourceID is extracted by GetLatestVimScripts from the script's page on
vim.sf.net; whenever it is greater than the one stored in the
GetLatestVimScripts.dat file, the script will be downloaded
(see |GetLatestVimScripts_dat|).

If your script's author has included a special comment line in his/her plugin,
the plugin itself will be used by GetLatestVimScripts to build your
<GetLatestVimScripts.dat> file, including any dependencies on other scripts it
may have.  As an example, consider: >

	" GetLatestVimScripts: 884  1 :AutoInstall: AutoAlign.vim

This comment line tells getscript.vim to check Vim script #884 and that the
script is automatically installable.  Getscript will also use this line to
help build the GetLatestVimScripts.dat file, by including a line such as: >

	884 1 :AutoInstall: AutoAlign.vim
<
assuming that such a line isn't already in GetLatestVimScripts.dat file.
See |glvs-plugins| for more.  Thus, GetLatestVimScripts thus provides a
comprehensive ability to keep your plugins up-to-date!

In summary:

  * Optionally tell getscript that it is allowed to build/append a
    GetLatestVimScripts.dat file based upon already installed plugins: >
	let g:GetLatestVimScripts_allowautoinstall=1
<
  * A line such as >
	" GetLatestVimScripts: 884  1 :AutoInstall: AutoAlign.vim
<   in an already-downloaded plugin constitutes the concurrence of the
    plugin author that getscript may do AutoInstall.  Not all plugins
    may be AutoInstall-able, and the plugin's author is best situated
    to know whether or not his/her plugin will AutoInstall properly.

  * A line such as >
	884 1 :AutoInstall: AutoAlign.vim
<   in your GetLatestVimScripts.dat file constitutes your permission
    to getscript to do AutoInstall.  AutoInstall requires both your
    and the plugin author's permission.  See |GetLatestVimScripts_dat|.


						*GetLatestVimScripts_dat*
As an example of a <GetLatestVimScripts.dat> file:
>
    ScriptID SourceID Filename
    --------------------------
    294 1 :AutoInstall: Align.vim
    120 2 Decho.vim
     40 3 DrawIt.tar.gz
    451 4 EasyAccents.vim
    195 5 engspchk.vim
    642 6 GetLatestVimScripts.vim
    489 7 Manpageview.vim
<
Note: the first two lines are required, but essentially act as comments.


==============================================================================
5. GetLatestVimScripts Friendly Plugins	*getscript-plugins* *glvs-plugins*

		(this section is for plugin authors)~

If a plugin author includes the following comment anywhere in their plugin,
GetLatestVimScripts will find it and use it to automatically build the user's
GetLatestVimScripts.dat files:
>
	                         src_id
	                            v
	" GetLatestVimScripts: ### ### yourscriptname
	                        ^
	                    scriptid
<
As an author, you should include such a line in to refer to your own script
plus any additional lines describing any plugin dependencies it may have.
Same format, of course!

If your command is auto-installable (see |glvs-autoinstall|), and most scripts
are, then you may include :AutoInstall: just before "yourscriptname":
>
	                         src_id
	                            v
	" GetLatestVimScripts: ### ### :AutoInstall: yourscriptname
	                        ^
	                    scriptid
<
NOTE: The :AutoInstall: feature requires both the plugin author's and~
      the user's permission to operate!~

GetLatestVimScripts commands for those scripts are then appended, if not
already present, to the user's GetLatest/GetLatestVimScripts.dat file.  It is
a relatively painless way to automate the acquisition of any scripts your
plugins depend upon.

Now, as an author, you probably don't want GetLatestVimScripts to download
your own scripts atop your own copy, thereby overwriting your not-yet-released
hard work.  GetLatestVimScripts provides a solution for this:  put
>
	0 0 yourscriptname
<
into your <GetLatestVimScripts.dat> file and GetLatestVimScripts will skip
examining the "yourscriptname" scripts for those GetLatestVimScripts comment
lines.  As a result, those lines won't be inadvertently installed into your
<GetLatestVimScripts.dat> file and subsequently used to download your own
scripts.  This is especially important to do if you've included the
:AutoInstall: option.

Be certain to use the same "yourscriptname" in the "0 0 yourscriptname" line
as you've used in your GetLatestVimScripts comment!


==============================================================================
6. GetLatestVimScripts AutoInstall			*getscript-autoinstall*
							*glvs-autoinstall*

GetLatestVimScripts now supports "AutoInstall".  Not all scripts are
supportive of auto-install, as they may have special things you need to do to
install them (please refer to the script's "install" directions).  On the
other hand, most scripts will be auto-installable.

To let GetLatestVimScripts do an autoinstall, the data file's comment field
should begin with (surrounding blanks are ignored): >

	:AutoInstall:
<
Both colons are needed, and it should begin the comment (yourscriptname)
field.

One may prevent any autoinstalling by putting the following line in your
<.vimrc>: >

	let g:GetLatestVimScripts_allowautoinstall= 0
<
With :AutoInstall: enabled, as it is by default, files which end with

	---.tar.bz2  : decompressed & untarred in .vim/ directory
	---.vba.bz2  : decompressed in .vim/ directory, then vimball handles it
	---.vmb.bz2  : idem
	---.vim.bz2  : decompressed & moved into .vim/plugin directory
	---.tar.gz   : decompressed & untarred in .vim/ directory
	---.vba.gz   : decompressed in .vim/ directory, then vimball handles it
	---.vmb.gz   : idem
	---.vim.gz   : decompressed & moved into .vim/plugin directory
	---.vba      : moved to .vim/ directory, then vimball handles it
	---.vmb      : idem
	---.vim      : moved to .vim/plugin directory
	---.zip      : unzipped in .vim/ directory

and which merely need to have their components placed by the untar/gunzip or
move-to-plugin-directory process should be auto-installable.  Vimballs, of
course, should always be auto-installable.

When is a script not auto-installable?  Let me give an example:

	.vim/after/syntax/blockhl.vim

The <blockhl.vim> script provides block highlighting for C/C++ programs; it is
available at:

	https://www.vim.org/scripts/script.php?script_id=104

Currently, vim's after/syntax only supports by-filetype scripts (in
blockhl.vim's case, that's after/syntax/c.vim).  Hence, auto-install would
possibly overwrite the current user's after/syntax/c.vim file.

In my own case, I use <aftersyntax.vim> (renamed to after/syntax/c.vim) to
allow a after/syntax/c/ directory:

	https://www.vim.org/scripts/script.php?script_id=1023

The script allows multiple syntax files to exist separately in the
after/syntax/c subdirectory.  I can't bundle aftersyntax.vim in and build an
appropriate tarball for auto-install because of the potential for the
after/syntax/c.vim contained in it to overwrite a user's c.vim.


==============================================================================
7. GetLatestVimScripts Options					*glvs-options*
>
	g:GetLatestVimScripts_wget
<	default= "wget"
		This variable holds the name of the command for obtaining
		scripts.
>
	g:GetLatestVimScripts_options
<	default= "-q -O"
		This variable holds the options to be used with the
		g:GetLatestVimScripts_wget command.
>
	g:GetLatestVimScripts_allowautoinstall
<	default= 1
		This variable indicates whether GetLatestVimScripts is allowed
		to attempt to automatically install scripts.  Furthermore, the
		plugin author has to have explicitly indicated that his/her
		plugin is automatically installable (via the :AutoInstall:
		keyword in the GetLatestVimScripts comment line).
>
	g:GetLatestVimScripts_autoinstalldir
<	default= $HOME/.vim     (linux)
	default= $HOME/vimfiles (windows)
		Override where :AutoInstall: scripts will be installed.
		Doesn't override vimball installation.
>
	g:GetLatestVimScripts_scriptaddr
<	default='https://www.vim.org/scripts/script.php?script_id='
		Override this if your system needs
	  ...  ='http://vim.sourceforge.net/script.php?script_id='
>
	g:GetLatestVimScripts_downloadaddr
<	default='https://www.vim.org/scripts/download_script.php?src_id='
		Override this if your system needs
	  ...  ='http://vim.sourceforge.net/scripts/download_script.php?src_id='
>
	g:GetLatestVimScripts_bunzip2
<	default= bunzip2
		This variable holds the name of the command to decompress .bz2
		files
>
	g:GetLatestVimScripts_bunzip3
<	default= bunzip3
		This variable holds the name of the command to decompress .bz3
		files
>
	g:GetLatestVimScripts_gunzip
<	default= gunzip
		This variable holds the name of the command to decompress .gz
		files
>
	g:GetLatestVimScripts_unxz
<	default= unxz
		This variable holds the name of the command to decompress .xz
		files
>
	g:GetLatestVimScripts_unzip
<	default= unzip
		This variable holds the name of the command to decompress .zip
		files

Note: The variables associated with decompression commands help workaround
      crossplatform issues. For example, on Windows is possible to delegate
      this calls into `wsl` by doing: >

	let g:GetLatestVimScripts_bunzip2= "wsl -e bunzip2"
	let g:GetLatestVimScripts_gunzip= "wsl -e gunzip"
	let g:GetLatestVimScripts_unxz= "wsl -e unxz"
	let g:GetLatestVimScripts_unzip= "wsl -e unzip"
<
==============================================================================
8. GetLatestVimScripts Algorithm		*glvs-algorithm* *glvs-alg*

The Vim sourceforge page dynamically creates a page by keying off of the
so-called script-id.  Within the webpage of

	https://www.vim.org/scripts/script.php?script_id=40

is a line specifying the latest source-id (src_id).  The source identifier
numbers are always increasing, hence if the src_id is greater than the one
recorded for the script in GetLatestVimScripts then it's time to download a
newer copy of that script.

GetLatestVimScripts will then download the script and update its internal
database of script ids, source ids, and scriptnames.

The AutoInstall process will:

	Move the file from GetLatest/ to the following directory
		Unix   : $HOME/.vim
		Windows: $HOME\vimfiles
	if the downloaded file ends with ".bz2"
		bunzip2 it
	else if the downloaded file ends with ".bz3"
		bunzip3 it
	else if the downloaded file ends with ".gz"
		gunzip it
	if the resulting file ends with ".zip"
		unzip it
	else if the resulting file ends with ".tar"
		tar -oxvf it
	else if the resulting file ends with ".vim"
		move it to the plugin subdirectory


==============================================================================
9. GetLatestVimScripts History		*getscript-history* *glvs-hist* {{{1

v38 Feb 25, 2025 : * handles additional decompression option for .bzip3 files.
v37 Sep 23, 2024 : * Support for the new vimball's .vmb extension (old .vba
		     extension conflicted with visual basic).
		   * Support for |glvs-autoinstall| in ftplugins and packages.
		   * Allow platform driven customization of decompressing
		     commands.
v36 Apr 22, 2013 : * (glts) suggested use of plugin/**/*.vim instead of
		     plugin/*.vim in globpath() call.
		   * (Andy Wokula) got warning message when setting
		     g:loaded_getscriptPlugin
v35 Apr 07, 2012 : * (MengHuan Yu) pointed out that the script URL has
		     changed (somewhat).  However, it doesn't work, and
		     the original one does (under Linux). I'll make it
		     yet-another-option.
v34 Jun 23, 2011 : * handles additional decompression options for tarballs
                     (tgz taz tbz txz)
v33 May 31, 2011 : * using fnameescape() instead of escape()
		   * *.xz support
v32 Jun 19, 2010 : * (Jan Steffens) added support for xz compression
v31 Jun 29, 2008 : * (Bill McCarthy) fixed having hls enabled with getscript
		   * (David Schaefer) the acd option interferes with vimballs
		     Solution: bypass the acd option
v30 Jun 13, 2008 : * GLVS now checks for existence of fnameescape() and will
		     issue an error message if it is not supported
v29 Jan 07, 2008 : * Bram M pointed out that cpo is a global option and that
                     getscriptPlugin.vim was setting it but not restoring it.
v28 Jan 02, 2008 : * improved shell quoting character handling, cygwin
		     interface, register-a bypass
    Oct 29, 2007   * Bill McCarthy suggested a change to getscript that avoids
                     creating pop-up windows
v24 Apr 16, 2007 : * removed save&restore of the fo option during script
                     loading
v23 Nov 03, 2006 : * ignores comments (#...)
                   * handles vimballs
v22 Oct 13, 2006 : * supports automatic use of curl if wget is not
                     available
v21 May 01, 2006 : * now takes advantage of autoloading.
v20 Dec 23, 2005 : * Eric Haarbauer found&fixed a bug with unzip use;
                     unzip needs the -o flag to overwrite.
v19 Nov 28, 2005 : * v18's GetLatestVimScript line accessed the wrong
                     script! Fixed.
v18 Mar 21, 2005 : * bugfix to automatic database construction
                   * bugfix - nowrapscan caused an error
                     (tnx to David Green for the fix)
    Apr 01, 2005   * if shell is bash, "mv" instead of "ren" used in
                     :AutoInstall:s, even though its o/s is windows
    Apr 01, 2005   * when downloading errors occurred, GLVS was
                     terminating early.  It now just goes on to trying
                     the next script (after trying three times to
                     download a script description page)
    Apr 20, 2005   * bugfix - when a failure to download occurred,
                     GetLatestVimScripts would stop early and claim that
                     everything was current.  Fixed.
v17 Aug 25, 2004 : * g:GetLatestVimScripts_allowautoinstall, which
                     defaults to 1, can be used to prevent all
                     :AutoInstall:
v16 Aug 25, 2004 : * made execution of bunzip2/gunzip/tar/zip silent
                   * fixed bug with :AutoInstall: use of helptags
v15 Aug 24, 2004 : * bugfix: the "0 0 comment" download prevention wasn't
                     always preventing downloads (just usually).  Fixed.
v14 Aug 24, 2004 : * bugfix -- helptags was using dotvim, rather than
                     s:dotvim.  Fixed.
v13 Aug 23, 2004 : * will skip downloading a file if its scriptid or srcid
                     is zero.  Useful for script authors; that way their
                     own GetLatestVimScripts activity won't overwrite
                     their scripts.
v12 Aug 23, 2004 : * bugfix - a "return" got left in the distribution that
                     was intended only for testing.  Removed, now works.
                   * :AutoInstall: implemented
v11 Aug 20, 2004 : * GetLatestVimScripts is now a plugin:
                   * :GetLatestVimScripts command
                   * (runtimepath)/GetLatest/GetLatestVimScripts.dat
                     now holds scripts that need updating
v10 Apr 19, 2004 : * moved history from script to doc
v9  Jan 23, 2004 :   windows (win32/win16/win95) will use
                     double quotes ("") whereas other systems will use
                     single quotes ('') around the urls in calls via wget
v8  Dec 01, 2003 :   makes three tries at downloading
v7  Sep 02, 2003 :   added error messages if "Click on..." or "src_id="
                     not found in downloaded webpage
                     Uses t_ti, t_te, and rs to make progress visible
v6  Aug 06, 2003 :   final status messages now display summary of work
                     ( "Downloaded someqty scripts" or
                       "Everything was current")
                     Now GetLatestVimScripts is careful about downloading
                     GetLatestVimScripts.vim itself!
                     (goes to <NEW_GetLatestVimScripts.vim>)
v5  Aug 04, 2003 :   missing an endif near bottom
v4  Jun 17, 2003 :   redraw! just before each "considering" message
v3  May 27, 2003 :   Protects downloaded files from errant shell
                     expansions with single quotes: '...'
v2  May 14, 2003 :   extracts name of item to be obtained from the
                     script file.  Uses it instead of comment field
                     for output filename; comment is used in the
                     "considering..." line and is now just a comment!
                   * Fixed a bug: a string-of-numbers is not the
                     same as a number, so I added zero to them
                     and they became numbers.  Fixes comparison.

==============================================================================
vim:tw=78:ts=8:noet:ft=help:fdm=marker
