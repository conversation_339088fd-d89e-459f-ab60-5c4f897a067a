*quickfix.txt*  For Vim version 9.1.  Last change: 2025 Apr 06


		  VIM REFERENCE MANUAL    by <PERSON>


This subject is introduced in section |30.1| of the user manual.

1. Using QuickFix commands		|quickfix|
2. The error window			|quickfix-window|
3. Using more than one list of errors	|quickfix-error-lists|
4. Using :make				|:make_makeprg|
5. Using :grep				|grep|
6. Selecting a compiler			|compiler-select|
7. The error format			|error-file-format|
8. The directory stack			|quickfix-directory-stack|
9. Specific error file formats		|errorformats|
10. Customizing the quickfix window	|quickfix-window-function|

The quickfix commands are not available when the |+quickfix| feature was
disabled at compile time.

=============================================================================
1. Using QuickFix commands			*quickfix* *Quickfix* *E42*

Vim has a special mode to speedup the edit-compile-edit cycle.  This is
inspired by the quickfix option of the Manx's Aztec C compiler on the Amiga.
The idea is to save the error messages from the compiler in a file and use Vim
to jump to the errors one by one.  You can examine each problem and fix it,
without having to remember all the error messages.

In Vim the quickfix commands are used more generally to find a list of
positions in files.  For example, |:vimgrep| finds pattern matches.  You can
use the positions in a script with the |getqflist()| function.  Thus you can
do a lot more than the edit/compile/fix cycle!

If you have the error messages in a file you can start Vim with: >
	vim -q filename

From inside Vim an easy way to run a command and handle the output is with the
|:make| command (see below).

The 'errorformat' option should be set to match the error messages from your
compiler (see |errorformat| below).

					*quickfix-stack* *quickfix-ID*
Each quickfix list has a unique identifier called the quickfix ID and this
number will not change within a Vim session. The |getqflist()| function can be
used to get the identifier assigned to a list. There is also a quickfix list
number which may change whenever more than 'chistory' lists are added to a
quickfix stack.

						*location-list* *E776*
A location list is a window-local quickfix list. You get one after commands
like `:lvimgrep`, `:lgrep`, `:lhelpgrep`, `:lmake`, etc., which create a
location list instead of a quickfix list as the corresponding `:vimgrep`,
`:grep`, `:helpgrep`, `:make` do.
						*location-list-file-window*
A location list is associated with a window and each window can have a
separate location list.  A location list can be associated with only one
window.  The location list is independent of the quickfix list.

When a window with a location list is split, the new window gets a copy of the
location list.  When there are no longer any references to a location list,
the location list is destroyed.

						*quickfix-changedtick*
Every quickfix and location list has a read-only changedtick variable that
tracks the total number of changes made to the list.  Every time the quickfix
list is modified, this count is incremented. This can be used to perform an
action only when the list has changed.  The |getqflist()| and |getloclist()|
functions can be used to query the current value of changedtick.  You cannot
change the changedtick variable.

The following quickfix commands can be used.  The location list commands are
similar to the quickfix commands, replacing the 'c' prefix in the quickfix
command with 'l'.

							*E924*
If the current window was closed by an |autocommand| while processing a
location list command, it will be aborted.

							*E925* *E926*
If the current quickfix or location list was changed by an |autocommand| while
processing a quickfix or location list command, it will be aborted.

							*:cc*
:cc[!] [nr]		Display error [nr].  If [nr] is omitted, the same
:[nr]cc[!]		error is displayed again.  Without [!] this doesn't
			work when jumping to another buffer, the current buffer
			has been changed, there is the only window for the
			buffer and both 'hidden' and 'autowrite' are off.
			When jumping to another buffer with [!] any changes to
			the current buffer are lost, unless 'hidden' is set or
			there is another window for this buffer.
			The 'switchbuf' settings are respected when jumping
			to a buffer.
			When used in the quickfix window the line number can
			be used, including "." for the current line and "$"
			for the last line.

							*:ll*
:ll[!] [nr]		Same as ":cc", except the location list for the
:[nr]ll[!]		current window is used instead of the quickfix list.

						*:cn* *:cne* *:cnext* *E553*
:[count]cn[ext][!]	Display the [count] next error in the list that
			includes a file name.  If there are no file names at
			all, go to the [count] next error.  See |:cc| for
			[!] and 'switchbuf'.

							*:lne* *:lnext*
:[count]lne[xt][!]	Same as ":cnext", except the location list for the
			current window is used instead of the quickfix list.

:[count]cN[ext][!]		*:cp* *:cprevious*  *:cprev* *:cN* *:cNext*
:[count]cp[revious][!]	Display the [count] previous error in the list that
			includes a file name.  If there are no file names at
			all, go to the [count] previous error.  See |:cc| for
			[!] and 'switchbuf'.


:[count]lN[ext][!]		*:lp* *:lprevious* *:lprev* *:lN* *:lNext*
:[count]lp[revious][!]	Same as ":cNext" and ":cprevious", except the location
			list for the current window is used instead of the
			quickfix list.

							*:cabo* *:cabove*
:[count]cabo[ve]	Go to the [count] error above the current line in the
			current buffer.  If [count] is omitted, then 1 is
			used.  If there are no errors, then an error message
			is displayed.  Assumes that the entries in a quickfix
			list are sorted by their buffer number and line
			number. If there are multiple errors on the same line,
			then only the first entry is used.  If [count] exceeds
			the number of entries above the current line, then the
			first error in the file is selected.

							*:lab* *:labove*
:[count]lab[ove]	Same as ":cabove", except the location list for the
			current window is used instead of the quickfix list.

							*:cbel* *:cbelow*
:[count]cbel[ow]	Go to the [count] error below the current line in the
			current buffer.  If [count] is omitted, then 1 is
			used.  If there are no errors, then an error message
			is displayed.  Assumes that the entries in a quickfix
			list are sorted by their buffer number and line
			number.  If there are multiple errors on the same
			line, then only the first entry is used.  If [count]
			exceeds the number of entries below the current line,
			then the last error in the file is selected.

							*:lbel* *:lbelow*
:[count]lbel[ow]	Same as ":cbelow", except the location list for the
			current window is used instead of the quickfix list.

							*:cbe* *:cbefore*
:[count]cbe[fore]	Go to the [count] error before the current cursor
			position in the current buffer.  If [count] is
			omitted, then 1 is used.  If there are no errors, then
			an error message is displayed.  Assumes that the
			entries in a quickfix list are sorted by their buffer,
			line and column numbers.  If [count] exceeds the
			number of entries before the current position, then
			the first error in the file is selected.

							*:lbe* *:lbefore*
:[count]lbe[fore]	Same as ":cbefore", except the location list for the
			current window is used instead of the quickfix list.

							*:caf* *:cafter*
:[count]caf[ter]	Go to the [count] error after the current cursor
			position in the current buffer.  If [count] is
			omitted, then 1 is used.  If there are no errors, then
			an error message is displayed.  Assumes that the
			entries in a quickfix list are sorted by their buffer,
			line and column numbers.  If [count] exceeds the
			number of entries after the current position, then
			the last error in the file is selected.

							*:laf* *:lafter*
:[count]laf[ter]	Same as ":cafter", except the location list for the
			current window is used instead of the quickfix list.

							*:cnf* *:cnfile*
:[count]cnf[ile][!]	Display the first error in the [count] next file in
			the list that includes a file name.  If there are no
			file names at all or if there is no next file, go to
			the [count] next error.  See |:cc| for [!] and
			'switchbuf'.

							*:lnf* *:lnfile*
:[count]lnf[ile][!]	Same as ":cnfile", except the location list for the
			current window is used instead of the quickfix list.

:[count]cNf[ile][!]			*:cpf* *:cpfile* *:cNf* *:cNfile*
:[count]cpf[ile][!]	Display the last error in the [count] previous file in
			the list that includes a file name.  If there are no
			file names at all or if there is no next file, go to
			the [count] previous error.  See |:cc| for [!] and
			'switchbuf'.


:[count]lNf[ile][!]			*:lpf* *:lpfile* *:lNf* *:lNfile*
:[count]lpf[ile][!]	Same as ":cNfile" and ":cpfile", except the location
			list for the current window is used instead of the
			quickfix list.

							*:crewind* *:cr*
:cr[ewind][!] [nr]	Display error [nr].  If [nr] is omitted, the FIRST
			error is displayed.  See |:cc|.

							*:lrewind* *:lr*
:lr[ewind][!] [nr]	Same as ":crewind", except the location list for the
			current window is used instead of the quickfix list.

							*:cfirst* *:cfir*
:cfir[st][!] [nr]	Same as ":crewind".

							*:lfirst* *:lfir*
:lfir[st][!] [nr]	Same as ":lrewind".

							*:clast* *:cla*
:cla[st][!] [nr]	Display error [nr].  If [nr] is omitted, the LAST
			error is displayed.  See |:cc|.

							*:llast* *:lla*
:lla[st][!] [nr]	Same as ":clast", except the location list for the
			current window is used instead of the quickfix list.

							*:cq* *:cquit*
:cq[uit][!]
:{N}cq[uit][!]
:cq[uit][!] {N}		Quit Vim with error code {N}.  {N} defaults to one.
			Useful when Vim is called from another program:
			e.g., a compiler will not compile the same file again,
			`git commit` will abort the committing process, `fc`
			(built-in for shells like bash and zsh) will not
			execute the command, etc.
			{N} can also be zero, in which case Vim exits
			normally.
			WARNING: All changes in files are lost!  Also when the
			[!] is not used.  It works like ":qall!" |:qall|,
			except that Vim returns a non-zero exit code.

							*:cf* *:cfi* *:cfile*
:cf[ile][!] [errorfile]	Read the error file and jump to the first error.
			This is done automatically when Vim is started with
			the -q option.  You can use this command when you
			keep Vim running while compiling.  If you give the
			name of the errorfile, the 'errorfile' option will
			be set to [errorfile].  See |:cc| for [!].
			If the encoding of the error file differs from the
			'encoding' option, you can use the 'makeencoding'
			option to specify the encoding.

							*:lf* *:lfi* *:lfile*
:lf[ile][!] [errorfile]	Same as ":cfile", except the location list for the
			current window is used instead of the quickfix list.
			You can not use the -q command-line option to set
			the location list.


:cg[etfile] [errorfile]					*:cg* *:cgetfile*
			Read the error file.  Just like ":cfile" but don't
			jump to the first error.
			If the encoding of the error file differs from the
			'encoding' option, you can use the 'makeencoding'
			option to specify the encoding.


:lg[etfile] [errorfile]					*:lg* *:lge* *:lgetfile*
			Same as ":cgetfile", except the location list for the
			current window is used instead of the quickfix list.

							*:caddf* *:caddfile*
:caddf[ile] [errorfile]	Read the error file and add the errors from the
			errorfile to the current quickfix list. If a quickfix
			list is not present, then a new list is created.
			If the encoding of the error file differs from the
			'encoding' option, you can use the 'makeencoding'
			option to specify the encoding.

							*:laddf* *:laddfile*
:laddf[ile] [errorfile]	Same as ":caddfile", except the location list for the
			current window is used instead of the quickfix list.

						*:cb* *:cbuffer* *E681*
:[range]cb[uffer][!] [bufnr]
			Read the error list from the current buffer.
			When [bufnr] is given it must be the number of a
			loaded buffer.  That buffer will then be used instead
			of the current buffer.
			A range can be specified for the lines to be used.
			Otherwise all lines in the buffer are used.
			See |:cc| for [!].

						*:lb* *:lbuffer*
:[range]lb[uffer][!] [bufnr]
			Same as ":cbuffer", except the location list for the
			current window is used instead of the quickfix list.

						*:cgetb* *:cgetbuffer*
:[range]cgetb[uffer] [bufnr]
			Read the error list from the current buffer.  Just
			like ":cbuffer" but don't jump to the first error.

						*:lgetb* *:lgetbuffer*
:[range]lgetb[uffer] [bufnr]
			Same as ":cgetbuffer", except the location list for
			the current window is used instead of the quickfix
			list.

						*:cad* *:cadd* *:caddbuffer*
:[range]cad[dbuffer] [bufnr]
			Read the error list from the current buffer and add
			the errors to the current quickfix list.  If a
			quickfix list is not present, then a new list is
			created. Otherwise, same as ":cbuffer".

							*:laddb* *:laddbuffer*
:[range]laddb[uffer] [bufnr]
			Same as ":caddbuffer", except the location list for
			the current window is used instead of the quickfix
			list.

							*:cex* *:cexpr* *E777*
:cex[pr][!] {expr}	Create a quickfix list using the result of {expr} and
			jump to the first error.
			If {expr} is a String, then each newline terminated
			line in the String is processed using the global value
			of 'errorformat' and the result is added to the
			quickfix list.
			If {expr} is a List, then each String item in the list
			is processed and added to the quickfix list.  Non
			String items in the List are ignored.
			See |:cc| for [!].
			Examples: >
				:cexpr system('grep -n xyz *')
				:cexpr getline(1, '$')
<
							*:lex* *:lexpr*
:lex[pr][!] {expr}	Same as |:cexpr|, except the location list for the
			current window is used instead of the quickfix list.

							*:cgete* *:cgetexpr*
:cgete[xpr] {expr}	Create a quickfix list using the result of {expr}.
			Just like |:cexpr|, but don't jump to the first error.

							*:lgete* *:lgetexpr*
:lgete[xpr] {expr}	Same as |:cgetexpr|, except the location list for the
			current window is used instead of the quickfix list.

							*:cadde* *:caddexpr*
:cadde[xpr] {expr}	Evaluate {expr} and add the resulting lines to the
			current quickfix list. If a quickfix list is not
			present, then a new list is created. The current
			cursor position will not be changed. See |:cexpr| for
			more information.
			Example: >
    :g/mypattern/caddexpr expand("%") .. ":" .. line(".") ..  ":" .. getline(".")
<
						*:lad* *:ladd* *:laddexpr*
:lad[dexpr] {expr}	Same as ":caddexpr", except the location list for the
			current window is used instead of the quickfix list.

							*:cl* *:clist*
:cl[ist] [from] [, [to]]
			List all errors that are valid |quickfix-valid|.
			If numbers [from] and/or [to] are given, the respective
			range of errors is listed.  A negative number counts
			from the last error backwards, -1 being the last error.
			The |:filter| command can be used to display only the
			quickfix entries matching a supplied pattern. The
			pattern is matched against the filename, module name,
			pattern and text of the entry.

:cl[ist] +{count}	List the current and next {count} valid errors.  This
			is similar to ":clist from from+count", where "from"
			is the current error position.

:cl[ist]! [from] [, [to]]
			List all errors.

:cl[ist]! +{count}	List the current and next {count} error lines.  This
			is useful to see unrecognized lines after the current
			one.  For example, if ":clist" shows:
	8384 testje.java:252: error: cannot find symbol ~
			Then using ":cl! +3" shows the reason:
	8384 testje.java:252: error: cannot find symbol ~
	8385:   ZexitCode = Fmainx(); ~
	8386:               ^ ~
	8387:   symbol:   method Fmainx() ~

:lli[st] [from] [, [to]]				*:lli* *:llist*
			Same as ":clist", except the location list for the
			current window is used instead of the quickfix list.

:lli[st]! [from] [, [to]]
			List all the entries in the location list for the
			current window.

If you insert or delete lines, mostly the correct error location is still
found because hidden marks are used.  Sometimes, when the mark has been
deleted for some reason, the message "line changed" is shown to warn you that
the error location may not be correct.  If you quit Vim and start again the
marks are lost and the error locations may not be correct anymore.

Two autocommands are available for running commands before and after a
quickfix command (':make', ':grep' and so on) is executed. See
|QuickFixCmdPre| and |QuickFixCmdPost| for details.

						*QuickFixCmdPost-example*
When 'encoding' differs from the locale, the error messages may have a
different encoding from what Vim is using.  To convert the messages you can
use this code: >
	function QfMakeConv()
	   let qflist = getqflist()
	   for i in qflist
	      let i.text = iconv(i.text, "cp936", "utf-8")
	   endfor
	   call setqflist(qflist)
	endfunction

	au QuickfixCmdPost make call QfMakeConv()
Another option is using 'makeencoding'.

							*quickfix-title*
Every quickfix and location list has a title. By default the title is set to
the command that created the list. The |getqflist()| and |getloclist()|
functions can be used to get the title of a quickfix and a location list
respectively. The |setqflist()| and |setloclist()| functions can be used to
modify the title of a quickfix and location list respectively. Examples: >
	call setqflist([], 'a', {'title' : 'Cmd output'})
	echo getqflist({'title' : 1})
	call setloclist(3, [], 'a', {'title' : 'Cmd output'})
	echo getloclist(3, {'title' : 1})
<
							*quickfix-index*
When you jump to a quickfix/location list entry using any of the quickfix
commands (e.g. |:cc|, |:cnext|, |:cprev|, etc.), that entry becomes the
currently selected entry. The index of the currently selected entry in a
quickfix/location list can be obtained using the getqflist()/getloclist()
functions. Examples: >
	echo getqflist({'idx' : 0}).idx
	echo getqflist({'id' : qfid, 'idx' : 0}).idx
	echo getloclist(2, {'idx' : 0}).idx
<
For a new quickfix list, the first entry is selected and the index is 1.  Any
entry in any quickfix/location list can be set as the currently selected entry
using the setqflist() function. Examples: >
	call setqflist([], 'a', {'idx' : 12})
	call setqflist([], 'a', {'id' : qfid, 'idx' : 7})
	call setloclist(1, [], 'a', {'idx' : 7})
<
							*quickfix-size*
You can get the number of entries (size) in a quickfix and a location list
using the |getqflist()| and |getloclist()| functions respectively. Examples: >
	echo getqflist({'size' : 1})
	echo getloclist(5, {'size' : 1})
<
							*quickfix-context*
Any Vim type can be associated as a context with a quickfix or location list.
The |setqflist()| and the |setloclist()| functions can be used to associate a
context with a quickfix and a location list respectively. The |getqflist()|
and the |getloclist()| functions can be used to retrieve the context of a
quickfix and a location list respectively. This is useful for a Vim plugin
dealing with multiple quickfix/location lists.
Examples: >

	let somectx = {'name' : 'Vim', 'type' : 'Editor'}
	call setqflist([], 'a', {'context' : somectx})
	echo getqflist({'context' : 1})

	let newctx = ['red', 'green', 'blue']
	call setloclist(2, [], 'a', {'id' : qfid, 'context' : newctx})
	echo getloclist(2, {'id' : qfid, 'context' : 1})
<
							*quickfix-parse*
You can parse a list of lines using 'errorformat' without creating or
modifying a quickfix list using the |getqflist()| function. Examples: >
	echo getqflist({'lines' : ["F1:10:Line10", "F2:20:Line20"]})
	echo getqflist({'lines' : systemlist('grep -Hn quickfix *')})
This returns a dictionary where the "items" key contains the list of quickfix
entries parsed from lines. The following shows how to use a custom
'errorformat' to parse the lines without modifying the 'errorformat' option: >
	echo getqflist({'efm' : '%f#%l#%m', 'lines' : ['F1#10#Line']})
<

EXECUTE A COMMAND IN ALL THE BUFFERS IN QUICKFIX OR LOCATION LIST:
							*:cdo*
:cdo[!] {cmd}		Execute {cmd} in each valid entry in the quickfix list.
			It works like doing this: >
				:cfirst
				:{cmd}
				:cnext
				:{cmd}
				etc.
<			When the current file can't be |abandon|ed and the [!]
			is not present, the command fails.
			When going to the next entry fails execution stops.
			The last buffer (or where an error occurred) becomes
			the current buffer.
			{cmd} can contain '|' to concatenate several commands.

			Only valid entries in the quickfix list are used.
			A range can be used to select entries, e.g.: >
				:10,$cdo cmd
<			To skip entries 1 to 9.

			Note: While this command is executing, the Syntax
			autocommand event is disabled by adding it to
			'eventignore'.  This considerably speeds up editing
			each buffer.
			Also see |:bufdo|, |:tabdo|, |:argdo|, |:windo|,
			|:ldo|, |:cfdo| and |:lfdo|.

							*:cfdo*
:cfdo[!] {cmd}		Execute {cmd} in each file in the quickfix list.
			It works like doing this: >
				:cfirst
				:{cmd}
				:cnfile
				:{cmd}
				etc.
<			Otherwise it works the same as `:cdo`.

							*:ldo*
:ld[o][!] {cmd}		Execute {cmd} in each valid entry in the location list
			for the current window.
			It works like doing this: >
				:lfirst
				:{cmd}
				:lnext
				:{cmd}
				etc.
<			Only valid entries in the location list are used.
			Otherwise it works the same as `:cdo`.

							*:lfdo*
:lfdo[!] {cmd}		Execute {cmd} in each file in the location list for
			the current window.
			It works like doing this: >
				:lfirst
				:{cmd}
				:lnfile
				:{cmd}
				etc.
<			Otherwise it works the same as `:ldo`.

FILTERING A QUICKFIX OR LOCATION LIST:
		    *cfilter-plugin* *:Cfilter* *:Lfilter* *package-cfilter*
If you have too many entries in a quickfix list, you can use the cfilter
plugin to reduce the number of entries.  Load the plugin with: >vim

    packadd cfilter

Then you can use the following commands to filter a quickfix/location list: >

    :Cfilter[!] /{pat}/
    :Lfilter[!] /{pat}/

The |:Cfilter| command creates a new quickfix list from the entries matching
{pat} in the current quickfix list. {pat} is a Vim |regular-expression|
pattern. Both the file name and the text of the entries are matched against
{pat}. If the optional ! is supplied, then the entries not matching {pat} are
used. The pattern can be optionally enclosed using one of the following
characters: ', ", /. If the pattern is empty, then the last used search
pattern is used.

The |:Lfilter| command does the same as |:Cfilter| but operates on the current
location list.

The current quickfix/location list is not modified by these commands, so you
can go back to the unfiltered list using the |:colder|/|:lolder| command.

=============================================================================
2. The error window					*quickfix-window*

					    *:cope* *:copen* *w:quickfix_title*
:cope[n] [height]	Open a window to show the current list of errors.

			When [height] is given, the window becomes that high
			(if there is room).  When [height] is omitted the
			window is made ten lines high.

			If there already is a quickfix window, it will be made
			the current window.  It is not possible to open a
			second quickfix window.  If [height] is given the
			existing window will be resized to it.

							*quickfix-buffer*
			The window will contain a special buffer, with
			'buftype' equal to "quickfix".  Don't change this!
			The window will have the w:quickfix_title variable set
			which will indicate the command that produced the
			quickfix list. This can be used to compose a custom
			status line if the value of 'statusline' is adjusted
			properly. Whenever this buffer is modified by a
			quickfix command or function, the |b:changedtick|
			variable is incremented.  You can get the number of
			this buffer using the getqflist() and getloclist()
			functions by passing the "qfbufnr" item. For a
			location list, this buffer is wiped out when the
			location list is removed.

							*:lop* *:lopen*
:lop[en] [height]	Open a window to show the location list for the
			current window. Works only when the location list for
			the current window is present.  You can have more than
			one location window opened at a time.  Otherwise, it
			acts the same as ":copen".

							*:ccl* *:cclose*
:ccl[ose]		Close the quickfix window.

							*:lcl* *:lclose*
:lcl[ose]		Close the window showing the location list for the
			current window.

							*:cw* *:cwindow*
:cw[indow] [height]	Open the quickfix window when there are recognized
			errors.  If the window is already open and there are
			no recognized errors, close the window.

							*:lw* *:lwindow*
:lw[indow] [height]	Same as ":cwindow", except use the window showing the
			location list for the current window.

							*:cbo* *:cbottom*
:cbo[ttom]		Put the cursor in the last line of the quickfix window
			and scroll to make it visible.  This is useful for
			when errors are added by an asynchronous callback.
			Only call it once in a while if there are many
			updates to avoid a lot of redrawing.

							*:lbo* *:lbottom*
:lbo[ttom]		Same as ":cbottom", except use the window showing the
			location list for the current window.

Normally the quickfix window is at the bottom of the screen.  If there are
vertical splits, it's at the bottom of the rightmost column of windows.  To
make it always occupy the full width: >
	:botright cwindow
You can move the window around with |window-moving| commands.
For example, to move it to the top: CTRL-W K
The 'winfixheight' option will be set, which means that the window will mostly
keep its height, ignoring 'winheight' and 'equalalways'.  You can change the
height manually (e.g., by dragging the status line above it with the mouse).

In the quickfix window, each line is one error.  The line number is equal to
the error number.  The current entry is highlighted with the QuickFixLine
highlighting.  You can change it to your liking, e.g.: >
	:hi QuickFixLine ctermbg=Yellow guibg=Yellow

You can use ":.cc" to jump to the error under the cursor.
Hitting the <Enter> key or double-clicking the mouse on a line has the same
effect.  The file containing the error is opened in the window above the
quickfix window.  If there already is a window for that file, it is used
instead.  If the buffer in the used window has changed, and the error is in
another file, jumping to the error will fail.  You will first have to make
sure the window contains a buffer which can be abandoned.

When you select a file from the quickfix window, the following steps are used
to find a window to edit the file:

1. If a window displaying the selected file is present in the current tabpage
   (starting with the window before the quickfix window), then that window is
   used.
2. If the above step fails and if 'switchbuf' contains "usetab" and a window
   displaying the selected file is present in any one of the tabpages
   (starting with the first tabpage) then that window is used.
3. If the above step fails then a window in the current tabpage displaying a
   buffer with 'buftype' not set (starting with the window before the quickfix
   window) is used.
4. If the above step fails and if 'switchbuf' contains "uselast", then the
   previously accessed window is used.
5. If the above step fails then the window before the quickfix window is used.
   If there is no previous window, then the window after the quickfix window
   is used.
6. If the above step fails, then a new horizontally split window above the
   quickfix window is used.

					*CTRL-W_<Enter>* *CTRL-W_<CR>*
You can use CTRL-W <Enter> to open a new window and jump to the error there.

When the quickfix window has been filled, two autocommand events are
triggered.  First the 'filetype' option is set to "qf", which triggers the
FileType event (also see |qf.vim|).  Then the BufReadPost event is triggered,
using "quickfix" for the buffer name.  This can be used to perform some action
on the listed errors.  Example: >
	au BufReadPost quickfix  setlocal modifiable
		\ | silent exe 'g/^/s//\=line(".") .. " "/'
		\ | setlocal nomodifiable
This prepends the line number to each line.  Note the use of "\=" in the
substitute string of the ":s" command, which is used to evaluate an
expression.
The BufWinEnter event is also triggered, again using "quickfix" for the buffer
name.

Note: When adding to an existing quickfix list the autocommand are not
triggered.

Note: Making changes in the quickfix window has no effect on the list of
errors.  'modifiable' is off to avoid making changes.  If you delete or insert
lines anyway, the relation between the text and the error number is messed up.
If you really want to do this, you could write the contents of the quickfix
window to a file and use ":cfile" to have it parsed and used as the new error
list.

						*location-list-window*
The location list window displays the entries in a location list.  When you
open a location list window, it is created below the current window and
displays the location list for the current window.  The location list window
is similar to the quickfix window, except that you can have more than one
location list window open at a time. When you use a location list command in
this window, the displayed location list is used.

When you select a file from the location list window, the following steps are
used to find a window to edit the file:

1. If a non-quickfix window associated with the location list is present in
   the current tabpage, then that window is used.
2. If the above step fails and if the file is already opened in another window
   in the current tabpage, then that window is used.
3. If the above step fails and 'switchbuf' contains "usetab" and if the file
   is opened in a window in any one of the tabpages, then that window is used.
4. If the above step fails then a window in the current tabpage showing a
   buffer with 'buftype' not set is used.
5. If the above step fails, then the file is edited in a new window.

In all of the above cases, if the location list for the selected window is not
yet set, then it is set to the location list displayed in the location list
window.

							*quickfix-window-ID*
You can use the |getqflist()| and |getloclist()| functions to obtain the
window ID of the quickfix window and location list window respectively (if
present).  Examples: >
	echo getqflist({'winid' : 1}).winid
	echo getloclist(2, {'winid' : 1}).winid
<
							*getqflist-examples*
The |getqflist()| and |getloclist()| functions can be used to get the various
attributes of a quickfix and location list respectively. Some examples for
using these functions are below:
>
    " get the title of the current quickfix list
    :echo getqflist({'title' : 0}).title

    " get the identifier of the current quickfix list
    :let qfid = getqflist({'id' : 0}).id

    " get the identifier of the fourth quickfix list in the stack
    :let qfid = getqflist({'nr' : 4, 'id' : 0}).id

    " check whether a quickfix list with a specific identifier exists
    :if getqflist({'id' : qfid}).id == qfid

    " get the index of the current quickfix list in the stack
    :let qfnum = getqflist({'nr' : 0}).nr

    " get the items of a quickfix list specified by an identifier
    :echo getqflist({'id' : qfid, 'items' : 0}).items

    " get the number of entries in a quickfix list specified by an id
    :echo getqflist({'id' : qfid, 'size' : 0}).size

    " get the context of the third quickfix list in the stack
    :echo getqflist({'nr' : 3, 'context' : 0}).context

    " get the number of quickfix lists in the stack
    :echo getqflist({'nr' : '$'}).nr

    " get the number of times the current quickfix list is changed
    :echo getqflist({'changedtick' : 0}).changedtick

    " get the current entry in a quickfix list specified by an identifier
    :echo getqflist({'id' : qfid, 'idx' : 0}).idx

    " get all the quickfix list attributes using an identifier
    :echo getqflist({'id' : qfid, 'all' : 0})

    " parse text from a List of lines and return a quickfix list
    :let myList = ["a.java:10:L10", "b.java:20:L20"]
    :echo getqflist({'lines' : myList}).items

    " parse text using a custom 'efm' and return a quickfix list
    :echo getqflist({'lines' : ['a.c#10#Line 10'], 'efm':'%f#%l#%m'}).items

    " get the quickfix list window id
    :echo getqflist({'winid' : 0}).winid

    " get the quickfix list window buffer number
    :echo getqflist({'qfbufnr' : 0}).qfbufnr

    " get the context of the current location list
    :echo getloclist(0, {'context' : 0}).context

    " get the location list window id of the third window
    :echo getloclist(3, {'winid' : 0}).winid

    " get the location list window buffer number of the third window
    :echo getloclist(3, {'qfbufnr' : 0}).qfbufnr

    " get the file window id of a location list window (winnr: 4)
    :echo getloclist(4, {'filewinid' : 0}).filewinid
<
							*setqflist-examples*
The |setqflist()| and |setloclist()| functions can be used to set the various
attributes of a quickfix and location list respectively. Some examples for
using these functions are below:
>
    " create an empty quickfix list with a title and a context
    :let t = 'Search results'
    :let c = {'cmd' : 'grep'}
    :call setqflist([], ' ', {'title' : t, 'context' : c})

    " set the title of the current quickfix list
    :call setqflist([], 'a', {'title' : 'Mytitle'})

    " change the current entry in the list specified by an identifier
    :call setqflist([], 'a', {'id' : qfid, 'idx' : 10})

    " set the context of a quickfix list specified by an identifier
    :call setqflist([], 'a', {'id' : qfid, 'context' : {'val' : 100}})

    " create a new quickfix list from a command output
    :call setqflist([], ' ', {'lines' : systemlist('grep -Hn main *.c')})

    " parse text using a custom efm and add to a particular quickfix list
    :call setqflist([], 'a', {'id' : qfid,
		\ 'lines' : ["a.c#10#L10", "b.c#20#L20"], 'efm':'%f#%l#%m'})

    " add items to the quickfix list specified by an identifier
    :let newItems = [{'filename' : 'a.txt', 'lnum' : 10, 'text' : "Apple"},
		    \ {'filename' : 'b.txt', 'lnum' : 20, 'text' : "Orange"}]
    :call setqflist([], 'a', {'id' : qfid, 'items' : newItems})

    " empty a quickfix list specified by an identifier
    :call setqflist([], 'r', {'id' : qfid, 'items' : []})

    " free all the quickfix lists in the stack
    :call setqflist([], 'f')

    " set the title of the fourth quickfix list
    :call setqflist([], 'a', {'nr' : 4, 'title' : 'SomeTitle'})

    " create a new quickfix list at the end of the stack
    :call setqflist([], ' ', {'nr' : '$',
			\ 'lines' : systemlist('grep -Hn class *.java')})

    " create a new location list from a command output
    :call setloclist(0, [], ' ', {'lines' : systemlist('grep -Hn main *.c')})

    " replace the location list entries for the third window
    :call setloclist(3, [], 'r', {'items' : newItems})
<
=============================================================================
3. Using more than one list of errors			*quickfix-error-lists*

So far it has been assumed that there is only one list of errors.  Actually
there can be multiple used lists that are remembered; see 'chistory' and
'lhistory'.
When starting a new list, the previous ones are automatically kept.  Two
commands can be used to access older error lists.  They set one of the
existing error lists as the current one.

						*:colder* *:col* *E380*
:col[der] [count]	Go to older error list.  When [count] is given, do
			this [count] times.  When already at the oldest error
			list, an error message is given.

						*:lolder* *:lol*
:lol[der] [count]	Same as `:colder`, except use the location list for
			the current window instead of the quickfix list.

						*:cnewer* *:cnew* *E381*
:cnew[er] [count]	Go to newer error list.  When [count] is given, do
			this [count] times.  When already at the newest error
			list, an error message is given.

						*:lnewer* *:lnew*
:lnew[er] [count]	Same as `:cnewer`, except use the location list for
			the current window instead of the quickfix list.

						*:chistory* *:chi*
:[count]chi[story]	Show the list of error lists.  The current list is
			marked with ">".  The output looks like:
			  error list 1 of 3; 43 errors   :make  ~
			> error list 2 of 3; 0 errors    :helpgrep tag  ~
			  error list 3 of 3; 15 errors   :grep ex_help *.c ~

			When [count] is given, then the count'th quickfix
			list is made the current list. Example: >
				" Make the 4th quickfix list current
				:4chistory
<
						*:lhistory* *:lhi*
:[count]lhi[story]	Show the list of location lists, otherwise like
			`:chistory`.

When adding a new error list, it becomes the current list.

When ":colder" has been used and ":make" or ":grep" is used to add a new error
list, one newer list is overwritten.  This is especially useful if you are
browsing with ":grep" |grep|.  If you want to keep the more recent error
lists, use ":cnewer 99" first.

To get the number of lists in the quickfix and location list stack, you can
use the |getqflist()| and |getloclist()| functions respectively with the list
number set to the special value '$'. Examples: >
	echo getqflist({'nr' : '$'}).nr
	echo getloclist(3, {'nr' : '$'}).nr
To get the number of the current list in the stack: >
	echo getqflist({'nr' : 0}).nr
<
=============================================================================
4. Using :make						*:make_makeprg*

							*:mak* *:make*
:mak[e][!] [arguments]	1. All relevant |QuickFixCmdPre| autocommands are
			   executed.
			2. If the 'autowrite' option is on, write any changed
			   buffers
			3. An errorfile name is made from 'makeef'.  If
			   'makeef' doesn't contain "##", and a file with this
			   name already exists, it is deleted.
			4. The program given with the 'makeprg' option is
			   started (default "make") with the optional
			   [arguments] and the output is saved in the
			   errorfile (for Unix it is also echoed on the
			   screen).
			5. The errorfile is read using 'errorformat'.
			6. All relevant |QuickFixCmdPost| autocommands are
			   executed.  See example below.
			7. If [!] is not given the first error is jumped to.
			8. The errorfile is deleted.
			9. You can now move through the errors with commands
			   like |:cnext| and |:cprevious|, see above.
			This command does not accept a comment, any "
			characters are considered part of the arguments.
			If the encoding of the program output differs from the
			'encoding' option, you can use the 'makeencoding'
			option to specify the encoding.

							*:lmak* *:lmake*
:lmak[e][!] [arguments]
			Same as ":make", except the location list for the
			current window is used instead of the quickfix list.

The ":make" command executes the command given with the 'makeprg' option.
This is done by passing the command to the shell given with the 'shell'
option.  This works almost like typing

	":!{makeprg} [arguments] {shellpipe} {errorfile}".

{makeprg} is the string given with the 'makeprg' option.  Any command can be
used, not just "make".  Characters '%' and '#' are expanded as usual on a
command-line.  You can use "%<" to insert the current file name without
extension, or "#<" to insert the alternate file name without extension, for
example: >
   :set makeprg=make\ #<.o

[arguments] is anything that is typed after ":make".
{shellpipe} is the 'shellpipe' option.
{errorfile} is the 'makeef' option, with ## replaced to make it unique.

The placeholder "$*" can be used for the argument list in {makeprg} if the
command needs some additional characters after its arguments.  The $* is
replaced then by all arguments.  Example: >
   :set makeprg=latex\ \\\\nonstopmode\ \\\\input\\{$*}
or simpler >
   :let &mp = 'latex \\nonstopmode \\input\{$*}'
"$*" can be given multiple times, for example: >
   :set makeprg=gcc\ -o\ $*\ $*

The 'shellpipe' option defaults to ">" for the Amiga and ">%s 2>&1" for Win32.
This means that the output of the compiler is saved in a file and not shown on
the screen directly.  For Unix "| tee" is used.  The compiler output is shown
on the screen and saved in a file the same time.  Depending on the shell used
"|& tee" or "2>&1| tee" is the default, so stderr output will be included.

If 'shellpipe' is empty, the {errorfile} part will be omitted.  This is useful
for compilers that write to an errorfile themselves (e.g., Manx's Amiga C).


Using QuickFixCmdPost to fix the encoding ~

It may be that 'encoding' is set to an encoding that differs from the messages
your build program produces.  This example shows how to fix this after Vim has
read the error messages: >

	function QfMakeConv()
	   let qflist = getqflist()
	   for i in qflist
	      let i.text = iconv(i.text, "cp936", "utf-8")
	   endfor
	   call setqflist(qflist)
	endfunction

	au QuickfixCmdPost make call QfMakeConv()

(Example by Faque Cheng)
Another option is using 'makeencoding'.

==============================================================================
5. Using :vimgrep and :grep				*grep* *lid*

Vim has two ways to find matches for a pattern: internal and external.  The
advantage of the internal grep is that it works on all systems and uses the
powerful Vim search patterns.  An external grep program can be used when the
Vim grep does not do what you want.

The internal method will be slower, because files are read into memory.  The
advantages are:
- Line separators and encoding are automatically recognized, as if a file is
  being edited.
- Uses Vim search patterns.  Multi-line patterns can be used.
- When plugins are enabled: compressed and remote files can be searched.
	|gzip| |netrw|

To be able to do this Vim loads each file as if it is being edited.  When
there is no match in the file the associated buffer is wiped out again.  The
'hidden' option is ignored here to avoid running out of memory or file
descriptors when searching many files.  However, when the |:hide| command
modifier is used the buffers are kept loaded.  This makes following searches
in the same files a lot faster.

Note that |:copen| (or |:lopen| for |:lgrep|) may be used to open a buffer
containing the search results in linked form.  The |:silent| command may be
used to suppress the default full screen grep output.  The ":grep!" form of
the |:grep| command doesn't jump to the first match automatically.  These
commands can be combined to create a NewGrep command: >

        command! -nargs=+ NewGrep execute 'silent grep! <args>' | copen 42


5.1 Using Vim's internal grep

					*:vim* *:vimgrep* *E682* *E683*
:vim[grep][!] /{pattern}/[g][j][f] {file} ...
			Search for {pattern} in the files {file} ... and set
			the error list to the matches.  Files matching
			'wildignore' are ignored; files in 'suffixes' are
			searched last.

			{pattern} is a Vim search pattern.  Instead of
			enclosing it in / any non-ID character (see
			|'isident'|) can be used, so long as it does not
			appear in {pattern}.
			'ignorecase' applies.  To overrule it put |/\c| in the
			pattern to ignore case or |/\C| to match case.
			'smartcase' is not used.
			If {pattern} is empty (e.g. // is specified), the last
			used search pattern is used. |last-pattern|

			Flags:
			'g'  Without the 'g' flag each line is added only
			     once.  With 'g' every match is added.

			'j'  Without the 'j' flag Vim jumps to the first
			     match.  With 'j' only the quickfix list is
			     updated.  With the [!] any changes in the current
			     buffer are abandoned.

			'f'  When the 'f' flag is specified, fuzzy string
			     matching is used to find matching lines. In this
			     case, {pattern} is treated as a literal string
			     instead of a regular expression.  See
			     |fuzzy-matching| for more information about fuzzy
			     matching strings.

			|QuickFixCmdPre| and |QuickFixCmdPost| are triggered.
			A file that is opened for matching may use a buffer
			number, but it is reused if possible to avoid
			consuming buffer numbers.

:{count}vim[grep] ...
			When a number is put before the command this is used
			as the maximum number of matches to find.  Use
			":1vimgrep pattern file" to find only the first.
			Useful if you only want to check if there is a match
			and quit quickly when it's found.

			Every second or so the searched file name is displayed
			to give you an idea of the progress made.
			Examples: >
				:vimgrep /an error/ *.c
				:vimgrep /\<FileName\>/ *.h include/*
				:vimgrep /myfunc/ **/*.c
<			For the use of "**" see |starstar-wildcard|.

:vim[grep][!] {pattern} {file} ...
			Like above, but instead of enclosing the pattern in a
			non-ID character use a white space separated pattern.
			The pattern must start with an ID character.
			Example: >
				:vimgrep Error *.c
<
							*:lv* *:lvimgrep*
:lv[imgrep][!] /{pattern}/[g][j][f] {file} ...
:lv[imgrep][!] {pattern} {file} ...
			Same as ":vimgrep", except the location list for the
			current window is used instead of the quickfix list.

						*:vimgrepa* *:vimgrepadd*
:vimgrepa[dd][!] /{pattern}/[g][j][f] {file} ...
:vimgrepa[dd][!] {pattern} {file} ...
			Just like ":vimgrep", but instead of making a new list
			of errors the matches are appended to the current
			list.

						*:lvimgrepa* *:lvimgrepadd*
:lvimgrepa[dd][!] /{pattern}/[g][j][f] {file} ...
:lvimgrepa[dd][!] {pattern} {file} ...
			Same as ":vimgrepadd", except the location list for
			the current window is used instead of the quickfix
			list.

5.2 External grep

Vim can interface with "grep" and grep-like programs (such as the GNU
id-utils) in a similar way to its compiler integration (see |:make| above).

[Unix trivia: The name for the Unix "grep" command comes from ":g/re/p", where
"re" stands for Regular Expression.]

							    *:gr* *:grep*
:gr[ep][!] [arguments]	Just like ":make", but use 'grepprg' instead of
			'makeprg' and 'grepformat' instead of 'errorformat'.
			When 'grepprg' is "internal" this works like
			|:vimgrep|.  Note that the pattern needs to be
			enclosed in separator characters then.
			If the encoding of the program output differs from the
			'encoding' option, you can use the 'makeencoding'
			option to specify the encoding.

							    *:lgr* *:lgrep*
:lgr[ep][!] [arguments]	Same as ":grep", except the location list for the
			current window is used instead of the quickfix list.

							*:grepa* *:grepadd*
:grepa[dd][!] [arguments]
			Just like ":grep", but instead of making a new list of
			errors the matches are appended to the current list.
			Example: >
				:call setqflist([])
				:bufdo grepadd! something %
<			The first command makes a new error list which is
			empty.  The second command executes "grepadd" for each
			listed buffer.  Note the use of ! to avoid that
			":grepadd" jumps to the first error, which is not
			allowed with |:bufdo|.
			An example that uses the argument list and avoids
			errors for files without matches: >
				:silent argdo try
				  \ | grepadd! something %
				  \ | catch /E480:/
				  \ | endtry"
<
			If the encoding of the program output differs from the
			'encoding' option, you can use the 'makeencoding'
			option to specify the encoding.

							*:lgrepa* *:lgrepadd*
:lgrepa[dd][!] [arguments]
			Same as ":grepadd", except the location list for the
			current window is used instead of the quickfix list.

5.3 Setting up external grep

If you have a standard "grep" program installed, the :grep command may work
well with the defaults.  The syntax is very similar to the standard command: >

	:grep foo *.c

Will search all files with the .c extension for the substring "foo".  The
arguments to :grep are passed straight to the "grep" program, so you can use
whatever options your "grep" supports.

By default, :grep invokes grep with the -n option (show file and line
numbers).  You can change this with the 'grepprg' option.  You will need to set
'grepprg' if:

a)	You are using a program that isn't called "grep"
b)	You have to call grep with a full path
c)	You want to pass other options automatically (e.g. case insensitive
	search.)

Once "grep" has executed, Vim parses the results using the 'grepformat'
option.  This option works in the same way as the 'errorformat' option - see
that for details.  You may need to change 'grepformat' from the default if
your grep outputs in a non-standard format, or you are using some other
program with a special format.

Once the results are parsed, Vim loads the first file containing a match and
jumps to the appropriate line, in the same way that it jumps to a compiler
error in |quickfix| mode.  You can then use the |:cnext|, |:clist|, etc.
commands to see the other matches.


5.4 Using :grep with id-utils

You can set up :grep to work with the GNU id-utils like this: >

	:set grepprg=lid\ -Rgrep\ -s
	:set grepformat=%f:%l:%m

then >
	:grep (regexp)

works just as you'd expect.
(provided you remembered to mkid first :)


5.5 Browsing source code with :vimgrep or :grep

Using the stack of error lists that Vim keeps, you can browse your files to
look for functions and the functions they call.  For example, suppose that you
have to add an argument to the read_file() function.  You enter this command: >

	:vimgrep /\<read_file\>/ *.c

You use ":cn" to go along the list of matches and add the argument.  At one
place you have to get the new argument from a higher level function msg(), and
need to change that one too.  Thus you use: >

	:vimgrep /\<msg\>/ *.c

While changing the msg() functions, you find another function that needs to
get the argument from a higher level.  You can again use ":vimgrep" to find
these functions.  Once you are finished with one function, you can use >

	:colder

to go back to the previous one.

This works like browsing a tree: ":vimgrep" goes one level deeper, creating a
list of branches.  ":colder" goes back to the previous level.  You can mix
this use of ":vimgrep" and "colder" to browse all the locations in a tree-like
way.  If you do this consistently, you will find all locations without the
need to write down a "todo" list.

=============================================================================
6. Selecting a compiler					*compiler-select*

						*:comp* *:compiler* *E666*
:comp[iler][!] {name}		Set options to work with compiler {name}.
				Without the "!" options are set for the
				current buffer.  With "!" global options are
				set.
				If you use ":compiler foo" in "file.foo" and
				then ":compiler! bar" in another buffer, Vim
				will keep on using "foo" in "file.foo".
				{not available when compiled without the
				|+eval| feature}


The Vim plugins in the "compiler" directory will set options to use the
selected compiler.  For `:compiler` local options are set, for `:compiler!`
global options.
							*current_compiler*
To support older Vim versions, the plugins always use "current_compiler" and
not "b:current_compiler".  What the command actually does is the following:

- Delete the "current_compiler" and "b:current_compiler" variables.
- Define the "CompilerSet" user command.  With "!" it does ":set", without "!"
  it does ":setlocal".
- Execute ":runtime! compiler/{name}.vim".  The plugins are expected to set
  options with "CompilerSet" and set the "current_compiler" variable to the
  name of the compiler.
- Delete the "CompilerSet" user command.
- Set "b:current_compiler" to the value of "current_compiler".
- Without "!" the old value of "current_compiler" is restored.


For writing a compiler plugin, see |write-compiler-plugin|.

Use the |compiler-make| plugin to undo the effect of a compiler plugin.

CPPCHECK			*quickfix-cppcheck* *compiler-cppcheck*

Use g/b:`c_cppcheck_params` to set cppcheck parameters. The global
settings by default include

- `--verbose`: Enables verbose output.
- `--force`: Forces checking of all configurations.
- `--inline-suppr`: Allows inline suppressions.
- `--enable=...`: Enables specific checks like warnings, style, performance,
  portability, information, and missing includes.
- `-j`: Utilizes multiple processors if available, determined by the
  `getconf` command if available (requires omitting the unusedFunction check)

For C++ files (`filetype == 'cpp'`), the `--language=c++` option is added to
ensure Cppcheck treats the file as C++.

If compile_commands.json is present in the current directory, it is added as a
`--project` parameter to the command line. Otherwise, by default the
directories in &path are passed as include directories. These can be set by
g/b:`c_cppcheck_includes` as a list of `-I` flags. Tim Pope's vim-apathy
plug-in [0] can expand &path. To also append the folders in a git repo use >

  let &l:path = join(systemlist('git ls-tree -d --name-only -r HEAD'), ',')

[0] https://github.com/tpope/vim-apathy

DOTNET							*compiler-dotnet*

The .NET CLI compiler outputs both errors and warnings by default. The output
may be limited to include only errors, by setting the g:dotnet_errors_only
variable to |v:true|.

The associated project name is included in each error and warning. To suppress
the project name, set the g:dotnet_show_project_file variable to |v:false|.

Example: limit output to only display errors, and suppress the project name: >
	let dotnet_errors_only = v:true
	let dotnet_show_project_file = v:false
	compiler dotnet
<
GCC					*quickfix-gcc*	*compiler-gcc*

There's one variable you can set for the GCC compiler:

g:compiler_gcc_ignore_unmatched_lines
				Ignore lines that don't match any patterns
				defined for GCC.  Useful if output from
				commands run from make are generating false
				positives.

JAVAC							*compiler-javac*

Commonly used compiler options can be added to 'makeprg' by setting the
b/g:javac_makeprg_params variable.  For example: >

	let g:javac_makeprg_params = "-Xlint:all -encoding utf-8"

MAVEN							*compiler-maven*

Commonly used compiler options can be added to 'makeprg' by setting the
b/g:maven_makeprg_params variable.  For example: >

	let g:maven_makeprg_params = "-DskipTests -U -X"

SPOTBUGS						*compiler-spotbugs*

SpotBugs is a static analysis tool that can be used to find bugs in Java.
It scans the Java bytecode of all classes in the currently open buffer.
(Therefore, `:compiler! spotbugs` is not supported.)

Commonly used compiler options can be added to 'makeprg' by setting the
"b:" or "g:spotbugs_makeprg_params" variable.  For example: >vim

	let b:spotbugs_makeprg_params = "-longBugCodes -effort:max -low"

The global default is "-workHard -experimental".

By default, the class files are searched in the directory where the source
files are placed.  However, typical Java projects use distinct directories
for source files and class files.  To make both known to SpotBugs, assign
their paths (distinct and relative to their common root directory) to the
following properties (using the example of a common Maven project): >vim

	let g:spotbugs_properties = {
		\ 'sourceDirPath':	['src/main/java'],
		\ 'classDirPath':	['target/classes'],
		\ 'testSourceDirPath':	['src/test/java'],
		\ 'testClassDirPath':	['target/test-classes'],
	\ }

Note that source and class path entries are expected to come in pairs: define
both "sourceDirPath" and "classDirPath" when you are considering at least one,
and apply the same logic to "testSourceDirPath" and "testClassDirPath".
Note that values for the path keys describe only for SpotBugs where to look
for files; refer to the documentation for particular compiler plugins for more
information.

The default pre- and post-compiler actions are provided for Ant, Maven, and
Javac compiler plugins and can be selected by assigning the name of a compiler
plugin (`ant`, `maven`, or `javac`) to the "compiler" key: >vim

	let g:spotbugs_properties = {
		\ 'compiler':		'maven',
	\ }

This single setting is essentially equivalent to all the settings below, with
the exception made for the "PreCompilerAction" and "PreCompilerTestAction"
values: their listed |Funcref|s will obtain no-op implementations whereas the
implicit Funcrefs of the "compiler" key will obtain the requested defaults if
available. >vim

	let g:spotbugs_properties = {
		\ 'PreCompilerAction':
			\ function('spotbugs#DefaultPreCompilerAction'),
		\ 'PreCompilerTestAction':
			\ function('spotbugs#DefaultPreCompilerTestAction'),
		\ 'PostCompilerAction':
			\ function('spotbugs#DefaultPostCompilerAction'),
		\ 'sourceDirPath':	['src/main/java'],
		\ 'classDirPath':	['target/classes'],
		\ 'testSourceDirPath':	['src/test/java'],
		\ 'testClassDirPath':	['target/test-classes'],
	\ }

With default actions, the compiler of choice will attempt to rebuild the class
files for the buffer (and possibly for the whole project) as soon as a Java
syntax file is loaded; then, `spotbugs` will attempt to analyze the quality of
the compilation unit of the buffer.

Vim commands proficient in 'makeprg' [0] can be composed with default actions.
Begin by considering which of the supported keys, "DefaultPreCompilerCommand",
"DefaultPreCompilerTestCommand", or "DefaultPostCompilerCommand", you need to
write an implementation for, observing that each of these keys corresponds to
a particular "*Action" key.  Follow it by defining a new function that always
declares an only parameter of type string and puts to use a command equivalent
of |:make|, and assigning its |Funcref| to the selected key.  For example:
>vim
	function! GenericPostCompilerCommand(arguments) abort
		execute 'make ' . a:arguments
	endfunction

	let g:spotbugs_properties = {
		\ 'DefaultPostCompilerCommand':
			\ function('GenericPostCompilerCommand'),
	\ }

When "PostCompilerAction" is available, "PostCompilerActionExecutor" is also
supported.  Its value must be a Funcref pointing to a function that always
declares a single parameter of type string and decides whether |:execute| can
be dispatched on its argument, containing a pending post-compiler action,
after ascertaining the current status of |:cc| (or |:ll|): >vim

	function! GenericPostCompilerActionExecutor(action) abort
		try
			cc
		catch /\<E42:/
			execute a:action
		endtry
	endfunction

Complementary, some or all of the available "Pre*Action"s (or "*Pre*Command"s)
may run `:doautocmd java_spotbugs_post User` in their implementations before
|:make| (or its equivalent) to define a once-only |ShellCmdPost| `:autocmd`
that will arrange for "PostCompilerActionExecutor" to be invoked; and then run
`:doautocmd java_spotbugs_post ShellCmdPost` to consume this event: >vim

	function! GenericPreCompilerCommand(arguments) abort
		if !exists('g:spotbugs_compilation_done')
			doautocmd java_spotbugs_post User
			execute 'make ' . a:arguments
			" only run doautocmd when :make was synchronous
			" see note below
			doautocmd java_spotbugs_post ShellCmdPost " XXX: (a)
			let g:spotbugs_compilation_done = 1
		else
			cc
		endif
	endfunction

	function! GenericPreCompilerTestCommand(arguments) abort
		if !exists('g:spotbugs_test_compilation_done')
			doautocmd java_spotbugs_post User
			execute 'make ' . a:arguments
			" only run doautocmd when :make was synchronous
			" see note below
			doautocmd java_spotbugs_post ShellCmdPost " XXX: (b)
			let g:spotbugs_test_compilation_done = 1
		else
			cc
		endif
	endfunction

	let g:spotbugs_properties = {
		\ 'compiler':		'maven',
		\ 'DefaultPreCompilerCommand':
			\ function('GenericPreCompilerCommand'),
		\ 'DefaultPreCompilerTestCommand':
			\ function('GenericPreCompilerTestCommand'),
		\ 'PostCompilerActionExecutor':
			\ function('GenericPostCompilerActionExecutor'),
	\ }

If a command equivalent of `:make` is capable of asynchronous execution and
consuming `ShellCmdPost` events, `:doautocmd java_spotbugs_post ShellCmdPost`
must be removed from such "*Action" (or "*Command") implementations (i.e. the
lines `(a)` and `(b)` in the listed examples) to retain a sequential order for
non-blocking execution, and any notification (see below) must be suppressed.
A `ShellCmdPost` `:autocmd` can be associated with any |:augroup| by assigning
its name to the "augroupForPostCompilerAction" key.

When default actions are not suited to a desired workflow, proceed by writing
arbitrary functions yourself and matching their Funcrefs to the supported
keys: "PreCompilerAction", "PreCompilerTestAction", and "PostCompilerAction".

The next example re-implements the default pre-compiler actions for a Maven
project and requests other default Maven settings with the "compiler" entry:
>vim
	function! MavenPreCompilerAction() abort
		call spotbugs#DeleteClassFiles()
		compiler maven
		make compile
		cc
	endfunction

	function! MavenPreCompilerTestAction() abort
		call spotbugs#DeleteClassFiles()
		compiler maven
		make test-compile
		cc
	endfunction

	let g:spotbugs_properties = {
		\ 'compiler':		'maven',
		\ 'PreCompilerAction':
			\ function('MavenPreCompilerAction'),
		\ 'PreCompilerTestAction':
			\ function('MavenPreCompilerTestAction'),
	\ }

Note that all entered custom settings will take precedence over the matching
default settings in "g:spotbugs_properties".
Note that it is necessary to notify the plugin of the result of a pre-compiler
action before further work can be undertaken.  Using |:cc| after |:make| (or
|:ll| after |:lmake|) as the last command of an action is the supported means
of such communication.

Two commands, "SpotBugsRemoveBufferAutocmd" and "SpotBugsDefineBufferAutocmd",
are provided to toggle actions for buffer-local autocommands.  For example, to
also run actions on any |BufWritePost| and |SigUSR1| event, add these lines to
`~/.vim/after/ftplugin/java.vim`: >vim

	if exists(':SpotBugsDefineBufferAutocmd') == 2
		SpotBugsDefineBufferAutocmd BufWritePost SigUSR1
	endif

Otherwise, you can turn to `:doautocmd java_spotbugs User` at any time.

The "g:spotbugs_properties" variable is consulted by the Java filetype plugin
(|ft-java-plugin|) to arrange for the described automation, and, therefore, it
must be defined before |FileType| events can take place for the buffers loaded
with Java source files.  It could, for example, be set in a project-local
|vimrc| loaded by [1].

Both "g:spotbugs_properties" and "b:spotbugs_properties" are recognized and
must be modifiable (|:unlockvar|).  The "*Command" entries are always treated
as global functions to be shared among all Java buffers.

The SpotBugs Java library and, by extension, its distributed shell scripts do
not support in the `-textui` mode listed pathnames with directory filenames
that contain blank characters [2].  To work around this limitation, consider
making a symbolic link to such a directory from a directory that does not have
blank characters in its name and passing this information to SpotBugs: >vim

	let g:spotbugs_alternative_path = {
		\ 'fromPath':	'path/to/dir_without_blanks',
		\ 'toPath':	'path/to/dir with blanks',
	\ }

[0] https://github.com/Konfekt/vim-compilers
[1] https://github.com/MarcWeber/vim-addon-local-vimrc
[2] https://github.com/spotbugs/spotbugs/issues/909

GNU MAKE						*compiler-make*

Since the default make program is "make", the compiler plugin for make,
:compiler make, will reset the 'makeprg' and 'errorformat' option to
the default values and unlet any variables that may have been set by a
previous compiler plugin.

MANX AZTEC C				*quickfix-manx* *compiler-manx*

To use Vim with Manx's Aztec C compiler on the Amiga you should do the
following:
- Set the CCEDIT environment variable with the command: >
	mset "CCEDIT=vim -q"
- Compile with the -qf option.  If the compiler finds any errors, Vim is
  started and the cursor is positioned on the first error.  The error message
  will be displayed on the last line.  You can go to other errors with the
  commands mentioned above.  You can fix the errors and write the file(s).
- If you exit Vim normally the compiler will re-compile the same file.  If you
  exit with the :cq command, the compiler will terminate.  Do this if you
  cannot fix the error, or if another file needs to be compiled first.

There are some restrictions to the Quickfix mode on the Amiga.  The
compiler only writes the first 25 errors to the errorfile (Manx's
documentation does not say how to get more).  If you want to find the others,
you will have to fix a few errors and exit the editor.  After recompiling,
up to 25 remaining errors will be found.

If Vim was started from the compiler, the :sh and some :!  commands will not
work, because Vim is then running in the same process as the compiler and
stdin (standard input) will not be interactive.

GROFF					*quickfix-groff* *compiler-groff*

The GROFF compiler plugin uses the mom macro set (documented in the groff_mom
manpage) as input and expects that the output file type extension is passed to
make, say :make html or :make pdf.

Additional arguments can be passed to groff by setting them in
`b:groff_compiler_args` or `g:groff_compiler_args`. The `language` argument
passed to groff is set using 'spelllang'; it can be overridden by setting
`b:groff_compiler_lang`. The default encoding is `UTF-8` and can be changed
by setting `b:groff_compiler_encoding` or `g:groff_compiler_encoding`.

PANDOC					*quickfix-pandoc* *compiler-pandoc*

The Pandoc compiler plugin expects that an output file type extension is
passed to make, say :make html or :make pdf.

Additional arguments can be passed to pandoc:

- either by appending them to make, say `:make html --self-contained` .
- or setting them in `b:pandoc_compiler_args` or `g:pandoc_compiler_args`.

The `--from` argument is an educated guess using the buffer file type;
it can be overridden by setting `b:pandoc_compiler_from`.
The `--metadata lang` argument is set using 'spelllang';
If `--from=markdown` is assumed and no title set in a title header or
YAML block, then the filename (without extension) is used as the title.

PERL					*quickfix-perl* *compiler-perl*

The Perl compiler plugin doesn't actually compile, but invokes Perl's internal
syntax checking feature and parses the output for possible errors so you can
correct them in quick-fix mode.

Warnings are forced regardless of "no warnings" or "$^W = 0" within the file
being checked.  To disable this set g:perl_compiler_force_warnings to a zero
value.  For example: >
	let g:perl_compiler_force_warnings = 0

MYPY TYPE CHECKER					*compiler-mypy*

Commonly used compiler options can be added to 'makeprg' by setting the
b/g:mypy_makeprg_params variable.  For example: >

	let b:mypy_makeprg_params = "--warn-unused-ignores"

The global default is "--strict --ignore-missing-imports".

RUFF LINTER						*compiler-ruff*

Commonly used compiler options can be added to 'makeprg' by setting the
b/g:ruff_makeprg_params variable.  For example: >

	let b:ruff_makeprg_params = "--max-line-length "..&textwidth

The global default is "--preview".

PYLINT LINTER						*compiler-pylint*

Commonly used compiler options can be added to 'makeprg' by setting the
b/g:pylint_makeprg_params variable.  For example: >

	let b:pylint_makeprg_params = "--max-line-length "..&textwidth

The global default is "--jobs=0" to use (almost) all cores.

PYUNIT COMPILER						*compiler-pyunit*

This is not actually a compiler, but a unit testing framework for the
Python language.  It is included into standard Python distribution
starting from version 2.0.  For older versions, you can get it from
http://pyunit.sourceforge.net.

When you run your tests with the help of the framework, possible errors
are parsed by Vim and presented for you in quick-fix mode.

Unfortunately, there is no standard way to run the tests.
The alltests.py script seems to be used quite often, that's all.
Useful values for the 'makeprg' options therefore are:
 setlocal makeprg=./alltests.py " Run a testsuite
 setlocal makeprg=python\ %:S   " Run a single testcase

Also see http://vim.sourceforge.net/tip_view.php?tip_id=280.

PYTEST COMPILER						*compiler-pytest*

Commonly used compiler options can be added to 'makeprg' by setting the
b/g:pytest_makeprg_params variable.  For example: >

  let b:pytest_makeprg_params = "--verbose --no-summary --disable-warnings"

The global default is "--tb=short --quiet"; Python warnings are suppressed.

TEX COMPILER						*compiler-tex*

Included in the distribution compiler for TeX ($VIMRUNTIME/compiler/tex.vim)
uses make command if possible.  If the compiler finds a file named "Makefile"
or "makefile" in the current directory, it supposes that you want to process
your *TeX files with make, and the makefile does the right work.  In this case
compiler sets 'errorformat' for *TeX output and leaves 'makeprg' untouched.  If
neither "Makefile" nor "makefile" is found, the compiler will not use make.
You can force the compiler to ignore makefiles by defining
b:tex_ignore_makefile or g:tex_ignore_makefile variable (they are checked for
existence only).

If the compiler chose not to use make, it needs to choose a right program for
processing your input.  If b:tex_flavor or g:tex_flavor (in this precedence)
variable exists, it defines TeX flavor for :make (actually, this is the name
of executed command), and if both variables do not exist, it defaults to
"latex".  For example, while editing chapter2.tex \input-ed from mypaper.tex
written in AMS-TeX: >

	:let b:tex_flavor = 'amstex'
	:compiler tex
<	[editing...] >
	:make mypaper

Note that you must specify a name of the file to process as an argument (to
process the right file when editing \input-ed or \include-ed file; portable
solution for substituting % for no arguments is welcome).  This is not in the
semantics of make, where you specify a target, not source, but you may specify
filename without extension ".tex" and mean this as "make filename.dvi or
filename.pdf or filename.some_result_extension according to compiler".

Note: tex command line syntax is set to usable both for MikTeX (suggestion
by Srinath Avadhanula) and teTeX (checked by Artem Chuprina).  Suggestion
from |errorformat-LaTeX| is too complex to keep it working for different
shells and OSes and also does not allow to use other available TeX options,
if any.  If your TeX doesn't support "-interaction=nonstopmode", please
report it with different means to express \nonstopmode from the command line.

TSC COMPILER						*compiler-tsc*

The executable and compiler options can be added to 'makeprg' by setting the
b/g:tsc_makeprg variable.  For example: >

	let b:tsc_makeprg = "npx tsc --noEmit"

TYPST COMPILER						*compiler-typst*

Vim includes a compiler plugin for Typst files. This compiler is enabled
automatically in Typst buffers by the Typst filetype plugin |ft-typst-plugin|.
Run |:make| to compile the current Typst file.

							*g:typst_cmd*
By default Vim will use "typst" as the command to run the Typst compiler. This
can be changed by setting the |g:typst_cmd| variable: >
	let g:typst_cmd = "/path/to/other/command"

=============================================================================
7. The error format					*error-file-format*

					*errorformat* *E372* *E373* *E374*
						*E375* *E376* *E377* *E378*
The 'errorformat' option specifies a list of formats that are recognized.  The
first format that matches with an error message is used.  You can add several
formats for different messages your compiler produces, or even entries for
multiple compilers.  See |efm-entries|.

Each entry in 'errorformat' is a scanf-like string that describes the format.
First, you need to know how scanf works.  Look in the documentation of your
C compiler.  Below you find the % items that Vim understands.  Others are
invalid.

Special characters in 'errorformat' are comma and backslash.  See
|efm-entries| for how to deal with them.  Note that a literal "%" is matched
by "%%", thus it is not escaped with a backslash.
Keep in mind that in the `:make` and `:grep` output all NUL characters are
replaced with SOH (0x01).

Note: By default the difference between upper and lowercase is ignored.  If
you want to match case, add "\C" to the pattern |/\C|.

Vim will read lines of any length, but only the first 4095 bytes are used, the
rest is ignored.  Items can only be 1023 bytes long.


Basic items

	%f		file name (finds a string)
	%b		buffer number (finds a number)
	%o		module name (finds a string)
	%l		line number (finds a number)
	%e		end line number (finds a number)
	%c		column number (finds a number representing character
			column of the error, byte index, a <tab> is 1
			character column)
	%v		virtual column number (finds a number representing
			screen column of the error (1 <tab> == 8 screen
			columns))
	%k		end column number (finds a number representing
			the character column of the error, byte index, or a
			number representing screen end column of the error if
			it's used with %v)
	%t		error type (finds a single character):
			    e - error message
			    w - warning message
			    i - info message
			    n - note message
	%n		error number (finds a number)
	%m		error message (finds a string)
	%r		matches the "rest" of a single-line file message %O/P/Q
	%p		pointer line (finds a sequence of '-', '.', ' ' or
			tabs and uses the length for the column number)
	%*{conv}	any scanf non-assignable conversion
	%%		the single '%' character
	%s		search text (finds a string)

The "%f" conversion may depend on the current 'isfname' setting.  "~/" is
expanded to the home directory and environment variables are expanded.

The "%f" and "%m" conversions have to detect the end of the string.  This
normally happens by matching following characters and items.  When nothing is
following the rest of the line is matched.  If "%f" is followed by a '%' or a
backslash, it will look for a sequence of 'isfname' characters.

On MS-Windows a leading "C:" will be included in "%f", even when using "%f:".
This means that a file name which is a single alphabetical letter will not be
detected.

The "%b" conversion is used to parse a buffer number.  This is useful for
referring to lines in a scratch buffer or a buffer with no name.  If a buffer
with the matching number doesn't exist, then that line is used as a non-error
line.

The "%p" conversion is normally followed by a "^".  It's used for compilers
that output a line like: >
	    ^
or >
   ---------^
to indicate the column of the error.  This is to be used in a multi-line error
message.  See |errorformat-javac| for a  useful example.

The "%s" conversion specifies the text to search for, to locate the error line.
The text is used as a literal string.  The anchors "^" and "$" are added to
the text to locate the error line exactly matching the search text and the
text is prefixed with the "\V" atom to make it "very nomagic".  The "%s"
conversion can be used to locate lines without a line number in the error
output.  Like the output of the "grep" shell command.
When the pattern is present the line number will not be used.

The "%o" conversion specifies the module name in quickfix entry.  If present
it will be used in quickfix error window instead of the filename.  The module
name is used only for displaying purposes, the file name is used when jumping
to the file.

Changing directory

The following uppercase conversion characters specify the type of special
format strings.  At most one of them may be given as a prefix at the beginning
of a single comma-separated format pattern.
Some compilers produce messages that consist of directory names that have to
be prepended to each file name read by %f (example: GNU make).  The following
codes can be used to scan these directory names; they will be stored in an
internal directory stack.					*E379*
	%D		"enter directory" format string; expects a following
			  %f that finds the directory name
	%X		"leave directory" format string; expects following %f

When defining an "enter directory" or "leave directory" format, the "%D" or
"%X" has to be given at the start of that substring.  Vim tracks the directory
changes and prepends the current directory to each erroneous file found with a
relative path.  See |quickfix-directory-stack| for details, tips and
limitations.


Multi-line messages				*errorformat-multi-line*

It is possible to read the output of programs that produce multi-line
messages, i.e. error strings that consume more than one line.  Possible
prefixes are:
	%E		start of a multi-line error message
	%W		start of a multi-line warning message
	%I		start of a multi-line informational message
	%N		start of a multi-line note message
	%A		start of a multi-line message (unspecified type)
	%>		for next line start with current pattern again |efm-%>|
	%C		continuation of a multi-line message
	%Z		end of a multi-line message
These can be used with '+' and '-', see |efm-ignore| below.

Using "\n" in the pattern won't work to match multi-line messages.

Example: Your compiler happens to write out errors in the following format
(leading line numbers not being part of the actual output):

     1	Error 275 ~
     2	line 42 ~
     3	column 3 ~
     4	' ' expected after '--' ~

The appropriate error format string has to look like this: >
   :set efm=%EError\ %n,%Cline\ %l,%Ccolumn\ %c,%Z%m

And the |:clist| error message generated for this error is:

 1:42 col 3 error 275:  ' ' expected after '--'

Another example: Think of a Python interpreter that produces the following
error message (line numbers are not part of the actual output):

     1	==============================================================
     2	FAIL: testGetTypeIdCachesResult (dbfacadeTest.DjsDBFacadeTest)
     3	--------------------------------------------------------------
     4	Traceback (most recent call last):
     5	  File "unittests/dbfacadeTest.py", line 89, in testFoo
     6	    self.assertEquals(34, dtid)
     7	  File "/usr/lib/python2.2/unittest.py", line 286, in
     8	 failUnlessEqual
     9	    raise self.failureException, \
    10	AssertionError: 34 != 33
    11
    12	--------------------------------------------------------------
    13	<USER> <GROUP> tests in 0.063s

Say you want |:clist| write the relevant information of this message only,
namely:
 5 unittests/dbfacadeTest.py:89:  AssertionError: 34 != 33

Then the error format string could be defined as follows: >
  :set efm=%C\ %.%#,%A\ \ File\ \"%f\"\\,\ line\ %l%.%#,%Z%[%^\ ]%\\@=%m

Note that the %C string is given before the %A here: since the expression
' %.%#' (which stands for the regular expression ' .*') matches every line
starting with a space, followed by any characters to the end of the line,
it also hides line 7 which would trigger a separate error message otherwise.
Error format strings are always parsed pattern by pattern until the first
match occurs.
							*efm-%>*
The %> item can be used to avoid trying patterns that appear earlier in
'errorformat'.  This is useful for patterns that match just about anything.
For example, if the error looks like this:

	Error in line 123 of foo.c: ~
	unknown variable "i" ~

This can be found with: >
	:set efm=xxx,%E%>Error in line %l of %f:,%Z%m
Where "xxx" has a pattern that would also match the second line.

Important: There is no memory of what part of the errorformat matched before;
every line in the error file gets a complete new run through the error format
lines.  For example, if one has: >
  setlocal efm=aa,bb,cc,dd,ee
Where aa, bb, etc. are error format strings.  Each line of the error file will
be matched to the pattern aa, then bb, then cc, etc.  Just because cc matched
the previous error line does _not_ mean that dd will be tried first on the
current line, even if cc and dd are multi-line errorformat strings.



Separate file name			*errorformat-separate-filename*

These prefixes are useful if the file name is given once and multiple messages
follow that refer to this file name.
	%O		single-line file message: overread the matched part
	%P		single-line file message: push file %f onto the stack
	%Q		single-line file message: pop the last file from stack

Example: Given a compiler that produces the following error logfile (without
leading line numbers):

     1	[a1.tt]
     2	(1,17)  error: ';' missing
     3	(21,2)  warning: variable 'z' not defined
     4	(67,3)  error: end of file found before string ended
     5
     6	[a2.tt]
     7
     8	[a3.tt]
     9	NEW compiler v1.1
    10	(2,2)   warning: variable 'x' not defined
    11	(67,3)  warning: 's' already defined

This logfile lists several messages for each file enclosed in [...] which are
properly parsed by an error format like this: >
  :set efm=%+P[%f],(%l\\,%c)%*[\ ]%t%*[^:]:\ %m,%-Q

A call of |:clist| writes them accordingly with their correct filenames:

  2 a1.tt:1 col 17 error: ';' missing
  3 a1.tt:21 col 2 warning: variable 'z' not defined
  4 a1.tt:67 col 3 error: end of file found before string ended
  8 a3.tt:2 col 2 warning: variable 'x' not defined
  9 a3.tt:67 col 3 warning: 's' already defined

Unlike the other prefixes that all match against whole lines, %P, %Q and %O
can be used to match several patterns in the same line.  Thus it is possible
to parse even nested files like in the following line:
  {"file1" {"file2" error1} error2 {"file3" error3 {"file4" error4 error5}}}
The %O then parses over strings that do not contain any push/pop file name
information.  See |errorformat-LaTeX| for an extended example.


Ignoring and using whole messages			*efm-ignore*

The codes '+' or '-' can be combined with the uppercase codes above; in that
case they have to precede the letter, e.g. '%+A' or '%-G':
	%-		do not include the matching multi-line in any output
	%+		include the whole matching line in the %m error string

One prefix is only useful in combination with '+' or '-', namely %G.  It parses
over lines containing general information like compiler version strings or
other headers that can be skipped.
	%-G		ignore this message
	%+G		general message


Pattern matching

The scanf()-like "%*[]" notation is supported for backward-compatibility
with previous versions of Vim.  However, it is also possible to specify
(nearly) any Vim supported regular expression in format strings.
Since meta characters of the regular expression language can be part of
ordinary matching strings or file names (and therefore internally have to
be escaped), meta symbols have to be written with leading '%':
	%\		The single '\' character.  Note that this has to be
			escaped ("%\\") in ":set errorformat=" definitions.
	%.		The single '.' character.
	%#		The single '*'(!) character.
	%^		The single '^' character.  Note that this is not
			useful, the pattern already matches start of line.
	%$		The single '$' character.  Note that this is not
			useful, the pattern already matches end of line.
	%[		The single '[' character for a [] character range.
	%~		The single '~' character.
When using character classes in expressions (see |/\i| for an overview),
terms containing the "\+" quantifier can be written in the scanf() "%*"
notation.  Example: "%\\d%\\+" ("\d\+", "any number") is equivalent to "%*\\d".
Important note: The \(...\) grouping of sub-matches can not be used in format
specifications because it is reserved for internal conversions.


Multiple entries in 'errorformat'			*efm-entries*

To be able to detect output from several compilers, several format patterns
may be put in 'errorformat', separated by commas (note: blanks after the comma
are ignored).  The first pattern that has a complete match is used.  If no
match is found, matching parts from the last one will be used, although the
file name is removed and the error message is set to the whole message.  If
there is a pattern that may match output from several compilers (but not in a
right way), put it after one that is more restrictive.

To include a comma in a pattern precede it with a backslash (you have to type
two in a ":set" command).  To include a backslash itself give two backslashes
(you have to type four in a ":set" command).  You also need to put a backslash
before a space for ":set".


Valid matches						*quickfix-valid*

If a line does not completely match one of the entries in 'errorformat', the
whole line is put in the error message and the entry is marked "not valid"
These lines are skipped with the ":cn" and ":cp" commands (unless there is
no valid line at all).  You can use ":cl!" to display all the error messages.

If the error format does not contain a file name Vim cannot switch to the
correct file.  You will have to do this by hand.


Examples

The format of the file from the Amiga Aztec compiler is:

	filename>linenumber:columnnumber:errortype:errornumber:errormessage

	filename	name of the file in which the error was detected
	linenumber	line number where the error was detected
	columnnumber	column number where the error was detected
	errortype	type of the error, normally a single 'E' or 'W'
	errornumber	number of the error (for lookup in the manual)
	errormessage	description of the error

This can be matched with this 'errorformat' entry:
	%f>%l:%c:%t:%n:%m

Some examples for C compilers that produce single-line error outputs:
%f:%l:\ %t%*[^0123456789]%n:\ %m	for Manx/Aztec C error messages
					(scanf() doesn't understand [0-9])
%f\ %l\ %t%*[^0-9]%n:\ %m		for SAS C
\"%f\"\\,%*[^0-9]%l:\ %m		for generic C compilers
%f:%l:\ %m				for GCC
%f:%l:\ %m,%Dgmake[%*\\d]:\ Entering\ directory\ `%f',
%Dgmake[%*\\d]:\ Leaving\ directory\ `%f'
					for GCC with gmake (concat the lines!)
%f(%l)\ :\ %*[^:]:\ %m			old SCO C compiler (pre-OS5)
%f(%l)\ :\ %t%*[^0-9]%n:\ %m		idem, with error type and number
%f:%l:\ %m,In\ file\ included\ from\ %f:%l:,\^I\^Ifrom\ %f:%l%m
					for GCC, with some extras

Extended examples for the handling of multi-line messages are given below,
see |errorformat-Jikes| and |errorformat-LaTeX|.

Note the backslash in front of a space and double quote.  It is required for
the :set command.  There are two backslashes in front of a comma, one for the
:set command and one to avoid recognizing the comma as a separator of error
formats.


Filtering messages

If you have a compiler that produces error messages that do not fit in the
format string, you could write a program that translates the error messages
into this format.  You can use this program with the ":make" command by
changing the 'makeprg' option.  For example: >
   :set mp=make\ \\\|&\ error_filter
The backslashes before the pipe character are required to avoid it to be
recognized as a command separator.  The backslash before each space is
required for the set command.

=============================================================================
8. The directory stack				*quickfix-directory-stack*

Quickfix maintains a stack for saving all used directories parsed from the
make output.  For GNU-make this is rather simple, as it always prints the
absolute path of all directories it enters and leaves.  Regardless if this is
done via a 'cd' command in the makefile or with the parameter "-C dir" (change
to directory before reading the makefile).  It may be useful to use the switch
"-w" to force GNU-make to print out the working directory before and after
processing.

Maintaining the correct directory is more complicated if you don't use
GNU-make.  AIX-make for example doesn't print any information about its
working directory.  Then you need to enhance the makefile.  In the makefile of
LessTif there is a command which echoes "Making {target} in {dir}".  The
special problem here is that it doesn't print information on leaving the
directory and that it doesn't print the absolute path.

To solve the problem with relative paths and missing "leave directory"
messages Vim uses the following algorithm:

1) Check if the given directory is a subdirectory of the current directory.
   If this is true, store it as the current directory.
2) If it is not a subdir of the current directory, try if this is a
   subdirectory of one of the upper directories.
3) If the directory still isn't found, it is assumed to be a subdirectory
   of Vim's current directory.

Additionally it is checked for every file, if it really exists in the
identified directory.  If not, it is searched in all other directories of the
directory stack (NOT the directory subtree!).  If it is still not found, it is
assumed that it is in Vim's current directory.

There are limitations in this algorithm.  These examples assume that make just
prints information about entering a directory in the form "Making all in dir".

1) Assume you have following directories and files:
   ./dir1
   ./dir1/file1.c
   ./file1.c

   If make processes the directory "./dir1" before the current directory and
   there is an error in the file "./file1.c", you will end up with the file
   "./dir1/file.c" loaded by Vim.

   This can only be solved with a "leave directory" message.

2) Assume you have following directories and files:
   ./dir1
   ./dir1/dir2
   ./dir2

   You get the following:

   Make output			  Directory interpreted by Vim
   ------------------------	  ----------------------------
   Making all in dir1		  ./dir1
   Making all in dir2		  ./dir1/dir2
   Making all in dir2		  ./dir1/dir2

   This can be solved by printing absolute directories in the "enter directory"
   message or by printing "leave directory" messages.

To avoid this problem, ensure to print absolute directory names and "leave
directory" messages.

Examples for Makefiles:

Unix:
    libs:
	    for dn in $(LIBDIRS); do				\
		(cd $$dn; echo "Entering dir '$$(pwd)'"; make); \
		echo "Leaving dir";				\
	    done

Add
    %DEntering\ dir\ '%f',%XLeaving\ dir
to your 'errorformat' to handle the above output.

Note that Vim doesn't check if the directory name in a "leave directory"
messages is the current directory.  This is why you could just use the message
"Leaving dir".

=============================================================================
9. Specific error file formats			*errorformats*

						*errorformat-Jikes*
Jikes(TM), a source-to-bytecode Java compiler published by IBM Research,
produces simple multi-line error messages.

An 'errorformat' string matching the produced messages is shown below.
The following lines can be placed in the user's |vimrc| to overwrite Vim's
recognized default formats, or see |:set+=| how to install this format
additionally to the default. >

  :set efm=%A%f:%l:%c:%*\\d:%*\\d:,
	\%C%*\\s%trror:%m,
	\%+C%*[^:]%trror:%m,
	\%C%*\\s%tarning:%m,
	\%C%m
<
Jikes(TM) produces a single-line error message when invoked with the option
"+E", and can be matched with the following: >

  :setl efm=%f:%l:%v:%*\\d:%*\\d:%*\\s%m
<
						*errorformat-javac*
This 'errorformat' has been reported to work well for javac, which outputs a
line with "^" to indicate the column of the error: >
  :setl efm=%A%f:%l:\ %m,%-Z%p^,%-C%.%#
or: >
  :setl efm=%A%f:%l:\ %m,%+Z%p^,%+C%.%#,%-G%.%#
<
Here is an alternative from Michael F. Lamb for Unix that filters the errors
first: >
  :setl errorformat=%Z%f:%l:\ %m,%A%p^,%-G%*[^sl]%.%#
  :setl makeprg=javac\ %:S\ 2>&1\ \\\|\ vim-javac-filter

You need to put the following in "vim-javac-filter" somewhere in your path
(e.g., in ~/bin) and make it executable: >
   #!/bin/sed -f
   /\^$/s/\t/\ /g;/:[0-9]\+:/{h;d};/^[ \t]*\^/G;

In English, that sed script:
- Changes single tabs to single spaces and
- Moves the line with the filename, line number, error message to just after
  the pointer line. That way, the unused error text between doesn't break
  vim's notion of a "multi-line message" and also doesn't force us to include
  it as a "continuation of a multi-line message."

						*errorformat-ant*
For ant (http://jakarta.apache.org/) the above errorformat has to be modified
to honour the leading [javac] in front of each javac output line: >
  :set efm=%A\ %#[javac]\ %f:%l:\ %m,%-Z\ %#[javac]\ %p^,%-C%.%#

The 'errorformat' can also be configured to handle ant together with either
javac or jikes.  If you're using jikes, you should tell ant to use jikes' +E
command line switch which forces jikes to generate one-line error messages.
This is what the second line (of a build.xml file) below does: >
  <property name = "build.compiler"       value = "jikes"/>
  <property name = "build.compiler.emacs" value = "true"/>

The 'errorformat' which handles ant with both javac and jikes is: >
  :set efm=\ %#[javac]\ %#%f:%l:%c:%*\\d:%*\\d:\ %t%[%^:]%#:%m,
	   \%A\ %#[javac]\ %f:%l:\ %m,%-Z\ %#[javac]\ %p^,%-C%.%#
<
						*errorformat-jade*
parsing jade (see http://www.jclark.com/) errors is simple: >
  :set efm=jade:%f:%l:%c:%t:%m
<
						*errorformat-LaTeX*
The following is an example how an 'errorformat' string can be specified
for the (La)TeX typesetting system which displays error messages over
multiple lines.  The output of ":clist" and ":cc" etc. commands displays
multi-lines in a single line, leading white space is removed.
It should be easy to adopt the above LaTeX errorformat to any compiler output
consisting of multi-line errors.

The commands can be placed in a |vimrc| file or some other Vim script file,
e.g. a script containing LaTeX related stuff which is loaded only when editing
LaTeX sources.
Make sure to copy all lines of the example (in the given order), afterwards
remove the comment lines.  For the '\' notation at the start of some lines see
|line-continuation|.

		First prepare 'makeprg' such that LaTeX will report multiple
		errors; do not stop when the first error has occurred: >
 :set makeprg=latex\ \\\\nonstopmode\ \\\\input\\{$*}
<
		Start of multi-line error messages: >
 :set efm=%E!\ LaTeX\ %trror:\ %m,
	\%E!\ %m,
<		Start of multi-line warning messages; the first two also
		include the line number.  Meaning of some regular expressions:
		  - "%.%#"  (".*")   matches a (possibly empty) string
		  - "%*\\d" ("\d\+") matches a number >
	\%+WLaTeX\ %.%#Warning:\ %.%#line\ %l%.%#,
	\%+W%.%#\ at\ lines\ %l--%*\\d,
	\%WLaTeX\ %.%#Warning:\ %m,
<		Possible continuations of error/warning messages; the first
		one also includes the line number: >
	\%Cl.%l\ %m,
	\%+C\ \ %m.,
	\%+C%.%#-%.%#,
	\%+C%.%#[]%.%#,
	\%+C[]%.%#,
	\%+C%.%#%[{}\\]%.%#,
	\%+C<%.%#>%.%#,
	\%C\ \ %m,
<		Lines that match the following patterns do not contain any
		important information; do not include them in messages: >
	\%-GSee\ the\ LaTeX%m,
	\%-GType\ \ H\ <return>%m,
	\%-G\ ...%.%#,
	\%-G%.%#\ (C)\ %.%#,
	\%-G(see\ the\ transcript%.%#),
<		Generally exclude any empty or whitespace-only line from
		being displayed: >
	\%-G\\s%#,
<		The LaTeX output log does not specify the names of erroneous
		source files per line; rather they are given globally,
		enclosed in parentheses.
		The following patterns try to match these names and store
		them in an internal stack.  The patterns possibly scan over
		the same input line (one after another), the trailing "%r"
		conversion indicates the "rest" of the line that will be
		parsed in the next go until the end of line is reached.

		Overread a file name enclosed in '('...')'; do not push it
		on a stack since the file apparently does not contain any
		error: >
	\%+O(%f)%r,
<		Push a file name onto the stack.  The name is given after '(': >
	\%+P(%f%r,
	\%+P\ %\\=(%f%r,
	\%+P%*[^()](%f%r,
	\%+P[%\\d%[^()]%#(%f%r,
<		Pop the last stored file name when a ')' is scanned: >
	\%+Q)%r,
	\%+Q%*[^()])%r,
	\%+Q[%\\d%*[^()])%r

Note that in some cases file names in the LaTeX output log cannot be parsed
properly.  The parser might have been messed up by unbalanced parentheses
then.  The above example tries to catch the most relevant cases only.
You can customize the given setting to suit your own purposes, for example,
all the annoying "Overfull ..." warnings could be excluded from being
recognized as an error.
Alternatively to filtering the LaTeX compiler output, it is also possible
to directly read the *.log file that is produced by the [La]TeX compiler.
This contains even more useful information about possible error causes.
However, to properly parse such a complex file, an external filter should
be used.  See the description further above how to make such a filter known
by Vim.

						*errorformat-Perl*
In $VIMRUNTIME/tools you can find the efm_perl.pl script, which filters Perl
error messages into a format that quickfix mode will understand.  See the
start of the file about how to use it.  (This script is deprecated, see
|compiler-perl|.)

=============================================================================
10. Customizing the quickfix window		*quickfix-window-function*

The default format for the lines displayed in the quickfix window and location
list window is:

    <filename>|<lnum> col <col>|<text>

The values displayed in each line correspond to the "bufnr", "lnum", "col" and
"text" fields returned by the |getqflist()| function.

For some quickfix/location lists, the displayed text needs to be customized.
For example, if only the filename is present for a quickfix entry, then the
two "|" field separator characters after the filename are not needed.  Another
use case is to customize the path displayed for a filename. By default, the
complete path (which may be too long) is displayed for files which are not
under the current directory tree. The file path may need to be simplified to a
common parent directory.

The displayed text can be customized by setting the 'quickfixtextfunc' option
to a Vim function.  This function will be called with a dict argument and
should return a List of strings to be displayed in the quickfix or location
list window. The dict argument will have the following fields:

    quickfix	set to 1 when called for a quickfix list and 0 when called for
		a location list.
    winid	for a location list, set to the id of the window with the
		location list. For a quickfix list, set to 0. Can be used in
		getloclist() to get the location list entry.
    id		quickfix or location list identifier
    start_idx	index of the first entry for which text should be returned
    end_idx	index of the last entry for which text should be returned

The function should return a single line of text to display in the quickfix
window for each entry from start_idx to end_idx. The function can obtain
information about the entries using the |getqflist()| function and specifying
the quickfix list identifier "id". For a location list, getloclist() function
can be used with the "winid" argument. If an empty list is returned, then the
default format is used to display all the entries. If an item in the returned
list is an empty string, then the default format is used to display the
corresponding entry.

If a quickfix or location list specific customization is needed, then the
'quickfixtextfunc' attribute of the list can be set using the |setqflist()| or
|setloclist()| function. This overrides the global 'quickfixtextfunc' option.

The example below displays the list of old files (|v:oldfiles|) in a quickfix
window. As there is no line, column number and error text information
associated with each entry, the 'quickfixtextfunc' function returns only the
filename.
Example: >
    " create a quickfix list from v:oldfiles
    call setqflist([], ' ', {'lines' : v:oldfiles, 'efm' : '%f',
					\ 'quickfixtextfunc' : 'QfOldFiles'})
    func QfOldFiles(info)
	" get information about a range of quickfix entries
	let items = getqflist({'id' : a:info.id, 'items' : 1}).items
	let l = []
	for idx in range(a:info.start_idx - 1, a:info.end_idx - 1)
	    " use the simplified file name
	  call add(l, fnamemodify(bufname(items[idx].bufnr), ':p:.'))
	endfor
	return l
    endfunc
<

 vim:tw=78:ts=8:noet:ft=help:norl:
