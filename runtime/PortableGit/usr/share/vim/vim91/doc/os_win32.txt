*os_win32.txt*  For Vim version 9.1.  Last change: 2024 May 11


		  VIM REFERENCE MANUAL    by <PERSON>


						*win32* *Win32* *MS-Windows*
This file documents the idiosyncrasies of the Win32 version of Vim.

The Win32 version of Vim works on Windows 7, 8, 10 and 11.  There are both
console and GUI versions.

If you have Windows XP or Vista then Vim 9.0 up to patch level 495 can be
used.

The 32 bit version also runs on 64 bit MS-Windows systems.

1. Known problems		|win32-problems|
2. Startup			|win32-startup|
3. Restore screen contents	|win32-restore|
4. Using the mouse		|win32-mouse|
5. Running under Windows 95	|win32-win95|
6. Running under Windows 3.1	|win32-win3.1|
7. Installation package		|win32-installer|
8. Win32 mini FAQ		|win32-faq|

Additionally, there are a number of common Win32 and DOS items:
File locations			|dos-locations|
Using backslashes		|dos-backslash|
Standard mappings		|dos-standard-mappings|
Screen output and colors	|dos-colors|
File formats			|dos-file-formats|
:cd command			|dos-:cd|
Interrupting			|dos-CTRL-Break|
Temp files			|dos-temp-files|
Shell option default		|dos-shell|
PowerShell defaults		|dos-powershell|

Win32 GUI			|gui-w32|

Credits:
The Win32 version was written by <PERSON> <<EMAIL>>.
The original Windows NT port was done by Roger Knobbe <<EMAIL>>.
The GUI version was made by George V. Reilly and Robert Webb.

For compiling see "src/INSTALLpc.txt".			*win32-compiling*

							*WSL*
When using Vim on WSL (Windows Subsystem for Linux) the remarks here do not
apply, `has('win32')` will return false then.  In case you need to know
whether Vim is running on WSL you can use `exists('$WSLENV')`.

==============================================================================
1. Known problems					*win32-problems*

When doing file name completion, Vim also finds matches for the short file
name.  But Vim will still find and use the corresponding long file name.  For
example, if you have the long file name "this_is_a_test" with the short file
name "this_i~1", the command ":e *1" will start editing "this_is_a_test".

==============================================================================
2. Startup						*win32-startup*

Current directory					*win32-curdir*

If Vim is started with a single file name argument, and it has a full path
(starts with "x:\"), Vim assumes it was started from the file explorer and
will set the current directory to where that file is.  To avoid this when
typing a command to start Vim, use a forward slash instead of a backslash.
Example: >

	vim c:\text\files\foo.txt

Will change to the "C:\text\files" directory. >

	vim c:/text\files\foo.txt

Will use the current directory.


Term option						*win32-term*

The only kind of terminal type that the Win32 version of Vim understands is
"win32", which is built-in.  If you set 'term' to anything else, you will
probably get very strange behavior from Vim.  Therefore Vim does not obtain
the default value of 'term' from the environment variable "TERM".

$PATH							*win32-PATH*

The directory of the Vim executable is appended to $PATH.  This is mostly to
make "!xxd" work, as it is in the Tools menu.  And it also means that when
executable() returns 1 the executable can actually be executed.

Command line arguments					*win32-cmdargs*

Analysis of a command line into parameters is not standardised in MS-Windows.
Vim and gvim used to use different logic to parse it (before 7.4.432), and the
logic was also depended on what it was compiled with.  Now Vim and gvim both
use the CommandLineToArgvW() Win32 API, so they behave in the same way.

The basic rules are:					*win32-backslashes*
      a) A parameter is a sequence of graphic characters.
      b) Parameters are separated by white space.
      c) A parameter can be enclosed in double quotes to include white space.
      d) A sequence of zero or more backslashes (\) and a double quote (")
	is special.  The effective number of backslashes is halved, rounded
	down.  An even number of backslashes reverses the acceptability of
	spaces and tabs, an odd number of backslashes produces a literal
	double quote.

So:
	"	is a special double quote
	\"	is a literal double quote
	\\"	is a literal backslash and a special double quote
	\\\"	is a literal backslash and a literal double quote
	\\\\"	is 2 literal backslashes and a special double quote
	\\\\\"	is 2 literal backslashes and a literal double quote
	etc.

Example: >
	vim "C:\My Music\freude" +"set ignorecase" +/"\"foo\\" +\"bar\\\"

opens "C:\My Music\freude" and executes the line mode commands: >
	set ignorecase; /"foo\ and /bar\"

These rules are also described in the reference of the CommandLineToArgvW API:
    https://msdn.microsoft.com/en-us/library/windows/desktop/bb776391.aspx

							*win32-quotes*
There are additional rules for quotes (which are not well documented).
As described above, quotes inside a file name (or any other command line
argument) can be escaped with a backslash.  E.g. >
	vim -c "echo 'foo\"bar'"

Alternatively use three quotes to get one: >
	vim -c "echo 'foo"""bar'"

The quotation rules are:

1. A `"` starts quotation.
2. Another `"` or `""` ends quotation. If the quotation ends with `""`, a `"`
   is produced at the end of the quoted string.

Examples, with [] around an argument:
        "foo"           -> [foo]
        "foo""          -> [foo"]
        "foo"bar        -> [foobar]
        "foo" bar       -> [foo], [bar]
        "foo""bar       -> [foo"bar]
        "foo"" bar      -> [foo"], [bar]
        "foo"""bar"     -> [foo"bar]


==============================================================================
3. Restore screen contents				*win32-restore*

When 'restorescreen' is set (which is the default), Vim will restore the
original contents of the console when exiting or when executing external
commands.  If you don't want this, use ":set nors".	|'restorescreen'|

==============================================================================
4. Using the mouse					*win32-mouse*

The Win32 version of Vim supports using the mouse.  If you have a two-button
mouse, the middle button can be emulated by pressing both left and right
buttons simultaneously - but note that in the Win32 GUI, if you have the right
mouse button pop-up menu enabled (see 'mouse'), you should err on the side of
pressing the left button first.				|mouse-using|

When the mouse doesn't work, try disabling the "Quick Edit Mode" feature of
the console.

==============================================================================
5. Running under Windows 95				*win32-win95*
					*windows95* *windows98* *windowsme*
Windows 95/98/ME support was removed in patch 8.0.0029  If you want to use it
you will need to get a version older than that.

==============================================================================
6. Running under Windows 3.1				*win32-win3.1*

				*win32s* *windows-3.1* *gui-w32s* *win16*
There was a special version of gvim that runs under Windows 3.1 and 3.11.
Support was removed in patch 7.4.1364.

==============================================================================
7. Installation package					*win32-installer*

A simple installer for windows is available at http://www.vim.org/download.php
(stable version) and nightly builds are also available at
https://github.com/vim/vim-win32-installer/releases/

The nightly builds include 32bit and 64bit builds, have most features enabled
and usually also contain an extra cryptographic signed installer, so Windows
will not complain.

To use the installer, simply run the exe file.  The following switches are
also supported: >

    gvim_<version>.exe /S           -> silent install without any dialogues
    gvim_<version>.exe /D=C:\vim    -> Install into directory c:\vim
                                    -> /D must be the last argument
    gvim_<version>.exe /S /D=c:\vim -> silent install into c:\vim
<
The default installation directory can alternatively be given by setting the
$VIM environment variable.

==============================================================================
8. Win32 mini FAQ					*win32-faq*

Q. How do I change the font?
A. In the GUI version, you can use the 'guifont' option.  Example: >
	:set guifont=Lucida_Console:h15:cDEFAULT
<  In the console version, you need to set the font of the console itself.
   You cannot do this from within Vim.

Q. How do I type dead keys on Windows NT?
A. Dead keys work on NT 3.51.  Just type them as you would in any other
   application.
   On NT 4.0, you need to make sure that the default locale (set in the
   Keyboard part of the Control Panel) is the same as the currently active
   locale.  Otherwise the NT code will get confused and crash!  This is a NT
   4.0 problem, not really a Vim problem.

Q. I'm using Vim to edit a symbolically linked file on a Unix NFS file server.
   When I write the file, Vim does not "write through" the symlink.  Instead,
   it deletes the symbolic link and creates a new file in its place.  Why?
A. On Unix, Vim is prepared for links (symbolic or hard).  A backup copy of
   the original file is made and then the original file is overwritten.  This
   assures that all properties of the file remain the same.  On non-Unix
   systems, the original file is renamed and a new file is written.  Only the
   protection bits are set like the original file.  However, this doesn't work
   properly when working on an NFS-mounted file system where links and other
   things exist.  The only way to fix this in the current version is not
   making a backup file, by ":set nobackup nowritebackup"     |'writebackup'|

Q. I'm using Vim to edit a file on a Unix file server through Samba.  When I
   write the file, the owner of the file is changed.  Why?
A. When writing a file Vim renames the original file, this is a backup (in
   case writing the file fails halfway).  Then the file is written as a new
   file.  Samba then gives it the default owner for the file system, which may
   differ from the original owner.
   To avoid this set the 'backupcopy' option to "yes".  Vim will then make a
   copy of the file for the backup, and overwrite the original file.  The
   owner isn't changed then.

Q. How do I get to see the output of ":make" while it's running?
A. Basically what you need is to put a tee program that will copy its input
   (the output from make) to both stdout and to the errorfile.  You can find a
   copy of tee (and a number of other GNU tools) at
   http://gnuwin32.sourceforge.net or http://unxutils.sourceforge.net
   Alternatively, try the more recent Cygnus version of the GNU tools at
   http://www.cygwin.com
   When you do get a copy of tee, you'll need to add >
	:set shellpipe=\|\ tee
<  to your _vimrc.

Q. I'm storing files on a remote machine that works with VisionFS, and files
   disappear!
A. VisionFS can't handle certain dot (.) three letter extension file names.
   SCO declares this behavior required for backwards compatibility with 16bit
   DOS/Windows environments.  The two commands below demonstrate the behavior:
>
	echo Hello > file.bat~
	dir > file.bat
<
   The result is that the "dir" command updates the "file.bat~" file, instead
   of creating a new "file.bat" file.  This same behavior is exhibited in Vim
   when editing an existing file named "foo.bat" because the default behavior
   of Vim is to create a temporary file with a '~' character appended to the
   name.  When the file is written, it winds up being deleted.

   Solution: Add this command to your _vimrc file: >
	:set backupext=.temporary

Q. How do I change the blink rate of the cursor?
A. You can't!  This is a limitation of the NT console.  NT 5.0 is reported to
   be able to set the blink rate for all console windows at the same time.

							*:!start*
Q. How can I asynchronously run an external command or program, or open a
   document or URL with its default program?
A. When using :! to run an external command, you can run it with "start". For
   example, to run notepad: >
	:!start notepad
<   To open "image.jpg" with the default image viewer: >
        :!start image.jpg
<   To open the folder of the current file in Windows Explorer: >
        :!start %:h
<   To open the Vim home page with the default browser: >
        :!start http://www.vim.org/
<
   Using "start" stops Vim switching to another screen, opening a new console,
   or waiting for the program to complete; it indicates that you are running a
   program that does not affect the files you are editing.  Programs begun
   with :!start do not get passed Vim's open file handles, which means they do
   not have to be closed before Vim.
   To avoid this special treatment, use ":! start".
   There are two optional arguments (see the next Q):
       /min  the window will be minimized
       /b    no console window will be opened
   You can use only one of these flags at a time.  A second one will be
   treated as the start of the command.
						*windows-asynchronously*
Q. How do I avoid getting a window for programs that I run asynchronously?
A. You have three possible solutions depending on what you want:
   1) You may use the /min flag in order to run program in a minimized state
      with no other changes. It will work equally for console and GUI
      applications.
   2) You can use the /b flag to run console applications without creating a
      console window for them (GUI applications are not affected). But you
      should use this flag only if the application you run doesn't require any
      input.  Otherwise it will get an EOF error because its input stream
      (stdin) would be redirected to \\.\NUL (stdout and stderr too).
   3) Set the '!' flag in the 'guioptions' option |'go-!'|. This will make Vim
      run the "start" command inside Vims terminal window and not open a
      console window.

   Example for a console application, run Exuberant ctags: >
        :!start /min ctags -R .
<   When it has finished you should see file named "tags" in your current
   directory.  You should notice the window title blinking on your taskbar.
   This is more noticeable for commands that take longer.
   Now delete the "tags" file and run this command: >
        :!start /b ctags -R .
<   You should have the same "tags" file, but this time there will be no
   blinking on the taskbar.
   Example for a GUI application: >
        :!start /min notepad
        :!start /b notepad
<   The first command runs notepad minimized and the second one runs it
   normally.

						*windows-icon*
Q. I don't like the Vim icon, can I change it?
A. Yes, place your favorite icon in bitmaps/vim.ico in a directory of
   'runtimepath'.  For example ~/vimfiles/bitmaps/vim.ico.


 vim:tw=78:ts=8:noet:ft=help:norl:
