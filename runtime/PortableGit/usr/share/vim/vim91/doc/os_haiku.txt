*os_haiku.txt*	For Vim version 9.1.  Last change: 2020 May 13


		  VIM REFERENCE MANUAL    by <PERSON>


							*Hai<PERSON>*
This file contains the particularities for the Haiku version of Vim.  For
matters not discussed in this file, <PERSON>im behaves very much like the Unix
|os_unix.txt| version.

Haiku is an open-source operating system inspired by BeOS, that specifically
targets personal computing.

 1. General			|haiku-general|
 2. Compiling Vim		|haiku-compiling|
 3. The Haiku GUI		|haiku-gui|
 4. The $VIM directory		|haiku-vimdir|
 5. The $USER_SETTINGS_DIR
    directory			|haiku-user-settings-dir|
 6. Drag & Drop			|haiku-dragndrop|
 7. Single Launch vs. Multiple
    Launch			|haiku-launch|
 8. Fonts			|haiku-fonts|
 9. The meta key modifier	|haiku-meta|
10. Mouse key mappings		|haiku-mouse|
11. Color names			|haiku-colors|
12. GUI Toolbar Images		|haiku-toolbar-images|
13. Credits			|haiku-support-credits|
14. Bugs & to-do		|haiku-bugs|


1. General						*haiku-general*

The default syntax highlighting mostly works with different foreground colors
to highlight items.  This works best if you set your Terminal window to a
darkish background and light letters.  Some middle-grey background (for
instance (r,g,b)=(168,168,168)) with black letters also works nicely.


2. Compiling Vim					*haiku-compiling*

Vim can be compiled using the standard configure/make approach. Running
./configure without any arguments or passing --enable-gui=haiku, will compile
vim with the Haiku GUI support.  Run ./configure --help , to find out other
features you can enable/disable.

Haiku uses "ncurses6" as its terminal library, therefore you need to have
"ncurses6_devel" package installed from HaikuDepot in order to configure
the Haiku build.  Just append "--with-tlib=ncurses" to ./configure command.

Now you should use "make" to compile Vim, then "make install" to install it.
For seamless integration into Haiku, the GUI-less vim binary should be
additionally installed over the GUI version.  Typical build commands are:

  ./configure --prefix=`finddir B_SYSTEM_NONPACKAGED_DIRECTORY` \
    --datarootdir=`finddir B_SYSTEM_NONPACKAGED_DATA_DIRECTORY` \
    --mandir=`finddir B_SYSTEM_NONPACKAGED_DIRECTORY`/documentation/man \
    --with-tlib=ncurses
  make clean
  make install

  ./configure --prefix=`finddir B_SYSTEM_NONPACKAGED_DIRECTORY`  \
    --datarootdir=`finddir B_SYSTEM_NONPACKAGED_DATA_DIRECTORY` \
    --mandir=`finddir B_SYSTEM_NONPACKAGED_DIRECTORY`/documentation/man \
    --with-tlib=ncurses \
    --disable-gui
  make clean
  make install


3. The Haiku GUI					*haiku-gui*

Normally Vim starts with the GUI if you start it as gvim or vim -g.  The vim
version with GUI tries to determine if it was started from the Tracker instead
of the Terminal, and if so, uses the GUI anyway.  However, the current detection
scheme is fooled if you use the command "vim - </dev/null".

Stuff that does not work yet:

- Mouse up events are not generated when outside the window.  You can notice
  this when selecting text and moving the cursor outside the window, then
  letting go of the mouse button.  Another way is when you drag the scrollbar
  and do the same thing.  Because Vim still thinks you are still playing with
  the scrollbar it won't change it itself.  There is a workaround which kicks
  in when the window is activated or deactivated (so it works best with focus-
  follows-mouse turned on).
- The cursor does not flash.


4. The $VIM directory					*haiku-vimdir*

$VIM is the symbolic name for the place where Vim's support files are stored.
The default value for $VIM is set at compile time and can be determined with:

  :version

The normal value is /boot/system/data/vim for Haikuports version,
/boot/system/non-packaged/data/vim for manual builds.  If you don't like it
you can set the VIM environment variable to override this, or set 'helpfile'
in your .vimrc: >

  :if version >= 500
  :    set helpfile=~/vim/runtime/doc/help.txt
  :    syntax on
  :endif


5. The $USER_SETTINGS_DIR directory		*haiku-user-settings-dir*

$USER_SETTINGS_DIR is the symbolic name for the place where Haiku
configuration and settings files are stored.

The normal value is /boot/home/<USER>/settings.


6. Drag & Drop						*haiku-dragndrop*

You can drop files and directories on either the Vim icon (starts a new Vim
session, unless you use the File Types application to set Vim to be "Single
Launch") or on the Vim window (starts editing the files).  Dropping a folder
sets Vim's current working directory |:cd| |:pwd|.  If you drop files or
folders with either SHIFT key pressed, Vim changes directory to the folder
that contains the first item dropped.  When starting Vim, there is no need to
press shift: Vim behaves as if you do.

Files dropped set the current argument list. |argument-list|


7. Single Launch vs. Multiple Launch			*haiku-launch*

As distributed Vim's Application Flags (as seen in the FileTypes preference)
are set to Multiple Launch.  If you prefer, you can set them to Single Launch
instead.  Attempts to start a second copy of Vim will cause the first Vim to
open the files instead.  This works from the Tracker but also from the command
line.  In the latter case, non-file (option) arguments are not supported.
Another drawback of the Single Launch is silent ignore of "Open With ..."
requests by vim instance that running as non-GUI application even GUI support
was compiled in. Vim instance running with GUI has no such problems.

NB: Only the GUI version has a BApplication (and hence Application Flags).
This section does not apply to the GUI-less version, should you compile one.


8. Fonts						*haiku-fonts*

Set fonts with >

  :set guifont=DejaVu_Sans_Mono/Book/12

where the first part is the font family, the second part the style, and the
third part the size.  You can use underscores instead of spaces in family and
style.

Best results are obtained with monospaced fonts.  Vim attempts to use all
fonts in B_FIXED_SPACING mode but apparently this does not work for
proportional fonts (despite what the BeBook says).

To verify which encodings are supported by the current font give the >

  :digraphs

command, which lists a bunch of characters with their ISO Latin 1 encoding.
If, for instance, there are "box" characters among them, or the last character
isn't a dotted-y, then for this font the encoding does not work.

If the font you specify is unavailable, you get the system fixed font.

GUI Font Selection Dialog is available at giving the:

  :set guifont=*

command.


9. The meta key modifier				*haiku-meta*

The META key modifier is obtained by the left or right OPTION keys.  This is
because the ALT (aka COMMAND) keys are not passed to applications.


10. Mouse key mappings					*haiku-mouse*

Vim calls the various mouse buttons LeftMouse, MiddleMouse and RightMouse.  If
you use the default Mouse preference settings these names indeed correspond to
reality.  Vim uses this mapping:

    Button 1 -> LeftMouse,
    Button 2 -> RightMouse,
    Button 3 -> MiddleMouse.

If your mouse has fewer than 3 buttons you can provide your own mapping from
mouse clicks with modifier(s) to other mouse buttons.  See the file
$VIM/macros/swapmous.vim for an example.		|gui-mouse-mapping|


11. Color names						*haiku-colors*

Vim has a number of color names built-in. Additional names can be defined in
|v:colornames|. See |:colorscheme| for details.


12. GUI Toolbar Images					*haiku-toolbar-images*

Alternative set of toolbar images should be the PNG image of any height you
like. Image width is calculated to contain at least 32 buttons in one-row
cells.
The image should be stored under the name $VIRUNTIME/bitmaps/builtin-tools.png
More info about the buttons assignment are at |builtin-tools|.


13. Credits						*haiku-support-credits*

Haiku port is based on work done for BeOS version by many people
 - BeBox GUI support Copyright 1998 by Olaf Seibert;
 - Ported to R4 by Richard Offer <<EMAIL>> Jul 99;
 - Those who contributed, not listed above but not forgotten;
 - Haiku support by Siarzhuk Zharski <<EMAIL>> Apr-Mai 2009.

All the changes and patches released under vim-license.

Thank you, all!


14. Bugs & to-do					*haiku-bugs*

The port is under development now and far away from the perfect state. For bug
reports, patches and wishes, please use the Vim mailing list or Vim Github
repository.

Mailing list: https://www.vim.org/maillist.php
Vim Github repository: https://github.com/vim/vim


 vim:tw=78:ts=8:ft=help:norl:
