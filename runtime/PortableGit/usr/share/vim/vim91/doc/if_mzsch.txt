*if_mzsch.txt*  For Vim version 9.1.  Last change: 2020 Oct 14


		  VIM REFERENCE MANUAL    by <PERSON>


The MzScheme Interface to Vim				*mzscheme* *MzScheme*

1. Commands				|mzscheme-commands|
2. Examples				|mzscheme-examples|
3. Threads				|mzscheme-threads|
4. Vim access from MzScheme		|mzscheme-vim|
5. mzeval() Vim function		|mzscheme-mzeval|
6. Using Function references		|mzscheme-funcref|
7. Dynamic loading			|mzscheme-dynamic|
8. MzScheme setup			|mzscheme-setup|

{only available when Vim was compiled with the |+mzscheme| feature}

Based on the work of <PERSON> Fulgham.
Dynamic loading added by Sergey <PERSON>

MzScheme and PLT Scheme names have been rebranded as Racket. For more
information please check http://racket-lang.org

Futures and places of Racket version 5.x up to and including 5.3.1 do not
work correctly with processes created by Vim.
The simplest solution is to build Racket on your own with these features
disabled: >
  ./configure --disable-futures --disable-places --prefix=your-install-prefix

To speed up the process, you might also want to use --disable-gracket and
--disable-docs

==============================================================================
1. Commands						*mzscheme-commands*

							*:mzscheme* *:mz*
:[range]mz[scheme] {stmt}
			Execute MzScheme statement {stmt}.

:[range]mz[scheme] << [trim] [{endmarker}]
{script}
{endmarker}
			Execute inlined MzScheme script {script}.
			Note: This command doesn't work when the MzScheme
			feature wasn't compiled in.  To avoid errors, see
			|script-here|.

			If [endmarker] is omitted from after the "<<", a dot
			'.' must be used after {script}, like for the
			|:append| and |:insert| commands.  Refer to
			|:let-heredoc| for more information.


							*:mzfile* *:mzf*
:[range]mzf[ile] {file}	Execute the MzScheme script in {file}.

All of these commands do essentially the same thing - they execute a piece of
MzScheme code, with the "current range" set to the given line
range.

In the case of :mzscheme, the code to execute is in the command-line.
In the case of :mzfile, the code to execute is the contents of the given file.

MzScheme interface defines exception exn:vim, derived from exn.
It is raised for various Vim errors.

During compilation, the MzScheme interface will remember the current MzScheme
collection path. If you want to specify additional paths use the
'current-library-collection-paths' parameter. E.g., to cons the user-local
MzScheme collection path: >
    :mz << EOF
    (current-library-collection-paths
	(cons
	    (build-path (find-system-path 'addon-dir) (version) "collects")
	    (current-library-collection-paths)))
    EOF
<

All functionality is provided through module vimext.

The exn:vim is available without explicit import.

To avoid clashes with MzScheme, consider using prefix when requiring module,
e.g.: >
	:mzscheme (require (prefix vim- vimext))
<
All the examples below assume this naming scheme.

							*mzscheme-sandbox*
When executed in the |sandbox|, access to some filesystem and Vim interface
procedures is restricted.

==============================================================================
2. Examples						*mzscheme-examples*
>
	:mzscheme (display "Hello")
	:mz (display (string-append "Using MzScheme version " (version)))
	:mzscheme (require (prefix vim- vimext)) ; for MzScheme < 4.x
	:mzscheme (require (prefix-in vim- 'vimext)) ; MzScheme 4.x
	:mzscheme (vim-set-buff-line 10 "This is line #10")

To see what version of MzScheme you have: >
	:mzscheme (display (version))
<
Inline script usage: >
	function! <SID>SetFirstLine()
	    :mz << EOF
	    (display "!!!")
	    (require (prefix vim- vimext))
	    ; for newer versions (require (prefix-in vim- 'vimext))
	    (vim-set-buff-line 1 "This is line #1")
	    (vim-beep)
	EOF
	endfunction

	nmap <F9> :call <SID>SetFirstLine() <CR>
<
File execution: >
	:mzfile supascript.scm
<
Vim exception handling: >
	:mz << EOF
	(require (prefix vim- vimext))
	; for newer versions (require (prefix-in vim- 'vimext))
	(with-handlers
	  ([exn:vim? (lambda (e) (display (exn-message e)))])
	  (vim-eval "nonsense-string"))
	EOF
<
Auto-instantiation of vimext module (can be placed in your |vimrc|): >
    function! MzRequire()
	:redir => l:mzversion
	:mz (version)
	:redir END
	if strpart(l:mzversion, 1, 1) < "4"
	    " MzScheme versions < 4.x:
	    :mz (require (prefix vim- vimext))
	else
	    " newer versions:
	    :mz (require (prefix-in vim- 'vimext))
	endif
    endfunction

    if has("mzscheme")
	silent call MzRequire()
    endif
<
==============================================================================
3. Threads						*mzscheme-threads*

The MzScheme interface supports threads. They are independent from OS threads,
thus scheduling is required. The option 'mzquantum' determines how often
Vim should poll for available MzScheme threads.
NOTE
Thread scheduling in the console version of Vim is less reliable than in the
GUI version.

==============================================================================
4. Vim access from MzScheme				*mzscheme-vim*

							*mzscheme-vimext*
The 'vimext' module provides access to procedures defined in the MzScheme
interface.

Common
------
    (command {command-string})	    Perform the vim ":Ex" style command.
    (eval {expr-string})	    Evaluate the vim expression into
				    respective MzScheme object: |Lists| are
				    represented as Scheme lists,
				    |Dictionaries| as hash tables,
				    |Funcref|s as functions (see also
				    |mzscheme-funcref|)
				    NOTE the name clashes with MzScheme eval,
				    use module qualifiers to overcome this.
    (range-start)		    Start/End of the range passed with
    (range-end)			    the Scheme command.
    (beep)			    beep
    (get-option {option-name} [buffer-or-window]) Get Vim option value (either
				    local or global, see set-option).
    (set-option {string} [buffer-or-window])
				    Set a Vim option. String must have option
				    setting form (like optname=optval, or
				    optname+=optval, etc.) When called with
				    {buffer} or {window} the local option will
				    be set. The symbol 'global can be passed
				    as {buffer-or-window}. Then |:setglobal|
				    will be used.

Buffers							 *mzscheme-buffer*
-------
    (buff? {object})		    Is object a buffer?
    (buff-valid? {object})	    Is object a valid buffer? (i.e.
				    corresponds to the real Vim buffer)
    (get-buff-line {linenr} [buffer])
				    Get line from a buffer.
    (set-buff-line {linenr} {string} [buffer])
				    Set a line in a buffer. If {string} is #f,
				    the line gets deleted.  The [buffer]
				    argument is optional. If omitted, the
				    current buffer will be used.
    (get-buff-line-list {start} {end} [buffer])
				    Get a list of lines in a buffer. {Start}
				    and {end} are 1-based and inclusive.
    (set-buff-line-list {start} {end} {string-list} [buffer])
				    Set a list of lines in a buffer. If
				    string-list is #f or null, the lines get
				    deleted. If a list is shorter than
				    {end}-{start} the remaining lines will
				    be deleted.
    (get-buff-name [buffer])	    Get a buffer's text name.
    (get-buff-num [buffer])	    Get a buffer's number.
    (get-buff-size [buffer])	    Get buffer line count.
    (insert-buff-line-list {linenr} {string/string-list} [buffer])
				    Insert a list of lines into a buffer after
				    {linenr}. If {linenr} is 0, lines will be
				    inserted at start.
    (curr-buff)			    Get the current buffer. Use other MzScheme
				    interface procedures to change it.
    (buff-count)		    Get count of total buffers in the editor.
    (get-next-buff [buffer])	    Get next buffer.
    (get-prev-buff [buffer])	    Get previous buffer. Return #f when there
				    are no more buffers.
    (open-buff {filename})	    Open a new buffer (for file "name")
    (get-buff-by-name {buffername}) Get a buffer by its filename or #f
					if there is no such buffer.
    (get-buff-by-num {buffernum})   Get a buffer by its number (return #f if
				    there is no buffer with this number).

Windows							    *mzscheme-window*
------
    (win? {object})		    Is object a window?
    (win-valid? {object})	    Is object a valid window (i.e. corresponds
				    to the real Vim window)?
    (curr-win)			    Get the current window.
    (win-count)			    Get count of windows.
    (get-win-num [window])	    Get window number.
    (get-win-by-num {windownum})    Get window by its number.
    (get-win-buffer	[window])   Get the buffer for a given window.
    (get-win-height [window])
    (set-win-height {height} [window])  Get/Set height of window.
    (get-win-width [window])
    (set-win-width {width} [window])Get/Set width of window.
    (get-win-list [buffer])	    Get list of windows for a buffer.
    (get-cursor [window])	    Get cursor position in a window as
				    a pair (linenr . column).
    (set-cursor (line . col) [window])  Set cursor position.

==============================================================================
5. mzeval() Vim function				    *mzscheme-mzeval*

To facilitate bi-directional interface, you can use |mzeval()| function to
evaluate MzScheme expressions and pass their values to Vim script.

==============================================================================
6. Using Function references				    *mzscheme-funcref*

MzScheme interface allows use of |Funcref|s so you can call Vim functions
directly from Scheme. For instance: >
    function! MyAdd2(arg)
	return a:arg + 2
    endfunction
    mz (define f2 (vim-eval "function(\"MyAdd2\")"))
    mz (f2 7)
< or : >
    :mz (define indent (vim-eval "function('indent')"))
    " return Vim indent for line 12
    :mz (indent 12)
<

==============================================================================
7. Dynamic loading				*mzscheme-dynamic* *E815*

On MS-Windows the MzScheme libraries can be loaded dynamically. The |:version|
output then includes |+mzscheme/dyn|.

This means that Vim will search for the MzScheme DLL files only when needed.
When you don't use the MzScheme interface you don't need them, thus you can
use Vim without these DLL files.
NOTE: Newer version of MzScheme (Racket) require earlier (trampolined)
initialisation via scheme_main_setup.  So Vim always loads the MzScheme DLL at
startup if possible.  This may make Vim startup slower.

To use the MzScheme interface the MzScheme DLLs must be in your search path.
In a console window type "path" to see what directories are used.

On MS-Windows the options 'mzschemedll' and 'mzschemegcdll' are used for the
name of the library to load.  The initial value is specified at build time.

The version of the DLL must match the MzScheme version Vim was compiled with.
For MzScheme version 209 they will be "libmzsch209_000.dll" and
"libmzgc209_000.dll". To know for sure look at the output of the ":version"
command, look for -DDYNAMIC_MZSCH_DLL="something" and
-DDYNAMIC_MZGC_DLL="something" in the "Compilation" info.

For example, if MzScheme (Racket) is installed at C:\Racket63, you may need
to set the environment variable as the following: >

  PATH=%PATH%;C:\Racket63\lib
  PLTCOLLECTS=C:\Racket63\collects
  PLTCONFIGDIR=C:\Racket63\etc
<
==============================================================================
8. MzScheme setup				    *mzscheme-setup* *E895*

Vim requires "racket/base" module for if_mzsch core (fallback to "scheme/base"
if it doesn't exist), "r5rs" module for test and "raco ctool" command for
building Vim.  If MzScheme did not have them, you can install them with
MzScheme's raco command:
>
  raco pkg install scheme-lib       # scheme/base module
  raco pkg install r5rs-lib         # r5rs module
  raco pkg install cext-lib         # raco ctool command
<
======================================================================
  vim:tw=78:ts=8:noet:sts=4:ft=help:norl:
