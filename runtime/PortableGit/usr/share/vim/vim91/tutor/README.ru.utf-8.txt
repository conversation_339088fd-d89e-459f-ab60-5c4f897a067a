«Учебник» — это практическое пособие для начинающих пользователей редактора Vim.

На освоение представленного материала большинству начинающих пользователей
потребуется менее часа. По окончанию курса вы сможете выполнять несложные
операции над текстом с помощью редактора Vim.

Файл, содержащий обучающие уроки, называется «tutor». Чтобы начать с ним
работать, просто наберите команду "vim tutor1" и следуйте инструкциям,
приведённым в уроках. Задания в учебнике предполагают редактирование файла,
поэтому НЕ ДЕЛАЙТЕ ЭТОГО В ОРИГИНАЛЬНОЙ КОПИИ ФАЙЛА.

Для полноценной работы с учебником вы можете использовать программу "vimtutor".
При запуске этой программы будет создана временная копия файла для работы с ним.

Я планировал добавление в учебник более развёрнутых уроков, но на это уже не
хватило времени. Если занятия вам понравились, то, пожалуйста, напишите мне об
этом и присылайте любые улучшения, которые вы сделаете.

Боб Уэр (Bob Ware), Colorado School of Mines, США, Колорадо, Голден, 80401,
(303) 273-3987
<EMAIL> <EMAIL> <EMAIL>

Все вышесказанное, касающееся первой главы учебника, также относится и 
ко второй главе учебника. За исключением того, что для открытия второй главы
необходимо воспользоваться командой "vim tutor2".

Вторая глава учебника была написана Полом Д. Паркером (Paul D. Parker).

Переводы
-----------

Файлы tutor1.xx и tutor1.xx.utf-8 для первой главы, и tutor2.xx и
tutor2.xx.utf-8 для второй главы, являются переводами учебника (где xx — код
языка). Кодировка текста в файлах tutor1.xx или tutor2.xx может быть latin1 или
другая традиционная кодировка. Если не требуется перевод в такой традиционной
кодировке, вам нужно просто подготовить файл tutor1.xx.utf-8 tutor2.xx.utf-8.
Если необходима другая кодировка текста, вы также можете сделать такой файл, его
наименование должно быть tutor1.xx.enc tutor1.xx.enc (замените «enc» на
фактическое название кодировки). Возможно, что для этого потребуется настроить
файл «tutor.vim».
Для создания файла tutor1.xx или tutor2.xx из tutor1.xx.utf-8 или
tutor2.xx.utf-8 соответственно, можно использовать команду "make". Посмотрите
файл «Makefile», чтобы получить подробную информацию. (Для некоторых языков файл
tutor1.xx.utf-8 создаётся из tutor1.xx в силу исторических причин).

[Брам Моленар (Bram Moolenaar) и др. изменили этот файл для редактора Vim]
