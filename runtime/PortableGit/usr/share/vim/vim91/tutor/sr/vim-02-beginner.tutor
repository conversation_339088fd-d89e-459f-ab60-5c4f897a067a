#             Добродошли   у   VIM   приручник

#                       ДРУГО  ПОГЛАВЉЕ

  Ево змајева: ако је ово ваш први додир са програмом vim и намеравали
  сте да уроните у уводно поглавље, молимо вас да на командној линији
  Vim едитора откуцате
~~~ cmd
        :Tutor vim-01-beginner
~~~
  или само отворите [прво поглавље](@tutor:vim-01-beginner) приручника на линку.

  Приближно време потребно за успешан завршетак овог поглавља је
  између 8 и 10 минута, у зависности од времена потрошеног на
  експериментисање.


# Лекција 2.1.1: ИМЕНОВАНИ РЕГИСТРИ

** Сачувајте истовремено две тргнуте речи, па их налепите **

  1. Померите курсор на линију испод обележену са ✓

  2. Поставите се на било које слово речи ’Пера’ и откуцајте `"ayiw`{normal}

**ПАМЋЕЊЕ** *у регистар(”) (а) (y)ank [тргни] (i)nner [унутрашњу] (w)ord [реч]*

  3. Поставите се унапред на реч ’колачића’ (`fk`{normal} или `$B`{normal}
     или `/ко`{normal} `<ЕНТЕР>`{normal}) и откуцајте `"byiw`{normal}

  4. Поставите се на било које слово речи ’Жика’ и откуцајте `ciw<CTRL-r>a<ESC>`{normal}

**ПАМЋЕЊЕ**: *(c)hange [измени] (i)nner [унутрашњу] (w)ord [реч] са <садржајем
(r)егистра> (a)*

  5. Поставите се на било које слово речи ’торте’ и откуцајте `ciw<CTRL-r>b<ESC>`{normal}


а) Од сада ће Пера бити задужен за следовања колачића
б) У том смислу, Жика ће самостално одлучивати о судбини торте

НАПОМЕНА: У регистре може и да се брише, нпр. `"sdiw`{normal} ће обрисати
          реч под курсором у регистар s.

РЕФЕРЕНЦЕ: [Регистри](registers)
           [Именовани регистри](quotea)
           [Покрети](text-objects)
           [CTRL-R](i_CTRL-R)


# Лекција 2.1.2: РЕГИСТАР ИЗРАЗА

** Умећите резултате израчунавања „у лету” **

  1. Померите курсор на линију испод обележену са ✗

  2. Поставите се на било коју цифру броја у њој

  3. Откуцајте `ciw<CTRL-r>=`{normal}60\*60\*24 `<ЕНТЕР>`{normal}

  4. У наредној линији, пређите у режим уметање и додајте данашњи датум
     помоћу `<CTRL-r>=`{normal}`system('date')`{vim} `<ЕНТЕР>`{normal}

НАПОМЕНА: Сви позиви оперативном систему зависе од система на којем се
          извршавају, нпр. на Windows употребите `system('date /t’)`{vim} или
          `:r!date /t`{vim}

Заборавио сам колико секунди има у дану, 84600 је л’ да?
Данас је: 

НАПОМЕНА: исто може да се постигне са `:pu=`{normal}`system('date')`{vim}
          или, са мање притисака на тастере `:r!date`{vim}

РЕФЕРЕНЦА: [Регистар израза](quote=)


# Лекција 2.1.3: БРОЈЧАНИ РЕГИСТРИ

** Притискајте `yy`{normal} и `dd`{normal} и уочите ефекат који имају на регистре **

  1. Померите курсор на линију испод обележену са ✓

  2. тргните нулту линију, па затим погледајте садржаје регистара са
     `:reg`{vim} `<ЕНТЕР>`{normal}

  3. обришите линију 0. са `"cdd`{normal}, па затим погледајте садржаје регистара
     (где очекујете да видите линију 0?)

  4. наставите да бришете сваку наредну линију, посматрајући успут регистре `:reg`{vim}

НАПОМЕНА: требало би да приметите како се брисања целих линија померају низ
          листу након додавања нових обрисаних линија

  5. Сада (p)aste [налепите] следеће регистре у редоследу:
     c, 7, 4, 8, 2. тј. са `"7p`{normal}


0. Ово
9. лелујаво
8. тајна
7. је
6. на
5. оси
4. једна
3. ратна
2. порука
1. поштовања


НАПОМЕНА: брисања комплетних линија (dd) много дуже остају у бројчаним
          регистрима у односу на тргања целих линија или брисања која
          користе мање покрете

РЕФЕРЕНЦА: [Бројчани регистри](quote0)


# Лекција 2.1.4: ЛЕПОТА МАРКЕРА

** Избегавање аритметике код неискусних програмера **

НАПОМЕНА: уобичајен проблем приликом писања кода је премештање великих
          делова кода. Следећа техника помаже да се спречи потреба за
          израчунавањима броја линије који је потребан у операцијама као што
          су `"a147d`{normal} или `:945,1091d a`{vim} или још горе, првобитном употребом
          `i<CTRL-r>=`{normal}1091-945 `<ЕНТЕР>`{normal}

  1. Померите курсор на линију испод обележену са ✓

  2. Пређите на прву линију функције и маркирајте је са `ma`{normal}

НАПОМЕНА: тачна позиција унутар линије НИЈЕ битна!

  3. Померите се на крај линије и онда на крај блока кода са `$%`{normal}

  4. Обришите блок у регистар са `"ad'a`{normal}

**ПАМЋЕЊЕ**: *у регистар(") (a) постави (d)eletion [брисање] од курсора до
         ЛИНИЈЕ која садржи маркер(') (a)*

  5. Налепите блок између BBB и CCC са `"ap`{normal}

НАПОМЕНА: вежбајте више пута ову операцију да би вам постала природна `ma$%"ad'a`{normal}

~~~ cmd
AAA
function itGotRealBigRealFast() {
  if ( somethingIsTrue ) {
    doIt()
  }
  // таксономија наше функције се изменила па више нема
  // азбучног смисла на својој тренутној позицији

  // замислите стотине линија кода

  // наивно бисте се померили на почетак и крај и записали или
  // запамтили оба броја линије
}
BBB
CCC
~~~

НАПОМЕНА: маркери и регистри не деле простор имена, тако да је регистар а
          потпуно независан од маркера а. Ово није случај са регистрима и
          макроима.

РЕФЕРЕНЦЕ: [Маркери](marks)
           [Покрети маркера](mark-motions)  (разлика између ' и \`)


# РЕЗИМЕ лекције 2.1

  1. Да сачувате (тргнете, обришете) текст у, и вратите (налепите) из, укупно
     26 регистара (a-z) 
  2. Тргните целу реч са било које позиције унутар речи: `yiw`{normal}
  3. Измените целу реч са било које позиције унутар речи: `ciw`{normal}
  4. Уметните текст директно из регистра у режиму уметање: `<CTRL-r>a`{normal}

  5. Уметните резултате простих аритметичких операција:
     `<CTRL-r>=`{normal}60\*60 `<ЕНТЕР>`{normal} у режиму уметања
  6. Уметните резултате системских позива:
     `<CTRL-r>=`{normal}`system('ls -1')`{vim} у режиму уметања

  7. Погледајте садржај регистара са `:reg`{vim}
  8. Научите крајње одредиште брисања комплетних линија: `dd`{normal} у
     бројчане регистре, тј. опадајући од регистра 1 - 9. Имајте на уму да
     се брисања целих линија одржавају у регистрима дуже од било које друге
     операције
  9. Научите крајња одредишта свих тргања у бројчане регистре и колико се
     тамо задржавају

 10. Постављајте маркере из командног режима `m[a-zA-Z0-9]`{normal}
 11. Премештајте по линијама на маркер са `'`{normal}


# ЗАКЉУЧАК

  Овим се завршава друго поглавље Vim приручника. Још увек се ради на њему.

  Ово поглавље је написао Пол Д. Паркер.

  Изменио за vim-tutor-режим Restorer

  Превео на српски Иван Пешић.
