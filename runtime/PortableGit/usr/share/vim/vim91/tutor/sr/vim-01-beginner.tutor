#             Добродошли   у   VIM   приручник

Vim је моћан едитор са много команди, сувише да бисмо их овде све описали.
Приручник је замишљен тако да опише довољан број команди помоћу којих Vim можете
лагодно користите као едитор опште намене. Врло је ВАЖНО запамтити да је овај
приручник прилагођен за учење употребом. То значи да вежбе морате урадити да би
их научили како треба. Ако само читате текст, ускоро ћете заборавити оно што је
најважније!

За сада, ако је Caps Lock укључен ИСКЉУЧИТЕ га. Притисните тастер ј довољно пута
тако да цела лекција 0 стане на екран.

# Лекција 0

НАПОМЕНА: команде у лекцијама ће мењати текст, али те измене се неће чувати.
Не брините ако забрљате ствари; просто упамтите да ће притисак на [<Esc>](<Esc>) па
онда на [u](u) да поништи последњу измену.

Овај приручник је интерактиван и постоји неколико ствари које треба да знате.
- На линковима као што је [овај](holy-grail  ) притисните [<Eнтер>](<Enter>) и отвориће се одређени
  одељак помоћи.
- Или једноставно притисните [K](K) на било којој речи да пронађете њену
  доументацију!
- Понекад ће бити потребно да изментите текст као што је
овај овде
Када извршите исправне измене, знак ✗ са леве стране ће се променити у ✓.
Већ видим како схватате колико је Vim опасан. ;)
На другим местима ће се од вас затражити да извршите команду (то ћу да објасним
касније):
~~~ cmd
    :help <Enter>
~~~
или да притиснете низ тастера
~~~ normal
    <Esc>0f<Space>d3wP$P
~~~

Текст унутар < и > (као `<Enter>`{normal}) описује тастер који треба да се
притисне, а не текст који се куца.

А сада, пређите на наредну лекцију (употребите тастер `j`{normal} да скролујете
наниже).

## Лекција 1.1: ПОМЕРАЊЕ КУРСОРА

** За померање курсора, притискајте тастере `h`, `j`, `k`, `l` како је приказано. **

         ↑
         k        Савет: тастер `h`{normal} је са леве стране и помера курсор у лево.
     ← h   l →           тастер `l`{normal} је са десне стране и помера курсор у десно.
         j               тастер `j`{normal} изгледа као стрелица наниже.
         ↓

  1. Померајте курсор по екрану док се не навикнете на команде.

  2. Притисните тастер (`j`{normal}) док не почне да се понавља.
     Сада знате како да дођете до наредне лекције.

  3. Користећи тастер наниже, пређите на лекцију 1.2.

НАПОМЕНА: ако било када не будете сигурни у то шта сете управо откуцали,
          притисните <Esc> да пређете у Нормални режим. Па затим поново
          откуцајте команду коју сте хтели.

НАПОМЕНА: курсорске стрелице би такође требало да раде. Али ако користите
	  тастере hjkl, једном када се навикенете, моћи ћете да се крећете много 
	  брже. Заиста!

# Лекција 1.2: ИЗЛАЗАК ИЗ ПРОГРАМА VIM

!! НАПОМЕНА: пре извођења било ког корака, обавезно прочитајте целу лекцију !!

 1. Притисните тастер <Esc> (да се сигурно нађете у Нормалном режиму).

 2. Откуцајте:

    `:q!`{vim} `<Enter>`{normal}.

     Овим се напушта едитор, уз ОДБАЦИВАЊЕ свих измена које сте направили.

 3. Отворите vim и вратите се назад овде тако што ћете извршити комканду која
    вас је и довела у овај приручник. То би могло да буде:

     :Tutor <Enter>

 4. Ако сте запамтили ове кораке, извршите их редом од 1 до 3 тако да изађете из
    едитора и поново уђете у њега.

НАПОМЕНА: [:q!](:q) <Ентер> ће да одбаци све измене које сте направили. За неколико
          лекција ћете научити како да сачувате измене.

 5. Померите курсор наниже на лекцију 1.3.

## Лекција 1.3: УРЕЂИВАЊЕ ТЕКСТА: БРИСАЊЕ

** Притисните `x`{normal} за брисање карактера под курсором. **

 1. Померите курсор на линију испод означену са ✗.

 2. Да бисте исправили грешке, померајте курсор све док се не нађе на слову које 
    треба да се избрише.

 3. Притисните [тастер x](x) да обришете нежељени карактер.

 4. Понављајте кораке 2 до 4 све док реченица није исправна.

РРРибаа рииби гризззе ррреепп.

 5. Сада када је ред исправљен, пређите на лекцију 1.4.

НАПОМЕНА: док користите приручник, немојте учити команде напамет, већ вежбајте
          њихову примену.

# Лекција 1.4: УРЕЂИВАЊЕ ТЕКСТА: УМЕТАЊЕ

** Притисните `i`{normal} да уметнете текст. **

 1. Померите курсор на прву линију испод означену са ✗.

 2. Да бисте текст првог реда исправили тако да буде исти као текст у другом
    реду, поставите курсор на први карактер ИЗА места на које текст треба да се
    уметне.

 3. Притисните `i`{normal} и откуцајте потребно.

 4. Када исправите једну грешку, притисните `<Esc>`{normal} да се вратите у
    Нормални режим. Понављајте кораке 2 до 4 да исправите целу реченицу.

До тека неоје в ред.
Део текста недостаје из овог реда.

 5. Када осетите да поуздано умећете текст, пређите на лекцију 1.5.

# Лекција 1.5: УРЕЂИВАЊЕ ТЕКСТА: НАДОВЕЗИВАЊЕ

** Притисните `A`{normal} за надовезивање текста. **

 1. Померите курсор на прву линију испод означену са ✗. Није важно на ком
    карактеру се курсор налази унутар тог реда.

 2. Притисните [A](A) и откуцајте текст који недостаје.

 3. Када надовежете текст притисните `<Esc>`{normal} да се вратите у Номални
    режим.

 4. Померите курсор на другу линију означену са ✗ и понављајте кораке 2 и 3 да
    исправите реченицу.

Део текста недостаје у
Део текста недостаје у овом реду.
Део текста недостаје
Део текста недостаје и овде.

 5. Када осетите да поуздано надовезујете текст, пређите на лекцију 1.6.

# Лекција 1.6: УРЕЂИВАЊЕ ФАЈЛА

** Користите `:wq`{vim} да сачувате фајл и изађете из едитора.  **

!! НАПОМЕНА: пре извођења било ког корака, обавезно прочитајте целу лекцију !!

 1. Изађите из приручника као у лекцији 1.2: `:q!`{vim} Или, ако имате приступ
    другом терминалу, ово што следи изведите у њему.

 2. На одзиву љуске откуцајте следећу команду:
~~~ sh
     $ vim tutor
~~~
    'vim' је команда која покреће Vim едитор, 'tutor' је име фајла који желите
    да уређујете. Употребите фајл који може да се мења.

 3. Уметните и обришите текст као у претходним лекцијама.

 4. Сачувајте фајл са изменама и напустите Vim са:
~~~ cmd
     :wq
~~~
    Приметите да је потребно да притиснете `<Ентер>` да би се извршила команда.

 5. Ако сте сте напустили vimtutor у кораку 1, покрените га поново и пређите на
    резиме који следи.

 6. Када прочитате и у потпуности разумете кораке изнад: извршите их.

# РЕЗИМЕ лекције 1

 1. Курсор се помера или стрелицама или тастерима hjkl.
     h (лево)     j (десно)     k (горе)      l (доле)

 2. Да бисте покренули Vim из одзива љуске, откуцајте:

~~~ sh
    $ vim ИМЕФАЈЛА
~~~

 3. Да напустите Vim откуцајте: `<Esc>`{normal} `:q!`{vim} `<Enter>`{normal} да
    одбаците све измене.
              ИЛИ откуцајте: `<Esc>`{normal} `:wq`{vim} `<Enter>`{normal} да
    сачувате измене.

 4. Да бисте обрисали карактер на курсору откуцајте: `x`{normal}

 5. Да уметнете или надовежете текст, откуцајте:
    `i`{normal} уметните текст `<Esc>`{normal}     умеће текст испред курсора.
    `A`{normal} надовежите текст `<Esc>`{normal}   надовезује на крај реда.

НАПОМЕНА: притисак на `<Esc>`{normal} вас поставља у Нормални режим или отказује
	  нежељену или делимично извршену команду.

Наставите сада са лекцијом 2.

# Лекција 2.1: КОМАНДЕ БРИСАЊА

** Откуцајте `dw`{normal} да обришете реч. **

 1. Притисните `<Esc>`{normal} да се сигурно нађете у Нормалном режиму.

 2. Померите курсор на линију испод, обележену са ✗.

 3. Померите курсор на почетак речи која треба да се обрише.

 4. Откуцајте [d](d)[w](w) да бисте уклонили реч.

Неке речи смешно не припадају на папир овој реченици.

 5. Понављајте кораке 3 и 4 све док не исправите реченицу, па пређите на лекцију
    2.2.

# Лекција 2.2: ЈОШ КОМАНДИ БРИСАЊА

** Откуцајте `d$`{normal} да обришете све до краја реда. **

 1. Притисните `<Esc>`{normal} да се сигурно нађете у Нормалном режиму.

 2. Померите курсор на линију испод, обележену са ✗.

 3. Померите курсор на крај исправног реда (ПОСЛЕ прве . ).

 4. Откуцајте `d$`{normal} да обришете све до краја реда.

Неко је унео крај овог реда двапут. крај овог реда двапут.

 5. Пређите на лекцију 2.3 у којој следи подробније објашњење.

# Лекција 2.3: О ОПЕРАТОРИМА И ПОКРЕТИМА

Многе команде које мењају текст се састоје од [оператора](operator) и [покрета](motion).
Облик команде брисања са [d](d) оператором брисања је следећи:

    d   покрет

  При чему је:
    d      - оператор брисања.
    покрет - оно на чему ће оператор да делује (наведено испод).

  Кратак списак покрета:
    [w](w) - до почетка наредне речи, НЕ УКЉУЧУЈУЋИ њен први карактер.
    [e](e) - до краја текуће речи, УКЉУЧУЈУЋИ последњи карактер.
    [$]($) - до краја линије, УКЉУЧУЈУЋИ последњи карактер.

  Дакле, куцање `de`{normal} ће да обрише текст од курсора до краја речи.

НАПОМЕНА: притиском само на тастер покрета док сте у Нормалном режиму, без
	  оператора, курсор се помера на начин који одговара том покрету.

# Лекција 2.4: КОРИШЋЕЊЕ БРОЈАЊА ЗА ПОКРЕТ

** Уношење неког броја пре покрета, он се извршава наведени број пута. **

 1. Поставите курсор на почетак доњег реда означеног са ✓.

 2. Откуцајте `2w`{normal} да померите курсор две речи унапред.

 3. Откуцајте `3e`{normal} да омерите курсор на крај треће наредне речи.

 4. Откуцајте `0`{normal} ([нулу](0)) да померите курсор на почетак реда.

 5. Поновите кораке 2 и 3 са неким другим бројевима.

Реченица са речима по којој можете померати курсор.

 6. Пређите на лекцију 2.5.

# Лекција 2.5: КОРИШЋЕЊЕ БРОЈАЊА ЗА ВЕЋЕ БРИСАЊЕ

** Уношење неког броја с оператором понавља оператор тај број пута. **

Комбинацијом оператора брисања и покрета поменутог изнад, можете унети број
понављања пре покрета да бисте обрисали више:
     d   број   покрет

 1. Померите курсор на прво слово речи исписане ВЕЛИКИМ СЛОВИМА у реду
    означеном са ✗.

 2. Откуцајте `d2w`{normal} да обришете две речи са ВЕЛИКИМ СЛОВИМА

 3. Понављајте кораке 1 и 2 са различитим бројем понављања тако да једном
    командом обришете узастопне речи са ВЕЛИКИМ СЛОВИМА

Овај АБВГД ЂЕЖ ред ЗИЈК ЛЉ МНЊ ОП речи је РСТ ЋУФХЦ исправљен.

# Лекција 2.6: ОПЕРАЦИЈЕ НАД РЕДОВИМА

** Откуцајте `dd`{normal} да обришете цео ред. **

Због учесталости брисања целих редова, аутори програма Vi су дошли до закључка
да је лакше брисати редове ако се d просто откуца двапут.

 1. Померите курсор на други ред у доњој строфи.
 2. Откуцајте [dd](dd) да обришете ред.
 3. Сада се померите на четврти ред.
 4. Откуцајте `2dd`{normal} да обришете два реда.

1)  Sedlo mi je od marame,
2)  blato na sve strane,
3)  uzda od kanapa,
4)  auto mi je ovde,
5)  satovi pokazuju vreme,
6)  a bič mi je od očina
7)  prebijena štapa.

# Лекција 2.7: КОМАНДА ЗА ПОНИШТАВАЊЕ

** Притисните `u`{normal} да поништите последње команде, `U`{normal} да исправите цео ред. **

 1. Померите курсор на линију испод, означену са ✗ и поставите га на прву
    грешку.
 2. Откуцајте `x`{normal} да обришете први нежељени карактер.
 3. Сада откуцајте `u`{normal} да поништите последњу извршену команду.
 4. Овај пут исправите све грешке у реду користећи команду `x`{normal}.
 5. Онда откуцајте велико `U`{normal} да ред вратите у првобитно стање.
 6. Онда неколико пута откуцајте `u`{normal} да поништите команду `U`{normal} и претходне команде.
 7. Сада откуцајте `<C-r>`{normal} (Control + R) неколико пута да вратите измене
    (поништите поништавања).

Ииисправите грешке уу оввом реду ии пооништитеее их.

 8. Ово су веома корисне команде. Пређите сада на резиме лекције 2.

# РЕЗИМЕ лекције 2

 1. Да обришете од курсора до наредне речи, откуцајте:    `dw`{normal}
 2. Да обришете од курсора до краја реда, откуцајте:   `d$`{normal}
 3. Да обришете цео ред, откуцајте:                           `dd`{normal}
 4. Да поновите покрет, унесите број испред њега:           `2w`{normal}

 5. Облик команде измене:
               оператор   [број]   покрет
    где је:
       оператор -   представља радњу, рецимо [d](d) за брисање
       [број]   -   необавезан број понављања покрета
       покрет   -   кретање преко текста над којем се ради, на пример:
                        [w](w) (реч),
                        [$]($) (до краја реда), итд.

 6. Да се померите на почетак реда, употребите нулу: [0](0)

 7. Да поништите претходне акције, откуцајте:      `u`{normal}  (мало u)
    Да поништите све измене у реду, откуцајте:     `U`{normal}  (велико U)
    Да вратите измене, откуцајте:                  `<C-r>`{normal}

# Лекција 3.1: КОМАНДА ПОСТАВЉАЊА

** Откуцајте `p`{normal} да поставите претходно обрисани текст иза курсора. **

 1. Померите курсор на први ✓ ред испод.

 2. Откуцајте `dd`{normal} да обришете ред и да га сместите у Vim регистар.

 3. Померите курсор на ред c), ИЗНАД места где треба поставити избрисани ред.

 4. Откуцајте `p`{normal} да поставите ред испод курсора.

 5. Понављајте кораке 2 до 4 да поставите све линије у правилном редоследу.

г) пребијена штапа.
б) узда од канапа,
в) а бич ми је од очина
а) Седло ми је од мараме,

# Лекција 3.2: КОМАНДА ЗАМЕНЕ

** Откуцајте `rx`{normal} да карактер испод курсора замените са x. **

 1. Померите курсор на први наредни ред обележен са ✗.

 2. Померите курсор тако да се нађе на првој грешки.

 3. Откуцајте `r`{normal} па затим карактер који би ту требало да буде.

 4. Понављајте кораке 2 и 3 све док први ред не постане исти као други.

Кеди ју овеј ред угашен, нако је протресао пусташне тестере!
Када је овај ред уношен, неко је притискао погрешне тастере!

 5. Сада пређите на лекцију 3.3.

НАПОМЕНА: упамтите да је потребно да учите вежбањем, а не памћењем.

# Лекција 3.3: ОПЕРАТОР ИЗМЕНЕ

** Да измените текст до краја речи, откуцајте `ce`{normal}.  **

 1. Померите курсор на први следећи ред означен са ✗.

 2. Поставите курсор на „а” у „ракдур”.

 3. Откуцајте `ce`{normal} и исправите реч (у овом случају, откуцајте „ед”).

 4. Притисните `<Esc>`{normal} и померите курсор на наредни карактер који треба
    исправити.

 5. Понављајте кораке 3 и 4 све док прва реченица не буде иста као друга.

Овај ракдур има неколико рејга које трефља испрпикати операгром измене.
Овај ред има неколико речи које треба исправити оператором измене.

Уочите да [c](c)e брише реч и поставља едитор у режим Уметање.

# Лекција 3.4: ЈОШ ИЗМЕНА УПОТРЕБОМ `c`{normal}

** Оператор измене се користи са истим покретима као и оператор брисања. **

 1. Оператор измене функционише на исти начин као и оператор брисања. Облик је
    следећи:

	 c    [број]   покрет

 2. Покрети су исти, рецимо `w`{normal} (реч) и `$`{normal} (крај реда).

 3. Померите курсор на први следећи ред означен са ✗.

 4. Померите курсор на прву грешку.

 5. Откукцајте `c$`{normal} и унесите остатак реда тако да буде исти као други
    ред, па притисните `<Esc>`{normal}.

Крај овог реда треба изменити тако да изгледа као ред испод.
Крај овог реда треба исправити коришћењем `c$`{normal} команде.

НАПОМЕНА: за исправљање грешака током куцања, можете користити тастер брисања у
          лево.

# РЕЗИМЕ лекције 3

 1. За постављање текста који сте управо обрисали, притисните [p](p). Ово
    обрисани текст поставља непосредно ИЗА курсора (ако је био обрисан један
    или више редова, садржај ће доћи на ред испод курсора). 

 2. Да замените карактер под курсором, откуцајте [r](r) па затим карактер који
    желите на том месту.

 3. [Оператор измене](c) вам дозвољава промену текста од курсора до позиције на
    којој се завршава покрет. Примера ради, откуцајте `ce`{normal} да измените
    текст од позиције курсора до краја речи, `c$`{normal} да измените до краја
    реда.

 4. Облик операције измене је:

     c   [број]   покрет

Пређите сада на наредну лекцију.

# Лекција 4.1: ПОЗИЦИЈА КУРСОРА И СТАТУС ФАЈЛА

** Притисните `<C-g>`{normal} да вам се прикаже позиција курсора у фајлу и
 статус фајла. Притисните `G`{normal} да се померите на неки ред у фајлу.  **

НАПОМЕНА: прочитајте целу лекцију пре извођења било ког корака!!

 1. Држите тастер `<Ctrl>`{normal} и притисните `g`{normal}. Ово зовемо `<C-g>`{normal}. Едитор ће на дну
    екрана исписати поруку са именом фајла и позицијом курсора у фајлу.
    Запамтите број реда за корак 3.

НАПОМЕНА: у доњем десном углу може се видети позиција курсора ако је укључена
          опција ['ruler']('ruler').

 2. Притисните [G](G) да се померите на крај фајла.
    Откуцајте [gg](gg) да се преместите на почетак фајла.

 3. Откуцајте број реда на коме сте били малопре, па онда `G`{normal}. Курсор ће се
    вратити на ред у којем је био када сте притиснули `<C-g>`{normal}.

 4. Ако се осећате спремним, извршите кораке 1 до 3.

# Лекција 4.2: КОМАНДА ПРЕТРАЖИВАЊА

** Откуцајте `/`{normal} па израз који желите да пронађете. **

 1. У Нормалном режиму откуцајте карактер `/`{normal}. Приметите да се он и
    курсор појављују на дну екрана као и `:`{normal} команда.

 2. Сада откуцајте ’грррешка’ `<Enter>`{normal}. Ово је реч коју желите да
    пронађете.

 3. За поновно тражење истог израза, једноставно притисните [n](n).
    За тражење истог израза у супротном смеру, притисните [N](N).

 4. За тражење израза унатраг, употребите [?](?) уместо `/`{normal}.

 5. За повратак на претходну позицију са које сте скочили, притисните `<C-o>`{normal}
    (држите притиснут тастер `<Ctrl>`{normal} док притискате слово `o`{normal}). Понављајте
    за раније позиције. `<C-i>`{normal} иде унапред.

„грррешка” је погрешно; уместо грррешка треба да стоји грешка.

НАПОМЕНА: ако претрага дође до краја текста, тражење ће се наставити од његовог
	  почетка, осим ако је опција ['wrapscan']('wrapscan') искључена.

# Лекција 4.3: ТРАЖЕЊЕ ПАРА ЗАГРАДЕ

** Откуцајте `%`{normal} да пронађете пар  ),], или }. **

 1. Поставите курсор на било коју (, [, или { отворену заграду у реду испод
    означеном са ✓.

 2. Откуцајте сада карактер [%](%).

 3. Курсор ће се померити на одговарајућу затворену заграду.

 4. Откуцајте `%`{normal} да померите курсор на другу заграду пара.

 5. Померите курсор на неку од осталих (,),[,],{ or } и проверите шта ради `%`{normal}.

Ред ( тестирања обичних ( [ угластих ] и { витичастих } заграда.))

НАПОМЕНА: ово је врло корисно у исправљању кода са распареним заградама!

# Лекција 4.4: КОМАНДА ЗАМЕНЕ

** Откуцајте `:s/старо/ново/g` да замените „старо” са „ново”. **

 1. Померите курсор на ред испод означен са ✗.

 2. Откуцајте
~~~ cmd
    :s/рди/ри/
~~~

    ПРИМЕТИТЕ да је команда [:s](:s) заменила само прво појављивање „рди” у реду.

 3. Откуцајте сада
~~~ cmd
     :s/рди/ри/g
~~~

    Додавање [заставице](:s_flags) g значи да ће команда функционисати у целом
    реду, замењујући сва појављивања „рди” у њему.

Рдиба рдиби грдизе реп.

 4. Да замените сва појављивања низа карактера између нека два реда, откуцајте
~~~ cmd
     :#,#s/старо/ново/g
~~~
    где су #,# крајњи бројеви редова у опсегу у којем треба да се изврши
    замена.

    Откуцајте
~~~ cmd
    :%s/старо/ново/g
~~~
    да замените сва појављивања у целом фајлу.

    Откуцајте
~~~ cmd
    :%s/старо/ново/gc
~~~
    да пронађете сва појављивања у целом фајлу, уз приказивање питања за свако
    од њих, да ли извршити замену или не.

# РЕЗИМЕ лекције 4

 1. `<C-g>`{normal}     приказује позицију курсора у тексту и статус фајла.
    `G`{normal}         помера курсор на крај фајла.
    број `G`{normal}    помера курсор на наведени ред.
    `gg`{normal}        помера курсор на први ред.

 2. Куцањем `/`{normal} након чека следи израз, тражи се УНАПРЕД тај израз.
    Куцањем `?`{normal} након чека следи израз, тражи се УНАЗАД тај израз.
    Након претраге, користите `n`{normal} да пронађете наредно појављивање у
    истом смеру, или `N`{normal} да га пронађете у супртном смеру.
    `<C-o>`{normal} вас води на раније позиције, `<C-i>`{normal} на новије позиције.

 3. Када се курсор налази на (,),[,],{, или }, куцање `%`{normal} помера курсор на њен
    пар.

 4. Да замените први израз старо у линији, откуцајте
~~~ cmd
        :s/старо/ново
~~~
    Да замените сва појављивања старо са ново у линији, откуцајте
~~~ cmd
        :s/старо/ново/g
~~~
    Да замените сва појављивања у опсегу редова #, откуцајте
~~~ cmd
        :#,#s/старо/ново/g
~~~
    Да замените сва појављивања у целом фајлу, откуцајте
~~~ cmd
        :%s/старо/ново/g
~~~
    За затражите потврду сваке замене, додајте затавицу ’c’
~~~ cmd
        :%s/старо/ново/gc
~~~

# Лекција 5.1: КАКО СЕ ИЗВРШАВА СПОЉНА КОМАНДА

** Откуцајте `:!`{vim} па име спољне команде коју желите да извршите. **

 1. Откуцајте познату команду `:`{normal} да поставите курсор на дно екрана.
    На тај начин можете да унесете команду командне-линије.

 2. Откуцајте сада [!](!cmd) (узвичник). Ово вам омогућава да извршите било коју
    спољну команду љуске.

 3. Као пример, откуцајте „ls” након „!”, па притисните `<Enter>`{normal}.
    Ово ће вам приказати садржај директоријума, као да сте у одзиву љуске.

НАПОМЕНА: На овај начин може да се изврши било која спољна команда, заједно са
	  аргументима.

НАПОМЕНА: Све `:`{vim} команде морају да се заврше притиском на `<Enter>`{normal}.
          У даљем тексту то нећемо увек напомињати.

# Лекција 5.2: ВИШЕ О ЧУВАЊУ ФАЈЛОВА

** За чување измена над текстом, откуцајте `:w`{vim} ИМЕ_ФАЈЛА. **

 1. Откуцајте `:!ls`{vim} да видите садржај директоријума.
    Већ знате да морате притиснути `<Enter>`{normal} након тога.

 2. Изаберите име фајла које још увек не постоји, нпр. TEST.

 3. Сада откуцајте:
~~~ cmd
        :w TEST
~~~
   (где је тест TEST име фајла које сте изабрали)

 4. На овај начин чувате цео фајл (Vim тутор) под именом TEST.
    Да бисте то проверили, откуцајте поново `:!ls`{vim} да погледате директоријум.

НАПОМЕНА: Ако бисте напустили Vim и покренули га поново са `vim TEST`, фајл би
	  био тачна копија овог фајла у тренутку када сте га сачували.

 5. Избришите сада фајл тако што ћете откуцати:
~~~ cmd
        :!rm TEST
~~~

# Лекција 5.3: ЧУВАЊЕ ОЗНАЧЕНОГ ТЕКСТА

** Да бисте сачували део фајла, откуцајте `v`{normal} покрет `:w ИМЕ_ФАЈЛА`{vim}. **

 1. Померите курсор на ову линију.

 2. Притисните [v](v) и померите курсор пет редова наниже. Приметите да је текст
    истакнут.

 3. Притисните карактер `:`{normal}. Појавиће се

        :'<,'>

    на дну екрана.

 4. Откуцајте

        `:w TEST`{vim}

    где је TEST име фајла који још увек не постоји. Проверите да заиста пише

        `:'<,'>w TEST`{vim}

    пре него што притиснете `<Enter>`{normal}.

 5. Vim ће сачувати означене редове у фајл TEST. Употребите `:!ls`{vim} да то
    проверите. Не бришите га још! Користићемо га у следећој лекцији.

НАПОМЕНА: Притисак на [v](v) покреће [Визуелни избор](visual-mode). Можете да померате курсор
          наоколо и тако мењате величино изабраног текста. Затим можете да
	  употребите операторе над тим текстом. На пример, `d`{normal} ће да избрише
	  текст.

# Лекција 5.4: УЧИТАВАЊЕ ФАЈЛОВА У ТЕКСТ

** Да садржај фајла уметнете у текст, откуцајте `:r ИМЕ_ФАЈЛА`{vim}. **

 1. Поставите курсор непосредно изнад ове линије.

НАПОМЕНА: Када извршите корак 2, видећете текст из лекције 5.3. Затим померите
	  курсор НАНИЖЕ да бисте поново видели ову лекцију.

 2. Учитајте сада фајл TEST користећи команду

        `:r TEST`{vim}

     где је TEST име фајла које сте користили у претходној лекцији. Садржај
     учитаног фајла је убачен испод курсора.

 3. Да бисте потврдили да је фајл учитан, вратите курсор уназад и уочите да
    постоје две копије лекције 5.3, оригинална и она из фајла.

НАПОМЕНА: Такође можете и да учитате и излаз спољне команде. На пример,

        `:r !ls`{vim}

      учитава излаз команде `ls` и поставља га испод курсора.

# РЕЗИМЕ лекције 5

 1. [:!команда](:!cmd) извршава спољну команду.

     Корисни примери:
     `:!ls`{vim}              -  приказује садржај директоријума
     `:!rm ИМЕ_ФАЈЛА`{vim}    -  уклања ИМЕ_ФАЈЛА

 2. [:w](:w) ИМЕ_ФАЈЛА             уписује текући Vim фајл на диск под именом
                             ИМЕ_ФАЈЛА.

 3. [v](v)  покрет  :w ИМЕ_ФАЈЛА  чува Визуелно изабране линије у фајл
                             ИМЕ_ФАЈЛА.

 4. [:r](:r) ИМЕ_ФАЈЛА             учитава фајл ИМЕ_ФАЈЛА са диска и поставља
                             његов садржај испод позиције курсора.

 5. [:r !dir](:r!)                  чита излаз dir команде и поставља га испод
                             позиције курсора.

# Лекција 6.1: КОМАНДА ОТВОРИ

** Откуцајте `o`{normal} да отворите ред испод курсора и пређете у режим Уметање. **

 1. Померите курсор на линију испод означену са ✓.

 2. Откуцајте мало слово `o`{normal} да [отворите](o) нови ред ИСПОД курсора и пређете
    у режим Уметање.

 3. Сада откуцајте неки текст и притисните `<Esc>`{normal} да напустите режим
    Уметање.

Када притиснете `o`{normal} курсор прелази у новоотворени ред у режиму Уметање.

 4. Да бисте линију отворили ИЗНАД курсора, уместо малог `o`{normal} откуцајте [велико O](O).
    Пробајте ово у реду испод.

Отворите ред изнад овог куцањем великог O док је курсор у овом реду.

# Лекција 6.2: КОМАНДА НАДОВЕЗИВАЊА

** Откуцајте `a`{normal} да уметнете текст ИЗА курсора. **

 1. Померите курсор на почетак следећег реда означеног са ✗.

 2. Притискајте `e`{normal} све док се курсор не нађе на крају речи „ре”.

 3. Откуцајте мало `a`{normal} да [append-надовежете](a) текст ИЗА курсора.

 4. Допуните реч као што је приказано у реду испод њега. Притисните `<Esc>`{normal} да
    напустите режим Уметање.

 5. Употребите `e`{normal} да се померите на наредну непотпуну реч и поновите кораке 3
    и 4.

Овај ре омогућава ве надов текста у неком реду.
Овај ред омогућава вежбање надовезивања текста у неком реду.

НАПОМЕНА: Команде [a](a), [i](i) и [A](A) све активирају исти режим Уметање, једина разлика
          је у позицији од које се умећу нови карактери.

# Лекција 6.3: ДРУГИ НАЧИН ЗА ЗАМЕНУ

** Откуцајте велико `R`{normal} да замените више од једног карактера. **

 1. Померите курсор на први наредни ред означен са ✗. Померите курсор на
    почетак првог „xxx”.

 2. Сада притисните `R`{normal} ([велико R](R)) и откуцајте број који се налази испод, у
    наредном реду, тако да замени „xxx”.

 3. Притисните `<Esc>`{normal} да напустите [режим Замена](mode-replace). Приметите да остатак реда
    остаје неизмењен.

 4. Поновите кораке да замените и друго „xxx”.

Додавање 123 на xxx даје xxx.
Додавање 123 на 456 даје 579.

НАПОМЕНА: Режим Замена је исти као режим Уметање, само што сваки откуцани
	  карактер брише постојећи карактер.

# Лекција 6.4: КОПИРАЊЕ И НАЛЕПЉИВАЊЕ ТЕКСТА

** Користите оператор `y`{normal} да копирате текст, а `p`{normal} да га налепите. **

 1. Померите курсор наниже на линију означену са ✓ и поставите курсор након „а)”.

 2. Покрените Визуелни режим са `v`{normal} и померите курсор непосредно испред „први”.

 3. Откуцајте `y`{normal} да [yank-тргнете](yank) (копирате) истакнути текст.

 4. Померите курсор до краја наредног реда: `j$`{normal}

 5. Притисните `p`{normal} да [put-поставите](put) (налепите) текст.

 6. Притисните `a`{normal} па откуцајте „други”. Притисните `<Esc>`{normal} да напустите
    режим Уметање.

 7. Употребите Визуелни режим да изаберете „ред.”, тргните га са `y`{normal}, померите
    курсор на крај наредног реда са `j$`{normal} и тамо налепите текст са `p`{normal}

а) Ово је први ред.
б)

НАПОМЕНА: `y`{normal} можете да користите и као оператор: `yw`{normal} ће да тргне једну реч.

# Лекција 6.5: ПОСТАВЉАЊЕ ОПЦИЈА

** Поставите опцију тако да претрага и замена игноришу величину слова.  **

 1. Потражите реч ’разлика’ са: `/разлика`
    Поновите неколико пута притиском на `n`{normal}.

 2. Поставите опцију 'ic' (Ignore case) тако што унесете:
~~~ cmd
        :set ic
~~~
 3. Сада поново потражите реч ’разлика’ притиском на `n`{normal}.
    Уочите да се сада проналазе и Разлика и РАЗЛИКА.

 4. Поставите опције 'hlsearch' и 'incsearch':
~~~ cmd
        :set hls is
~~~
 5. Сада откуцајте поново команду претраге и уочите шта се дешава: /разлика <Enter>

 6. Поново искључите игнорисање разлике у величини слова:
~~~ cmd
        :set noic
~~~
 7. Ако желите да промените стање опције, ставите испред „inv” испред њеног имена:
~~~ cmd
        :set invic
~~~
НАПОМЕНА: Да уклоните истицање подударања, унесите:
~~~ cmd
        :nohlsearch
~~~
НАПОМЕНА: Ако желите да се не прави разлика у величини слова само за једну
          команду претраге, употребите [\c](/\c) у изразу: /игнориши\c <Enter>

# РЕЗИМЕ лекције 6

 1. Притисните `o`{normal} да отворите ред ИСПОД курсора и покренете режим Уметање.
    Притисните `O`{normal} да отворите ред ИЗНАД курсора.

 2. Притисните `a`{normal} да уметнете текст ИЗА курсора.
    Притисните `A`{normal} да уметнете текст на крај реда.

 3. Команда `e`{normal} помера курсор на крај речи.

 4. Оператор `y`{normal} копира текст, `p`{normal} га налепљује.

 5. Куцање великог `R`{normal} активира режим Замена све док се не притисне `<Esc>`{normal}.

 6. Куцање „[:set](:set) xxx” поставља опцију „xxx”. Неке од опција су:

        'ic' 'ignorecase'   не разликују се велика/мала слова током претраге
        'is' 'incsearch'    приказују се делимична подударања израза претраге
        'hls' 'hlsearch'    истичу се сви пронађени изрази

     Можете да користите или кратко или дуго име опције.

 7. Ставите „no” испред имена опције да је искључите:
~~~ cmd
        :set noic
~~~
 8. Ставите „inv” да промените стање опције:
~~~ cmd
        :set invic
~~~

# Лекција 7.1: ДОБИЈАЊЕ ПОМОЋИ

** Користите систем директне помоћи. **

Vim има детаљни систем директне помоћи. За почетак, покушајте нешто од следећег:
    - притисните тастер `<HELP>`{normal} (ако га имате на тастатури)
    - притисните тастер `<F1>`{normal} (ако га имате на тастатури)
    - откуцајте
        `:help`{vim}

Прочитајте текст у прозору помоћи да сазнате начин на који помоћ ради.
Откуцајте `<C-w><C-w>`{normal} да прелазите из једног прозора у други.
Откуцајте `:q`{vim} да затворите прозор помоћи.

Помоћ о практично било којој теми можете добити додавањем аргумента команди
„:help”. Покушајте следеће (не заборавите да притиснете <Enter> на крају):
~~~ cmd
    :help w
    :help c_CTRL-D
    :help insert-index
    :help user-manual
~~~
# Лекција 7.2: ПРАВЉЕЊЕ СКРИПТЕ ЗА ПОКРЕТАЊЕ

** Активирајте Vim могућности. **

Vim има много више могућности него Vi, али је већина њих подразумевано
искључена. Да бисте укључили још фукционалности, морате да креирате „vimrc”
фајл.

 1. Започните уређивање „vimrc” фајла.
    `:call mkdir(stdpath('config'),'p')`{vim}
    `:exe 'edit' stdpath('config').'/init.vim'`{vim}

 2. Сачувајте фајл са:
    `:w`{vim}

  У овај „vimrc” фајл можете додати сва подешавања која желите.
  За више информација откуцајте `:help vimrc-intro`{vim}.

# Lesson 7.3: АУТОМАТСКО ДОВРШАВАЊЕ

** Довршавање команде линије са `<C-d>`{normal} и `<Tab>`{normal}. **

 1. Погледајте који фајлови постоје у директоријуму: `:!ls`{vim}

 2. Откуцајте почетак команде: `:e`{vim}

 3. Притисните `<C-d>`{normal} и Vim ће вам приказати списак команди које почињу на
    „e”.

 4. Притисните `<Tab>`{normal} и Vim ће да доврши име команде на „:edit”.

 5. Сада додајте размак и почетак имена постојећег фајла: `:edit FA`{vim}

 6. Притисните `<Tab>`{normal}. Vim ће довршити име (ако је јединствено).

НАПОМЕНА: Довршавање функционише за многе команде. Нарочито је корисно за
      `    :help`{vim}.

# РЕЗИМЕ лекције 7

 1. Откуцајте `:help`{vim}
    или притисните `<F1>`{normal} или `<Help>`{normal} да отворите прозор помоћи.

 2. Откуцајте `:help ТЕМА`{vim} да пронађете помоћ о ТЕМА.

 3. Откуцајте `<C-w><C-w>`{normal} да пређете у други прозор

 4. Откуцајте `:q`{vim} да затворите прозор помоћи

 5. Направите vimrc скрипту за покретање у којој чувате ваша омиљена подешавања.

 6. Док се налазите у командном режиму, притисните `<C-d>`{normal} да видите сва могућа
    довршавања. Притисните `<Tab>`{normal} да изаберете једно од њих.

# ЗАКЉУЧАК

Циљ овог приручника је био кратак преглед Vim едитора, колико да вам омогући да
га релативно једноставно користите. Он дефинитивно није потпун, јер Vim има
много, много више команди. Консултујте помоћ често.

На мрежи постоји много ресурса помоћу којих можете више да научите о Vim
едитору. Ево неколико:

- *Learn Vim Progressively*: http://yannesposito.com/Scratch/en/blog/Learn-Vim-Progressively/
- *Learning Vim in 2014*: http://benmccormick.org/learning-vim-in-2014/
- *Vimcasts*: http://vimcasts.org/
- *Vim Video-Tutorials by Derek Wyatt*: http://derekwyatt.org/vim/tutorials/
- *Learn Vimscript the Hard Way*: http://learnvimscriptthehardway.stevelosh.com/
- *7 Habits of Effective Text Editing*: http://www.moolenaar.net/habits.html
- *vim-galore*: https://github.com/mhinz/vim-galore

Ако вам више одговара књига, често се препоручују *Practical Vim* и наставак
*Modern Vim* аутора Дру Нила.

Овај приручник су написали Мајкс С. Пирс и Роберт К. Вер, Колорадо рударска
школа, користећи идеје које је доставио Чарлс Смит из Колорадо државног
универзитета. И-мејл: <EMAIL>.

Прилагодио за Vim Брам Моленар.
Прилагодио за vim-tutor-mode Фелипе Моралес.

Превод на српски: Иван Нејгебауер <<EMAIL>>
Верзија 1.0, мај/јуни 2014.
Прилагодио за vim-tutor-mode Иван Пешић.
