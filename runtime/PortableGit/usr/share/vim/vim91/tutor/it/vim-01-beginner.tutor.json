{"expect": {"27": -1, "107": "La mucca saltò sopra la luna.", "129": "Un po' di testo manca da questa riga.", "130": "Un po' di testo manca da questa riga.", "150": "Un po' di testo manca da questa riga.", "151": "Un po' di testo manca da questa riga.", "152": "Un po' di testo manca anche qui.", "153": "Un po' di testo manca anche qui.", "230": "Ci sono alcune parole che non appartengono a questa frase.", "246": "Qualcuno ha scritto due volte la fine di questa riga.", "287": -1, "306": "Questa riga di parole è stata pulita.", "321": -1, "322": -1, "323": -1, "324": -1, "325": -1, "326": -1, "327": -1, "344": "Correggete gli errori su questa riga e rimetteteli usando undo.", "384": -1, "385": -1, "386": -1, "387": -1, "401": "Quando questa riga è stata immessa, qualcuno ha premuto i tasti sbagliati!", "402": "Quando questa riga è stata immessa, qualcuno ha premuto i tasti sbagliati!", "423": "Questa riga ha alcune parole che vanno cambiate usando il comando change.", "424": "Questa riga ha alcune parole che vanno cambiate usando il comando change.", "445": "La fine di questa riga va corretta usando il comando `c$`.", "446": "La fine di questa riga va corretta usando il comando `c$`.", "515": -1, "537": -1, "563": "Di solito la stagione migliore per ammirare i fiori è la primavera.", "765": -1, "771": -1, "790": "Questa riga serve per far pratica ad aggiungere testo a una riga.", "791": "Questa riga serve per far pratica ad aggiungere testo a una riga.", "811": "Sommando 123 a 456 si ottiene 579.", "812": "Sommando 123 a 456 si ottiene 579.", "839": "a) Questo è il primo elemento.", "840": "b) <PERSON><PERSON> è il secondo elemento."}}