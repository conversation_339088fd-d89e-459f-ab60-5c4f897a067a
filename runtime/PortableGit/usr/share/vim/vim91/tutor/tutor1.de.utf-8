===============================================================================
=      W i l l k o m m e n   im   V I M   T u t o r    -    Version 1.7.de.1  =
===============================================================================

   Vim ist ein sehr mächtiger Editor, der viele Befehle bereitstellt; zu viele,
   um alle in einem Tutor wie diesem zu erklären.  Dieser Tutor ist so
   gestaltet, um genug Befehle vorzustellen, dass Du die Fähigkeit erlangst,
   Vim mit Leichtigkeit als einen Allzweck-Editor zu verwenden.
   Die Zeit für das Durcharbeiten dieses Tutors beträgt ca. 25-30 Minuten,
   abh<PERSON><PERSON><PERSON> davon, wie viel Zeit Du mit Experimentieren verbringst.

   ACHTUNG:
   Die in den Lektionen angewendeten Kommandos werden den Text modifizieren.
   Erstelle eine Kopie dieser Datei, in der Du üben willst (falls Du "vimtutor"
   aufgerufen hast, ist dies bereits eine Kopie).

   Es ist wichtig, sich zu vergegenwärtigen, dass dieser Tutor für das Anwenden
   konzipiert ist. Das bedeutet, dass Du die Befehle anwenden musst, um sie
   richtig zu lernen. Wenn Du nur den Text liest, vergisst Du die Befehle!

   Jetzt stelle sicher, dass deine Umstelltaste NICHT gedrückt ist und betätige
   die   j   Taste genügend Mal, um den Cursor nach unten zu bewegen, so dass
   Lektion 1.1.1 den Bildschirm vollkommen ausfüllt.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lektion 1.1.1: BEWEGEN DES CURSORS

  ** Um den Cursor zu bewegen, drücke die h,j,k,l Tasten wie unten gezeigt. **
	     ^		 Hilfestellung:
	     k		 Die h Taste befindet sich links und bewegt nach links.
       < h	 l >	 Die l Taste liegt rechts und bewegt nach rechts.
	     j		 Die j Taste ähnelt einem Pfeil nach unten.
	     v
  1. Bewege den Cursor auf dem Bildschirm umher, bis Du Dich sicher fühlst.

  2. Halte die Nach-Unten-Taste (j) gedrückt, bis sie sich wiederholt.
     Jetzt weißt Du, wie Du Dich zur nächsten Lektion bewegen kannst.

  3. Benutze die Nach-Unten-Taste, um Dich zu Lektion 1.1.2 zu bewegen.

Anmerkung: Immer, wenn Du Dir unsicher bist über das, was Du getippt hast,
	   drücke <ESC> , um Dich in den Normalmodus zu begeben.
	   Dann gib das gewünschte Kommando noch einmal ein.

Anmerkung: Die Cursor-Tasten sollten ebenfalls funktionieren. Aber wenn Du
	   hjkl benutzt, wirst Du in der Lage sein, Dich sehr viel schneller
	   umherzubewegen, wenn Du Dich einmal daran gewöhnt hast. Wirklich!
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Lektion 1.1.2: VIM BEENDEN


  !! Hinweis: Bevor Du einen der unten aufgeführten Schritte ausführst, lies
     diese gesamte Lektion!!

  1. Drücke die <ESC> Taste (um sicherzustellen, dass Du im Normalmodus bist).

  2. Tippe:	:q! <ENTER>.
     Dies beendet den Editor und VERWIRFT alle Änderungen, die Du gemacht hast.

  3. Wenn Du die Eingabeaufforderung siehst, gib das Kommando ein, das Dich zu
     diesem Tutor geführt hat. Dies wäre:	vimtutor <ENTER>

  4. Wenn Du Dir diese Schritte eingeprägt hast und Du Dich sicher fühlst,
     führe Schritte 1 bis 3 aus, um den Editor zu verlassen und wieder
     hineinzugelangen.

Anmerkung:  :q! <ENTER>  verwirft alle Änderungen, die Du gemacht hast. Einige
     Lektionen später lernst Du, die Änderungen in einer Datei zu speichern.

  5. Bewege den Cursor abwärts zu Lektion 1.1.3.
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.1.3: TEXT EDITIEREN - LÖSCHEN


	 ** Drücke  x  , um das Zeichen unter dem Cursor zu löschen. **

  1. Bewege den Cursor zu der mit ---> markierten Zeile unten.

  2. Um die Fehler zu beheben, bewege den Cursor, bis er über dem Zeichen steht,
     das gelöscht werden soll.

  3. Drücke die  x  Taste, um das unerwünschte Zeichen zu löschen.

  4. Wiederhole die Schritte 2 bis 4, bis der Satz korrekt ist.

---> Die Kkuh sprangg übberr deen Moond.

  5. Nun, da die Zeile korrekt ist, gehe weiter zur Lektion 1.1.4.

Anmerkung: Während Du durch diesen Tutor gehst, versuche nicht, auswendig zu
    lernen, lerne vielmehr durch Anwenden.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.1.4: TEXT EDITIEREN - EINFÜGEN


		    **  Drücke  i  , um Text einzufügen. **

  1. Bewege den Cursor zur ersten unten stehenden mit ---> markierten Zeile.

  2. Um die erste Zeile mit der zweiten gleichzumachen, bewege den Cursor auf
     das erste Zeichen NACH der Stelle, an der Text eingefügt werden soll.

  3. Drücke  i  und gib die nötigen Ergänzungen ein.

  4. Wenn jeweils ein Fehler beseitigt ist, drücke <ESC> , um zum Normalmodus
     zurückzukehren.
		 Wiederhole Schritte 2 bis 4, um den Satz zu korrigieren.

---> In dieser ft etwas .
---> In dieser Zeile fehlt etwas Text.

  5. Wenn Du Dich mit dem Einfügen von Text sicher fühlst, gehe zu Lektion 1.1.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.1.5: TEXT EDITIEREN - ANFÜGEN


		     ** Drücke  A  , um Text anzufügen. **

  1. Bewege den Cursor zur ersten unten stehenden mit ---> markierten Zeile.
     Dabei ist gleichgültig, auf welchem Zeichen der Zeile der Cursor steht.

  2. Drücke  A  und gib die erforderlichen Ergänzungen ein.

  3. Wenn das Anfügen abgeschlossen ist, drücke <ESC>, um in den Normalmodus
     zurückzukehren.

  4. Bewege den Cursor zur zweiten mit ---> markierten Zeile und wiederhole
     die Schritte 2 und 3, um den Satz zu auszubessern.

---> In dieser Zeile feh
     In dieser Zeile fehlt etwas Text.
---> Auch hier steh
     Auch hier steht etwas Unvollständiges.

  5. Wenn Du dich mit dem Anfügen von Text sicher fühlst, gehe zu Lektion 1.1.6.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lektion 1.1.6: EINE DATEI EDITIEREN

		 ** Benutze  :wq  , um eine Datei zu speichern und Vim zu verlassen. **

  !! Hinweis: Bevor Du einen der unten aufgeführten Schritte ausführst, lies
     diese gesamte Lektion!!

  1. Verlasse den Editor so wie in Lektion 1.1.2:  :q!       
	   Oder, falls du Zugriff zu einem anderen Terminal hast, führe das 
		 Folgende dort aus.

  2. Gib dieses Kommando in die Eingabeaufforderung ein:  vim tutor <ENTER>
     'vim' ist der Aufruf des Editors, 'tutor' ist die zu editierende Datei.
     Benutze eine Datei, die geändert werden darf.

  3. Füge Text ein oder lösche ihn, wie Du in den vorangehenden Lektionen 
     gelernt hast.

  4. Speichere die geänderte Datei und verlasse Vim mit:  :wq  <ENTER>

  5. Falls Du in Schritt 1 den vimtutor beendet hast, starte vimtutor neu und
	   bewege dich abwärts bis zur folgenden Zusammenfassung.

  6. Nachdem Du obige Schritte gelesen und verstanden hast: führe sie durch.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ZUSAMMENFASSUNG VON LEKTION 1.1


  1. Der Cursor wird mit den Pfeiltasten oder den Tasten hjkl bewegt.
	 h (links)     j (unten)     k (aufwärts)    l (rechts)

  2. Um Vim aus der Eingabeaufforderung zu starten, tippe: vim DATEI <ENTER>

  3. Um Vim zu verlassen und alle Änderungen zu verwerfen, tippe:
		<ESC>  :q!  <ENTER> .

  4. Um das Zeichen unter dem Cursor zu löschen, tippe:  x

  5. Um Text einzufügen oder anzufügen, tippe:
	 i   Einzufügenden Text eingeben   <ESC>    Einfügen vor dem Cursor
	 A   Anzufügenden Text eingeben    <ESC>    Anfügen nach dem Zeilenende

Anmerkung: Drücken von <ESC> bringt Dich in den Normalmodus oder bricht ein
     ungewolltes, erst teilweise eingegebenes Kommando ab.

     Nun fahre mit Lektion 1.2 fort.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Lektion 1.2.1: LÖSCHKOMMANDOS


		   ** Tippe  dw  , um ein Wort zu löschen. **

  1. Drücke  <ESC>  , um sicherzustellen, dass Du im Normalmodus bist.

  2. Bewege den Cursor zu der mit ---> markierten Zeile unten.

  3. Bewege den Cursor zum Anfang eines Wortes, das gelöscht werden soll.

  4. Tippe  dw  , um das Wort zu entfernen.

  Anmerkung: Der Buchstabe  d  erscheint auf der untersten Zeile des Schirms,
        wenn Du ihn eingibst. Vim wartet darauf, dass Du  w  eingibst. Falls Du
        ein anderes Zeichen als  d  siehst, hast Du etwas Falsches getippt;
        drücke <ESC> und beginne noch einmal.

---> Einige Wörter lustig gehören nicht Papier in diesen Satz.

  5. Wiederhole die Schritte 3 und 4, bis der Satz korrekt ist und gehe
     zur Lektion 1.2.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lektion 1.2.2: WEITERE LÖSCHKOMMANDOS


	    ** Tippe  d$  , um bis zum Ende der Zeile zu löschen. **

  1. Drücke <ESC> , um sicherzustellen, dass Du im Normalmodus bist.

  2. Bewege den Cursor zu der mit ---> markierten Zeile unten.

  3. Bewege den Cursor zum Ende der korrekten Zeile (NACH dem ersten . ).

  4. Tippe    d$    , um bis zum Zeilenende zu löschen.

---> Jemand hat das Ende der Zeile doppelt eingegeben. doppelt eingegeben.


  5. Gehe weiter zur Lektion 1.2.3 , um zu verstehen, was hierbei vorgeht.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Lektion 1.2.3: ÜBER OPERATOREN UND BEWEGUNGSZÜGE


  Viele Kommandos, die Text ändern, setzen sich aus einem Operator und einer
  Bewegung zusammen. Das Format für ein Löschkommando mit dem Löschoperator  d
  lautet wie folgt:

    d  Bewegung

  wobei:
    d        - der Löschoperator
    Bewegung - worauf der Löschoperator angewandt wird (unten aufgeführt).

  Eine kleine Auflistung von Bewegungen:
    w - bis zum Beginn des nächsten Wortes OHNE dessen erstes Zeichen.
    e - zum Ende des aktuellen Wortes MIT dessen letztem Zeichen.
    $ - zum Ende der Zeile MIT dem letzten Zeichen.

  Dementsprechend löscht die Eingabe von  de  vom Cursor an bis zum Wortende.

Anmerkung:  Die Eingabe lediglich des Bewegungsteils im Normalmodus bewegt den
  Cursor entsprechend.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Lektion 1.2.4: ANWENDUNG EINES ZÄHLERS FÜR EINEN BEWEGUNGSSCHRITT


   ** Die Eingabe einer Zahl vor einem Bewegungsschritt wiederholt diesen. **

  1. Bewege den Cursor zum Beginn der mit ---> markierten Zeile unten.

  2. Tippe  2w  , um den Cursor zwei Wörter vorwärts zu bewegen.

  3. Tippe  3e  , um den Cursor zum Ende des dritten Wortes zu bewegen.

  4. Tippe  0  (Null) , um zum Anfang der Zeile zu gelangen.

  5. Wiederhole Schritte 2 und 3 mit verschiedenen Nummern.

  ---> Dies ist nur eine Zeile aus Wörtern, um sich darin herumzubewegen.

  6. Gehe weiter zu Lektion 1.2.5.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	 Lektion 1.2.5: ANWENDUNG EINES ZÄHLERS FÜR MEHRERE LÖSCHVORGÄNGE


   ** Die Eingabe einer Zahl mit einem Operator wiederholt diesen mehrfach. **

  In der Kombination aus Löschoperator und Bewegungsschritt (siehe oben) 
  stellt man, um mehr zu löschen dem Schritt einen Zähler voran:
	 d  Nummer  Bewegungsschritt

  1. Bewege den Cursor zum ersten Wort in GROSSBUCHSTABEN in der mit --->
     markieren Zeile.

  2. Tippe  d2w  , um die zwei Wörter in GROSSBUCHSTABEN zu löschen.

  3. Wiederhole Schritte 1 und  2 mit einem anderen Zähler, um die darauffol-
     genden Wörter in GROSSBUCHSTABEN mit einem einzigen Kommando zu löschen.

--->  Diese ABC DE Zeile FGHI JK LMN OP mit Wörtern ist Q RS TUV bereinigt.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lektion 1.2.6: ARBEITEN AUF ZEILEN


	       ** Tippe  dd  , um eine ganze Zeile zu löschen. **

  Wegen der Häufigkeit, dass man ganze Zeilen löscht, kamen die Entwickler von
  Vi darauf, dass es leichter wäre, einfach zwei d's einzugeben, um eine Zeile
  zu löschen.

  1. Bewege den Cursor zur zweiten Zeile in der unten stehenden Redewendung.
  2. Tippe  dd  , um die Zeile zu löschen.
  3. Nun bewege Dich zur vierten Zeile.
  4. Tippe  2dd  , um zwei Zeilen zu löschen.

--->  1)  Rosen sind rot,
--->  2)  Matsch ist lustig,
--->  3)  Veilchen sind blau,
--->  4)  Ich habe ein Auto,
--->  5)  Die Uhr sagt die Zeit,
--->  6)  Zucker ist süß,
--->  7)  So wie Du auch.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.2.7: RÜCKGÄNGIG MACHEN (UNDO)


	 ** Tippe u , um die letzten Kommandos rückgängig zu machen **
	      ** oder U , um eine ganze Zeile wiederherzustellen. **

  1. Bewege den Cursor zu der mit ---> markierten Zeile unten
     und setze ihn auf den ersten Fehler.
  2. Tippe  x  , um das erste unerwünschte Zeichen zu löschen.
  3. Nun tippe  u  , um das soeben ausgeführte Kommando rückgängig zu machen.
  4. Jetzt behebe alle Fehler auf der Zeile mit Hilfe des x  Kommandos.
  5. Nun tippe ein großes  U , um die Zeile in ihren Ursprungszustand
     wiederherzustellen.
  6. Nun tippe  u  einige Male, um das U und die vorhergehenden Kommandos
     rückgängig zu machen.
  7. Nun tippe CTRL-R (halte CTRL gedrückt und drücke R) mehrere Male, um die
     Kommandos wiederherzustellen (die Rückgängigmachungen rückgängig machen).

---> Beehebe die Fehller diesser Zeile und sttelle sie mitt 'undo' wieder her.

  8. Dies sind sehr nützliche Kommandos.  Nun gehe weiter zur Zusammenfassung 
     von Lektion 1.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ZUSAMMENFASSUNG VON LEKTION 1.2


  1. Um vom Cursor bis zum nächsten Wort zu löschen, tippe:    dw
  2. Um vom Cursor bis zum Ende einer Zeile zu löschen, tippe:     d$
  3. Um eine ganze Zeile zu löschen, tippe:    dd

  4. Um eine Bewegung zu wiederholen, stelle eine Nummer voran:   2w
  5. Das Format für ein Änderungskommando ist:
               Operator   [Anzahl]   Bewegungsschritt
     wobei:
       Operator - gibt an, was getan werden soll, zum Beispiel  d  für delete
       [Anzahl] - ein optionaler Zähler, um den Bewegungsschritt zu wiederholen
       Bewegungsschritt - Bewegung über den zu ändernden Text, wie
		  w (Wort), $ (zum Ende der Zeile), etc.

  6. Um Dich zum Anfang der Zeile zu begeben, benutze die Null:  0

  7. Um vorherige Aktionen rückgängig zu machen, tippe:		u (kleines u)
     Um alle Änderungen auf einer Zeile rückgängig zu machen:   U (großes U)
     Um die Rückgängigmachungen rückgängig zu machen, tippe:    CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   Lektion 1.3.1: ANFÜGEN (PUT)


     ** Tippe  p  , um vorher gelöschten Text nach dem Cursor anzufügen. **

  1. Bewege den Cursor zur ersten unten stehenden mit ---> markierten Zeile.

  2. Tippe  dd  , um die Zeile zu löschen und sie in einem Vim-Register zu
     speichern.

  3. Bewege den Cursor zur Zeile c), ÜBER derjenigen, wo die gelöschte Zeile
     platziert werden soll.

  4.  Tippe   p   , um die Zeile unterhalb des Cursors zu platzieren.

  5. Wiederhole die Schritte 2 bis 4, um alle Zeilen in die richtige
     Reihenfolge zu bringen.

---> d) Kannst Du das auch?
---> b) Veilchen sind blau,
---> c) Intelligenz ist lernbar,
---> a) Rosen sind rot,
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lektion 1.3.2: ERSETZEN (REPLACE)


   ** Tippe  rx  , um das Zeichen unter dem Cursor durch  x zu ersetzen. **

  1. Bewege den Cursor zur ersten unten stehenden mit ---> markierten Zeile.

  2. Bewege den Cursor, bis er sich auf dem ersten Fehler befindet.

  3. Tippe  r  und anschließend das Zeichen, welches dort stehen sollte.

  4. Wiederhole Schritte 2 und 3, bis die erste Zeile gleich der zweiten ist.

--->  Alf diese Zeite eingegoben wurde, wurden einike falsche Tasten gelippt!
--->  Als diese Zeile eingegeben wurde, wurden einige falsche Tasten getippt!

  5. Nun fahre fort mit Lektion 1.3.2.

Anmerkung: Erinnere Dich daran, dass Du durch Anwenden lernen solltest, nicht 
     durch Auswendiglernen.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Lektion 1.3.3: ÄNDERN (CHANGE)


      ** Um eine Änderung bis zum Wortende durchzuführen, tippe  ce . **

  1. Bewege den Cursor zur ersten unten stehenden mit ---> markierten Zeile.

  2. Platziere den Cursor auf das  s  von Wstwr.

  3. Tippe  ce  und die Wortkorrektur ein (in diesem Fall tippe  örter ).

  4. Drücke <ESC> und bewege den Cursor zum nächsten zu ändernden Zeichen.

  5. Wiederhole Schritte 3 und 4 bis der erste Satz gleich dem zweiten ist.

---> Einige Wstwr dieser Zlaww lasdjlaf mit dem Ändern-Operator gaaauu werden.
---> Einige Wörter dieser Zeile sollen mit dem Ändern-Operator geändert werden.

Beachte, dass  ce  das Wort löscht und Dich in den Eingabemodus versetzt.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.3.4: MEHR ÄNDERUNGEN MITTELS c


     ** Das change-Kommando arbeitet mit denselben Bewegungen wie delete.  **

  1. Der change Operator arbeitet in gleicher Weise wie delete. Das Format ist:

         c    [Anzahl]  Bewegungsschritt

  2. Die Bewegungsschritte sind die gleichen , so wie  w  (Wort) und  $
     (Zeilenende).

  3. Bewege Dich zur ersten unten stehenden mit ---> markierten Zeile.

  4. Bewege den Cursor zum ersten Fehler.

  5. Tippe  c$  , gib den Rest der Zeile wie in der zweiten ein, drücke <ESC> .

---> Das Ende dieser Zeile soll an die zweite Zeile angeglichen werden.
---> Das Ende dieser Zeile soll mit dem  c$  Kommando korrigiert werden.

Anmerkung: Du kannst die Rücktaste benutzen, um Tippfehler zu korrigieren.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ZUSAMMENFASSUNG VON LEKTION 1.3


  1. Um einen vorher gelöschten Text anzufügen, tippe   p . Dies fügt den
     gelöschten Text NACH dem Cursor an (wenn eine ganze Zeile gelöscht wurde,
     wird diese in die Zeile unter dem Cursor eingefügt).

  2. Um das Zeichen unter dem Cursor zu ersetzen, tippe   r   und danach das 
     an dieser Stelle gewollte Zeichen.

  3. Der Änderungs- (change) Operator erlaubt, vom Cursor bis zum Ende des
     Bewegungsschrittes zu ändern. Tippe  ce  , um eine Änderung vom Cursor bis
     zum Ende des Wortes vorzunehmen;  c$  bis zum Ende einer Zeile.

  4. Das Format für change ist:

	 c   [Anzahl]  Bewegungsschritt

  Nun fahre mit der nächsten Lektion fort.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lektion 1.4.1: CURSORPOSITION UND DATEISTATUS

 ** Tippe CTRL-G , um deine Dateiposition sowie den Dateistatus anzuzeigen. **
     ** Tippe G , um Dich zu einer Zeile in der Datei zu begeben. **

Anmerkung: Lies diese gesamte Lektion, bevor Du irgendeinen Schritt ausführst!!

  1. Halte die Ctrl Taste unten und drücke  g . Dies nennen wir CTRL-G.
     Eine Statusmeldung am Fuß der Seite erscheint mit dem Dateinamen und der
     Position innerhalb der Datei. Merke Dir die Zeilennummer für Schritt 3.

Anmerkung: Möglicherweise siehst Du die Cursorposition in der unteren rechten
      Bildschirmecke. Dies ist Auswirkung der 'ruler' Option 
	  (siehe :help 'ruler')

  2. Drücke  G  , um Dich zum Ende der Datei zu begeben.
     Tippe  gg  , um Dich zum Anfang der Datei zu begeben.

  3. Gib die Nummer der Zeile ein, auf der Du vorher warst, gefolgt von  G .
     Dies bringt Dich zurück zu der Zeile, auf der Du gestanden hast, als Du
     das erste Mal CTRL-G gedrückt hast.

  4. Wenn Du Dich sicher genug fühlst, führe die Schritte 1 bis 3 aus.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lektion 1.4.2: DAS SUCHEN - KOMMANDO


  ** Tippe  /  gefolgt von einem Ausdruck, um nach dem Ausdruck zu suchen. **

  1. Im Normalmodus, tippe das  /  Zeichen.  Beachte, dass das  / und der
     Cursor am Fuß des Schirms erscheinen, so wie beim :	Kommando.

  2. Nun tippe 'Fehhler' <ENTER>. Dies ist das Wort, nach dem Du suchen willst.

  3. Um nach demselben Ausdruck weiterzusuchen, tippe einfach  n (für next).
     Um nach demselben Ausdruck in der Gegenrichtung zu suchen, tippe  N .

  4. Um nach einem Ausdruck rückwärts zu suchen , benutze  ?  statt  / .

  5. Um dahin zurückzukehren, von wo Du gekommen bist, drücke CTRL-O (Halte
     Ctrl unten und drücke den Buchstaben o). Wiederhole dies, um noch weiter
     zurückzugehen.  CTRL-I geht vorwärts.

--->  Fehler schreibt sich nicht "Fehhler"; Fehhler ist ein Fehler
Anmerkung: Wenn die Suche das Dateiende erreicht hat, wird sie am Anfang
        fortgesetzt, es sei denn, die 'wrapscan' Option wurde abgeschaltet.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.4.3: PASSENDE KLAMMERN FINDEN


   ** Tippe  % , um eine gegenüberliegenden Klammer ),], oder } zu finden. **

  1. Platziere den Cursor auf irgendeinem der Zeichen (, [, oder { in der unten
     stehenden Zeile, die mit ---> markiert ist.

  2. Nun tippe das  %  Zeichen.

  3. Der Cursor bewegt sich zur passenden gegenüberliegenden Klammer.

  4. Tippe  % , um den Cursor zur passenden anderen Klammer zu bewegen.

  5. Setze den Cursor auf ein anderes (,),[,],{ oder } und probiere  %  aus.

---> Dies ( ist eine Testzeile ( mit [ verschiedenen ] { Klammern }  darin. ))

Anmerkung: Diese Funktionalität ist sehr nützlich bei der Fehlersuche in einem
     Programmtext, in dem passende Klammern fehlen!


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lektion 1.4.4: DAS ERSETZUNGSKOMMANDO (SUBSTITUTE)


	 ** Tippe :s/alt/neu/g  , um 'alt' durch 'neu' zu ersetzen. **

  1. Bewege den Cursor zu der unten stehenden mit ---> markierten Zeile.

  2. Tippe  :s/diee/die <ENTER> .  Beachte, dass der Befehl nur das erste
     Vorkommen von "diee" ersetzt.

  3. Nun tippe   :s/diee/die/g . Das Zufügen des Flags  g   bedeutet, eine
     globale Ersetzung über die Zeile durchzuführen, dies ersetzt alle 
	 Vorkommen von "diee" auf der Zeile.

---> diee schönste Zeit, um diee Blumen anzuschauen, ist diee Frühlingszeit.

  4. Um alle Vorkommen einer Zeichenkette innerhalb zweier Zeilen zu ändern,
     tippe  :#,#s/alt/neu/g  wobei #,# die Zeilennummern des Bereiches sind,
                             in dem die Ersetzung durchgeführt werden soll.
     Tippe  :%s/alt/neu/g    um alle Vorkommen in der gesamten Datei zu ändern.
     Tippe  :%s/alt/neu/gc   um alle Vorkommen in der gesamten Datei zu finden
                     mit einem Fragedialog, ob ersetzt werden soll oder nicht.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ZUSAMMENFASSUNG VON LEKTION 1.4

  1. CTRL-G  zeigt die aktuelle Dateiposition sowie den Dateistatus.
             G  bringt Dich zum Ende der Datei.
     Nummer  G  bringt Dich zur entsprechenden Zeilennummer.
            gg  bringt Dich zur ersten Zeile.

  2. Die Eingabe von  /  plus einem Ausdruck sucht VORWÄRTS nach dem Ausdruck.
     Die Eingabe von  ?  plus einem Ausdruck sucht RÜCKWÄRTS nach dem Ausdruck.
     Tippe nach einer Suche  n  , um das nächste Vorkommen in der gleichen
     Richtung zu finden; oder  N  , um in der Gegenrichtung zu suchen.
     CTRL-O bringt Dich zurück zu älteren Positionen, CTRL-I zu neueren.

  3. Die Eingabe von  %  , wenn der Cursor sich auf (,),[,],{, oder }
     befindet, bringt Dich zur Gegenklammer.

  4. Um das erste Vorkommen von "alt" in einer Zeile durch "neu" zu ersetzen,
             tippe       :s/alt/neu
     Um alle Vorkommen von "alt" in der Zeile ersetzen, tippe  :s/alt/neu/g
     Um Ausdrücke innerhalb zweier Zeilen # zu ersetzen        :#,#s/alt/neu/g
     Um alle Vorkommen in der ganzen Datei zu ersetzen, tippe  :%s/alt/neu/g
     Für eine jedesmalige Bestätigung, addiere 'c' (confirm)   :%s/alt/neu/gc
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lektion 1.5.1: AUSFÜHREN EINES EXTERNEN KOMMANDOS


  ** Gib  :! , gefolgt von einem externen Kommando ein, um es auszuführen. **

  1. Tippe das vertraute Kommando  :  , um den Cursor auf den Fuß des Schirms
     zu setzen. Dies erlaubt Dir, ein Kommandozeilen-Kommando einzugeben.

  2. Nun tippe ein  !  (Ausrufezeichen).  Dies ermöglicht Dir, ein beliebiges,
     externes Shellkommando auszuführen.

  3. Als Beispiel tippe   ls   nach dem  !  und drücke <ENTER>. Dies liefert
     eine Auflistung deines Verzeichnisses; genauso, als wenn Du in der
     Eingabeaufforderung wärst.  Oder verwende  :!dir  , falls ls nicht geht.

Anmerkung:  Mit dieser Methode kann jedes beliebige externe Kommando
     ausgeführt werden, auch mit Argumenten.

Anmerkung:  Alle  :  Kommandos müssen durch Eingabe von <ENTER>
     abgeschlossen werden. Von jetzt an erwähnen wir dies nicht jedesmal.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lektion 1.5.2: MEHR ÜBER DAS SCHREIBEN VON DATEIEN


** Um am Text durchgeführte Änderungen zu speichern, tippe :w DATEINAME. **

  1. Tippe  :!dir  oder  :!ls  , um eine Auflistung deines Verzeichnisses zu
     erhalten.  Du weißt nun bereits, dass Du danach <ENTER> eingeben musst.

  2. Wähle einen Dateinamen, der noch nicht existiert, z.B. TEST.

  3. Nun tippe:  :w TEST   (wobei TEST der gewählte Dateiname ist).

  4. Dies speichert die ganze Datei (den Vim Tutor) unter dem Namen TEST.
     Um dies zu überprüfen, tippe nochmals  :!ls  bzw.  !dir, um deinen
     Verzeichnisinhalt zu sehen.

Anmerkung: Würdest Du Vim jetzt beenden und danach wieder mit vim TEST
    starten, dann wäre diese Datei eine exakte Kopie des Tutors zu dem
    Zeitpunkt, als Du ihn gespeichert hast.

  5. Nun entferne die Datei durch Eingabe von (MS-DOS):    :!del TEST
                      oder (Unix):                         :!rm TEST
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Lektion 1.5.3: AUSWÄHLEN VON TEXT ZUM SCHREIBEN

** Um einen Abschnitt der Datei zu speichern,  tippe  v  Bewegung  :w DATEI **

  1. Bewege den Cursor zu dieser Zeile.

  2. Tippe  v  und bewege den Cursor zum fünften Auflistungspunkt unten.
     Beachte, dass der Text hervorgehoben wird.

  3. Drücke das Zeichen  : . Am Fuß des Schirms erscheint  :'<,'> .

  4. Tippe  w TEST  , wobei TEST ein noch nicht vorhandener Dateiname ist.
     Vergewissere Dich, dass Du  :'<,'>w TEST  siehst, bevor Du <ENTER> drückst.

  5. Vim schreibt die ausgewählten Zeilen in die Datei TEST. Benutze  :!dir
     oder  :!ls , um sie zu sehen. Lösche sie noch nicht! Wir werden sie in
     der nächsten Lektion benutzen.

Hinweis: Drücken von  v  startet die Visuelle Auswahl. Du kannst den Cursor
   umherbewegen, um die Auswahl zu vergrößern oder zu verkleinern. Anschließend
   lässt sich ein Operator anwenden, um mit dem Text etwas zu tun. Zum Beispiel
   löscht  d  den Text.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	      Lektion 1.5.4: EINLESEN UND ZUSAMMENFÜHREN VON DATEIEN


       ** Um den Inhalt einer Datei einzulesen, tippe  :r DATEINAME  **

  1. Platziere den Cursor direkt über dieser Zeile.

BEACHTE:  Nachdem Du Schritt 2 ausgeführt hast, wirst Du Text aus Lektion 1.5.3
       sehen. Dann bewege Dich wieder ABWÄRTS, Lektion 1.5.4 wiederzusehen.

  2. Nun lies deine Datei TEST ein indem Du das Kommando  :r TEST  ausführst,
     wobei TEST der von Dir verwendete Dateiname ist.
     Die eingelesene Datei wird unterhalb der Cursorzeile eingefügt.

  3. Um zu überprüfen, dass die Datei eingelesen wurde, gehe zurück und 
     beachte, dass es jetzt zwei Kopien von Lektion 1.5.3 gibt, das Original und 
	 die eingefügte Dateiversion.

Anmerkung: Du kannst auch die Ausgabe eines externen Kommandos einlesen. Zum
     Beispiel liest  :r !ls  die Ausgabe des Kommandos ls ein und platziert
     sie unterhalb des Cursors.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ZUSAMMENFASSUNG VON LEKTION 1.5


  1. :!Kommando  führt ein externes Kommando aus.

      Einige nützliche Beispiele sind
	(MS-DOS)	  (Unix)
	 :!dir		   :!ls		   -  zeigt eine Verzeichnisauflistung.
	 :!del DATEINAME   :!rm DATEINAME  -  entfernt Datei DATEINAME.

  2. :w DATEINAME  speichert die aktuelle Vim-Datei unter dem Namen  DATEINAME.

  3. v  Bewegung  :w DATEINAME  schreibt die Visuell ausgewählten Zeilen in
     die Datei DATEINAME.

  4. :r DATEINAME  lädt die Datei DATEINAME und fügt sie unterhalb der
     Cursorposition ein.

  5. :r !dir  liest die Ausgabe des Kommandos dir und fügt sie unterhalb der
     Cursorposition ein.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lektion 1.6.1: ZEILEN ÖFFNEN (OPEN)


   ** Tippe  o	, um eine Zeile unterhalb des Cursors zu öffnen und Dich in **
                      ** den Einfügemodus zu begeben. **

  1. Bewege den Cursor zu der ersten mit ---> markierten Zeile unten.

  2. Tippe o (klein geschrieben), um eine Zeile UNTERHALB des Cursors zu öffnen
     und Dich in den Einfügemodus zu begeben.

  3. Nun tippe etwas Text und drücke <ESC> , um den Einfügemodus zu verlassen.

---> Mit  o  wird der Cursor auf der offenen Zeile im Einfügemodus platziert.

  4. Um eine Zeile ÜBERHALB des Cursors aufzumachen, gib einfach ein großes  O
     statt einem kleinen  o  ein. Versuche dies auf der unten stehenden Zeile.

---> Öffne eine Zeile über dieser mit O , wenn der Cursor auf dieser Zeile ist.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lektion 1.6.2: TEXT ANFÜGEN (APPEND)


	     ** Tippe  a  , um Text NACH dem Cursor einzufügen. **

  1. Bewege den Cursor zum Anfang der ersten Übungszeile mit ---> unten.

  2. Drücke  e  , bis der Cursor am Ende von  Zei  steht.

  3. Tippe ein kleines  a  , um Text NACH dem Cursor anzufügen.

  4. Vervollständige das Wort so wie in der Zeile darunter.  Drücke <ESC> ,
     um den Einfügemodus zu verlassen.

  5. Bewege Dich mit  e  zum nächsten unvollständigen Wort und wiederhole
     Schritte 3 und 4.

---> Diese Zei bietet Gelegen , Text in einer Zeile anzufü.
---> Diese Zeile bietet Gelegenheit, Text in einer Zeile anzufügen.

Anmerkung:  a, i und A gehen alle gleichermaßen in den Einfügemodus; der
            einzige Unterschied ist, wo die Zeichen eingefügt werden.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	      Lektion 1.6.3: EINE ANDERE ART DES ERSETZENS (REPLACE)


       ** Tippe ein großes  R  , um mehr als ein Zeichen zu ersetzen. **

  1. Bewege den Cursor zur ersten unten stehenden, mit ---> markierten Zeile.
     Bewege den Cursor zum Anfang des ersten  xxx .

  2. Nun drücke  R  und tippe die Nummer, die darunter in der zweiten Zeile
     steht, so dass diese das xxx ersetzt.

  3. Drücke <ESC> , um den Ersetzungsmodus zu verlassen. Beachte, dass der Rest
     der Zeile unverändert bleibt.

  4. Wiederhole die Schritte, um das verbliebene xxx zu ersetzen.

---> Das Addieren von 123 zu xxx ergibt xxx.
---> Das Addieren von 123 zu 456 ergibt 579.

Anmerkung: Der Ersetzungsmodus ist wie der Einfügemodus, aber jedes eingetippte
           Zeichen löscht ein vorhandenes Zeichen.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lektion 1.6.4: TEXT KOPIEREN UND EINFÜGEN

 ** Benutze den  y  Operator, um Text zu kopieren;  p  , um ihn einzufügen **

  1. Gehe zu der mit ---> markierten Zeile unten; setze den Cursor hinter "a)".

  2. Starte den Visuellen Modus mit  v  , bewege den Cursor genau vor "erste".

  3. Tippe  y  , um den hervorgehoben Text zu kopieren.

  4. Bewege den Cursor zum Ende der nächsten Zeile:  j$

  5. Tippe  p , um den Text einzufügen und anschließend:  a zweite <ESC> .

  6. Benutze den Visuellen Modus, um " Eintrag." auszuwählen, kopiere mittels
     y , bewege Dich zum Ende der nächsten Zeile mit  j$  und füge den Text
     dort mit  p  an.

--->  a) dies ist der erste Eintrag.
      b)

Anmerkung: Du kannst  y  auch als Operator verwenden;  yw  kopiert ein Wort.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Lektion 1.6.5: OPTIONEN SETZEN

      ** Setze eine Option so, dass eine Suche oder Ersetzung Groß- **
		      ** und Kleinschreibung ignoriert **

  1. Suche nach 'ignoriere', indem Du    /ignoriere   eingibst.
     Wiederhole die Suche einige Male, indem Du die n - Taste drückst.

  2. Setze die 'ic' (Ignore case) - Option, indem Du   :set ic   eingibst.

  3. Nun suche wieder nach 'ignoriere', indem Du  n  tippst.
     Beachte, dass jetzt Ignoriere und auch IGNORIERE gefunden wird.

  4. Setze die 'hlsearch' und 'incsearch' - Optionen:     :set hls is

  5. Wiederhole die Suche und beobachte, was passiert: /ignoriere <ENTER>

  6. Um das Ignorieren von Groß/Kleinschreibung abzuschalten, tippe:  :set noic

Anmerkung: Um die Hervorhebung der Treffer zu entfernen, gib ein:  :nohlsearch
Anmerkung: Um die Schreibweise für eine einzige Suche zu ignorieren, benutze \c
           im Suchausdruck:  /ignoriere\c  <ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ZUSAMMENFASSUNG VON LEKTION 1.6

 1. Tippe  o  , um eine Zeile UNTER dem Cursor zu öffnen und den Einfügemodus
                zu starten
    Tippe  O  , um eine Zeile ÜBER dem Cursor zu öffnen.

 2. Tippe  a  , um Text NACH dem Cursor anzufügen.
    Tippe  A  , um Text nach dem Zeilenende anzufügen.

 3. Das Kommando  e  bringt Dich zum Ende eines Wortes.

 4. Der Operator  y  (yank) kopiert Text,  p  (put) fügt ihn ein.

 5. Ein großes  R  geht in den Ersetzungsmodus bis zum Drücken von  <ESC> .

 6. Die Eingabe von ":set xxx" setzt die Option "xxx". Einige Optionen sind:
	'ic' 'ignorecase'    Ignoriere Groß/Kleinschreibung bei einer Suche
	'is' 'incsearch'     Zeige Teilübereinstimmungen für einen Suchausdruck
	'hls' 'hlsearch'     Hebe alle passenden Ausdrücke hervor
    Der Optionsname kann in der Kurz- oder der Langform angegeben werden.

 7. Stelle einer Option "no" voran, um sie abzuschalten:   :set noic
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lektion 1.7.1: AUFRUFEN VON HILFE


		     ** Nutze das eingebaute Hilfesystem **

  Vim besitzt ein umfassendes eingebautes Hilfesystem.  Für den Anfang probiere
  eins der drei folgenden Dinge aus:
	- Drücke die <Hilfe> - Taste (falls Du eine besitzt)
	- Drücke die <F1> Taste (falls Du eine besitzt)
	- Tippe   :help <ENTER>

  Lies den Text im Hilfefenster, um zu verstehen wie die Hilfe funktioniert.
  Tippe  CTRL-W CTRL-W   , um von einem Fenster zum anderen zu springen.
  Tippe   :q <ENTER>  , um das Hilfefenster zu schließen.

  Du kannst Hilfe zu praktisch jedem Thema finden, indem Du dem ":help"-
  Kommando ein Argument gibst.  Probiere folgendes (<ENTER> nicht vergessen):

	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lektion 1.7.2: ERSTELLE EIN START-SKRIPT


	          **  Aktiviere die Features von Vim **

  Vim besitzt viele Funktionalitäten, die über Vi hinausgehen, aber die meisten
  von ihnen sind standardmäßig deaktiviert. Um mehr Funktionalitäten zu nutzen,
  musst Du eine "vimrc" - Datei erstellen.

  1. Starte das Editieren der "vimrc"-Datei, abhängig von deinem System:
	:e ~/.vimrc		für Unix
	:e ~/_vimrc		für MS-Windows

  2. Nun lies den Inhalt der Beispiel-"vimrc"-Datei ein:
	:r $VIMRUNTIME/vimrc_example.vim

  3. Speichere die Datei mit:
	:w

  Beim nächsten Start von Vim wird die Syntaxhervorhebung aktiviert sein.
  Du kannst all deine bevorzugten Optionen zu dieser "vimrc"-Datei zufügen.
  Für mehr Informationen tippe  :help vimrc-intro
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Lektion 1.7.3: VERVOLLSTÄNDIGEN


	   ** Kommandozeilenvervollständigung mit CTRL-D und <TAB> **

  1. Stelle sicher, dass Vim nicht im Vi-Kompatibilitätsmodus ist:  :set nocp

  2. Siehe nach, welche Dateien im Verzeichnis existieren:  :!ls  oder  :!dir

  3. Tippe den Beginn eines Kommandos:  :e

  4. Drücke  CTRL-D  und Vim zeigt eine Liste mit "e" beginnender Kommandos.

  5. Drücke  <TAB>  und Vim vervollständigt den Kommandonamen zu ":edit".

  6. Nun füge ein Leerzeichen und den Anfang einer existierenden Datei an:
     :edit DAT

  7. Drücke <TAB>. Vim vervollständigt den Namen (falls er eindeutig ist).

Anmerkung: Vervollständigung funktioniert für viele Kommandos. Probiere
     einfach CTRL-D und <TAB>.  Dies ist insbesondere nützlich für  :help .
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				     ZUSAMMENFASSUNG VON LEKTION 1.7


  1. Tippe  :help  oder drücke <F1> oder <Help>, um ein Hilfefenster zu öffnen.

  2. Tippe  :help Kommando  , um Hilfe über  Kommando  zu erhalten.

  3. Tippe  CTRL-W CTRL-W  , um zum anderen Fenster zu springen.

  4. Tippe  :q  , um das Hilfefenster zu schließen.

  5. Erstelle ein vimrc - Startskript mit deinen bevorzugter Einstellungen.

  6. Drücke CTRL-D nach dem Tippen eines  :  Kommandos, um mögliche
     Vervollständigungen anzusehen.
     Drücke <TAB> , um eine Vervollständigung zu anzuwenden.






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Damit ist der Vim Tutor beendet.  Seine Intention war, einen kurzen und
  bündigen Überblick über den Vim Editor zu geben; gerade genug, um relativ
  leicht mit ihm umgehen zu können.  Der Vim Tutor hat nicht den geringsten
  Anspruch auf Vollständigkeit; Vim hat noch weitaus mehr Kommandos. Lies als
  nächstes das User Manual: ":help user-manual".

  Für weiteres Lesen und Lernen ist folgendes Buch empfehlenswert :
	Vim - Vi Improved - von Steve Oualline
	Verlag: New Riders
  Das erste Buch, welches durchgängig Vim gewidmet ist.  Besonders nützlich
  für Anfänger.  Viele Beispiele und Bilder sind enthalten.
  Siehe https://iccf-holland.org/click5.html

  Folgendes Buch ist älter und mehr über Vi als Vim, aber auch empfehlenswert:
	Textbearbeitung mit dem Vi-Editor  -  von Linda Lamb und Arnold Robbins
	Verlag O'Reilly - ISBN: 3897211262
  In diesem Buch kann man fast alles finden, was man mit Vi tun möchte.
  Die sechste Ausgabe enthält auch Informationen über Vim.

  Als aktuelle Referenz für Version 6.2 und knappe Einführung dient das
  folgende Buch:
	vim ge-packt von Reinhard Wobst
	mitp-Verlag, ISBN 3-8266-1425-9
  Trotz der kompakten Darstellung ist es durch viele nützliche Beispiele auch
  für Einsteiger empfehlenswert.  Probekapitel und die Beispielskripte sind
  online erhältlich.  Siehe https://iccf-holland.org/click5.html

  Dieses Tutorial wurde geschrieben von Michael C. Pierce und Robert K. Ware,
  Colorado School of Mines. Es benutzt Ideen, die Charles Smith, Colorado State
  University, zur Verfügung stellte.  E-Mail: <EMAIL>.

  Bearbeitet für Vim von Bram Moolenaar.
  Deutsche Übersetzung von Joachim Hofmann 2015.  E-Mail: <EMAIL>

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
