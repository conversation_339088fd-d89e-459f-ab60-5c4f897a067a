===============================================================================
=    D o b r o d o š l i   u   VIM   p r i r u č n i k    -    Verzija 1.7    =
===============================================================================
=			  DRUGO   P O G L A V LJ E			      =
===============================================================================

     Evo zmajeva: ako je ovo vaš prvi dodir sa programom vim i nameravali
     ste da uronite u uvodno poglavlje, molimo vas da otkucate :q<enter> i
     pokušate ponovo.

     Približno vreme potrebno za uspešan završetak ovog poglavlja je 
     između 8 i 10 minuta, u zavisnosti od vremena potrošenog na
     eksperimentisanje.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcija 2.1.1: IMENOVANI REGISTRI


	 ** Sačuvajte istovremeno dve trgnute reči, pa ih nalepite **

  1. Pomerite kursor na liniju ispod obeleženu sa --->

  2. Postavite se na bilo koje slovo reči ’Pera’ i otkucajte "ayiw

PAMĆENJE: u registar(") (a) (y)ank [trgni] (i)nner [unutrašnju] (w)ord [reč]

  3. Postavite se unapred na reč ’kolačića’ (fk ili $B ili /ko<enter>) i
     otkucajte "byiw

  4. Postavite se na bilo koje slovo reči ’Žika’ i otkucajte ciw<C-r>a<ESC>

PAMĆENJE: (c)hange [izmeni] (i)nner [unutrašnju] (w)ord [reč] sa
          <sadržajem (r)egistra> (a)

  5. Postavite se na bilo koje slovo reči ’torte’ i otkucajte ciw<C-r>b<ESC>

--->  a) Od sada će Pera biti zadužen za sledovanja kolačića
      b) U tom smislu, Žika će samostalno odlučivati o sudbini torte

NAPOMENA: U registre može i da se briše, npr. "sdiw će obrisati reč pod
          kursorom u registar s.

REFERENCE:	Registri		:h registers
		Imenovani registri	:h quotea
		Pokreti			:h motion.txt<enter> /inner<enter>
		CTRL-R			:h insert<enter> /CTRL-R<enter>

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lekcija 2.1.2: REGISTAR IZRAZA

		** Umećite rezultate izračunavanja „u letu” **

  1. Pomerite kursor na liniju ispod obeleženu sa --->

  2. Postavite se na bilo koju cifru broja u njoj

  3. Otkucajte ciw<C-r>=60*60*24<enter>

  4. U narednoj liniji, pređite u režim umetanje i dodajte današnji datum
     pomoću <C-r>=system('date')<enter>

NAPOMENA: Svi pozivi operativnom sistemu zavise od sistema na kojem se
          izvršavaju, npr. na Windows upotrebite system('date /t') ili
          :r!date /t

---> Zaboravio sam koliko sekundi ima u danu, 84600 je l’ da?
     Danas je: 

NAPOMENA: isto može da se postigne sa :pu=system('date')
          ili sa manje pritisaka na tastere: :r!date

REFERENCA:	Registar izraza		:h quote=

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lekcija 2.1.3: BROJČANI REGISTRI

       ** Pritiskajte yy i dd i uočite efekat koji imaju na registre **

  1. Pomerite kursor na liniju ispod obeleženu sa --->

  2. trgnite nultu liniju, pa zatim pogledajte sadržaje registara sa
     :reg<enter>

  3. obrišite liniju 0. sa "cdd, pa zatim pogledajte sadržaje registara
     (gde očekujete da vidite liniju 0?)

  4. nastavite da brišete svaku narednu liniju, posmatrajući usput registre
     sa :reg

NAPOMENA: trebalo bi da primetite kako se brisanja celih linija pomeraju niz
          listu nakon dodavanja novih obrisanih linija 
  5. Sada (p)aste [nalepite] sledeće registre u redosledu:
     c, 7, 4, 8, 2. tj. sa "7p

---> 0. Ovo
     9. lelujavo
     8. tajna
     7. je
     6. na
     5. osi
     4. jedna
     3. ratna
     2. poruka
     1. poštovanja

NAPOMENA: brisanja kompletnih linija (dd) mnogo duže ostaju u brojčanim
          registrima u odnosu na trganja celih linija ili brisanja koja
          koriste manje pokrete

REFERENCE:	Brojčani registri	:h quote0

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lekcija 2.1.4: LEPOTA MARKERA

	    ** Izbegavanje aritmetike kod neiskusnih programera **

NAPOMENA: uobičajen problem prilikom pisanja koda je premeštanje velikih
          delova koda. Sledeća tehnika pomaže da se spreči potreba za
          izračunavanjima broja linije koji je potreban u operacijama kao što
          su "a147d ili :945,1091d a ili još gore, prvobitnom upotrebom
          i<C-r>=1091-945<enter>

  1. Pomerite kursor na liniju ispod obeleženu sa --->

  2. Pređite na prvu liniju funkcije i markirajte je sa ma

NAPOMENA: tačna pozicija unutar linije NIJE bitna!

  3. Pomerite se na kraj linije i onda na kraj bloka koda sa $%

  4. Obrišite blok u registar sa "ad'a

PAMĆENJE: u registar(") (a) postavi (d)eletion [brisanje] od kursora do
          LINIJE koja sadrži marker(') (a)

  5. Nalepite blok između BBB i CCC sa "ap

NAPOMENA: vežbajte više puta ovu operaciju da bi vam postala prirodna
          ma$%"ad'a

---> AAA
     function itGotRealBigRealFast() {
       if ( somethingIsTrue ) {
         doIt()
       }
       // taksonomija naše funkcije se izmenila pa više nema
       // azbučnog smisla na svojoj trenutnoj poziciji

       // zamislite stotine linija koda

       // naivno biste se pomerili na početak i kraj i zapisali ili
       // zapamtili oba broja linije
     }
     BBB
     CCC

NOTE: markeri i registri ne dele prostor imena, tako da je registar a
      potpuno nezavisan od markera a. Ovo nije slučaj sa registrima i
      makroima.

REFERENCE: 	Markeri 	:h marks
		Pokreti markera	:h mark-motions  (razlika između ' i `)

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      REZIME lekcije 2.1

  1. Da sačuvate (trgnete, obrišete) tekst u, i vratite (nalepite) iz, ukupno
     26 registara (a-z)
  2. Trgnite celu reč sa bilo koje pozicije unutar reči: yiw
  3. Izmenite celu reč sa bilo koje pozicije unutar reči: ciw
  4. Umetnite tekst direktno iz registra u režimu umetanje: (C-r)a

  5. Umetnite rezultate prostih aritmetičkih operacija: (C-r)=60*60<enter> u
     režimu umetanja
  6. Umetnite rezultate sistemskih poziva: (C-r)=system('ls -1') u režimu
     umetanja

  7. Pogledajte sadržaj registara sa :reg
  8. Naučite krajnje odredište brisanja kompletnih linija: dd u brojčane
     registre, tj. opadajući od 1 - 9. Imajte na umu da se brisanja celih
     linija održavaju u registrima duže od bilo koje druge operacije
  9. Naučite krajnja odredišta svih trganja u brojčane registre i koliko se
     tamo zadržavaju

 10. Postavljajte markere iz komandnog režima m[a-zA-Z0-9]
 11. Premeštajte po linijama na marker sa ' 

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Ovim se završava drugo poglavlje Vim priručnika. Još uvek se radi na njemu.

  Ovo poglavlje je napisao Pol D. Parker.

  Preveo na srpski Ivan Pešić.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
