===============================================================================
=      欢     迎     阅     读   《 V I M  教  程 》   ——      版本 1.7       =
===============================================================================

     Vim 是一个具有很多命令的功能非常强大的编辑器。限于篇幅，在本教程当中
     就不详细介绍了。本教程的设计目标是讲述一些必要的基本命令，而掌握好这
     些命令，您就能够很容易地将 Vim 当作一个通用编辑器来使用了。

     完成本教程的内容大约需要25-30分钟，取决于您训练的时间。

     注意：
     每一节的命令操作将会更改本文。推荐您复制本文的一个副本，然后在副本上
     进行训练(如果您是通过"vimtutor"来启动教程的，那么本文就已经是副本了)。

     切记一点：本教程的设计思路是在使用中进行学习的。也就是说，您需要通过
     执行命令来学习它们本身的正确用法。如果您只是阅读而不操作，那么您可能
     会很快遗忘这些命令的！

     好了，现在请确定您的Shift-Lock(大小写锁定键)还没有按下，然后按键盘上
     的字母键 j 足够多次来移动光标，直到第一节的内容能够完全充满屏幕。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第一讲第一节：移动光标


	     ** 要移动光标，请依照说明分别按下 h、j、k、l 键。 **

	     ^
	     k		    提示： h 的键位于左边，每次按下就会向左移动。
       < h	 l >		   l 的键位于右边，每次按下就会向右移动。
	     j			   j 键看起来很象一支尖端方向朝下的箭头。
	     v

  1. 请随意在屏幕内移动光标，直至您觉得舒服为止。

  2. 按下下行键(j)，直到出现光标重复下行。

---> 现在您应该已经学会如何移动到下一讲吧。

  3. 现在请使用下行键，将光标移动到第一讲第二节。

提示：如果您不敢确定您所按下的字母，请按下<ESC>键回到正常(Normal)模式。
      然后再次从键盘输入您想要的命令。

提示：光标键应当也能正常工作的。但是使用hjkl键，在习惯之后您就能够更快
      地在屏幕内四处移动光标。真的是这样！

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第一讲第二节：VIM的进入和退出


  !! 特别提示：敬请阅读本一节的完整内容，然后再执行以下所讲解的命令。

  1. 按<ESC>键(这是为了确保您处在正常模式)。

  2. 然后输入：			:q! <回车>
     这种方式的退出编辑器会丢弃您进入编辑器以来所做的改动。

  3. 如果您看到了命令行提示符，请输入能够带您回到本教程的命令，那就是：
     vimtutor <回车>

  4. 如果您自信已经牢牢记住了这些步骤的话，请从步骤1执行到步骤3退出，然
     后再次进入编辑器。

提示： :q! <回车> 会丢弃您所做的任何改动。几讲之后您将学会如何保存改动到文件。

  5. 将光标下移到第一讲第三节。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第一讲第三节：文本编辑之删除


   ** 在正常(Normal)模式下，可以按下 x 键来删除光标所在位置的字符。**

  1. 请将光标移动到本节中下面标记有 ---> 的那一行。

  2. 为了修正输入错误，请将光标移至准备删除的字符的位置处。

  3. 然后按下 x 键将错误字符删除掉。

  4. 重复步骤2到步骤4，直到句子修正为止。

---> The ccow jumpedd ovverr thhe mooon.

  5. 好了，该行已经修正了，下面是第一讲第四节。

特别提示：在浏览本教程时，不要强行记忆。记住一点：在使用中学习。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第一讲第四节：文本编辑之插入


	 ** 在正常模式下，可以按下 i 键来插入文本。**

  1. 请将光标移动到本节中下面标记有 ---> 的第一行。

  2. 为了使得第一行内容雷同于第二行，请将光标移至文本第一个准备插入字符
     的位置。

  3. 然后按下 i 键，接着输入必要的文本字符。

  4. 每个错误修正完毕后，请按下 <ESC> 键返回正常模式。
     重复步骤2至步骤4以便修正句子。

---> There is text misng this .
---> There is some text missing from this line.

  5. 如果您对文本插入操作已经很满意，请接着阅读下面的第一讲第五节。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第一讲第五节：文本编辑之添加


			** 按 A 键以添加文本。 **

  1. 移动光标到下面第一个标记有 ---> 的一行。
     光标放在那一行的哪个字符上并不重要。

  2. 按 A 键输入必要的添加内容。

  3. 文本添加完毕后，按 <ESC> 键回到正常模式。

  4. 移动光标到下面第二个标记有 ---> 的一行。重复步骤2和步骤3以改正这个句子。

---> There is some text missing from th
     There is some text missing from this line.
---> There is also some text miss
     There is also some text missing here.

  5. 当您对添加文本操作感到满意时，请继续学习第一讲第六节。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第一讲第六节：编辑文件

		    ** 使用 :wq 以保存文件并退出。 **

  特别提示：在执行以下步骤之前，请先读完整个小节！

  1. 如您在第一讲第二节中所做的那样退出本教程： :q!
     或者，如果您可以访问另一个终端，请在那里执行以下操作。

  2. 在 shell 的提示符下输入命令： vim tutor <回车>
     'vim'是启动 Vim 编辑器的命令，'tutor'是您希望编辑的文件的名字。
     请使用一个可以改动的文件。

  3. 使用您在前面的教程中学到的命令插入删除文本。

  4. 保存改动过的文件并退出 Vim，按这些键： :wq  <回车>

  5. 如果您在步骤1中已经退出 vimtutor，请重启 vimtutor 移动到下面的小结一节。

  6. 阅读完以上步骤，弄懂它们的意义，然后在实践中进行练习。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第一讲小结


  1. 光标在屏幕文本中的移动既可以用箭头键，也可以使用 hjkl 字母键。
	 h (左移)	j (下行)       k (上行)	    l (右移)

  2. 欲进入 Vim 编辑器(从命令行提示符)，请输入：vim 文件名 <回车>

  3. 欲退出 Vim 编辑器，请输入 <ESC>   :q!   <回车> 放弃所有改动。
                      或者输入 <ESC>   :wq   <回车> 保存改动。

  4. 在正常模式下删除光标所在位置的字符，请按： x

  5. 欲插入或添加文本，请输入：

	 i   输入欲插入文本   <ESC>		在光标前插入文本
	 A   输入欲添加文本   <ESC>             在一行后添加文本

特别提示：按下 <ESC> 键会带您回到正常模式或者撤消一个不想输入或部分完整
的命令。

好了，第一讲到此结束。下面接下来继续第二讲的内容。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第二讲第一节：删除类命令


	    ** 输入 dw 可以从光标处删除至一个单词的末尾。**

  1. 请按下 <ESC> 键确保您处于正常模式。

  2. 请将光标移动到本节中下面标记有 ---> 的那一行。

  3. 请将光标移至准备要删除的单词的起始处。

  4. 接着输入 dw 删除掉该单词。

  特别提示：当您输入时，字母 d 会同时出现在屏幕的最后一行。Vim 在等待您输入
  字母 w。如果您看到的是除 d 外的其他字符，那表明您按错了；请按下 <ESC> 键，
  然后重新再来。

---> There are a some words fun that don't belong paper in this sentence.

  5. 重复步骤3和步骤4，直至句子修正完毕。接着继续第二讲第二节内容。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第二讲第二节：更多删除类命令


		   ** 输入 d$ 从当前光标删除到行末。**

  1. 请按下 <ESC> 键确保您处于正常模式。

  2. 请将光标移动到本节中下面标记有 ---> 的那一行。

  3. 请将光标移动到该行的尾部(也就是在第一个点号‘.’后面)。

  4. 然后输入 d$ 从光标处删至当前行尾部。

---> Somebody typed the end of this line twice. end of this line twice.


  5. 请继续学习第二讲第三节就知道是怎么回事了。





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第二讲第三节：关于命令和对象


  许多改变文本的命令都由一个操作符和一个动作构成。
  使用删除操作符 d 的删除命令的格式如下：

	d   motion

  其中：
    d      - 删除操作符。
    motion - 操作符的操作对象(在下面列出)。

  一个简短的动作列表：
    w - 从当前光标当前位置直到下一个单词起始处，不包括它的第一个字符。
    e - 从当前光标当前位置直到单词末尾，包括最后一个字符。
    $ - 从当前光标当前位置直到当前行末。

  因此输入 de 会从当前光标位置删除到单词末尾。

特别提示：
    对于勇于探索者，请在正常模式下面仅按代表相应动作的键而不使用操作符，您
    将看到光标的移动正如上面的对象列表所代表的一样。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第二讲第四节：使用计数指定动作


             ** 在动作前输入数字会使它重复那么多次。 **

  1. 移动光标到下面标记有 ---> 的一行的开始。

  2. 输入 2w 使光标向前移动两个单词。

  3. 输入 3e 使光标向前移动到第三个单词的末尾。

  4. 输入 0 (数字零) 移动光标到行首。

  5. 重复步骤2和步骤3，尝试不同的数字。

---> This is just a line with words you can move around in.

  6. 请继续学习第二讲第五节。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       第二讲第五节：使用计数以删除更多


	       ** 使用操作符时输入数字可以使它重复那么多次。 **

  上面已经提到过删除操作符和动作的组合，您可以在组合中动作之前插入一个数字以
  删除更多：
	 d   number(数字)   motion

  1. 移动光标到下面标记有 ---> 的一行中第一个大写字母单词上。

  2. 输入 d2w 以删除两个大写字母单词。

  3. 重复步骤1和步骤2，使用不同的数字使得用一个命令就能删除全部相邻的大写字母
     单词

--->  this ABC DE line FGHI JK LMN OP of words is Q RS TUV cleaned up.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    第二讲第六节：操作整行


		     ** 输入 dd 可以删除整一个当前行。 **

  鉴于整行删除的高频度，Vi 的设计者决定要简化整行删除操作，您仅需要在同一行上
  击打两次 d 就可以删除掉光标所在的整行了。

  1. 请将光标移动到本节中下面的短句段落中的第二行。
  2. 输入 dd 删除该行。
  3. 然后移动到第四行。
  4. 接着输入 2dd 删除两行。

--->  1)  Roses are red,
--->  2)  Mud is fun,
--->  3)  Violets are blue,
--->  4)  I have a car,
--->  5)  Clocks tell time,
--->  6)  Sugar is sweet
--->  7)  And so are you.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   第二讲第七节：撤消类命令


	** 输入 u 来撤消最后执行的命令，输入 U 来撤消对整行的修改。 **

  1. 请将光标移动到本节中下面标记有 ---> 的那一行，并将其置于第一个错误
     处。
  2. 输入 x 删除第一个不想保留的字母。
  3. 然后输入 u 撤消最后执行的(一次)命令。
  4. 这次要使用 x 修正本行的所有错误。
  5. 现在输入一个大写的 U ，恢复到该行的原始状态。
  6. 接着多次输入 u 以撤消 U 以及更前的命令。
  7. 然后多次输入 CTRL-R (先按下 CTRL 键不放开，接着按 R 键)，这样就
     可以重做被撤消的命令，也就是撤消掉撤消命令。

---> Fiix the errors oon thhis line and reeplace them witth undo.

  8. 这些都是非常有用的命令。下面是第二讲的小结了。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第二讲小结


  1. 欲从当前光标删除至下一个单词，请输入：dw
  2. 欲从当前光标删除至当前行末尾，请输入：d$
  3. 欲删除整行，请输入：dd

  4. 欲重复一个动作，请在它前面加上一个数字：2w
  5. 在正常模式下修改命令的格式是：
               operator   [number]   motion
     其中：
       operator - 操作符，代表要做的事情，比如 d 代表删除
       [number] - 可以附加的数字，代表动作重复的次数
       motion   - 动作，代表在所操作的文本上的移动，例如 w 代表单词(word)，
		  $ 代表行末等等。

  6. 欲移动光标到行首，请按数字0键：0

  7. 欲撤消以前的操作，请输入：u (小写的u)
     欲撤消在一行中所做的改动，请输入：U (大写的U)
     欲撤消以前的撤消命令，恢复以前的操作结果，请输入：CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   第三讲第一节：置入类命令


		** 输入 p 将最后一次删除的内容置入光标之后。 **

  1. 请将光标移动到本节中下面第一个标记有 ---> 的一行。

  2. 输入 dd 将该行删除，这样会将该行保存到 Vim 的一个寄存器中。

  3. 接着将光标移动到 c) 一行，即准备置入的位置的上方。记住：是上方哦。

  4. 然后在正常模式下(<ESC>键进入)输入 p 将该行粘贴置入。

  5. 重复步骤2至步骤4，将所有的行依序放置到正确的位置上。

---> d) Can you learn too?
---> b) Violets are blue,
---> c) Intelligence is learned,
---> a) Roses are red,



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   第三讲第二节：替换类命令


	  ** 输入 r 和一个字符替换光标所在位置的字符。**

  1. 请将光标移动到本节中下面标记有 ---> 的第一行。

  2. 请移动光标到第一个出错的位置。

  3. 接着输入 r 和要替换成的字符，这样就能将错误替换掉了。

  4. 重复步骤2和步骤3，直到第一行已经修改完毕。

--->  Whan this lime was tuoed in, someone presswd some wrojg keys!
--->  When this line was typed in, someone pressed some wrong keys!

  5. 然后我们继续学习第三讲第三节。

特别提示：切记您要在使用中学习，而不是在记忆中学习。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第三讲第三节：更改类命令


		 ** 要改变文本直到一个单词的末尾，请输入 ce **

  1. 请将光标移动到本节中下面标记有 ---> 的第一行。

  2. 接着把光标放在单词 lubw 的字母 u 的位置那里。

  3. 然后输入 ce 以及正确的单词(在本例中是输入 ine )。

  4. 最后按 <ESC> 键，然后光标定位到下一个错误第一个准备更改的字母处。

  5. 重复步骤3和步骤4，直到第一个句子完全雷同第二个句子。

---> This lubw has a few wptfd that mrrf changing usf the change operator.
---> This line has a few words that need changing using the change operator.

提示：请注意 ce 命令不仅仅是删除了一个单词，它也让您进入插入模式了。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       第三讲第四节：使用c更改更多


	   ** 更改类操作符可以与删除中使用的同样的动作配合使用。 **

  1. 更改类操作符的工作方式跟删除类是一致的。操作格式是：

         c    [number]   motion

  2. 动作参数(motion)也是一样的，比如 w 代表单词，$代表行末等等。

  3. 请将光标移动到本节中下面标记有 ---> 的第一行。

  4. 接着将光标移动到第一个错误处。

  5. 然后输入 c$ 使得该行剩下的部分更正得同第二行一样。最后按 <ESC> 键。

---> The end of this line needs some help to make it like the second.
---> The end of this line needs to be corrected using the  c$  command.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				  第三讲小结


  1. 要重新置入已经删除的文本内容，请按小写字母 p 键。该操作可以将已删除
     的文本内容置于光标之后。如果最后一次删除的是一个整行，那么该行将置
     于当前光标所在行的下一行。

  2. 要替换光标所在位置的字符，请输入小写的 r 和要替换掉原位置字符的新字
     符即可。

  3. 更改类命令允许您改变从当前光标所在位置直到动作指示的位置中间的文本。
     比如输入 ce 可以替换当前光标到单词的末尾的内容；输入 c$ 可以替换当
     前光标到行末的内容。

  4. 更改类命令的格式是：

	 c   [number]   motion

现在我们继续学习下一讲。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第四讲第一节：定位及文件状态

  ** 输入 CTRL-G 显示当前编辑文件中当前光标所在行位置以及文件状态信息。
     输入大写 G 则直接跳转到文件中的某一指定行。**

  提示：切记要先通读本节内容，之后才可以执行以下步骤!!!

  1. 按下 CTRL 键不放开然后按 g 键。我们称这个键组合为 CTRL-G。
     您会看到页面最底部出现一个状态信息行，显示的内容是当前编辑的文件名
     和文件中光标位置。请记住行号，它会在步骤3中用到。

提示：您也许会在屏幕的右下角看到光标位置，这会在 'ruler' 选项设置时发生
      (参见 :help 'ruler')

  2. 输入大写 G 可以使得当前光标直接跳转到文件最后一行。
     输入 gg 可以使得当前光标直接跳转到文件第一行。

  3. 输入您曾停留的行号，然后输入大写 G。这样就可以返回到您第一次按下
     CTRL-G 时所在的行了。

  4. 如果您觉得没问题的话，请执行步骤1至步骤3的操作进行练习。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第四讲第二节：搜索类命令


     ** 输入 / 加上一个字符串可以用以在当前文件中查找该字符串。**

  1. 在正常模式下输入 / 字符。您此时会注意到该字符和光标都会出现在屏幕底
     部，这跟 : 命令是一样的。

  2. 接着输入 errroor <回车>。那个errroor就是您要查找的字符串。

  3. 要查找同上一次的字符串，只需要按 n 键。要向相反方向查找同上一次的字
     符串，请输入大写 N 即可。

  4. 如果您想逆向查找字符串，请使用 ? 代替 / 进行。

  5. 要回到您之前的位置按 CTRL-O (按住 Ctrl 键不放同时按下字母 o)。重复按可以
     回退更多步。CTRL-I 会跳转到较新的位置。

--->  "errroor" is not the way to spell error;  errroor is an error.
提示：如果查找已经到达文件末尾，查找会自动从文件头部继续查找，除非
      'wrapscan' 选项被复位。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   第四讲第三节：配对括号的查找


	      ** 输入 % 可以查找配对的括号 )、]、}。**

  1. 把光标放在本节下面标记有 --> 那一行中的任何一个 (、[ 或 { 处。

  2. 接着按 % 字符。

  3. 此时光标的位置应当是在配对的括号处。

  4. 再次按 % 就可以跳回配对的第一个括号处。

  5. 移动光标到另一个 (、)、[、]、{ 或 } 处，按 % 查看其所作所为。

---> This ( is a test line with ('s, ['s ] and {'s } in it. ))


提示：在程序调试时，这个功能用来查找不配对的括号是很有用的。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第四讲第四节：替换命令


		** 输入 :s/old/new/g 可以替换 old 为 new。**

  1. 请将光标移动到本节中下面标记有 ---> 的那一行。

  2. 输入 :s/thee/the <回车> 。请注意该命令只改变光标所在行的第一个匹配
     串。

  3. 输入 :s/thee/the/g	则是替换全行的匹配串，该行中所有的 "thee" 都会被
     改变。

---> thee best time to see thee flowers is in thee spring.

  4. 要替换两行之间出现的每个匹配串，请
     输入   :#,#s/old/new/g   其中 #,# 代表的是替换操作的若干行中
                              首尾两行的行号。
     输入   :%s/old/new/g     则是替换整个文件中的每个匹配串。
     输入   :%s/old/new/gc    会找到整个文件中的每个匹配串，并且对每个匹配串
                              提示是否进行替换。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第四讲小结


  1. CTRL-G 用于显示当前光标所在位置和文件状态信息。
     G 用于将光标跳转至文件最后一行。
     先敲入一个行号然后输入大写 G 则是将光标移动至该行号代表的行。
     gg 用于将光标跳转至文件第一行。

  2. 输入 / 然后紧随一个字符串是在当前所编辑的文档中正向查找该字符串。
     输入 ? 然后紧随一个字符串则是在当前所编辑的文档中反向查找该字符串。
     完成一次查找之后按 n 键是重复上一次的命令，可在同一方向上查
     找下一个匹配字符串所在；或者按大写 N 向相反方向查找下一匹配字符串所在。
     CTRL-O 带您跳转回较旧的位置，CTRL-I 则带您到较新的位置。

  3. 如果光标当前位置是括号(、)、[、]、{、}，按 % 会将光标移动到配对的括号上。

  4. 在一行内替换头一个字符串 old 为新的字符串 new，请输入  :s/old/new
     在一行内替换所有的字符串 old 为新的字符串 new，请输入  :s/old/new/g
     在两行内替换所有的字符串 old 为新的字符串 new，请输入  :#,#s/old/new/g
     在文件内替换所有的字符串 old 为新的字符串 new，请输入  :%s/old/new/g
     进行全文替换时询问用户确认每个替换需添加 c 标志        :%s/old/new/gc

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		第五讲第一节：在 VIM 内执行外部命令的方法


	   ** 输入 :! 然后紧接着输入一个外部命令可以执行该外部命令。**

  1. 按下我们所熟悉的 : 命令使光标移动到屏幕底部。这样您就可以输入一行命令了。

  2. 接着输入感叹号 ! 这个字符，这样就允许您执行外部的 shell 命令了。

  3. 我们以 ls 命令为例。输入 !ls <回车> 。该命令就会列举出您当前目录的
     内容，就如同您在命令行提示符下输入 ls 命令的结果一样。如果 !ls 没起
     作用，您可以试试 :!dir 看看。

提示：所有的外部命令都可以以这种方式执行，包括带命令行参数的那些。

提示：所有的 : 命令都必须以敲 <回车> 键结束。从今以后我们就不会总是提到这一点
      了。





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第五讲第二节：关于保存文件的更多信息


	     ** 要将对文件的改动保存到文件中，请输入 :w FILENAME 。**

  1. 输入 :!dir 或者 :!ls 获知当前目录的内容。您应当已知道最后还得敲
     <回车> 吧。

  2. 选择一个未被用到的文件名，比如 TEST。

  3. 接着输入 :w TEST  (此处 TEST 是您所选择的文件名。)

  4. 该命令会以 TEST 为文件名保存整个文件 (Vim 教程)。为了验证这一点，
     请再次输入 :!dir 或 :!ls 查看您的目录列表内容。

请注意：如果您退出 Vim 然后在以命令 vim TEST 再次启动 Vim，那么该文件内
     容应该同您保存时的文件内容是完全一样的。

  5. 现在您可以删除 TEST 文件了。在 MS-DOS 下，请输入：   :!del TEST
                                 在 Unix 下，请输入：     :!rm TEST


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    第五讲第三节：一个具有选择性的保存命令


	    ** 要保存文件的部分内容，请输入 v motion :w FILENAME **

  1. 移动光标到本行。

  2. 接着按 v 键，将光标移动至下面第五个条目上。您会注意到之间的文本被高亮了。

  3. 然后按 : 字符。您将看到屏幕底部会出现 :'<,'> 。

  4. 现在请输入 w TEST，其中 TEST 是一个未被使用的文件名。确认您看到了
     :'<,'>w TEST 之后按 <回车> 键。

  5. 这时 Vim 会把选中的行写入到以 TEST 命名的文件中去。使用 :!dir 或 :!ls
     确认文件被正确保存。这次先别删除它！我们在下一讲中会用到它。

提示：按 v 键使 Vim 进入可视模式进行选取。您可以四处移动光标使选取区域变大或
      变小。接着您可以使用一个操作符对选中文本进行操作。例如，按 d 键会删除
      选中的文本内容。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   第五讲第四节：提取和合并文件


       ** 要向当前文件中插入另外的文件的内容，请输入 :r FILENAME **

  1. 请把光标移动到本行上面一行。

特别提示：执行步骤2之后您将看到第五讲第三节的文字，请届时往下移动
          以再次看到本讲内容。

  2. 接着通过命令 :r TEST 将前面创建的名为 TEST 的文件提取进来。
     您所提取进来的文件将从光标所在位置处开始置入。

  3. 为了确认文件已经提取成功，移动光标回到原来的位置就可以注意有两份第
     五讲第三节的内容，一份是原始内容，另外一份是来自文件的副本。

提示：您还可以读取外部命令的输出。例如， :r !ls 可以读取 ls 命令的输出，并
      把它放置在光标下面。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第五讲小结


  1. :!command 用于执行一个外部命令 command。

     请看一些实际例子：
	 (MS-DOS)	  (Unix)
	  :!dir		   :!ls		   -  用于显示当前目录的内容。
	  :!del FILENAME   :!rm FILENAME   -  用于删除名为 FILENAME 的文件。

  2. :w FILENAME  可将当前 VIM 中正在编辑的文件保存到名为 FILENAME 的文
     件中。

  3. v motion :w FILENAME 可将当前编辑文件中可视模式下选中的内容保存到文件
     FILENAME 中。

  4. :r FILENAME 可提取磁盘文件 FILENAME 并将其插入到当前文件的光标位置
     后面。

  5. :r !dir 可以读取 dir 命令的输出并将其放置到当前文件的光标位置后面。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 第六讲第一节：打开类命令


	 ** 输入 o 将在光标的下方打开新的一行并进入插入模式。**

  1. 请将光标移动到本节中下面标记有 ---> 的那一行。

  2. 接着输入小写的 o 在光标 *下方* 打开新的一行，这个命令会使您
     进入插入模式。

  3. 然后输入一些文字，之后按 <ESC> 键退出插入模式而进入正常模式。

---> After typing  o  the cursor is placed on the open line in Insert mode.

  4. 为了在光标 *上方* 打开新的一行，只需要输入大写的 O 而不是小写的 o
     就可以了。请在下行测试一下吧。

---> Open up a line above this by typing O while the cursor is on this line.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第六讲第二节：附加类命令


		     ** 输入 a 将可在光标之后插入文本。 **

  1. 请在正常模式下将光标移动到本节中下面标记有 ---> 的第一行的行首。

  2. 接着输入 e 直到光标位于 li 的末尾。

  3. 输入小写的 a 则可在光标之后插入文本了。

  4. 将单词补充完整，就像下一行中的那样。之后按 <ESC> 键退出插入模式回到
     正常模式。

  5. 使用 e 移动光标到下一步不完整的单词，重复步骤3和步骤4。

---> This li will allow you to pract appendi text to a line.
---> This line will allow you to practice appending text to a line.

提示：a、i 和 A 都会带您进入插入模式，惟一的区别在于字符插入的位置。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    第六讲第三节：另外一个置换类命令的版本


		      ** 输入大写的 R 可连续替换多个字符。**

  1. 请将光标移动到本节中下面标记有 ---> 的第一行。移动光标到第一个 xxx 的
     起始位置。

  2. 然后输入大写的 R 开始把第一行中的不同于第二行的剩余字符逐一输入，就
     可以全部替换掉原有的字符而使得第一行完全雷同第二行了。

  3. 接着按 <ESC> 键退出替换模式回到正常模式。您可以注意到尚未替换的文本
     仍然保持原状。

  4. 重复以上步骤，将剩余的 xxx 也替换掉。

---> Adding 123 to xxx gives you xxx.
---> Adding 123 to 456 gives you 579.

提示：替换模式与插入模式相似，不过每个输入的字符都会删除一个已有的字符。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  第六讲第四节：复制粘贴文本


		 ** 使用操作符 y 复制文本，使用 p 粘贴文本 **

  1. 定位到下面标记有 ---> 的一行，将光标移动到 "a)" 之后。

  2. 接着使用 v 进入可视模式，移动光标到 "first" 的前面。

  3. 现在输入 y 以抽出(复制)高亮的文本。

  4. 然后移动光标到下一行的末尾：j$

  5. 接着输入 p 以放置(粘贴)复制了的文本。然后输入：a second <ESC>。

  6. 使用可视模式选中 " item."，用 y 复制，再用 j$ 将光标移动到下一行末尾，
     用 p 将文本粘贴到那里。

--->  a) this is the first item.
      b)

  提示：您还可以把 y 当作操作符来使用；例如 yw 可以用来复制一个单词。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    第六讲第五节：设置类命令的选项


		  ** 设置可使查找或者替换可忽略大小写的选项 **

  1. 要查找单词 ignore 可在正常模式下输入 /ignore <回车>。
     要重复查找该词，可以重复按 n 键。

  2. 然后设置 ic 选项(Ignore Case，忽略大小写)，请输入： :set ic

  3. 现在可以通过键入 n 键再次查找单词 ignore。注意到 Ignore 和 IGNORE 现在
     也被找到了。

  4. 然后设置 hlsearch 和 incsearch 这两个选项，请输入： :set hls is

  5. 现在可以再次输入查找命令，看看会有什么效果： /ignore <回车>

  6. 要禁用忽略大小写，请输入： :set noic

提示：要移除匹配项的高亮显示，请输入：  :nohlsearch
提示：如果您想要仅在一次查找时忽略字母大小写，您可以使用 \c：
      /ignore\c <回车>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第六讲小结

  1. 输入小写的 o 可以在光标下方打开新的一行并进入插入模式。
     输入大写的 O 可以在光标上方打开新的一行。

  2. 输入小写的 a 可以在光标所在位置之后插入文本。
     输入大写的 A 可以在光标所在行的行末之后插入文本。

  3. e 命令可以使光标移动到单词末尾。

  4. 操作符 y 复制文本，p 粘贴先前复制的文本。

  5. 输入大写的 R 将进入替换模式，直至按 <ESC> 键回到正常模式。

  6. 输入 :set xxx 可以设置 xxx 选项。一些有用的选项如下：
  	'ic' 'ignorecase'	查找时忽略字母大小写
	'is' 'incsearch'	查找短语时显示部分匹配
	'hls' 'hlsearch'	高亮显示所有的匹配短语
     选项名可以用完整版本，也可以用缩略版本。

  7. 在选项前加上 no 可以关闭选项：  :set noic

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  第七讲第一节：获取帮助信息


		      ** 使用在线帮助系统 **

  Vim 拥有一个细致全面的在线帮助系统。要启动该帮助系统，请选择如下三种方
  法之一：
	- 按下 <HELP> 键 (如果键盘上有的话)
	- 按下 <F1> 键 (如果键盘上有的话)
	- 输入	:help <回车>

  请阅读帮助窗口中的文字以了解帮助是如何工作的。
  输入 CTRL-W CTRL-W   可以使您在窗口之间跳转。
  输入 :q <回车> 可以关闭帮助窗口。

  提供一个正确的参数给":help"命令，您可以找到关于该主题的帮助。请试验以
  下参数(可别忘了按回车键哦)：

	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  第七讲第二节：创建启动脚本


			  ** 启用 Vim 的特性 **

  Vim 的功能特性要比 Vi 多得多，但其中大部分都没有缺省启用。为了使用更多的
  特性，您得创建一个 vimrc 文件。

  1. 开始编辑 vimrc 文件，具体命令取决于您所使用的操作系统：
        :edit ~/.vimrc		这是 Unix 系统所使用的命令
        :edit ~/_vimrc		这是 MS-Windows 系统所使用的命令

  2. 接着读取 vimrc 示例文件的内容：
        :r $VIMRUNTIME/vimrc_example.vim

  3. 保存文件，命令为：
        :write

  下次您启动 Vim 时，编辑器就会有了语法高亮的功能。
  您可以把您喜欢的各种设置添加到这个 vimrc 文件中。
  要了解更多信息请输入 :help vimrc-intro

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    第七讲第三节：补全功能


	      ** 使用 CTRL-D 和 <TAB> 可以进行命令行补全 **

  1. 请确保 Vim 不是在以兼容模式运行： :set nocp

  2. 查看一下当前目录下已经存在哪些文件，输入： :!ls   或者  :!dir

  3. 现在输入一个命令的起始部分，例如输入： :e

  4. 接着按 CTRL-D 键，Vim 会显示以 e 开始的命令的列表。

  5. 然后按 <TAB> 键，Vim 会补全命令为 :edit 。

  6. 现在添加一个空格，以及一个已有文件的文件名的起始部分，例如： :edit FIL

  7. 接着按 <TAB> 键，Vim 会补全文件名(如果它是惟一匹配的)。

提示：补全对于许多命令都有效。您只需尝试按 CTRL-D 和 <TAB>。
      它对于 :help 命令非常有用。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				  第七讲小结


  1. 输入 :help 或者按 <F1> 键或 <Help> 键可以打开帮助窗口。

  2. 输入 :help cmd 可以找到关于 cmd 命令的帮助。

  3. 输入 CTRL-W CTRL-W  可以使您在窗口之间跳转。

  4. 输入 :q 以关闭帮助窗口

  5. 您可以创建一个 vimrc 启动脚本文件用来保存您偏好的设置。

  6. 当输入 : 命令时，按 CTRL-D 可以查看可能的补全结果。
     按 <TAB> 可以使用一个补全。







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  vim 教程到此就结束了。本教程只是为了简明地介绍一下 Vim 编辑器，但已足以让您
  很容易使用这个编辑器了。毋庸质疑，vim还有很多很多的命令，本教程所介
  绍的距离完整的差得很远。所以您要精通的话，还望继续努力哦。下一步您可以阅读
  Vim 的用户手册，使用的命令是： :help user-manual

  下面这本书值得推荐用于更进一步的阅读和学习：
	Vim - Vi Improved - 作者：Steve Oualline
	出版社：New Riders
  这是第一本完全讲解 Vim 的书籍。它对于初学者特别有用。其中包含有大量实例
  和图示。
  欲知详情，请访问 https://iccf-holland.org/click5.html

  以下这本书比较老了而且内容更多是关于 Vi 而非 Vim，但是也值得推荐：
	Learning the Vi Editor - 作者：Linda Lamb
	出版社：O'Reilly & Associates Inc.
  这是一本不错的书，通过它您几乎能够了解到任何您想要使用 Vi 做的事情。
  此书的第六个版本也包含了一些关于 Vim 的信息。

  本教程是由来自 Calorado School of Mines 的 Michael C. Pierce 和
  Robert K. Ware 所编写的，其中很多创意由来自 Colorado State University 的
  Charles Smith 提供。编者的电子邮箱是：<EMAIL>

  本教程已由 Bram Moolenaar 专为 Vim 进行修订。

  译制者附言：
  ===========
      简体中文教程翻译版之译制者为梁昌泰 <<EMAIL>>，还有
      另外一个联系地址：<EMAIL>。

      繁体中文教程是从简体中文教程翻译版使用 Debian GNU/Linux 中文项目小
      组的于广辉先生编写的中文汉字转码器  autoconvert 转换而成的，并对转
      换的结果做了一些细节的改动。

  变更记录：
  =========
      2012年10月01日 赵涛 <<EMAIL>>
      将 vimtutor 中译版从 1.5 升级到 1.7。

      2002年08月30日 梁昌泰 <<EMAIL>>
      感谢 RMS@SMTH 的指正，将多处错误修正。

      2002年04月22日 梁昌泰 <<EMAIL>>
      感谢 <EMAIL> 的指正，将两处错别字修正。

      2002年03月18日 梁昌泰 <<EMAIL>>
      根据Bram Moolenaar先生在2002年03月16日的来信要求，将vimtutor1.4中译
      版升级到vimtutor1.5。

      2001年11月15日 梁昌泰 <<EMAIL>>
      将vimtutor1.4中译版提交给Bram Moolenaar和Sven Guckes。
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
