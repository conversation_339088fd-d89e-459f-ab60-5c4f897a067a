===============================================================================
=    G o t i k a m   i n   n   W I M M - S c h a i n e r   -   Fassung 1.7    =
===============================================================================
=			       C H A P T E R - 1			      =
===============================================================================

   Dyr Wimm ist ayn gro mächtigs Blat, dös was mit aynn Wösn Befelh aufwartt; z
   vil,  däß myn s allsand  in aynn <PERSON>hainer  wie dönn daader  unterbräng.  Der
   Schainer ist yso aufbaut,  däß yr halt netty die Befelh allsand bringt, wost
   brauchst, däßst mit iem für s Eerste wirklich öbbs anfangen kanst.
   Durchhinarechtn kanst di, wennst willst, in ayner halbetn Stund; dös haisst,
   wennst di nit grooß mit n Pröbln und Tüftln aufhaltst.

   OBACHT:
   Die Faudungen,  wost daader finddst,  gaand istig  s Gwort öndern.  Dösswögn
   machst eyn n Böstn glei  ayn Aamum von derer Dautticht daader.  Haast alsnan
   dös Gwort daader mit n Befelh  "vimtutor bar"  ausherlaassn, ist s ee schoon
   ayn Aamum.
   Mir kan s  nit oft gnueg  sagn,  däß  der Schainer daader  istig  gan n Üebn
   ghoert.  Also muesst schoon aau die Befelh +ausfüern, wennst ys gscheid ler-
   nen willst. Mit n Lösn yllain ist s +nit taan!

   Ietz schaust grad non, däß dein Föststölltastn nit druckt ist; und aft geest
   glei aynmaal mit dyr j-Tastn abwärts (yso laaufft dös nömlich),  hinst däßst
   de gantze Letzn 1.1.1 auf n Bildschirm haast.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.1.1: MIT N MÖRKL UMAYNANDFARN

** Dyrmitst mit n Mörkl umaynandkimmst, druck  h, j, k und l  wie unt zaigt. **
            ^            Ayn Öslsbrugg:
            k            De Tastn  h  ist winster und +geet aau gan winster.
   < h             l >   S  l  leit zesm und richtt si gan zesm.
            j            S  j  kan myn wie aynn Pfeil gan unt seghn.
            v            Mit n  k  kimmst gan n KOPF.            
  1. Ietz ruedertst ainfach mit n Mörkl auf n Bildschirm umaynand,  hinst däßst
     di sicher füelst.
  2. Halt d Abhin-Tastn  (j)  druckt;  aft rumplt s  ainfach weiter.  Netty yso
     kimmst gan dyr naehstn Letzn.
     
  3. Wie gsait, ietz bewögst di also mit derer Tastn gan dyr Letzn 1.1.2.

Non öbbs: Allweil, wenn dyr niemer ganz wol ist, wasst öbbenn druckt haast, aft
          zipfst  <ESC>  ; naacher bist wider ganz gwon in dyr Befelhs-Artweis.

      
          Nöbnbei gsait kimmst gwonerweil aau mit de Pfeiltastnen weiter.  Aber
          hjkl  seind z haissn s Wimm-Urgstain;  und de "Hörtn" seind ganz dyr-
          für, däß myn bei +dene bleibt. Pröblt s ainfach aus!
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Letzn 1.1.2: ÖNN WIMM AUSSCHALTTN


  ALSO, EE WENNST ÖBBS VON DAA UNT AUSFÜERST,  LIS LIEBER ZEERST DE GANTZE LET-
  ZN!

  1. Druck d <ESC>-Tastn, dyrmitst aau gwiß in dyr Befelhs-Artweis bist.
  
  2. Demmlt  :q!<EIN>  .
     Daa dyrmit  benddst ys Blat  und verwirffst  allss,  wasst öbbenn  göndert
     haast.

  3. Balst önn Eingib seghst, gib dö Faudung ein,  wo di zo dönn Schainer brun-
     gen haat, also   vimtutor bar<EIN>  .

  4. Also, wenn ietz allsse sitzt, naacherd füerst d Schritt 1 hinst 3 aus, mit
     wasst ys Blat verlaasst und aft wider einhinkimmst.

Anmörkung: Mit  :q!<EIN>  verwirffst allss, wasst göndert older enther gschribn
           haast. In aynn Öttlych Letznen lernst acht, wiest dös allss in ayner
           Dautticht speichertst.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Letzn 1.1.3: GWORT BARECHTN - LÖSCHN 

         
         ** Druck  x  , dyrmitst dös Zaichen unter n Mörkl löschst. **

  1. Bewög di mit n Mörkl auf de mit  --->  angmörkte Zeil unt.

  2. Zo n Faeler Verbössern  farst mit n Mörkl  netty  auf dös Zaichen,  dös wo
     glöscht ghoert.

  3. Druck de Tastn  x  , däßst dös überflüssige Zaichen löschst.

  4. Ietz tuest so lang weiter mit 2 hinst 4, hinst däß dyr Saz stimmt.

---> De Kkuue sprangg übber nn Maanad.

  5. Wenn ietz de Zeil verbössert ist, geest gan dyr Letzn 1.1.4. weiter.

Und ganz wichtig:  Dyrweilst dönn Schainer durcharechtst,  versuech nit öbbenn,
                   allss auswendig z lernen;  nän, lern ainfach mit n Anwenddn!


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.1.4: GWORT BARECHTN - EINFÜEGN


                  **  Druck  i  , dyrmitst öbbs einfüegst. **

  1. Bewög önn Mörkl zo dyr eerstn untignen Zeil, wo mit  --->  angeet.

  2. Dyrmitst  de eerste Zeil  wie de zwaitte machst,  bewög önn Mörkl  auf dös
     eerste Zaichen NAACH derer Stöll, daa wo s Gwort eingfüegt werdn sollt.

  3. Druck  i  und gib dös ein, was abgeet.

  4. Wenn ieweils ayn Faeler verweitert ist, aft druck   <ESC>  ; dyrmit kimmst
     gan dyr Befelhsartweis zrugg.
     So, und ietz tuest ainfach yso weiter, hinst däß dyr Saz stimmt.

---> Daader gt dd öbbs b.
---> Daader geet diend öbbs ab.

  5. Balst mainst, däßst ys Gwort-Einfüegn kanst, aft geest gan dyr Letzn 1.1.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.1.5: GWORT BARECHTN - ANFÜEGN


                      ** Druck  A  gan n Gwort Anfüegn. **

  1. Gee mit n Mörkl gan dyr eerstn untignen Zeil, wo ayn  --->  dyrvor haat.
     Daa ist s gleich, wo gnaun dyr Mörkl in derer Zeil steet.  

  2. Demmlt  A  und gib de entspröchetn Ergöntzungen ein.

  3. Wennst mit n Anfüegn förtig bist, aft druckst  <ESC>  , däßst wider eyn de
     Befelhsartweis zruggkimmst.

  4. So,  und ietz geest aft non gan dyr zwaittn mit  --->  angmörktn Zeil; und
     daadl machst ys netty yso.

---> In derer Zeil gee  
     In derer Zeil geet ayn Weeng ayn Gwort ab.
---> Aau daader stee
     Aau daader steet öbbs Unvollstöndigs. 

  5. Wennst s Anfüegn von Gwort drauf haast, naacherd gee gan dyr Letzn 1.1.6.  
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Letzn 1.1.6: AYN DAUTTICHT BARECHTN


     ** Mit  :wq  speichertst ayn Dautticht und verlaasst önn Wimm ganz. **

  !! OBACHT:  Ee wennst mit dönn alln daa unt weitertuest, lis zeerst de gantze
     Letzn durch!!

  1. Verlaaß also s Blat, wie s in dyr Letzn 1.1.2. haisst, mit  :q!  !

  2. Gib dö Faudung eyn n Eingib ein:  vim Schainer<EIN>  . 'vim' ruefft s Blat
     auf,  und  'Schainer'  haisst de Dautticht,  wost barechtn willst.  Dyrmit
     haast also ayn Dautticht, dö wost barechtn kanst.

  3. Ietz  füegst öbbs ein older löschst öbbs,  wiest ys in de vorignen Letznen
     glernt haast.

  4. Speichert de gönderte Dautticht und verlaaß önn Wimm mit  :wq<EIN>  .

  5. Schmeiß önn Wimmschainer neu an und gee gan dyr folgetn Zammenfassung.

  6. Aft däßst  de obignen Schritt glösn und käppt haast,  kanst ys durchfüern.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.1


  1. Dyr Mörkl werd mit de Tastnen hjkl older aau mit de Pfeiltastnen gsteuert.
              h (winst)     j (ab)        k (auf)       l (zes)

  2. Um önn Wimm umbb n Eingib aus z ginnen, demmlt:  vim DAUTTICHT<EIN>  .

  3. Willst önn Wimm verlaassn und aau allss verwerffen, aft gibst ein:
                <ESC>  und  :q!<EIN>  .
     Gan n Verlaassn und Speichern aber zipfst  <ESC>  und  :wq<EIN>  .

  4. Willst dös Zaichen löschn, daa wo dyr Mörkl drauf ist, demmltst  x  .

  5. Willst öbbs vor n Mörkl eingöbn, zipfst  i  und drafter  <ESC>  .
     Mechst ys aber eyn s Zeilnend anhinhöngen, benutzt ys   A  .
     Und ainfach naach n Mörkl füegst ys mit  a  ein.

Anmörkung:  Druckst  <ESC>  ,  kimmst eyn de Befelhsartweis zrugg older brichst
            ayn Faudung ab, dö wo dyr schiefgangen ist.

     Ietz tue mit dyr Letzn 1.2 weiter.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                           Letzn 1.2.1: LÖSHFAUDUNGEN


                 ** Demmlt  dw  , dyrmitst ayn Wort löschst. **

  1. Druck  <ESC>  ,  dyrmit s aau gwiß ist,  däßst in dyr Befelhsartweis bist.

  2. Bewög önn Mörkl zo dyr mit  --->  angmörktn Zeil unt.

  3. Und daa geest ietz auf n Anfang von aynn Wort, dös wo glöscht ghoert.

  4. Zipf  dw  , däßst dös gantze Wort löschst.

  Nöbnbei:  Dyr Buechstabn  d  erscheint auf dyr lösstn Zeil  von n Bildschirm,
            sobaldst n eingibst. Dyr Wimm wartt ietz drauf, däß öbbs kimmt, al-
            so  daader ayn  w  .  Seghst  freilich  öbbs Anderts  wie ayn  d  ,
            naacherd haast  öbbs Falschs  demmlt.  Druck aft  <ESC>  und pröblt
            s non aynmaal.
---> Ayn Öttlych Wörter lustig ghoernd nit Fisper eyn dönn Saz einhin.

  5. Äfert  d Schritt  3 und 4,  hinst däß  dyr Saz pässt,  und gee aft gan dyr
     Letzn 1.2.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.2.2: NON MEERER LÖSHFAUDUNGEN


            ** Gib  d$  ein, däßst hinst eyn s Zeilnend löschst. **

  1. Druck  <ESC>  , dyrmitst aau gwiß in dyr Befelhsartweis bist.

  2. Bewög önn Mörkl hinst eyn de mit  --->  angmörkte Zeil untn.

  3. Gee mit n Mörkl auf s End von dyr faelerfreien Zeil, NAACH n eerstn  .   .

  4. Zipf  d$  , däßst hinst eyn s End von dyr Zeil löschst.
  
---> Öbber haat s End von dyr Zeil doplt eingöbn. doplt eingöbn.


  5. Gee weiter  gan dyr Letzn 1.2.3, dyrmitst versteest, was daader ablaaufft.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Letzn 1.2.3: PFEMERER UND WOLENDER


  Vil Faudungen,   wo s Gwort öndernd, sötznd si aus aynn Pfemerer und aynn Wo-
  lend zamm. Bal i also öbbs löschn will, schreib i ainsting  d  und aft s "Wo-
  lend", dös haisst also, "wolend", "wohin" däß i will - older was i halt gnaun
  löschn will.






  Daader also, was i wie löschn kan:
     w  -  hinst eyn n Anfang von n naehstn Wort AANE dönn sein eersts Zaichen.
     e  -  gan n End von n ietzundn Wort MIT dönn seinn lösstn Zaichen.
     $  -  zo n End von dyr Zeil MIT derer irn lösstn Zaichen.

  Also löscht de Tastnfolg  de  allss umbb n Mörkl hinst eyn s Wortend.
Anmörkung:   Gib i grad  dös zwaitte Zaichen  yllain ein,  ruckt halt dyr Mörkl
             entspröchet weiter.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Letzn 1.2.4: MIT AYNN ZÖLER D WOLENDER ÄFERN


 ** Gib i ayn Zal vor aynn Wolend ein, werd dös Sel entspröchet oft gangen. **

  1. Bewög önn Mörkl gan n Anfang von dyr Zeil mit  --->  dyrvor unt.

  2. Zipf  2w  , däßst mit n Mörkl zwai Wörter weitergeest.

  3. Zipf  3e  , däßst mit n Mörkl auf s End von n drittn Wort kimmst.

  4. Zipf  0  (aynn Nuller), däßst eyn n Anfang von dyr Zeil hinkimmst.

  5. Widerhol d Schritt 2 und 3 mit verschaidne Zöler.

  ---> Dös ist ietz grad ayn Zeil zo n drinn Umaynanderruedern.

  6. Gee weiter gan dyr Letzn 1.2.5.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Letzn 1.2.5: DURCH AYNN ZÖLER GLEI MEERER LÖSCHN


        ** Ayn Zal vor aynn Pfemerer füert dönn entspröchet oft aus. **

  Also, i mecht löschn,  und zwaar öbbs Bestimmts,  und dös so und so oft:  Daa
  dyrzue benutz i aynn Zöler:
                              d  Zöler  Wolend (also önn Bewögungsschrit)

  1. Bewög önn Mörkl zo n eerstn Wort in GROOSSBUECHSTABN in dyr mit  --->  an-
     gmörktn Zeil.

  2. Demmlt  d2w  , dyrmitst de ganz grooßgschribnen Wörter löschst.

  3. Äfert  d Schritt 1 und 2  mit dönn entspröchetn Zöler,  dyrmitst de drauf-
     folgetn  ganz großgschribnen Wörter  mit ayner ainzignen Faudung  löschst:


--->  Dö ABC DE Zeil FGHI JK LMN OP mit Wörter ist Q RS TUV ietz berichtigt.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                          Letzn 1.2.6: ARECHTN AUF ZEILN


               ** Zipf  dd  , um ayn gantze Zeil z löschn. **

  Weil s gro oft vürkimmt,  däß myn gantze Zeiln löscht,  kaamend schoon d Ent-
  wickler von n Urwimm daa drauf,  däß myn ainfach  dd  gan dönn Zwök schreibt.


  1. Bewög önn Mörkl gan dyr zwaittn Zeil in n untignen "Gedicht".
  2. Zipf  dd  ,  um dö Zeil z löschn.
  3. Ietz bewögst di gan dyr viertn Zeil.
  4. Zipf  2dd  , um zwo Zeiln zo n Löschn.

--->  1)  Roosn seind root;
--->  2)  Drunter ist s Koot.
--->  3)  Veigerln seind blau.
--->  4)  Umgrabn tuet s d Sau.
--->  5)  D Ur sait de Zeit,
--->  6)  Sait, däß s mi freut,
--->  7)  Dirndl, dein Gschau.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Letzn 1.2.7: RUGGGÖNGIG MACHEN (RUGGLN) 


             ** Zipf  u  , dyrmitst de lösstn Faudungen ruggltst **
             ** older   U  , um ayn gantze Zeil widerherzstölln. **

  1. Bewög önn Mörkl gan dyr mit  --->  angmörktn Zeil unt und gee dyrmit auf n
     eerstn Faeler.
  2. Zipf  x  , däßst dös eerste z vile Zaichen löschst.
  3. Ietz demmlt  u  , dyrmitst de lösste Faudung ruggltst.
  4. Ietz behöb  allsand Faeler  auf dyr Zeil  mit dyr Hilf  von n Befelh  x  .
  5. Aft gibst ayn  U  (grooß) ein,  däßst de Zeil wider yso hinbringst,  wie s
     gwösn ist.
  6. So,  und ietz  demmltst  so oft  u  , hinst däßst s  U  und de andern Fau-
     dungen rugggöngig gmacht haast.
  7. Und ietzet widerum  schreibst  so oft  <STRG>r  , hinst däßst  allsand Be-
     felh widerhergstöllt, z haissn allsse rugg-grugglt haast  (also d Rugggön-
     gigmachungen rugggöngig gmacht).
---> Beerichtig d Faeller voon dehrer Zeiil  und sttöll s mitt n Ruggruggln wi-
     der her.
  8. Die Faudungen seind gro wichtig; sö helffend ainn närrisch weiter.
     Ietz gee weiter gan dyr Zammenfassung von dyr Letzn 1.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.2


  1. Um von n Mörkl aus hinst eyn s naehste Wort zo n Löschn, zipf:  dw
  2. Um umbb n Mörkl hinst eyn s End von dyr Zeil zo n Löschn, demmlt  d$
  3. Dyrmitst ayn gantze Zeil löschst, gib ein:  dd
  4. Mechst ayn Bewögung, ayn "Wolend", öfters,  stöll de entspröchete Zal dyr-
     vor:  3dw  older aau:  d3w
  5. Dyr Pfueg für ayn Önderungsfaudung lautt yso:
               Pfemerer   [Zal]   Bewögungsschrit (Wolend)
     Und dös haisst:
      Dyr PFEMERER gibt an, WAS taan ghoert, öbbenn   d   =  löschn (»delete«).
      [ZAL] - Ayn Zal KAN myn angöbn, wenn myn halt ayn Wolend öfter habn will.
      S WOLEND,  also dyr Schrit WOHIN, besagt,  auf was i aushin will,  öbbenn
      auf aynn Wortanfang (  w  ), s End von dyr Zeil (  $  ) und so weiter.

  6. Däßst eyn n Anfang von dyr Zeil hinkimmst, schreib aynn Nuller:  0

  7. Um öbbs Vorigs wider z ruggln, gib ein:                u (klain also)
     Um allsand Önderungen in ayner Zeil z ruggln, haast:   U (also grooß)
     Um "rugg-z-ruggln", also allss wider herzstölln, zipf:  <STRG>r   

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                           Letzn 1.3.1: ANFÜEGN (»put«) 


     ** Zipf  p  , dyrmitst öbbs gnetty Glöschts naach n Mörkl anfüegst. **

  1. Bewög önn Mörkl gan dyr eerstn untignen Zeil mit  --->  dyrvor.

  2. Zipf  dd  ,  um sele Zeil z löschn und dyrmit in ayner Wimm-Osn zo n Spei-
     chern.

  3. Bewög önn Mörkl gan dyr Zeil c),  ÜBER derer, daa wo de glöschte Zeil ein-
     hinkemmen sollt.

  4. So,  und ietz gibst ainfach  p  ein,  und schoon haast dö Zeil unter derer
     mit n Mörkl drinn.
  5. Äfert  d Schritt 2 hinst 4,  hinst däßst  allsand Zeiln  yso naachynaynand
     haast, wie s hinghoernd.

---> d) Kanst du dös aau?
---> b) Veigerln seind blau.
---> c) Bedachtn kan myn lernen.
---> a) Roosn seind root.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Letzn 1.3.2: ERSÖTZN (»replace«)


      ** Zipf  rx  , um dös Zaichen unter n Mörkl durch  x  z ersötzn. **

  1. Bewög önn Mörkl zo dyr eerstn untignen Zeil mit  --->  dyrvor.

  2. Bewög önn Mörkl, hinst däß yr auf n eerstn Faeler steet.

  3. Zipf  r  und drafter dös Zaichen, wo dyrfür daa hinghoert.

  4. Widerhol d Schritt 2 und 3,  hinst däßst de eerste Zeil  gmaeß dyr zwaittn
     berichtigt haast:
--->  Wie dö Zeit eingobn wurd, wurdnd ainike falsche Zastnen zipft!
--->  Wie dö Zeil eingöbn wurd, wurdnd ainige falsche Tastnen zipft!

  5. Ietz tue mit dyr Letzn 1.3.3 weiter.

Anmörkung: Vergiß nit drauf, däßst mit n Anwenddn lernen solltst und nit öbbenn
           mit n Auswendiglernen!


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                          Letzn 1.3.3: ÖNDERN (»change«)


              ** Um hinst eyn s Wortend z öndern, zipf  ce  . **

  1. Gee mit n Mörkl auf de eerste mit  --->  angmörkte Zeil.

  2. Ietz farst netty auf s  "s"  von Wstwr hin.

  3. Zipf  ce  ein und aft d Wortberichtigung, daader also  örter  .

  4. Druck  <ESC>  und bewög önn Mörkl zo n naehstn Zaichen, wo göndert ghoert.

  5. Äfert  d Schritt 3 und 4,  hinst däß  dyr eerste Saz  wie dyr zwaitte ist.

---> Ainige Wstwr von derer Zlww ghhnnd mit n Öndern-Pfemerer gaauu.
---> Ainige Wörter von derer Zeil ghoernd mit n Öndern-Pfemerer göndert.

ce  löscht also s Wort und schlaaufft di eyn d Eingaab-Artweis.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                  Letzn 3.4.: NON MEERER ÖNDERUNGEN PFELFS  c


   ** D Löshfaudung  c  arechtt mit de nömlichnen Wolender wie dö mit  d  **

  1. Dyr Önder-Pfemerer arechtt anleich wie d Löshfaudung mit   d  ,  und zwaar
     yso:
         c    [Zal]   Bewögungsschrit (Wolend)

  2. D Wolender seind  de gleichn,  öbbenn  w  für Wort und  $  für s Zeilnend.


  3. Bewög di zo dyr eerstn untignen Zeil mit  --->  .

  4. Ietz geest auf dönn eerstn Faeler.

  5. Zipf  c$  , gib önn Rest von dyr Zeil wie in dyr zwaittn ein und druck aft
     <ESC>.
---> S End von derer Zeil sollt an de zwaitte daader anglichen werdn.
---> S End von derer Zeil sollt mit n Befelh  c$  berichtigt werdn.

Denk allweil dran,  däßst iederzeit  mit dyr Ruggtastn Faeler ausbössern kanst.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.3


  1. Um ayn  vorher glöschts Gwort  anzfüegn,  zipf  p  .  Daa dyrmit  werd dös
     gantze Gwort  NAACH n Mörkl  angfüegt.  Wenn s ayn gantze Zeil  gwösn ist,
     werd dö sel als de Zeil unterhalb n Mörkl eingfüegt.

  2. Um dös Zaichen  unter n Mörkl,  also wo dyr Mörkl ist,  z ersötzn, zipf  r
     und aft dös Zaichen, wost daadl habn willst.

  3. Dyr Önderungspfemerer ( c = »change«) laasst ainn umbb n Mörkl hinst eyn s
     End von n Wolend öndern. Zipf  ce  , dyrmitst umbb n Mörkl hinst eyn s End
     von n Wort öndertst, und  c$  hinst eyn s End von dyr Zeil.

  4. Für d Önderung lautt dyr Pfueg:

     c   [Zal]   Wolend

Ietz tue mit dyr naehstn Letzn weiter.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Letzn 1.4.1: MÖRKLSTÖLLUNG UND DAUTTICHTDARSTAND

** Demmlt <STRG>g, däßst önn Befand und Darstand von dyr Dautticht anzaigst. **
  ** Zipf  G  , dyrmitst auf ayn bestimmte Zeil in dyr Dautticht hinkimmst. **

Anmörkung: Lis dö gantze Letzn daader durch, ee wennst iewign öbbs unternimmst!

  1. Druck  <STRG>g  .  Auf dös hin erscheint auf derer Seitt ganz unt ayn Dar-
     standsmeldung  mit n Dauttichtnam  und n Befand  innerhalb  dyr Dautticht.
     Mörk dyr de Zeilnnummer für n Schrit 3.

Anmörkung:  Müglicherweis  seghst aau önn Mörklbefand  in n zesmen untern Bild-
            schirmögg. Aft ist s "Lindl" (»ruler«) eingstöllt;  meerer über dös
            laasst dyr dyr Befelh   :help 'ruler'   ausher.
  2. Druck  G  , um an s End von dyr Dautticht z kemmen.
     gg  gibst ein, däßst gan n Anfang von dyr Dautticht aufhinkimmst.

  3. Gib d Nummer von derer Zeil ein, daa wost vorher warst,  und aft non  G  .
     Dös bringt di zrugg gan seler Zeil,  daa wost stuenddst,  wiest dös eerste
     Maal  <STRG>g  gadruckst.

  4. Wennst di sicher gnueg füelst, aft füer d Schritt 1 hinst 3 aus.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Letzn 1.4.2: DYR BEFELH ZO N SUECHEN 
               

  ** Zipf  /  und dyrnaach aynn Ausdruk, um selbignen zo n Suechen. **

  1. Du gibst also in dyr Befelhsartweis s Zaichen  /  ein. Dös sel wie aau dyr
     Mörkl erscheinend drauf unt auf n Schirm, netty wie bei dyr Faudung  :  .

  2. Ietz zipf  Faeeler<EIN>  . Netty um dös 'Faeeler' willst ietz suechen.

  3. Willst um gnaun dönn Ausdruk weitersuechen, zipf ainfach  n  (wie »next«).
     Willst hinzrugg suechen, aft gibst  N  ein.

  4. Um von Haus aus zruggaus z suechen, nimm  ?  statt  /  her.

  5. Dyrmitst wider daa hinkimmst, wost herkemmen bist, nimm  <STRG>o , und dös
     öfter,  wennst weiter zrugg willst. Mit  <STRG>i  widerum kimmst vorwärts.

--->  Aynn Faeler schreibt myn nit "Faeeler"; Faeeler ist ayn Faeler

Anmörkung:  Wenn d Suech s Dauttichtend dyrraicht haat, geet s eyn n Anfang wi-
            der weiter dyrmit,  men Sach  dyr Schaltter 'wrapscan' wär auf aus.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Letzn 1.4.3: DE GÖGNKLAMMERN FINDDN


   ** Zipf  %  , um de entspröchete Klammer  )  ,  ]  older  }  z finddn. **

  1. Sötz önn Mörkl  auf iewign aine  von dene drei Klammern  (  ,  [  older  {
     in dyr untignen Zeil, wo mit  --->  angmörkt ist.

  2. Ietzet zipf s Zaichen  %  .

  3. Dyr Mörkl geet ietz auf de pässete schliessete Klammer.

  4. Ietz demmlt  %  , und dyrmit kimmst gan dyr öffneretn Klammer zrugg.

  5. Sötz önn Mörkl auf ayn anderne Klammer von   ({[]})   und pröblt   %  aus.

---> Dös ( ist  blooß ayn Pochzeil ( mit [ verschaidne ] { Klammern } drinn. ))

Anmörkung:  Um dö Müglichkeit gaast bsunders froo sein,  wennst aynmaal in aynn
            Spaichgwort verzweiflt ayn faelete Gögnklammer suechst!


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Letzn 1.4.4: D ERSÖTZUNGSFAUDUNG (»substitute«)


        ** Zipf  :s/alt/neu/g  , um 'alt' durch 'neu' zo n Ersötzn. **

  1. Gee mit n Mörkl zo dyr unt steehetn mit  --->  angmörktn Zeil.

  2. Zipf  :s/dee/de <EIN>  . Der Befelh ersötzt alsnan grad dös +eerste "dee",
     wo vürkimmt.

  3. Ietz pröblt s mit  :s/dee/de/g  . Dös zuesötzliche  g  ("Pflok"  nennt myn
     öbbs Sölchers) bewirkt, däß allss, was dyrmit kennzaichnet ist,  innerhalb
     von dyr ainn Zeil ersötzt werd.

---> Dee schoenste Zeit, däß myn dee Blüemln anschaut, ist dee schoene Lan-
     gesszeit.
  4. Um ietz  allsand Suechbegriff  innerhalb von zwo Zeiln  zo n Öndern,  zipf
     :#,#s/alt/neu/g  ,  wobei # ieweils für de eerste und lösste Zeil von dönn
     Pfraich steet.
     :%s/alt/neu/g  zipfst, däßst d Vürkemmen in dyr gantzn Dautticht öndertst.
        Mit  :%s/alt/neu/gc  finddst allsand Vürkemmen in dyr gsamtn Dautticht;
     daa werst aber zeerst non gfraagt, obst ys ersötzn willst older nity.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.4
                        
  1. <STRG>g   zaigt dönn ietzundn Dauttichtbefand  und önn Darstand dyrvon an.
           G   bringt di an s End von dyr Dautticht.
      <Zal>G   bringt di gan dyr entspröchetn Zeilnnummer.
     <Zal>gg   geet +grad yso.
          gg   bringt di zo dyr eerstn Zeil.
  2. D Eingaab von  /  mit aynn Ausdruk suecht VÜRSHLING um dönn Ausdruk.
     Gibst  ?  und aynn Suechbegrif ein, suecht s um dönn ÄRSHLING.
     Zipf naach ayner Suech  n  ;  naacherd werd in de gleiche Richtung weiter-
     gsuecht. Mit  N  geet s umkeerter weiter.
     <STRG>o  bringt di zo ölterne Befändd zrugg,  <STRG>i  zo neuerne.

  3. D Eingaab von  % , wenn dyr Mörkl auf ainer von dene Klammern steet:   ({[
     )]}  , bringt di zo dyr Gögnklammer.

  4. Um dös eerste Vürkemmen von  "alt"  in ayner Zeil durch  "neu"  z ersötzn,
     zipf  :s/alt/neu  .
     Um allsand in ayner Zeil z ersötzn, zipf  :s/alt/neu/g  .
     Mechst allss in zwo Zeiln ersötzn, demmlt zo n Beispil  :5,6s/alt/neu/g  .
     Mechst allss in dyr gantzn Dautticht ersötzn, gib ein:  :%s/alt/neu/g    .
     Willst ayn ieds Maal bstaetln, höng 'c' wie »confirm« hint anhin.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            Letzn 1.5.1: ZWISCHNDRINN AYNN AUSSERIGNEN BEFELH AUSFÜERN 
            

   ** Willst ayn Gfäßfaudung ausfüern, gib ainfach dö sel naach  :!  ein. **
 
  1. Zipf  dönn bekanntn Befelh   :  ,  dyrmitst  mit n Mörkl  auf n Bildschirm
     ganz abhin kimmst. Draufhin kanst aynn gwonen Gfäßbefelh eingöbn.

  2. Zeerst  kimmt aber non ayn Ruefzaichen   !  .  Und ietzet haast d Müglich-
     keit, ayn beliebige ausserige Gfäßfaudung auszfüern.

  3. Als Beispil zipf   :!ls<EIN>  ;  und schoon haast ayn Auflistung von deinn
     Verzaichniss,  netty  wie wennst  ganz gwon  in n Eingib wärst.  Geet   ls
     aus iewign aynn Grund nit, aft pröblt s mit  :!dir<EIN>  .

Also non aynmaal: Mit dönn Angang kan ayn iede beliebige ausserige Faudung aus-
                  gfüert werdn, aau mit Auerwerdd.

Und wolgmörkt:    Alle Befelh,  wo mit  :  angeend,  müessend mit  <EIN>  bstö-
                  tigt werdn. Dös dyrsagn myr fürbaß +niemer.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
           Letzn 1.5.2: NON MEERER DRÜBER, WIE MYN DAUTTICHTN SCHREIBT


     ** Um öbbs Gönderts neu z speichern, zipf  :w NEUER_DAUTTICHTNAM  . **

  1. Zipf  :!dir   older  :!ls  ,  däßst dyr ayn Auflistung von deinn Verzaich-
     niss ausherlaasst. Däßst drafter  <EIN>  eingöbn muesst, waisst ee schoon.

  2. Suech dyr aynn Dauttichtnam aus, dönn wo s non nit geit, öbbenn  POCH  .

  3. Ietz demmlt:  :w POCH  (also mit POCH als dönn neuen Dauttichtnam).

  4. Dös speichert ietz de gantze Dautticht, also önn Wimmschainer,  unter dönn
     Nam POCH. Dös kanst leicht überprüeffen, indem däßst ainfach  :!ls   older
     :!dir  zipfst und dyrmit deinn Verzaichnissinhalt seghst.

Anmörkung:  Stigst ietz aus n Wimm aus und gännst n aft wider mit   vim POCH  ,
            naacherd wär dö Dautticht ayn gnaune Aamum von n Schainer dyrselbn,
            wiest n gspeichert haast.

  5. Ietz verweitert dö Dautticht - fallsst s Fenstl haast - , mit   :!del POCH
     beziehungsweis bei aynn Unixgebäu mit   :!rm POCH   .
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            Letzn 1.5.3: AYNN TAIL VON N GWORT ZO N SPEICHERN AUSWALN

** Um aynn Tail von dyr Dautticht z speichern, zipf  v [Wolend] :w DAUTTICHT **

  1. Ruck önn Mörkl auf netty dö Zeil daader.

  2. Demmlt  v  und gee mit n Mörkl  auf dönn fümftn Auflistungspunt untet.  Du
     seghst glei, däß s Gwort vürherghöbt erscheint.

  3. Druck s Zaichen  :  . Ganz unt auf n Bildschirm erscheint  :'<,'>  .

  4. Zipf  w POCH  , wobei s dönn Dauttichtnam POCH  non nit geit.  Vergwiß di,
     däßst dös  :'<,'>w POCH  aau +seghst, ee wennst  <EIN>  druckst.

  5. Dyr Wimm schreibt de ausgwaltn Zeiln eyn de Dautticht POCH einhin.  Benutz
     :!dir  older  :!ls  ,  däßst dös überprüeffst. Lösh s fein nit öbbenn! Mir
     brauchend s nömlich für de naehste Letzn.

Anmörkung:  Druckt myn  v  , ginnt d Sichtisch-Auswal. Du kanst mit n Mörkl um-
            aynandfarn,  um d Auswal  z veröndern.  Drafter kan myn mit yn aynn
            Pfemerer mit dönn Gwort öbbs machen.  Zo n Beispil löscht   d   dös
            Gwort.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
               Letzn 1.5.4: EINLÖSN UND ZAMMENFÜERN VON DAUTTICHTN   


    ** Um önn Inhalt von ayner Dautticht einzlösn, zipf  :r DAUTTICHTNAM  **

  1. Sötz önn Mörkl über dö Zeil daader.

OBACHT:  Aft däßst önn Schrit 2  ausgfüert haast,  seghst auf aynmaal  öbbs aus
         dyr Letzn 1.5.3. Bewög di naacherd wider abwärts, dyrmitst dö Letzn wi-
         derfinddst.
  2. Ietz  lis  dein Dautticht  POCH ein,  indem däßst d Faudung   :r POCH aus-
     füerst,  wobei wie gsait POCH  für dönn  von dir  ausgsuechtn Dauttichtnam
     steet. De einglösne Dautticht werd unterhalb dyr Mörklzeil eingfüegt.

  3. Um zo n Überprüeffen,  ob de Dautticht  aau gwiß einglösn ist,  gee zrugg;
     und du seghst,  däß s ietz zwo Ausförtigungen  von dyr Letzn 1.5.3. geit, s
     Urniss und de eingfüegte Dauttichtfassung.

Anmörkung:  Du kanst aau  d Ausgaab von aynn Ausserigbefelh einlösn.  Zo n Bei-
            spil list   :r !ls  d Ausgaab von dyr Faudung  ls   ein und füegt s
            unterhalb n Mörkl ein.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.5


  1. :!FAUDUNG  füert aynn ausserignen Befelh aus.

      Daader ayn Öttlych gwänddte Beispiler:
      (Fenstl)              (Unix - Linux)
      :!dir                 :!ls                - listt s Verzaichniss auf.
      :!del DAUTTICHT       :!rm DAUTTICHT      - verweitert sele Dautticht.

  2. :w DAUTTICHT  speichert de ietzunde Wimmdautticht  unter dönn besagtn Nam.

  3. v  WOLEND  :w DAUTTICHTNAM   schreibt de sichtisch ausgwaltn Zeiln  eyn de
     Dautticht mit seln Nam.

  4. :r DAUTTICHTNAM  ladt sele Dautticht  und füegt s  unterhalb n Mörklbefand
     ein.

  5. :r !dir   list d Ausgaab  von dyr Faudung   dir   und füegt s  unterhalb n
     Mörklbefand ein.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Letzn 1.6.1: ZEIL ÖFFNEN (»open«)


       ** Zipf  o  , um ayn Zeil unterhalb n Mörkl z öffnen und eyn d **
                        ** Einfüegartweis z kemmen. **

  1. Bewög önn Mörkl zo dyr eerstn mit  --->  angmörktn Zeil unt.

  2. Zipf  o  (klain),  um ayn Zeil UNTERHALB n Mörkl z öffnen und mit dyr Ein-
     füegartweis weiterztuen.

  3. Ietzet zipf ayn Weeng öbbs  und druck  <ESC>  , um d Einfüegartweis z ver-
     laassn.
---> Mit  o  werd dyr Mörkl in dyr Einfüegartweis auf de offene Zeil gsötzt.

  4. Um ayn Zeil  OBERHALB n Mörkl aufzmachen,  gib ainfach ayn groosss O statt
     yn aynn klainen ein. Versuech dös auf dyr untignen Zeil.

---> Öffnet ayn Zeil über derer daader mit  O  ,  wenn dyr Mörkl auf derer Zeil
     ist.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.6.2: GWORT ANFÜEGN (»append«)


                ** Zipf  a  , um öbbs NAACH n Mörkl einzfüegn. **

  1. Bewög önn Mörkl gan n Anfang von dyr eerstn Üebungszeil mit  --->  unt.

  2. Druck  e  , hinst däß dyr Mörkl an n End von  Zei  steet.

  3. Zipf ayn klains  a  , um öbbs NAACH n Mörkl anzfüegn.

  4. Vergöntz dös Wort  wie in dyr Zeil drunter.  Druck  <ESC>  , um d Schreib-
     Artweis z verlaassn.

  5. Bewög di mit  e   zo n naehstn ungantzn Wort  und widerhol d Schritt 3 und
     4.

---> Dö Ze biett ayn Glögn , ayn Gwort in ayner Zeil anzfü.
---> Dö Zeil biett ayn Glögnet, ayn Gwort in ayner Zeil anzfüegn.

Anmörkung:   a  ,  i  und  A  bringend ainn gleichermaaßn eyn d Einfüegartweis;
             dyr ainzige Unterschaid ist, WO mit n Einfüegn angfangt werd.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
              Letzn 1.6.3: AYN ANDERNE WEIS ZO N ERSÖTZN (»replace«)


 ** Demmlt ayn groosss  R  , um meerer als wie grad ain Zaichen z ersötzn. **

  1. Bewög önn Mörkl zo dyr eerstn untignen, mit  --->  angmörktn Zeil.
     Gee mit n Mörkl gan n Anfang von n eerstn  xxx  .

  2. Ietz druck  R   und zipf sele Zal,  wo drunter  in dyr zwaittn Zeil steet,
     yso däß de sel s xxx ersötzt.

  3. Druck  <ESC>  ,  um d Ersötzungsartweis z verlaassn.  Du gspannst, däß dyr
     Rest von dyr Zeil unveröndert bleibt.

  4. Äfert die Schritt, um dös überblibne xxx z ersötzn.

---> S Zunddn von 123 zo xxx ergibt xxx.
---> S Zunddn von 123 zo 456 ergibt 579.

Anmörkung: D Ersötzungsartweis ist wie d Einfüegartweis,  aber ayn ieds eindem-
           mlte Zaichen löscht ayn vorhanddns.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.6.4: GWORT AAMEN UND EINFÜEGN

 ** Benutz önn Pfemerer  y  , um öbbs z aamen, und  p  , um öbbs einzfüegn. **

  1. Gee zo dyr mit  --->  angmörktn Zeil unt  und sötz önn Mörkl  hinter "a)".

  2. Ginn d Sichtisch-Artweis mit   v   und bewög önn Mörkl gnaun vor "eerste".
  
  3. Zipf  y  , um dönn vürherghöbtn Tail z aamen.

  4. Bewög önn Mörkl gan n End von dyr naehstn Zeil:  j$

  5. Demmlt  p  , um dös Gwort einzfüegn, und aft:  a zwaitte <ESC>  .

  6. Benutz d Sichtischartweis, um " Eintrag." auszwaln, aam s mittls  y  , be-
     wög di gan n End von dyr naehstn Zeil mit  j$   und füeg s Gwort dortn mit
     p  an.

--->  a) dös ist dyr eerste Eintrag.
      b)
Anmörkung:   Du kanst   y   aau als Pfemerer verwenddn;   yw  zo n Beispil aamt
             hinst eyn n naehstn Wortanfang (aane dönn selber).
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                           Letzn 1.6.5: SCHALTTER SÖTZN

** Sötz aynn Schaltter yso,  däß ayn Suech older Ersötzung Grooß- und Klain- **
                            ** schreibung übergeet. **
                            
  1. Suech um 'übergee", indem däßst   /übergee   eingibst.
     Widerhol d Suech ayn Öttlych Maal, indem däßst de Tastn  n  druckst.

  2. Sötz de Zwisl - önn Schaltter - 'ic' (»ignore case«), indem däßst  :set ic
     eingibst.
  3. Ietz suech wider um 'übergee' und tue aau wider mit  n  weiter.  Daa fallt
     dyr auf, däß ietz öbbenn aau  Übergee  und  ÜBERGEE  hergeet.

  4. Sötz de Zwisln 'hlsearch' und 'incsearch' pfelfs:   :set hls is

  5. Widerhol d Suech und bobacht, was ietz gschieght:   /übergee <EIN>

  6. Däßst grooß und klain wider gwon unterscheidst, zipf:   :set noic

Anmörkung:  Mechst de Tröffer niemer vürherghöbt seghn,  gib ein:   :nohlsearch
Anmörkung:  Sollt klain/grooß bei ayner ainzignen Suech wurst sein,  benutz  \c
            in n Suechausdruk:   /übergee\c <EIN>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.6

  1. Zipf  o  , um ayn Zeil  UNTERHALB  n Mörkl z öffnen und d Einfüegartweis z
                ginnen.
     Zipf  O  , um ayn Zeil OBERHALB n Mörkl z öffnen.

  2. Zipf  a  , um NAACH n Mörkl ayn Gwort einzfüegn.
     Zipf  A  , um ayn Gwort naach n Zeilnend anzfüegn.

  3. D Faudung  e  bringt di gan n End von aynn Wort.

  4. Dyr Pfemerer  y  (»yank«) aamt öbbs,  p  (»put«) füegt dös ein.

  5. Ayn groosss  R  geet eyn d Ersötzungsartweis, hinst däß myn  <ESC> druckt.

  6. D Eingaab von ":set xxx"  sötzt de Zwisl "xxx".  Ayn Öttlych Zwisln seind:
     'ic' 'ignorecase'   Grooß/klain wurst bei ayner Suech
     'is' 'incsearch'    Zaig aau schoon ayn Tailüberainstimmung
     'hls' 'hlsearch'    Höb allsand pässetn Ausdrück vürher
     Dyr Schaltternam kan in dyr Kurz- older Langform angöbn werdn.

  7. Stöll yn ayner Zwisl "no" voran, däßst ys abschalttst:   :set noic
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Letzn 1.7.1: AYN HILFGWORT AUFRUEFFEN


           ** Nutz dös einbaute Hilfgebäu, de "Betribsanlaittung". **

  Eyn n Wimm  ist ayn ausfüerliche "Gebrauchsanweisung"  einbaut.  Für s Eerste
  pröblt ainfach ains von dene dreu aus:
    - Druck d <HILF>-Tastn, wennst öbbenn aine haast.
    - Druck de Tastn <F1>, fallsst ys haast.
    - Zipf   :help <EIN>

  Lis di eyn s Hilffenster ein, dyrmitst draufkimmst, wie dös mit dyr Hilf geet.
  Demmlt  <STRG>w w  , um von ainn Fenster zo n andern zo n Springen.
  Demmlt   :q <EIN>  , um s Hilffenster zo n Schliessn.

  Du kanst zo so guet wie allssand ayn Hilf finddn,  indem däßst yn dyr Faudung
  :help  aynn Auerwerd naachstöllst und istig  <EIN>  nit vergisst. Pröblt dös:

     :help w
     :help c_CTRL-D
     :help insert-index
     :help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Letzn 1.7.2: ERSTÖLL AYN GIN-SCHRIPF


              ** Mutz önn Wimm mit de einbautn Faehigkeitn auf. **

  Dyr Wimm besitzt ayn Wösn Schäftungen,  wo über n Urwimm aushingeend, aber de
  meerern dyrvon  seind in dyr Vorgaab ausgschaltt.  Dyrmitst meerer aus n Wimm
  ausherholst, erstöllst ayn "vimrc"-Dautticht.

  1. Lög ayn "vimrc"-Dautticht an;  dös geet  ie naach Betribsgebäu  verschidn:
     :e ~/.vimrc     für s Unix
     :e ~/_vimrc     bei n Fenstl

  2. Ietz lis önn Inhalt von dyr Beispil-"vimrc"-Dautticht ein:
     :r $VIMRUNTIME/vimrc_example.vim

  3. Speichert de Dautticht mit:
     :w

  4. Bei n naehstn Gin von n Wimm  ist aft d Füegnussvürherhöbung zuegschalttn.
     Du kanst dyr allss  eyn dö Dautticht  einhinschreibn,  wasst bständig habn
     willst. Meerer dyrzue erfarst unter:   :help vimrc-intro
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                              Letzn 1.7.3: VERGÖNTZN


            ** Befelhszeilnvergöntzung mit  <STRG>d  und  <TAB>  **

  1. Vergwiß di, däß dyr Wimm nit auf n Urwimm-"Glais" fart:   :set nocp

  2. Schaug naach, wölcherne Dauttichtn däß s in n Verzaichniss geit:   :!ls
     older   :!dir
  3. Zipf önn Anfang von ayner Faudung:   :e

  4. Druck  <STRG>d  , und dyr Wimm zaigt ayn Listn von Faudungen, wo mit   "e"
     angeend.
  5. Druck  <TAB>  , und dyr Wimm vervollstöndigt önn Faudungsnam zo   ":edit".

  6. Füeg  ayn Laerzaichen  und önn Anfang  von ayner  besteehetn Dautticht an:
     :edit DAU

  7. Druck  <TAB>  . Dyr Wimm vergöntzt önn Nam, dös haisst, wenn yr aindeuttig
     ist.
Anmörkung:  D Vergöntzung  geit s für aynn Hauffen Faudungen.  Versuech ainfach
            <STRG>d  und  <TAB>  . Bsunders nützlich ist dös bei   :help  .
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         ZAMMENFASSUNG VON DYR LETZN 1.7


  1. Zipf  :help  oder druck  <F1>  oder  <HILF> , um ayn Hilffenster z öffnen.

  2. Zipf  :help FAUDUNG  , um auf ayn Hilf gan aynn Befelh z kemmen.

  3. Zipf  <STRG>w w  , um zo n andern Fenster z springen.

  4. Zipf  :q  , um s Hilffenster z schliessn.

  5. Erstöll ayn vimrc-Ginschripf  zuer Sicherung von deine Mötzneinstöllungen.

  6. Druck  <STRG>d  , aft däßst naach   :   ayn Faudung  angfangt haast,  dyr-
     mitst mügliche Vergöntzungen anzaigt kriegst.
     Druck  <TAB>  für ain Vervollstöndigung yllain.






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Dös wär ietzet s End von n Wimmschainer.  Gangen ist s daa drum,  aynn kurtzn
  und bündignen Überblik  über s Blat WIMM z lifern,  netty vil gnueg,  däß myn
  für s Eerste  wirklich öbbs  dyrmit anfangen kan.  Dyrmit ist s aber auf kain
  Weitn non nit taan;  dyr Wimm haat schoon non vil meerer  auf Lager.  Lis als
  Naehsts aynmaal s Benutzerhandbuech:   :help user-manual

  Zo n Weiterlösn und Weiterlernen wör dös Buech daader zo n Empfelhen:
    Vim - Vi Improved - von n OUALLINE Steve
    Verlaag: New Riders
  Dös ist dös eerste Buech, wo ganz yn n Wimm gwidmt ist, netty dös Grechte für
  Anfönger. Es haat ayn Wösn Beispiler und aau Bilder drinn.
  See https://iccf-holland.org/click5.html

  Dös folgete Buech  ist schoon ölter und meerer  über n Urwimm  als wie über n
  Wimm,  aber aau zo n Empfelhen:   Textbearbeitung mit dem vi-Editor - von dyr
  LAMB Linda und n ROBBINS Arnold  -  Verlaag O'Reilly  - Buechlaittzal (ISBN):
  3897211262
  In dönn Buech  kan myn fast allss finddn,  was myn mit n Urwimm angeen mecht.
  De söxte Ausgaab enthaltt aau schoon öbbs über n Wimm.
  Als ietzunde Bezugniss für d Fassung 7  und ayn pfrenge Einfüerung  dient dös
  folgete Buech:
    vim ge-packt  von n WOBST Reinhard
    mitp-Verlaag, Buechlaittzal 978-3-8266-1781-2
  Trotz dyr recht pfrengen Darstöllung  ist s durch seine viln nützlichnen Bei-
  spiler aau für Einsteiger grad grecht.  Probhaeupster und de Beispilschripfer
  seind zesig zo n Kriegn; see   https://iccf-holland.org/click5.html

  Verfasst habnd dönn Schainer dyr PIERCE Michael C. und WARE Robert K. von dyr
  Kolraader Knappnschuel (Colorado School of Mines). Er beruet auf Entwürff, wo
  dyr SMITH Charles  von dyr  Kolraader Allschuel  (Colorado State  University)
  zuer Verfüegung gstöllt haat. Gundpost:   <EMAIL>
  Für n Wimm haat n dyr MOOLENAAR Bram barechtt.
  De bairische Übersötzung  stammt von n HELL Sepp 2009,  ayn Weeng überarechtt
  2011. Sein Gundpostbrächt ist   <EMAIL>

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
