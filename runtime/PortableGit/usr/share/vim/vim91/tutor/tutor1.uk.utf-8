===============================================================================
=                         Ласкаво просимо в уроки VIM                        =
===============================================================================
        
     Vim дуже потужний редактор, що має багато команд. Всі команди неможливо
     помістити в підручнику на зразок цього, але цих уроків достатньо, щоб 
     ви навчились з легкістю користуватись Vim як універсальним редактором. 

     УВАГА:
     Уроки цього підручника вимагають зміни тексту. Зробіть копію файлу, щоб
     практикуватись на ньому.

     Важливо пам'ятати, що цей підручник має на меті навчання на практиці.
     Це означає що ви маєте застосовувати команди щоб вивчити їх. Просто
     прочитавши текст, ви забудете команди.

     Кнопки на клавіатурі, будемо позначати квадратними дужками: [кнопка].

     А зараз переконайтесь, що включена англійська розкладка і не затиснутий
     Caps Lock, і натисніть кнопку j щоб переміститись до першого уроку.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Урок 1.1.1: ПЕРЕМІЩЕННЯ КУРСОРА 


   ** Щоб переміщати курсор використовуйте кнопки [h],[j],[k],[l],
      як вказано на схемі: 
             ^
            [k]      
        <[h]   [l]>       
            [j]
             v                

  Таке розміщення спочатку може видатись трохи дивним. Як наприклад те, що
  кнопка [l] переміщує курсор вправо. Але клавіші розміщені так, щоб 
  мінімізувати кількість рухів. Найчастіша дія яку користувач робить з 
  текстовим файлом - це читає його. А при читанні прокручують текст вниз.
  Тому вниз прокручує [j] - вона знаходиться якраз під вказівним пальцем
  правої руки. 

  Курсор можна переміщувати і класичним способом (курсорними клавішами), але
  зручніше буде, якщо ви опануєте спосіб Vim. (Особливо якщо ви вже вмієте
  набирати всліпу).

  1. Попереміщуйте курсор по екрану, поки не призвичаїтесь.

  2. Перемістіться до наступного уроку. 


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                          Урок 1.1.2: Вихід з Vim


Увага! Перед тим як виконувати цей урок прочитайте його повністю.

  1. Натисніть [ESC] (щоб впевнитись що ви в звичайному режимі).

  2. Наберіть:  :q! [ENTER].
     Це завершує роботу, відкидаючи всі зміни які ви здійснили.

  3. Коли ви побачите привітання терміналу введіть команду яку ви використали
     щоб відкрити цей підручник. Скоріш за все це було: vim tutor.txt [ENTER]

  4. Якщо ви запам'ятали кроки з 1 по 3, виконайте їх, і переходьте до 
     наступного уроку. 

Зауваження: Команда :q! [ENTER]  завершує роботу і відкидає всі зміни. Через
            кілька уроків ви навчитесь зберігати зміни в файл.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Урок 1.1.3:РЕДАГУВАННЯ ТЕКСТУ. ВИДАЛЕННЯ. 


           ** Натисніть [x] щоб видалити символ під курсором. **

  1. Перемістіть курсор до лінії нижче, яка позначена так: --->.

  2. Щоб виправити помилки перемістіть курсор так, щоб він став над 
     символом який треба видалити.

  3. Натисніть [x] щоб видалити непотрібний символ.

  4. Повторіть кроки з другого по четвертий, поки речення не стане правильним.

---> Ккоровва перрестрибнуууууула ччерезз мііісяццць.

  5. Тепер, коли речення правильне, можна перейти до уроку 1.1.4.

Зауваження: Протягом навчання не старайтесь запам'ятати все.
            Вчіться практикою.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Урок 1.1.4: РЕДАГУВАННЯ ТЕКСТУ. ВСТАВКА


                      ** Натисніть  [i] щоб вставити текст. **

  1. Перемістіть курсор на перший рядок позначений:  --->.

  2. Перемістіть курсор на символ, ПІСЛЯ якого потрібно вставити текст.

  3. Натисніть [i] і наберіть необхідні вставки.

  4. Коли всі помилки виправлені натисніть [ESC] щоб повернутись в звичайний
     режим.

---> З прав текст.
---> З цього рядка пропав деякий текст.

  5. Коли призвичаїтесь вставляти текст - переходьте до уроку 1.1.5.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Урок 1.1.5: РЕДАГУВАННЯ ТЕКСТУ. ДОДАВАННЯ.


                        ** Натисніть [A] щоб додати текст. **

Увага! Тут і далі, коли мають наувазі клавішу з буквою в верхньому
       регістрі, то це означає що її натискають з затиснутою [SHIFT].

  1. Перемістіть курсор до першої лінії внизу позначеної --->.
     Не має значення над яким символом знаходиться курсор.

  2. Натисніть [A] і введіть необхідне доповнення.

  3. Коли додавання завершене натисніть [ESC] щоб повернутись в 
     звичайний режим.

  4. Перемістіть курсор до другої лінії позначеної ---> і повторіть
     кроки 2 і 3 щоб виправити речення.

---> З цього рядка пропущ
     З цього рядка пропущений текст.
---> З цього рядка також
     З цього рядка також пропущений текст.

  5. Після виконання вправ, переходьте до наступного уроку.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Урок 1.1.6: РЕДАГУВАННЯ ФАЙЛУ

                    ** Використайте :wq щоб зберегти файл і вийти.**

Увага! Перед виконанням уроку прочитайте його повністю.

  1. Вийдіть з цього підручника як ви робили в уроці 1.1.2:  :q![ENTER]
     Або якщо ви маєте доступ до іншого терміналу виконуйте наступні
     дії в ньому.

  2. В терміналі наберіть команду:  vim НазваФайлу [ENTER]
     'vim' - команда для запуску редактора, НазваФайлу - файл який будемо
     редагувати. Якщо ввести неіснуючий файл, то він створиться

  3. Відредагуйте текст, як навчились у попередніх уроках.

  4. Щоб зберегти зміни у файлі, і вийти з Vim наберіть:  :wq [ENTER]

  5. Якщо ви вийшли з підручника на першому кроці, то зайдіть в нього
     знову і переходьте до підсумку.

  6. Після прочитання і засвоєння попередніх кроків виконайте їх.
  
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               ПІДСУМОК УРОКУ 1.1


  1. Курсор керується курсорними клавішами, або клавішами [h][j][k][l]
         [h] (вліво)    [j] (вниз)       [k] (вверх)    [l] (вправо)

  2. Щоб запустити Vim з терміналу наберіть:  vim Назва файлу [ENTER]

  3. Щоб вийти з Vim наберіть: [ESC] :q! [ENTER]  щоб відкинути всі зміни.
                 або наберіть: [ESC] :wq [ENTER]  щоб зберегти всі зміни.

  4. Щоб видалити символ під курсором натисніть [x].

  5. Щоб вставити, чи доповнити текст наберіть:
         [i]   текст що вставляєтсья   [ESC]    вставиться перед курсором
         [A]   текст до додається      [ESC]    додасть текст до рядка

Зауваження: Натискання [ESC] перенесе вас в звичайний режим, чи відмінить
         не до кінця введену команду.

Тепер переходьте до уроку 1.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Урок 1.2.1: КОМАНДИ ВИДАЛЕННЯ


                       ** Введіть dw щоб видалити слово. **

  1. Натисніть [ESC], щоб переконатись що ви в звичайному режимі.

  2. Перемістіть курсор до лінії нижче позначеної --->.

  3. Перемістіть курсор до початку слова що має бути видалене.

  4. Введіть dw щоб слово пропало.

  Зауваження: Буква d з'явиться в останньому рядку екрану, якщо ви її натиснули.
              Vim чекає введення наступного символа. Якщо з'явилось щось інше
              значить ви щось не так ввели. Натисніть [ESC] і почніть спочатку.

---> Є деякі слова весело, які не потрібні папір в цьому реченні.

  5. Повторюйте кроки 3 і 4 поки речення не стане правильне, а тоді переходьте
     до уроку 1.2.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Урок 1.2.2: БІЛЬШЕ КОМАНД ВИДАЛЕННЯ


     ** Наберіть d$ щоб видалити символи від курсора до кінця рядка. **

  1. Натисніть [ESC] щоб переконатись що ви в звичайному режимі.

  2. Перемістіть курсор до лінії нижче, що позначена --->.

  3. Перемістіть курсор до кінця правильного рядка (ПІСЛЯ першої крапки).

  4. Введіть d$ щоб видалити все до кінця рядка.

---> Хтось надрукував кінець цього рядка двічі. кінець цього рядка двічі.


  5. Перейдіть до уроку 1.2.3 щоб розібратись в цьому детальніше.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Урок 1.2.3:ОПЕРАТОРИ І ПЕРЕМІЩЕННЯ


  Багато команд що змінюють текст утворені з оператора і переміщення.
  Формат команди видалення з оператором d подано нижче:

        d   переміщення

  Де:
    d      - оператор видалення.
    переміщення - з чим працює оператор (описано нижче).

  Короткий список переміщень:
    w - до початку наступного слова, НЕ ВКЛЮЧАЮЧИ його перший символ.
    e - до кінця поточного слова, ВКЛЮЧАЮЧИ останній символ.
    $ - до кінця рядка, ВКЛЮЧАЮЧИ останній символ.

  Тому введення de видалить символи від курсора, до кінця слова.

Зауваження: Натискання тільки переміщення в звичайному режимі відповідно 
            переміщує курсор.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
             Урок 1.2.4: ВИКОРИСТАННЯ ЛІЧИЛЬНИКА ДЛЯ ПЕРЕМІЩЕННЯ


   ** Введення числа перед переміщенням повторює його стільки раз. **

  1. Перемістіть курсор до початку рядка позначеного --->

  2. Введіть  2w щоб перемістити курсор на два слова вперед.

  3. Введіть 3e щоб перемістити курсор в кінець третього слова.

  4. Введіть 0 (нуль) щоб переміститись на початок рядка.

  5. Повторіть кроки 2 і 3 з різними числами.

---> А це просто рядок зі словами, серед яких можна рухати курсором.

  6. Переходьте до уроку 1.2.5.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Урок 1.2.5: БАГАТОРАЗОВЕ ВИДАЛЕННЯ


   ** Введення числа з оператором повторює його стільки ж разів. **

  В комбінації з оператором видалення, і переміщення з лічильника можна 
  видаляти потрібне число елементів.
  Для цього введіть 
         d   число   переміщення

  1. Перемістіться до першого слова в ВЕРХНЬОМУ РЕГІСТРІ в рядку 
     позначеному --->.

  2. Введіть d2w щоб видалити два слова.

  3. Повторіть кроки 1 і 2 з різними числами, щоб видалити все зайве.

--->  цей ABC DE рядок FGHI JK LMN OP слів Q RS TUV почищений.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Урок 1.2.6: ОПЕРАЦІЇ З РЯДКАМИ


                 ** Введіть  dd  щоб видалити весь рядок. **

  Через те, що видалення всього рядка є доволі частою дією розробники Vi
  вирішили що для цієї операції потрібна проста команда, як dd.

  1. Перемістіть курсор до другого рядка в вірші нижче.
  2. Введіть dd щоб видалити рядок.
  3. Потім перемістіться до четвертого рядка.
  4. Введіть 2dd щоб видалити два рядки.

--->  1)  Троянди червоні,
--->  2)  Багнюка весела,
--->  3)  Волошки голубі,
--->  4)  В мене є машина,
--->  5)  Годинник каже час,
--->  6)  Цукерки солодкі,
--->  7)  Дарую тобі.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Урок 1.2.7: ВІДКИНУТИ ЗМІНИ


  ** Натисніть u щоб скасувати останні команди, U щоб виправити ввесь рядок. **

  1. Перемістіть курсор до рядка нижче позначеного ---> на місце першої помилки.
  2. Натисніть x щоб видалити непотрібний символ.
  3. Потім натисніть u щоб відмінити виправлення.
  4. Цього разу виправте всі помилки в рядку використовуючи команду x .
  5. Після цього введіть U, і відкиньте всі зміни в цілому рядку.
  6. Натисніть u кілька разів, щоб відмінити U і попередні команди.
  7. Тепер натисніть CTRL-R кілька разів, щоб повторити відмінені команди 
     (відмінити відміну).

---> Вииправте помилки наа цьоому рядку і вііідмініть їх.

  8. Тепер можна переходити до підсумків другого уроку.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               ПІДСУМОК УРОКУ 1.2


  1. Щоб видалити все від курсора аж до початку наступного слова введіть: dw
  2. Щоб видалити від курсора до кінця рядка:    d$
  3. Щоб видалити увесь рядок:    dd

  4. Щоб повторити переміщення, поставте перед ним число повторів:   2w
  5. Формат команди зміни:
               оператор   [число]   переміщення
     де:
       оператор - що робити, як наприклад d для видалення
       [число] - кількість повторів 
       переміщення   - куди переміститись перед виконанням оператора, як
                як наприклад w (слово), $ (кінець рядка), і т.і.

  6. Щоб переміститись до початку рядка використовуйте нуль:  0

  7. Щоб відмінити попередню дію введіть:   u  (u в нижньому регістрі)
     Щоб відмінити всі зміни рядка введіть: U  (U в верхньому регістрі)
     Щоб скасувати відміну натисніть:       CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Урок 1.3.1: КОМАНДА PUT


       ** Введіть p щоб вставити перед тим видалений текст після курсору. **

  1. Перемістіть курсор до першої ---> лінії внизу.

  2. Введіть dd щоб видалити рядок і зберегти його в регістрі Vim.

  3. Перемістіть курсор до рядка в), НАД тим місцем де має бути видалений рядок.

  4. Натисніть p щоб вставити рядок під курсором.

  5. Повторіть кроки від 2 до 4 щоб вставити всі рядки в правильному порядку.

---> г) всіх до кузні іззива.
---> б) а в коваля серце тепле,
---> в) а він клепче та й співа,
---> а) А в тій кузні коваль клепле,



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Урок 1.3.2: Команда заміни


       ** Наберіть rx щоб замінити символ під курсором на x . **

  1. Перемістіть курсор до першого рядка нижче, позначеного  --->.

  2. Помістіть курсор над першою помилкою.

  3. Наберіть r а потім символ який має стояти там.

  4. Повторіть кроки з 2 по 3 поки перший рядок не стане еквівалентним другому.

--->  Коли ця лігія набираламт. хтось наьтснкв геправмльні унопкм!
--->  Коли ця лінія набиралась, хтось натиснув неправильні кнопки!

  5. Зараз переходьте до уроку 1.3.3.

Примітка: Ви маєте вчитись діями, а не простим заучуванням, пам'ятаєте?



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Урок 1.3.3: ОПЕРАТОР ЗАМІНИ


           ** Щоб зробити заміну до кінця слова введіть ce . **

  1. Перемістіть курсор до першого рядка позначеного --->.

  2. Помістіть курсор над у в слові рукра.

  3. Введіть ce і правильне закінчення слова (ядок в цьому випадку).

  4. Натисніть [ESC] і переходьте до наступного символа, який потрібно замінити.

  5. Повторюйте кроки 3 і 4 поки перше речення не стане таким самим як і друге.

---> Цей рукра має кілька слів що потретамув заміни за допоцкщшг оператора.
---> Цей рядок має кілька слів що потребують заміни за допомогою оператора.

Зауважте що ce видаляє слово, і поміщає вас в режим вставки.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Урок 1.3.4: БІЛЬШЕ ЗМІН З c


  ** Оператор заміни використовується з тими ж переміщеннями що і видалення. **

  1. Оператор заміни працює в такий же спосіб що і видалення. Формат:

         c    [число]   переміщення

  2. Переміщення ті ж самі, такі як w (слово) і $ (кінець рядка).

  3. Перемістіться до першого рядка позначеного --->.

  4. Перемістіть курсор до першої помилки.

  5. Наберіть c$ і решту рядка, щоб він став таким як другий і натисніть [ESC].

---> Кінець цього рядка потребує якихось дій щоб стати таким як кінець другого.
---> Кінець цього рядка можна виправити за допомогою команди c$.

Примітка: Можна використовувати кнопку Backspace щоб виправляти опечатки при 
                наборі.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               ПІДСУМОК УРОКУ 1.3


  1. Щоб вставити текст який був видалений наберіть p . Це вставляє 
     видалений текст ПІСЛЯ курсора (якщо був видалений рядок, вставка
     продовжиться з рядка під курсором).

  2. Щоб замінити символ під курсором наберіть r і необхідний символ.

  3. Оператор заміни дозволяє робити заміну тексту від курсору, до потрібного
     переміщення. Наприклад щоб замінити все від курсора до кінця слова
     вводять  ce . Щоб замінити закінчення рядка тиснуть c$  .

  4. Формат заміни:

         c   [число]   переміщення

Почнемо наступний урок.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                  Урок 1.4.1: ПОЗИЦІЯ КУРСОРА І СТАТУС ФАЙЛУ

  ** Введіть CTRL-G щоб побачити вашу позицію в файлі, і його статус.
     Введіть G  щоб переміститись на потрібний рядок файлу. **

  ПРИМІТКА: Прочитайте увесь урок перед виконанням будь-яких кроків!!

  1. Затисніть кнопку Ctrl і натисніть g . Це називається CTRL-G.
     Внизу з'явиться повідомлення з назвою файлу, і позицією в файлі. 
     Запам'ятайте номер рядка для кроку 3.

ПРИМІТКА: Ви бачите позицію курсора в нижньому правому кутку екрану.
          Це трапляється коли включена опція 'ruler' (читайте :help 'ruler'  )

  2. Натисніть G щоб переміститись до кінця файлу.
     Наберіть  gg щоб переміститись до початку файлу.

  3. Наберіть номер рядка де ви були а потім G. Це перенесе вас до потрібного
     рядка.

  4. Якщо ви запам'ятали три попередні кроки, то виконуйте.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Урок 1.4.2: КОМАНДА ПОШУКУ


         ** Введіть  / (слеш) і фразу, щоб шукати її в тексті. **

  1. В звичайному режимі введіть символ  /  . Зауважте що він і курсор 
     з'являються в низу екрану, як і з командою  :   .

  2. Тепер введіть 'очепятка' <ENTER>. Це буде словом яке ви шукатимете.

  3. Щоб здійснити пошук цієї фрази ще раз введіть  n .
     Щоб шукати в протилежному напрямку введіть  N .

  4. Щоб шукати фразу в зворотньому напрямку використайте  ?  замість  / .

  5. Щоб переміститись назад до того місця звідки прийшли натисніть CTRL-O.
     Повторіть щоб повернутись ще далі. (Це як кнопка назад в браузері)
     CTRL-I переміщує вперед.

--->  "очепятка" не є способом написати опечатка; очепятка це опечатка.
Примітка: Коли пошук досягає кінця файлу він продовжує з початку, хіба що 
          опція 'wrapscan' була виключена.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Урок 1.4.3: ПОШУК ПАРНИХ ДУЖОК


              ** Введіть  %  щоб знайти парну ),], чи } . **

  1. Помістіть курсор над будь-якою (, [, чи { в рядку нижче позначеному --->.

  2. Тепер введіть символ  %  .

  3. Курсор переміститься до відповідної дужки.

  4. Введіть  %  щоб перемістити курсор до іншої парної дужки.

  5. Спробуйте з іншими дужками, і подивіться що вийде.

---> Це ( тестовий рядок ( з такими [ такими ] і такими { дужками } в ньому. ))


Примітка: Це корисно при відлагоджуванні програми з неправильними дужками. 
          І взагалі в кожному тексті дужки мають стояти правильно!



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Урок 1.4.4: КОМАНДА ЗАМІНИ


        ** Наберіть :s/старе/нове/g щоб замінити 'старе' на 'нове'. **

  1. Перемістіть курсор до лінії нижче позначеної  --->.

  2. Введіть :s/(біп)/блять [ENTER] . Зауважте що ця команда змінює тільки перше
     входження (біп) в рядку.

  3. Потім наберіть  :s/(біп)/блять/g .  Додавання  g  вказує що заміни 
     робляться у всьому рядку глобально.

---> люди не лю(біп), коли в слові "лю(біп)" "(біп)" заміняють на "(бiп)".

  4. Щоб замінити кожне входження послідовності символів між двома рядками 
     наберіть  :#,#s/старе/нове/g    де #,# діапазон рядків в яких робиться 
                                     заміна.
     Введіть   :%s/старе/нове/g      щоб змінити кожне входження у цілому файлі.
     Введіть   :%s/старе/нове/gc        щоб замінити, кожне входження у файлі з
                                     підтвердженням кожної заміни.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               ПІДСУМОК УРОКУ 1.4 


  1. CTRL-G  виводить вашу позицію в файлі і назву файлу.
             G  переміщує в кінець файлу.
     число   G  переміщує до рядка з вказаним номером.
            gg  переміщує до першого рядка.

  2. Ввід  /    і послідовності символів шукає послідовність ПІСЛЯ курсора.
     Ввід  ?       і послідовності символів шукає послідовність ПЕРЕД курсором.
 
     Після пошуку введіть  n  щоб знайти наступне входження в тому ж напрямку
     або  N  щоб шукати в протилежному напрямку.
     CTRL-O відносить вас до старішої позиції, CTRL-I до новішої позиції.

  3. Ввід  %       коли курсор знаходиться над дужкою (,),[,],{, чи } переносить
     курсор до протилежної дужки.

  4. Щоб замінити перше входження старого слова на нове    :s/старе/нове
     Щоб замінити всі старі слова рядка на нові            :s/старе/нове/g
     Щоб замінити фрази між двома рядками                  :#,#s/старе/нове/g
     Щоб замінити всі входження в файлі                    :%s/старе/нове/g
     Щоб щоразу підтверджувати заміну додайте 'c'          :%s/старе/нове/gc

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                Урок 1.5.1: ЯК ВИКОНАТИ ЗОВНІШНЮ КОМАНДУ


   ** Введіть  :!  і зовнішню команду, щоб виконати ту команду. **

  1. Введіть відому команду  :  щоб встановити курсор в низу екрану.
     Це дозволяє вводити команди командного рядка.

  2. Тепер введіть  !  (символ знаку оклику) .  Це дозволить вам виконати
     будь-яку зовнішню команду.

  3. Як приклад введіть :!ls [ENTER].  Це покаже список файлів каталогу, так
     так ніби ви знаходитесь в оболонці терміналу. Або використайте :!dir
     якщо ви раптом знаходитесь в Windows.

Примітка:  Можна запускати будь-яку зовнішню команду таким способом, навіть з
           аргументами.

Примітка:  Всі команди що починаються з  :  мають закінчуватись натисканням
           [ENTER]. Більше на цьому не наголошуватиметься.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Урок 1.5.2: ЩЕ ПРО ЗАПИС ФАЙЛІВ


     ** Щоб зберегти змінений текст, введіть :w НАЗВА_ФАЙЛУ **

  1. Введіть  :!dir  чи  :!ls  щоб переглянути вміст вашої директорії.
     Вам вже казали що після цього тиснуть [ENTER].

  2. Виберіть ім'я файлу яке ще не використовується, наприклад TEST.
     (Взагалі то це не обов'язково, якщо вміст обраного файлу не 
     є цінним)

  3. Тепер введіть:      :w TEST   (де TEST це назва яку ви обрали.)

  4. Це зберігає увесь файл (підручник Vim ) під ім'ям TEST.
     Щоб перевірити знову наберіть  :!ls   щоб побачити зміни в каталозі.

Примітка: Якщо ви вийдете з Vim і запустите його знову командою vim TEST, 
          файл що ви відкриєте буде точною копією цього, коли ви його зберегли.

  5. Зараз видаліть файл ввівши (Unix):         :!rm TEST
                             чи (MS-DOS):       :!del TEST


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Урок 1.5.3: ВИБІР ТЕКСТУ ДЛЯ ЗАПИСУ


  ** Щоб зберегти частину файлу, наберіть  v  переміщення  :w НАЗВА_ФАЙЛУ **

  1. Перемістіть курсор до цього рядка.

  2. Натисніть  v  і перемістіть курсор на п'ять пунктів нижче. Зауважте, що 
     текст виділяється.

  3. Натисніть символ  :  .  Внизу екрану з'являються символи  :'<,'> .

  4. Введіть w TEST  , де TEST назва файлу що ще не використовується.  
     Переконайтесь що ви бачите  :'<,'>w TEST  перед тим як натиснути [ENTER].

  5. Vim запише вибрані рядки в файл TEST.  Використайте :!dir  чи  !ls
     щоб побачити це. Поки що не видаляйте його! Ми використаємо TEST в
     наступному уроці.

Зауваження: Натискання v починає режим візуального виділення. Ви можете
            переміщувати курсор щоб змінити розмір вибраної частини.
            Потім можна використати оператор щоб зробити щось з текстом.
            Наприклад  d  видалить текст.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Урок 1.5.4: ОТРИМАННЯ І ЗЛИТТЯ ФАЙЛІВ


       ** Щоб вставити вміст файлу введіть  :r НАЗВА_ФАЙЛУ  **

  1. Помістіть курсор десь над цим рядком.

Зауваження:  Після виконання кроку 2 ви побачите текст з уроку 1.5.3. Тоді
             перемістіться вниз, щоб побачити вміст цього уроку знову.

  2. Тоді отримайте вміст вашого файлу TEST використавши команду  :r TEST  ,
     де TEST назва файлу що ви використали.
     Файл що ви отримуєте поміщується під рядком курсора.

  3. Щоб перевірити що файл вставлено, прокрутіть текст назад, і переконаєтесь
     що тепер є дві копії урок 1.5.3, the original and the file version.

Примітка:  Також ви можете вставляти вивід зовнішньої програми. Наприклад
           :r !ls  читає вивід команди  ls  і вставляє його під курсором.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               Підсумок 1.5 уроку


  1.  :!команда  виконує зовнішню команду.

  2.  :w НАЗВА_ФАЙЛУ записує поточний файл на диск під назвою НАЗВА_ФАЙЛУ.

  3.  v  переміщення  :w НАЗВА_ФАЙЛУ зберігає візуально виділену частину тексту       
       в файл НАЗВА_ФАЙЛУ.

  4.  :r НАЗВА_ФАЙЛУ  отримує з диску файл НАЗВА_ФАЙЛУ і вставляє його під 
      курсором.

  5.  :r !ls  читає вивід команди ls і вставляє її під поточною позицією курсора



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Урок 1.6.1: КОМАНДА ВСТАВКИ

        
           ** Введіть o щоб вставити новий рядок під курсором. **

  1. Перемістіть курсор на рядок нижче, позначений --->.

  2. Натисніть  o  щоб вставити новий рядок ПІД курсором та перейти в 
     режим вставки.

  3. Тепер введіть текст і натисніть [ESC] щоб вийти з режиму вставки.

---> Після натискання  o  курсор ставиться на наступний рядок в режимі вставки.

  4. Щоб вставити рядок НАД ABOVE курсором пишуть  O  в верхньому регістрі, 
     замість o. Спробуйте на рядку нижче.

---> Щоб вставити рядок над цим введіть  O  .




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Урок 1.6.2: КОМАНДА ДОПИСУВАННЯ


             ** Натисніть  a  щоб вставити текст після курсору. **

  1. Перемістіть курсор до початку рядка внизу позначеного --->.
  
  2. Тисніть  e  поки курсор не буде в кінці ря .

  3. Натисніть  a  (маленьке) щоб додати текст ПІСЛЯ курсору.

  4. Допишіть слова як рядок внизу.  Натисніть [ESC] щоб вийти з режиму
     вставки.

  5. Використайте  e  щоб переміститись до наступного неповного слова та
     to move to the next incomplete word and repeat steps 3 and 4.
  
---> Цей ря дозволить вам попрактикува в дописува тексту до рядка.
---> Цей рядок дозволить вам попрактикуватись в дописуванні тексту до рядка.

Примітка:  a, i  і  A  переходять в один і той же режим вставки, єдиною різницею
           є тільки те, де вставляються символи.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Урок 1.6.3: ІНШИЙ СПОСІБ ЗАМІНИ


        ** Введіть велику  R  щоб замінити більш ніж один символ. **

  1. Перемістіть курсор до першого рядка внизу позначеного --->.  
     Перемістіть курсор до першого  xxx .

  2. Тепер натисніть  R  і введіть номер під ним з другого рядка, так що він
     замінює xxx .

  3. Натисніть [ESC] щоб покинути режим заміни.  Зауважте, що решта рядка 
     залишається незмінною.

  4. Повторіть кроки від 1 до 3 щоб замінити всі xxx на числа з другого рядка.

---> Додавання 123 до xxx дає xxx.
---> Додавання 123 до 456 дає 579.

Зауваження:  Режим заміни подібний до режиму вставки, тільки кожен введений 
             символ видаляє символ який стояв на його місці.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Урок 1.6.4: КОПІЮВАННЯ І ВСТАВКА


  ** Використайте оператор  y  щоб копіювати текст і  p  щоб його вставити **

  1. Перейдіть до рядка нижче позначеного ---> і покладіть курсор після "а)".
  
  2. Перейдіть в візуальний режим за допомогою клавіші  v  і перемістіть курсор
     якраз перед словом "один".
  
  3. Введіть  y  щоб копіювати (yank) виділений текст.

  4. Перемістіть курсор до кінця наступного рядка:  j$

  5. Натисніть  p  щоб вставити (put) текст.  Тоді введіть :  два [ESC] .

  6. так само додайте третій рядочок.

--->  а) це рядок номер один
      б)

  Зауваження: також можна використовувати  y  як оператор;  
              yw  копіює одне слово.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                            Урок 1.6.5: ВСТАНОВЛЕННЯ ОПЦІЙ


     ** Встановити опцію так що пошук чи заміна буде ігнорувати регістр **

  1. Знайдіть слово 'ігнорувати' ввівши :   /ігнорувати
     Повторіть кілька разів натискаючи  n .

  2. Встановіть опцію ігнорування регістру 'ic' (Ignore case) ввівши:  :set ic

  3. Тепер пошукайте 'ігнорувати' знову ввівши  n
     Зауважте що Ігнорувати та ІГНОРУВАТИ тепер також знаходяться.

  4. Ввімкніть 'hlsearch' (підсвітку пошуку) і 'incsearch' (інтерактивність)
     командою :set hls is  .

  5. Тепер пошукайте щось знову і зауважте зміни:  /ігнорувати [ENTER]

  6. Щоб вимкнути ігнорування регістру напишіть:  :set noic

Примітка:  Щоб вимкнути підсвітку співпадінь введіть:   :nohlsearch 
Примітка:  Якщо ви хочете не брати до уваги регістр тільки під час одного пошуку           
           використайте ключ  \c. Наприклад:  /ігнорувати\c  [ENTER]
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               ПІДСУМОК УРОКУ 1.6

  1. Введіть  о  щоб додати рядок ПІД курсором і почати режим вставки.
     Введіть  O  щоб додати рядок НАД курсором.

  2. Введіть  a  щоб вставити текст ПІСЛЯ курсора.
     Введіть  A  щоб додати текст до рядка.

  3. Переміщення  e  переміщує нас до кінця слова.

  4. Оператор  y  копіює текст,  p  вставляє його.

  5. Введення  R  переносить нас в режим заміни до натискання [ESC].

  6. Набір ":set xxx" встановлює опцію "xxx".  Деякі опції:
        'ic' 'ignorecase'       ігнорувати верхній/нижній регістр при пошуку
        'is' 'incsearch'        показувати співпадіння пошуку під час введення
                                фрази
        'hls' 'hlsearch'        пісвічувати всі співпадіння
     Можна одночасно використовувати і коротку і довгу форму запису опції.

  7. Використайте префікс "no" щоб вимкнути опцію:   :set noic

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Урок 1.7.1: ОТРИМАННЯ ДОПОМОГИ


              ** Використання вбудованої довідкової системи **

  Vim має всеосяжну систему довідки. Щоб ознайомитись з нею спробуйте один з
  таких способів:
        - натисніть кнопку [HELP] (якщо така є)
        - натисніть [F1]
        - наберіть   :help

  Прочитайте текст в вікні допомоги, щоб вияснити як вона працює.
  Натисніть CTRL-W  двічі щоб змінити вікно
  Наберіть   :q  щоб закрити вікно довідки.

  Можна знайти довідку майже на будь-яку тему додаючи аргумент після команди
  ":help" . Спробуйте одну з наступних (не забувайте натискати  [ENTER]):

        :help w
        :help c_CTRL-D
        :help insert-index
        :help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Урок 1.7.2: СТВОРЕННЯ СКРИПТА АВТОЗАПУСКУ


                      ** Ввімкнення додаткових функцій Vim **

  Vim має набагато більше функцій ніж Vi, але більшість з них відключені за 
  замовчуванням.  Щоб почати використання додаткових функцій потрібно створити
  файл "vimrc".

  1. Почніть редагування файлу "vimrc" .  Це залежить від вашої системи:
        :e ~/.vimrc          для Unix
        :e ~/_vimrc          для MS-Windows

  2. Тепер прочитайте приклад вмісту "vimrc" :
        :r $VIMRUNTIME/vimrc_example.vim

  3. Збережіть файл:
        :w

  Наступного разу коли ви запустите Vim він буде використовувати підсвітку 
  синтаксису. Можна додати всі ваші улюблені налаштування в цей файл. Для більш
  детальної інформації введіть  :help vimrc-intro

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             Урок 1.7.3: АВТОДОПОВНЕННЯ


              ** Автодоповнення за допомогою CTRL-D і [TAB] **

  1. Переконайтесь що в Vim не включена зворотня сумісність:  :set nocp

  2. Подивіться що за файли існують в каталозі:  :!ls   чи  :!dir

  3. Введіть початок команди:  :e

  4. Натисніть  CTRL-D  і Vim покаже список команд що починаються з "e".

  5. Натисніть [TAB]  і  Vim доповнить команду до ":edit".

  6. Тепер додайте пропуск і початок існуючого імені файлу:  :edit FIL

  7. Натисніть [TAB].  Vim доповнить ім'я (якщо воно унікальне).

Зауваження:  Доповнення працює для багатьох команд. Просто натискайте CTRL-D і
             [TAB]. Це особливо корисно для команди  :help .

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               ПІДСУМОК УРОКУ 1.7


  1. Введіть  :help  або натисніть [F1] щоб відкрити вікно довідки.

  2. Введіть  :help тема  щоб знайти довідку про  тему .

  3. Введіть  CTRL-W CTRL-W  щоб змінити вікно.

  4. Наберіть  :q  щоб закрити вікно

  5. Створіть скрипт vimrc щоб змінювати ваші налаштування при запуску.

  6. При наборі команди що починається з двокрапки  :  натисніть CTRL-D 
     щоб побачити можливі доповнення. Натисніть [TAB] щоб побачити одне з
     доповнень.







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  Це завершує уроки Vim .  Вони були націлені щоб дати вам короткий вступ в 
  редактор Vim, достатній для того щоб використовувати редактор комфортно.
  Ці уроки зовсім далеко від повних, бо Vim має набагато більше команд. Можна 
  прочитати інструкцію користувача : ":help user-manual".

  Для подальшого читання і вивчення рекомендується така книжка:
        Vim - Vi Improved - by Steve Oualline
        Publisher: New Riders
  Особливо корисна для початківців.
  Там багато прикладів і ілюстрацій.
  Дивіться https://iccf-holland.org/click5.html

  Ці уроки були написані Майклом С. Пірсом та Робертом Уаром.

  Модифіковано для Vim Бремом Муленаром.
  

  Переклад на українську Буник Т.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
