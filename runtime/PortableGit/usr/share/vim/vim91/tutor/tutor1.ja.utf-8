===============================================================================
=    V I M 教 本 (チュートリアル) へ よ う こ そ        -    Version 1.7      =
===============================================================================

     Vim は、このチュートリアルで説明するには多すぎる程のコマンドを備えた非常
     に強力なエディターです。このチュートリアルは、あなたが Vim を万能エディ
     ターとして使いこなせるようになるのに十分なコマンドについて説明をするよう
     になっています。

     チュートリアルを完了するのに必要な時間は、覚えたコマンドを試すのにどれだ
     け時間を使うのかにもよりますが、およそ30分です。

     ATTENTION:
     以下の練習用コマンドにはこの文章を変更するものもあります。練習を始める前
     にコピーを作成しましょう("vimtutor"したならば、既にコピーされています)。

     このチュートリアルが、使うことで覚えられる仕組みになっていることを、心し
     ておかなければなりません。正しく学習するにはコマンドを実際に試さなければ
     ならないのです。文章を読んだだけならば、きっと忘れてしまいます!

     さぁ、Capsロックキーが押されていないことを確認した後、画面にレッスン1.1.1
     が全部表示されるところまで、j キーを押してカーソルを移動しましょう。
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.1.1:  カーソルの移動


       ** カーソルを移動するには、示される様に h,j,k,l を押します。 **
	     ^
	     k		    ヒント:  h キーは左方向に移動します。
       < h	 l >		     l キーは右方向に移動します。
	     j			     j キーは下矢印キーのようなキーです。
	     v
  1. 移動に慣れるまで、スクリーンでカーソル移動させましょう。

  2. 下へのキー(j)を押しつづけると、連続して移動できます。
     これで次のレッスンに移動する方法がわかりましたね。

  3. 下へのキーを使って、レッスン1.1.2 に移動しましょう。

NOTE: 何をタイプしているか判らなくなったら、<ESC>を押してノーマルモードにし
      ます。それから入力しようとしていたコマンドを再入力しましょう。

NOTE: カーソルキーでも移動できます。しかし hjkl に一度慣れてしまえば、はるか
      に速く移動することができるでしょう。いやマジで!

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.1.2: VIM の起動と終了


  !! NOTE: 以下のあらゆるステップを行う前に、このレッスンを読みましょう!!

  1. <ESC>キーを押しましょう。(確実にノーマルモードにするため)

  2. 次のようにタイプ:		:q! <ENTER>
     これにより編集した内容を保存せずにエディタが終了します。

  3. このチュートリアルを始める為のコマンドを実行すると、ここに戻れます。
     そのコマンドは:		vimtutor <ENTER>

  4. これまでのステップを覚え自信がついたならば、ステップ 1 から 3 までを実
     際に試して、Vim を1度終了してから再び起動しましょう。

NOTE:  :q! <ENTER> は全ての変更を破棄します。レッスンにて変更をファイルに保
       存する方法についても勉強していきましょう。

  5. 1.1.3までカーソルを移動させましょう。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       レッスン 1.1.3: テキスト編集 - 削除


    ** ノーマルモードにてカーソルの下の文字を削除するには x を押します。 **

  1. 以下の ---> と示された行にカーソルを移動しましょう。

  2. 間違いを修正するために、削除する最初の文字までカーソルを移動します。

  3. 不必要な文字を x を押して削除しましょう。

  4. 文が正しくなるまで ステップ 2 から 4 を繰り返しましょう。

---> その ううさぎ は つつきき を こええてて とびはねたた

  5. 行が正しくなったら、レッスン 1.1.4 へ進みましょう。

NOTE: 全てのレッスンを通じて、覚えようとするのではなく実際にやってみましょう。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       レッスン 1.1.4: テキスト編集 - 挿入


	 ** ノーマルモードにてテキストを挿入するには i を押します。 **

  1. 以下の ---> と示された最初の行にカーソルを移動しましょう。

  2. 1行目を2行目と同じ様にするために、テキストを挿入しなければならない位置
     の次の文字にカーソルを移動します。

  3. i キーを押してから、追加が必要な文字をタイプしましょう。

  4. 間違いを修正したら <ESC> を押してコマンドモードに戻り、正しい文になる様
     にステップ 2 から 4 を繰り返しましょう。

---> この には 足りない テキスト ある。
---> この 行 には 幾つか 足りない テキスト が ある。

  5. 挿入の方法がわかったらレッスン 1.1.5 へ進みましょう。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     レッスン 1.1.5: テキスト編集 - 追加


		 ** テキストを追加するには A を押しましょう。 **

  1. 以下の ---> と示された最初の行にカーソルを移動しましょう。
     カーソルがその行のどの文字上にあってもかまいません。

  2. 追加が必要な場所で A をタイプしましょう。

  3. テキストを追加し終えたら、 <ESC> を押してノーマルモードに戻りましょう。

  4. 2行目の ---> と示された場所へ移動し、ステップ 2 から 3 を繰り返して文法
     を修正しましょう。

---> ここには間違ったテキストがあり
     ここには間違ったテキストがあります。
---> ここにも間違ったテキス
     ここにも間違ったテキストがあります。

  5. テキストの追加が軽快になってきたらレッスン 1.1.6 へ進みましょう。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     レッスン 1.1.6: ファイルの編集

	   ** ファイルを保存して終了するには :wq とタイプします。 **

  !! NOTE: 以下のステップを実行する前に、まず全体を読んでください!!

  1. 別の端末がある場合はそこで以下の内容を行ってください。そうでなければ、
     レッスン 1.1.2 でやったように :q! をタイプして、このチュートリアルを終了
     します。

  2. シェルプロンプトでこのコマンドをタイプします:  vim file.txt <ENTER>
     'vim' が Vim エディタを起動するコマンド、'file.txt' は編集したいファイル
     の名前です。変更できるファイルの名前を使いましょう。

  3. 前のレッスンで学んだように、テキストを挿入、削除します。

  4. 変更をファイルに保存します:  :wq <ENTER>

  5. ステップ 1 で vimtutor を終了した場合は vimtutor を再度起動し、以下の
     要約へ進みましょう。

  6. 以上のステップを読んで理解した上でこれを実行しましょう。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				レッスン 1.1 要約


  1. カーソルは矢印キーもしくは hjkl キーで移動します。
	 h (左)		j (下)         k (上)	    l (右)

  2. Vim を起動するにはプロンプトから vim ファイル名 <ENTER> とタイプします。

  3. Vim を終了するには	   <ESC> :q!	 <ENTER>  とタイプします(変更を破棄)。
               もしくは	   <ESC> :wq	 <ENTER>  とタイプします(変更を保存)。

  4. カーソルの下の文字を削除するには、ノーマルモードで x とタイプします。

  5. カーソルの位置に文字を挿入するには、ノーマルモードで i とタイプします。
	 i     テキストのタイプ	<ESC>         カーソル位置に追加
	 A     テキストの追加   <ESC>         行末に追加

NOTE: <ESC> キーを押すとノーマルモードに移行します。その際、間違ったり入力途
      中のコマンドを取り消すことができます。

さて、続けてレッスン 1.2 を始めましょう。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   レッスン 1.2.1: 削除コマンド


	    ** 単語の末尾までを削除するには dw とタイプしましょう。 **

  1. 確実にノーマルモードにするため <ESC> を押しましょう。

  2. 以下の ---> と示された行にカーソルを移動しましょう。

  3. 消したい単語の先頭にカーソルを移動しましょう。

  4. 単語を削除するために dw とタイプしましょう。

  NOTE: d をタイプすると、その文字がスクリーンの最下行に現われます。Vim は
	あなたが w をタイプするのを待っています。もし d 以外の文字が表示された
	時は何か間違っています。 <ESC> を押してやり直しましょう。

---> この 文 紙 には いくつかの たのしい 必要のない 単語 が 含まれて います。

  5. 3 から 4 までを文が正しくなるまで繰り返し、レッスン 1.2.2 へ進みましょう。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       レッスン 1.2.2: その他の削除コマンド


	   ** 行の末尾までを削除するには d$ とタイプしましょう。 **

  1. 確実にノーマルモードにするため <ESC> を押しましょう。

  2. 以下の ---> と示された行にカーソルを移動しましょう。

  3. 正しい文の末尾へカーソルを移動しましょう(最初の 。 の後です)。

  4. 行末まで削除するのに d$ とタイプしましょう。

---> 誰かがこの行の最後を2度タイプしました。 2度タイプしました。


  5. どういうことか理解するために、レッスン 1.2.3 へ進みましょう。





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     レッスン 1.2.3: オペレータとモーション


  テキストに変更を加える多くのコマンドはオペレータとモーションからなります。
  削除コマンド d のオペレータは次の様になっています:

  	d   モーション

  それぞれ:
    d          - 削除コマンド。
    モーション - 何に対して働きかけるか(以下に挙げます)。

  モーション一覧の一部:
    w - カーソル位置から空白を含む単語の末尾まで。
    e - カーソル位置から空白を含まない単語の末尾まで。
    $ - カーソル位置から行末まで。

  つまり de とタイプすると、カーソル位置から単語の終わりまでを削除します。

NOTE: 冒険したい人は、ノーマルモードにてオペレータなしにモーションを押して
      みましょう。カーソルが目的語一覧で示される位置に移動するはずです。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 レッスン 1.2.4: モーションにカウントを使用する


      ** 何回も行いたい繰り返しのモーションの前に数値をタイプします。 **

  1. 以下の ---> と示された行の先頭にカーソルを移動します。

  2. 2w をタイプして単語2つ分先に移動します。

  3. 3e をタイプして3つ目の単語の終端に移動します。

  4. 0 (ゼロ)をタイプして行頭に移動します。

  5. ステップ 2 と 3 を違う数値を使って繰り返します。

---> This is just a line with words you can move around in.

  6. レッスン 1.2.5 に進みましょう。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	   レッスン 1.2.5: より多くを削除するためにカウントを使用する


  ** オペレータとカウントをタイプすると、その操作が複数回繰り返されます。 **

  既述の削除のオペレータとモーションの組み合わせにカウントを追加することで、
  より多くの削除が行えます:
	 d   数値   モーション

  1. ---> と示された行の最初の大文字の単語にカーソルを移動しましょう。

  2. 大文字の単語2つを d2w とタイプして削除します。

  3. 連続した大文字の単語を、異なるカウントを指定した1つのコマンドで削除し、
     ステップ 1 と 2 を繰り返します。

---> このABC DE行のFGHI JK LMN OP単語はQ RS TUV綺麗になった。





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.2.6: 行の操作


		 ** 行全体を削除するには dd とタイプします。 **

  行全体を削除する頻度が多いので、Viのデザイナーは行の削除を d の2回タイプと
  いう簡単なものに決めました。

  1. 以下の句の2行目にカーソルを移動します。
  2. dd とタイプして行を削除します。
  3. さらに4行目に移動します。
  4. 2dd とタイプして2行を削除します。

--->  1)  バラは赤い、
--->  2)  つまらないものは楽しい、
--->  3)  スミレは青い、
--->  4)  私は車をもっている、
--->  5)  時計が時刻を告げる、
--->  6)  砂糖は甘い
--->  7)  オマエモナー

2回タイプで1行に対して作用させる方法は以下で述べるオペレータでも動作します。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.2.7: やり直しコマンド


 ** 最後のコマンドを取り消すには u を押します。U は行全体の取り消しです。 **

  1. 以下の ---> と示された行にカーソルを移動し、最初の間違いにカーソル
     を移動しましょう。
  2. x をタイプして最初のいらない文字を削除しましょう。
  3. さぁ、u をタイプして最後に実行したコマンドを取り消しましょう。
  4. 今度は、x を使用して行内の誤りを全て修正しましょう。
  5. 大文字の U をタイプして、行を元の状態に戻しましょう。
  6. u をタイプして直前の U コマンドを取り消しましょう。
  7. ではコマンドを再実行するのに CTRL-R (CTRL を押したまま R を打つ)を数回
     タイプしてみましょう(取り消しの取り消し)。

---> このの行のの間違いを修正々し、後でそれらの修正をを取り消しまますす。

  8. これはとても便利なコマンドです。さぁレッスン 1.2 要約へ進みましょう。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				レッスン 1.2 要約


  1. カーソル位置から次の単語までを削除するには dw とタイプします。
  2. カーソル位置から単語の末尾までを削除するには de とタイプします。
  3. カーソル位置から行の末尾までを削除するには d$ とタイプします。
  4. 行全体を削除するには dd とタイプします。

  5. モーションを繰り返すには数値を付与します:   2w
  6. 変更に用いるコマンドの形式は
               オペレータ   [数値]   モーション
     それぞれ:
       オペレータ - 削除 d の類で何をするか。
       [数値]     - そのコマンドを何回繰り返すか。
       モーション - w (単語)や e (単語末尾)、$ (行末)などの類で、テキストの
		    何に対して働きかけるか。

  7. 行の先頭に移動するにはゼロを使用します:  0

  8. 前回の動作を取り消す:	u   (小文字 u)
     行全体の変更を取り消す:	U   (大文字 U)
     取り消しの取り消し:	CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   レッスン 1.3.1: 貼り付けコマンド


    ** 最後に削除された行をカーソルの後に貼り付けるには p をタイプします。 **

  1. ---> と示された以下の最初の行にカーソルを移動しましょう。

  2. dd  とタイプして行を削除し、Vim のレジスタに格納しましょう。

  3. 削除した行が本来あるべき位置の上の行である c) 行まで、カーソルを移動させ
     ましょう。

  4. ノーマルモードで p をタイプして格納した行をカーソルの下に戻します。

  5. 順番が正しくなる様にステップ 2 から 4 を繰り返しましょう。

---> d) 貴方も学ぶことができる?
---> b) スミレは青い、
---> c) 知恵とは学ぶもの、
---> a) バラは赤い、



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.3.2: 置き換えコマンド


       ** カーソルの下の文字を x に置き換えるには rx をタイプします。 **

  1. 以下の ---> と示された最初の行にカーソルを移動しましょう。

  2. 最初の間違いの先頭にカーソルを移動しましょう。

  3. r とタイプし、間違っている文字を置き換える、正しい文字をタイプしましょう。

  4. 最初の行が正しくなるまでステップ 2 から 3 を繰り返しましょう。

--->  この合を人力した時ね、その人は幾つか問違ったキーを押しもした!
--->  この行を入力した時に、その人は幾つか間違ったキーを押しました!

  5. さぁ、レッスン 1.3.3 へ進みましょう。

NOTE: 実際に試しましょう。決して覚えるだけにはしないこと。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			レッスン 1.3.3: 変更コマンド


	 ** 単語の末尾までを変更するには ce とタイプします。 **

  1. 以下の ---> と示された最初の行にカーソルを移動しましょう。

  2. lubw の u の位置にカーソルを移動しましょう。

  3. ce とタイプし、正しい単語をタイプしましょう(この場合 'ine' とタイプ)。

  4. <ESC> をタイプしてから次の間違い(変更すべき文字の先頭)に移動します。

  5. 最初の行が次の行の様になるまでステップ 3 と 4 を繰り返します。

---> This lubw has a few wptfd that mrrf changing usf the change operator.
---> This line has a few words that need changing using the change operator.

ce は単語を削除した後、挿入モードに入ることに注意しましょう。
cc は同じことを行全体に対して行います。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     レッスン 1.3.4: c を使用したその他の変更


     ** 変更オペレータは、削除と同じ様にモーションを使用します。 **

  1. 変更オペレータは、削除と同じような動作をします。その形式は

         c    [数値]   モーション

  2. モーションも同じで、w は単語、 $ は行末などといったものです。

  3. 以下の ---> と示された最初の行にカーソルを移動しましょう。

  4. 最初の間違いへカーソルを移動しましょう。

  5. c$ とタイプして行の残りを２行目の様にし、<ESC> を押しましょう。

---> The end of this line needs some help to make it like the second.
---> The end of this line needs to be corrected using the  c$  command.

NOTE:  タイプ中の間違いはバックスペースキーを使って直すこともできます。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				レッスン 1.3 要約


  1. 既に削除されたテキストを再配置するには、p をタイプします。これは削除さ
     れたテキストをカーソルの後に挿入します(行単位で削除されたのならば、カー
     ソルのある次の行に挿入されます)。

  2. カーソルの下の文字を置き換えるには、r をタイプした後、それを置き換える
     文字をタイプします。

  3. 変更コマンドではカーソル位置から特定のモーションで指定される終端までを変
     更することが可能です。例えば ce ならばカーソル位置から単語の終わりまで、
     c$ ならば行の終わりまでを変更します。

  4. 変更コマンドの形式は

         c    [数値]   モーション

さぁ、次のレッスンへ進みましょう。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     レッスン 1.4.1: 位置とファイルの情報

  ** ファイル内での位置とファイルの状態を表示するには CTRL-G をタイプします。
     ファイル内のある行に移動するには G をタイプします。 **

     NOTE: ステップを実行する前に、このレッスン全てに目を通しましょう!!

  1. CTRL を押したまま g を押しましょう。この操作を CTRL-G と呼んでいます。
     ページの一番下にファイル名と行番号が表示されるはずです。 ステップ 3のため
     に行番号を覚えておきましょう。

NOTE:  画面の右下隅にカーソルの位置が表示されているかもしれません。これは
       'ruler' オプション(:help 'ruler' を参照)を設定することで表示されます。

  2. ファイルの最下行に移動するために G をタイプしましょう。
     ファイルの先頭に移動するには gg とタイプしましょう。

  3. 先ほどの行の番号をタイプし G をタイプしましょう。最初に CTRL-G を押した行
     に戻って来るはずです。

  4. 自信が持てたらステップ 1 から 3 を実行しましょう。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   レッスン 1.4.2: 検索コマンド


	 ** 語句を検索するには / と、前方検索する語句をタイプします。 **

  1. ノーマルモードで / という文字をタイプします。画面一番下に : コマンドと
     同じ様に / が現れることに気づくでしょう。

  2. では、'errroor' <ENTER> とタイプしましょう。これが検索したい単語です。

  3. 同じ語句をもう一度検索するときは 単に n をタイプします。
     逆方向に語句を検索するときは N をタイプします。

  4. 逆方向に語句を検索する場合は、/ の代わりに ? コマンドを使用します。

  5. 元の場所に戻るには CTRL-O (Ctrl を押し続けながら文字 o をタイプ)をタイプし
     ます。さらに戻るにはこれを繰り返します。CTRL-I は前方向です。

--->  "errroor" は error とスペルが違います;  errroor はいわゆる error です。
NOTE: 検索がファイルの終わりに達すると、オプション 'wrapscan' が設定されている
      場合は、ファイルの先頭から検索を続行します。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      レッスン 1.4.3: 対応する括弧を検索


	    ** 対応する ),] や } を検索するには % をタイプします。 **

  1. 下の ---> で示された行で (,[ か { のどれかにカーソルを移動しましょう。

  2. そこで % とタイプしましょう。

  3. カーソルは対応する括弧に移動するはずです。

  4. 最初の括弧に移動するには % とタイプしましょう。

  5. 他の (,),[,],{ や } でカーソルを移動し、% が何をしているか確認しましょう。

---> This ( is a test line with ('s, ['s ] and {'s } in it. ))


NOTE: この機能は括弧が一致していないプログラムをデバッグするのにとても役立ち
      ます。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       レッスン 1.4.4: 間違いを変更する方法


	** 'old' を 'new' に置換するには :s/old/new/g とタイプします。 **

  1. 以下の ---> と示された行にカーソルを移動しましょう。

  2. :s/thee/the <ENTER> とタイプしましょう。このコマンドはその行で最初に見つ
     かったものにだけ行われることに気をつけましょう。

  3. では :s/thee/the/g とタイプしましょう。追加した g フラグは行全体を置換す
     ることを意味します。この変更はその行で見つかった全ての箇所に対して行われ
     ます。

---> thee best time to see thee flowers is in thee spring.

  4. 複数行から見つかる文字の全ての箇所を変更するには
     :#,#s/old/new/g    #,# には置き換える範囲の開始と終了の行番号を指定する。
     :%s/old/new/g      ファイル全体で見つかるものに対して変更する。
     :%s/old/new/gc     ファイル全体で見つかるものに対して、1つ1つ確認をとりな
                        がら変更する。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				レッスン 1.4 要約


  1. CTRL-G はファイルでの位置とファイルの詳細を表示します。
          G はファイルの最下行に移動します。
     数値 G はその行に移動します。
         gg は先頭行に移動します。

  2. / の後に語句をタイプすると前方に語句を検索します。
     ? の後に語句をタイプすると後方に語句を検索します。
     検索の後の n は同じ方向の次の検索を、N は逆方向の検索をします。
     CTRL-O は場所を前に移し、CTRL-I は場所を次に移動します。

  3. (,),[,],{, もしくは } 上にカーソルがある状態で % をタイプすると対になる文
     字へ移動します。

  4. 現在行の最初の old を new に置換する。	:s/old/new
     現在行の全ての old を new に置換する。	:s/old/new/g
     2つの # 行の間で語句を置換する。		:#,#s/old/new/g
     ファイルの中の全ての検索語句を置換する。	:%s/old/new/g
     'c' を加えると置換の度に確認を求める。	:%s/old/new/gc

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    レッスン 1.5.1: 外部コマンドを実行する方法


	     ** :! の後に実行する外部コマンドをタイプします。 **

  1. 画面の最下部にカーソルが移動するよう、慣れ親しんだ : をタイプしましょう。
     これでコマンドライン命令がタイプできる様になります。

  2. ここで ! という文字(感嘆符)をタイプしましょう。
     これで外部シェルコマンドが実行できる様になります。

  3. 例として ! に続けて ls とタイプし <ENTER> を押しましょう。
     シェルプロンプトのようにディレクトリの一覧が表示されるはずです。
     もしくは ls が動かないならば :!dir を使用しましょう。

NOTE:  この方法によってあらゆるコマンドが実行することができます。もちろん引数
       も与えられます。

NOTE:  全ての : コマンドは <ENTER> を押して終了しなければなりません。
       以降ではこのことに言及しません。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    レッスン 1.5.2: その他のファイルへ書き込み


	** ファイルへ変更を保存するには :w ファイル名 とタイプします。 **

  1. ディレクトリの一覧を得るために :!dir もしくは :!ls とタイプしましょう。
     このあと <ENTER> を押すのは既にご存知ですね。

  2. TEST のように、そのディレクトリに無いファイル名を一つ選びます。

  3. では :w TEST とタイプしましょう (TEST は、選んだファイル名です)。

  4. これによりファイル全体が TEST という名前で保存されます。
     もう一度 :!dir もしくは :!ls とタイプしてディレクトリを確認してみましょう。

NOTE: ここで Vim を終了し、ファイル名 TEST と共に起動すると、保存した時の
     チュートリアルの複製ができ上がるはずです。

  5. さらに、次のようにタイプしてファイルを消しましょう(Windows):  :!del TEST
                                               もしくは(Unix):     :!rm TEST


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.5.3: 選択した書き込み


** ファイルの一部を保存するには、v モーションと :w FILENAME をタイプします。 **

  1. この行にカーソルを移動します。

  2. v を押し、以下の第5項目にカーソルを移動します。テキストが強調表示されるの
     に注目して下さい。

  3. 文字 : を押すと、画面の最下部に :'<,'> が現れます。

  4. w TEST (TEST は存在しないファイル名)をタイプします。
     <ENTER> を押す前に :'<,'>w TEST となっていることを確認して下さい。

  5. Vim は TEST というファイルに選択された行を書き込むでしょう。
     :!dir もしくは :!ls でそれを確認します。
     それは削除しないでおいて下さい。次のレッスンで使用します。

NOTE:  v を押すと、Visual 選択が始まります。カーソルを動かすことで、選択範囲を
       大きくも小さくもできます。さらに、その選択範囲に対してオペレータを適用
       できます。例えば d はテキストを削除します。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       レッスン 1.5.4: ファイルの取込と合併


	** ファイルの中身を挿入するには :r ファイル名 とタイプします。 **

  1. カーソルをこの行のすぐ上に合わせます。

NOTE:  ステップ 2 の実行後、レッスン 1.5.3 のテキストが現れます。下に下がってこ
       のレッスンに移動しましょう。

  2. では TEST というファイルを :r TEST というコマンドで読み込みましょう。
     ここでいう TEST は使うファイルの名前のことです。
     読み込まれたファイルは、カーソル行の下にあります。

  3. 取り込んだファイルを確認してみましょう。カーソルを戻すと、レッスン1.5.3 の
     オリジナルとファイルによるものの2つがあることがわかります。

NOTE: 外部コマンドの出力を読み込むこともできます。例えば、
      :r !ls は ls コマンドの出力をカーソル以下に読み込みます。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       レッスン 1.5 要約


  1.  :!command  によって 外部コマンドを実行する。

     よく使う例:
	 (Windows)	  (Unix)
	  :!dir		   :!ls		   -  ディレクトリ内の一覧を見る。
	  :!del FILENAME   :!rm FILENAME   -  ファイルを削除する。

  2.  :w ファイル名 によってファイル名というファイルがディスクに書き込まれる。

  3.  v モーションで :w FILENAME とすると、ビジュアル選択行がファイルに保存さ
      れる。

  4.  :r ファイル名 によりファイル名というファイルがディスクより取り込まれ、
      カーソル位置の下に挿入される。

  5.  :r !dir は dir コマンドの出力をカーソル位置以下に読み込む。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 レッスン 1.6.1: オープンコマンド


    ** o をタイプすると、カーソルの下の行が開き、挿入モードに入ります。 **

  1. 以下の ---> と示された最初の行にカーソルを移動しましょう。

  2. o (小文字) をタイプして、カーソルの下の行を開き、挿入モードに入ります。

  3. いくつか文字をタイプしてから、挿入モードを終了する為に <ESC> を
     タイプします。

---> o  をタイプするとカーソルは開いた行へ移動し挿入モードに入ります。

  4. カーソルの上の行に挿入するには、小文字の o ではなく、単純に大文字の O
     をタイプします。次の行で試してみましょう。

---> この行の上へ挿入するには、この行へカーソルを置いて O をタイプします。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			レッスン 1.6.2: 追加コマンド


      ** カーソルの次の位置からテキストを追加するには a とタイプします。 **

  1. カーソルを ---> で示された最初の行へ移動しましょう。

  2. e を押して li の終端部までカーソルを移動します。

  3. カーソルの後ろにテキストを追加するために a (小文字) をタイプします。

  4. その下の行のような単語に完成させます。挿入モードを抜ける為に <ESC> を押
     します。

  5. e を使って次の不完全な単語へ移動し、ステップ 3 と 4 を繰り返します。

---> This li will allow you to pract appendi text to a line.
---> This line will allow you to practice appending text to a line.

NOTE: a, i と A は同じ挿入モードへ移りますが、文字が挿入される位置だけが異なり
      ます。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			レッスン 1.6.3: その他の置換方法


	  ** 1文字以上を置き換えるには大文字の R とタイプしましょう。 **

  1. 以下の ---> と示された行にカーソルを移動します。最初の xxx の先頭に移動し
     ます。

  2. R を押して、2行目の数値をタイプすることで、xxx が置換されます。

  3. 置換モードを抜けるには <ESC> を押します。行の残りが変更されていないままに
     なることに注意してください。

  4. 残った xxx をステップを繰り返して置換しましょう。

---> Adding 123 to xxx gives you xxx.
---> Adding 123 to 456 gives you 579.

NOTE: 置換モードは挿入モードに似ていますが、全てのタイプされた文字は既存の文字
      を削除します。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   レッスン 1.6.4: テキストのコピーとペースト


     ** テキストのコピーにはオペレータ y を、ペーストには p を使います。 **

  1. ---> と示された行へ移動し、カーソルを "a)" の後に置いておきます。

  2. v でビジュアルモードを開始し、"first" の手前までカーソルを移動します。

  3. y をタイプして強調表示されたテキストを yank (コピー)します。

  4. 次の行の行末までカーソルを移動します:  j$

  5. p を押して貼り付け(put)てから、次をタイプします:  a second <ESC>

  6. ビジュアルモードで " item." を選択し、y でヤンク、次の行の行末まで j$ で
     移動し、 p でテキストをそこに put します。

--->  a) this is the first item.
      b)

  NOTE: y をオペレータとして使うこともできます。yw は単語を1つ yank します。
        yy は行を1つ yank し、p でその行を put します。
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       レッスン 1.6.5: オプションの設定


  ** 検索や置換の際に大文字/小文字を無視するには、オプションを設定します。 **

  1. 次の様に入力して 'ignore' を検索しましょう:  /ignore <ENTER>
     n を押して何度か検索を繰り返します。

  2. 次の様に入力して 'ic' (Ignore Case の略) オプションを設定します:  :set ic

  3. では n によってもう1度 'ignore' を検索します。
     n を押してさらに数回検索を繰り返しましょう。

  4. 'hlsearch' と 'incsearch' オプションを設定しましょう:  :set hls is

  5. 検索コマンドを再入力して、何が起こるか見てみましょう:  /ignore <ENTER>

  6. 大文字小文字の区別を無効にするには次の様に入力します:  :set noic

NOTE: マッチの強調表示をやめるには次の様に入力します:  :nohlsearch
NOTE: 1つの検索コマンドだけ大文字小文字の区別をやめたいならば、語句内で \c
      を使用します:  /ignore\c <ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				レッスン 1.6 要約

  1. o をタイプするとカーソルの下の行を開けて、そこで挿入モードになる。
     O (大文字) をタイプするとカーソルの上の行で挿入モードになる。

  2. カーソル上の文字の次からテキストを追加するには a とタイプする。
     行末にテキストを挿入するには大文字 A をタイプする。

  3. e コマンドは単語の終端にカーソルを移動する。

  4. y オペレータはテキストを yank (コピー)し、p はそれを put (ペースト)する。

  5. 大文字の R をタイプすると置換モードに入り、<ESC> を押すと抜ける。

  6. ":set xxx" とタイプするとオプション "xxx" が設定される。
	'ic' 'ignorecase'	検索時に大文字小文字の区別しない
	'is' 'incsearch'	検索フレーズに部分マッチしている部分を表示する
	'hls' 'hlsearch'	マッチするすべてを強調表示する
     長い方、短い方、どちらのオプション名でも使用できます。

  7. オプションを無効にするには "no" を付与する:  :set noic

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    レッスン 1.7.1: オンラインヘルプコマンド


		     ** オンラインヘルプを使用しましょう **

  Vim には広範にわたるオンラインヘルプシステムがあります。
  ヘルプを開始するには、これら3つのどれか1つを試してみましょう:
	- ヘルプキー <HELP> を押す(もしあるならば)。
	- <F1> キーを押す(もしあるならば)。
	- :help <ENTER> とタイプする。

  ヘルプウィンドウのテキストを読むと、ヘルプの動作が理解できます。
  CTRL-W CTRL-W  とタイプすると ヘルプウィンドウへジャンプします。
    :q <ENTER>   とタイプすると ヘルプウィンドウが閉じられます。

  ":help" コマンドに引数を与えることにより、あらゆる題名のヘルプを見つけること
  ができます。これらを試してみましょう(<ENTER> をタイプし忘れないように):

	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      レッスン 1.7.2: 起動スクリプトの作成

			 ** Vim の特徴を発揮する **

  Vim には Vi よりも多くの特徴を踏まえていますが、そのほとんどは初期状態にて
  使用不可となっています。より多くの特徴を使いはじめるには "vimrc" ファイル
  を作成します。

  1. "vimrc" ファイルの編集を開始します。これはシステムに依存します。
	:e ~/.vimrc		UNIX 向け
	:e ~/_vimrc		Windows 向け

  2. ここでサンプルの "vimrc" を読み込みます。
	:r $VIMRUNTIME/vimrc_example.vim

  3. 以下のようにファイルへ書き込みます。
	:w

  次回 Vim を起動すると、色づけ構文が使えるようになるでしょう。
  この "vimrc" ファイルへ、お好みの設定を追加することができます。
  より多くの情報を得るには :help vimrc-intro とタイプします。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      レッスン 1.7.3: 補完


	       ** CTRL-D と <TAB> でコマンドラインを補完する **

  1. 互換モードでないことを確認します:  :set nocp

  2. 現在のディレクトリに在るファイルを :!ls か :!dir で確認します。

  3. コマンドの先頭をタイプします:  :e

  4. CTRL-D を押すと Vim は "e" から始まるコマンドの一覧を表示します。

  5. d<TAB> とタイプすると Vim は ":edit" というコマンド名を補完します。

  6. さらに空白と、既存のファイル名の始まりを加えます:  :edit FIL

  7. <TAB> を押すと Vim は名前を補完します。(もし一つしか無かった場合)

NOTE: 補完は多くのコマンドで動作します。そして CTRL-D と <TAB> 押してみてくだ
      さい。特に :help の際に役立ちます。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       レッスン 1.7 要約


  1. ヘルプウィンドウを開くには :help とするか <F1> もしくは <HELP> を押す。

  2. コマンド(cmd)のヘルプを検索するには :help cmd とタイプする。

  3. 別のウィンドウへジャンプするには CTRL-W CTRL-W とタイプする。

  4. ヘルプウィンドウを閉じるには :q とタイプする。

  5. お好みの設定を保つには vimrc 起動スクリプトを作成する。

  6. : command で可能な補完を見るには CTRL-D をタイプする。
     補完を使用するには <TAB> を押す。







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  これにて Vim のチュートリアルを終わります。エディタを簡単に、しかも充分に
  使うことができるようにと、Vim の持つ概念の要点のみを伝えようとしました。
  Vim にはさらに多くのコマンドがあり、ここで全てを説明することはできません。
  以降はユーザーマニュアルを参照ください: ":help user-manual"

  これ以後の学習のために、次の本を推薦します。
	Vim - Vi Improved - by Steve Oualline
	出版社: New Riders
  最初の本は完全に Vim のために書かれました。とりわけ初心者にはお奨めです。
  多くの例題や図版が掲載されています。
  次のURLを参照して下さい https://iccf-holland.org/click5.html

  次は Vim よりも Vi について書かれた古い本ですが推薦します:
	Learning the Vi Editor - by Linda Lamb
	出版社: O'Reilly & Associates Inc.
  Vi でやりたいと思うことほぼ全てを知ることができる良書です。
  第6版では、Vim についての情報も含まれています。

  このチュートリアルは Colorado State University の Charles Smith のアイデア
  を基に、Colorado School of Mines の Michael C. Pierce と Robert K. Ware の
  両名によって書かれました。 E-mail: <EMAIL>.

  Modified for Vim by Bram Moolenaar.

  日本語訳  松本 泰弘    <<EMAIL>>
            vim-jpチーム <https://github.com/vim-jp/lang-ja>
  監修      村岡 太郎    <<EMAIL>>

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 vi:set ts=8 sts=4 sw=4 tw=78:
