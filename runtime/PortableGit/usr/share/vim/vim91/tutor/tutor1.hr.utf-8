===============================================================================
=    D o b r o d o š l i   u   VIM   p r i r u č n i k  -    Verzija 1.7      =
===============================================================================

     Vim je vrlo moćan editor koji ima mnogo naredbi, previše da bi ih
     se svih ovdje spomenulo.  Namjena priručnika je objasniti dovoljno
     naredbi kako bi početnici znatno lakše koristili ovaj svestran editor.

     Približno vrijeme potrebno za uspješan završetak priručnika je oko
     30 minuta a ovisi o tome koliko će te vremena odvojiti za vježbanje.

     UPOZORENJE:
     Naredbe u ovom priručniku će promijeniti ovaj tekst.
     Napravite kopiju ove datoteke kako bi ste na istoj vježbali
     (ako ste pokrenuli "vimtutor" ovo je već kopija).

     Vrlo je važno primijetiti da je ovaj priručnik namijenjen za vježbanje.
     Preciznije, morate izvršiti naredbe u Vim-u kako bi ste iste naučili
     pravilno koristiti. Ako samo čitate tekst, zaboraviti će te naredbe!

     Ako je CapsLock uključen ISKLJUČITE ga.  Pritiskajte tipku  j  kako
     bi pomakli kursor sve dok Lekcija 1.1.1 ne ispuni ekran.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcija 1.1.1:  POMICANJE KURSORA


 ** Za pomicanje kursora, pritisnite h,j,k,l tipke kako je prikazano **
	     ^
	     k		    Savjet:  h tipka je lijevo i pomiče kursor lijevo.
       < h	 l >		   l tipka je desno i pomiče kursor desno.
	     j                     j izgleda kao strelica usmjerena dolje.
	     v
  1. Pomičite kursor po ekranu dok se ne naviknete na korištenje.

  2. Držite tipku (j) pritisnutom.
     Sada znate kako doći do sljedeće lekcije.

  3. Koristeći tipku j prijeđite na sljedeću lekciju 1.1.2.

NAPOMENA:  Ako niste sigurni što ste zapravo pritisnuli uvijek koristite
           tipku <ESC> kako bi prešli u Normal mod i onda pokušajte ponovno.

NAPOMENA:  Kursorske tipke rade isto.  Korištenje hjkl tipaka je znatno
           brže, nakon što se jednom naviknete na njihovo korištenje.  Stvarno!

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   Lekcija 1.1.2: IZLAZ IZ VIM-a


  !! UPOZORENJE: Prije izvođenja bilo kojeg koraka,
                 pročitajte cijelu lekciju!!

  1. Pritisnite <ESC> tipku (Vim je sada u Normal modu).

  2. Otipkajte:	:q! <ENTER>.
     Izlaz iz editora, GUBE se sve napravljene promjene.

  3. Kada se pojavi ljuska, utipkajte naredbu koja je pokrenula
     ovaj priručnik:		vimtutor <ENTER>

  4. Ako ste upamtili ove korake, izvršite ih redom od 1 do 3
     kako bi ponovno pokrenuli editor.

NAPOMENA:  :q! <ENTER>  poništava sve promjene koje ste napravili.
           U sljedećim lekcijama naučit će te kako promjene sačuvati.

  5. Pomaknite kursor na Lekciju 1.1.3.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcija 1.1.3: PROMJENA TEKSTA - BRISANJE


	      ** Pritisnite  x  za brisanje znaka pod kursorom. **

  1. Pomaknite kursor na liniju označenu s --->.

  2. Kako bi ste ispravili pogreške, pomičite kursor dok se
     ne bude nalazio na slovu kojeg trebate izbrisati.

  3. Pritisnite tipku x kako bi uklonili neželjeno slovo.

  4. Ponovite korake od 2 do 4 dok ne ispravite sve pogreške.

---> KKKravaa jee presskočila mmjeseccc.

  5. Nakon što ispravite liniju, prijeđite na lekciju 1.1.4.

NAPOMENA:  Koristeći ovaj priručnik ne pokušavajte pamtiti
           već učite primjenom.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Lekcija 1.1.4: PROMJENA TEKSTA - UBACIVANJE


	  ** Pritisnite  i  za ubacivanje teksta ispred kursora. **

  1. Pomaknite kursor na prvu sljedeću liniju označenu s --->.

  2. Kako bi napravili prvu liniju istovjetnoj drugoj, pomaknite
     kursor na prvi znak POSLIJE kojeg će te utipkati potreban tekst.

  3. Pritisnite  i  te utipkajte potrebne nadopune.

  4. Nakon što ispravite pogrešku pritisnite <ESC> kako bi vratili Vim
     u Normal mod. Ponovite korake od 2 do 4 kako bi ispravili sve pogreške.

---> Nedje no teka od v lin.
---> Nedostaje nešto teksta od ove linije.

  5. Prijeđite na sljedeću lekciju.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcija 1.1.5: PROMJENA TEKSTA - DODAVANJE


	           ** Pritisnite  A  za dodavanje teksta. **

  1. Pomaknite kursor na prvu sljedeću liniju označenu s --->.
     Nije važno na kojem se slovu nalazi kursor na toj liniji.

  2. Pritisnite  A  i napravite potrebne promjene.

  3. Nakon što ste dodali tekst, pritisnite <ESC>
     za povratak u Normal mod.

  4. Pomaknite kursor na drugu liniju označenu s --->
     i ponovite korake 2 i 3 dok ne popravite tekst.

---> Ima nešto teksta koji nedostaje n
     Ima nešto teksta koji nedostaje na ovoj liniji.
---> Ima nešto teksta koji ne
     Ima nešto teksta koji nedostaje baš ovdje.

  5. Prijeđite na lekciju 1.1.6.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lekcija 1.1.6: PROMJENA DATOTEKE


     ** Koristite  :wq  za spremanje teksta i napuštanje Vim-a. **

  !! UPOZORENJE: Prije izvršavanja bilo kojeg koraka, pročitajte lekciju!!

  1. Izađite iz programa kao sto ste napravili u lekciji 1.1.2:  :q!

  2. Iz ljuske utipkajte sljedeću naredbu:  vim tutor <ENTER>
     'vim' je naredba pokretanja Vim editora, 'tutor' je ime datoteke koju
     želite uređivati.  Koristite datoteku koju imate ovlasti mijenjati.

  3. Ubacite i izbrišite tekst kao što ste to napravili u lekcijama prije.

  4. Sačuvajte promjenjeni tekst i izađite iz Vim-a:  :wq  <ENTER>

  5. Ponovno pokrenite vimtutor i nastavite čitati sažetak koji sljedi.

  6. Nakon sto pročitate gornje korake i u potpunosti ih razumijete:
     izvršite ih.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.1 SAŽETAK


  1. Kursor se pomiče strelicama ili pomoću hjkl tipaka.
	h (lijevo)	j (dolje)	k (gore)	l (desno)

  2. Pokretanje Vim-a iz ljuske:  vim IME_DATOTEKE <ENTER>

  3. Izlaz:	<ESC>	:q! <ENTER> 	sve promjene su izgubljene.
       ILI:	<ESC>	:wq <ENTER> 	promjene su sačuvane.

  4. Brisanje znaka na kojem se nalazi kursor:  x

  5. Ubacivanja ili dodavanje teksta:
	 i   utipkajte tekst <ESC>	unos ispred kursora
	 A   utipkajte tekst <ESC>	dodavanje na kraju linije

NAPOMENA:  Tipkanjem tipke <ESC> prebacuje Vim u Normal mod i
           prekida neželjenu ili djelomično završenu naredbu.

Nastavite čitati Lekciju 2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lekcija 1.2.1: NAREDBE BRISANJA


		 ** Tipkajte  dw  za brisanje riječi. **

  1. Pritisnite  <ESC>  kako bi bili sigurni da je Vim u Normal modu.

  2. Pomaknite kursor na liniju označenu s --->.

  3. Pomaknite kursor na početak riječi koju treba izbrisati.

  4. Otipkajte  dw  kako bi uklonili riječ.

NAPOMENA:  Vim će prikazati slovo  d  na zadnjoj liniji kad ga otipkate.
           Vim čeka da otipkate  w .  Ako je prikazano neko drugo slovo,
           krivo ste otipkali; pritisnite <ESC> i pokušajte ponovno.

---> Neke riječi smiješno ne pripadaju na papir ovoj rečenici.

  5. Ponovite korake 3 i 4 dok ne ispravite rečenicu;
     prijeđite na Lekciju 1.2.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcija 1.2.2: JOŠ BRISANJA


       ** Otipkajte  d$ za brisanje znakova do kraja linije. **

  1. Pritisnite  <ESC>  kako bi bili
     sigurni da je Vim u Normal modu.

  2. Pomaknite kursor na liniju označenu s  --->.

  3. Pomaknite kursor do kraja ispravne rečenice
     (POSLJE prve . ).

  4. Otipkajte  d$
     kako bi izbrisali sve znakove do kraja linije.

---> Netko je utipkao kraj ove linije dvaput. kraj ove linije dvaput.

  5. Prijeđite na Lekciju 1.2.3 za bolje objašnjenje.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	      Lekcija 1.2.3: UKRATKO O OPERATORIMA I POKRETIMA


  Mnogo naredbi koje mijenjaju tekst se sastoje od operatora i pokreta.
  Oblik naredbe brisanja sa  d  operatorom je sljedeći:

  	d   pokret

  Pri čemu je:
    d      - operator brisanja.
    pokret - ono na čemu će se operacija izvršavati (navedeno u nastavku).

  Kratka lista pokreta:
    w - sve do početka sljedeće riječi, NE UKLJUČUJUĆI prvo slovo.
    e - sve do kraja trenutačne riječi, UKLJUČUJUĆI zadnje slovo.
    $ - sve do kraje linije, UKLJUČUJUĆI zadnje slovo.

  Tipkanjem  de  će se brisati od kursora do kraja riječi.

NAPOMENA:  Pritiskajući samo pokrete dok ste u Normal modu bez operatora će
           pomicati kursor kao što je navedeno.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcija 1.2.4: KORIŠTENJE BROJANJA ZA POKRETE


  ** Tipkanjem nekog broja prije pokreta, pokret se izvršava toliko puta. **

  1. Pomaknite kursor na liniju označenu s --->.

  2. Otipkajte  2w  da pomaknete kursor dvije riječi naprijed.

  3. Otipkajte  3e  da pomaknete kursor na kraj treće riječi naprijed.

  4. Otipkajte  0  (nulu) da pomaknete kursor na početak linije.

  5. Ponovite korake 2 i 3 s nekim drugim brojevima.

---> Rečenica sa riječima po kojoj možete pomicati kursor.

  6. Prijeđite na Lekciju 1.2.5.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
             Lekcija 1.2.5: KORIŠTENJE BROJANJA ZA VEĆE BRISANJE


	  ** Tipkanje broja N s operatorom ponavlja ga N-puta. **

  U kombinaciji operatora brisanja i pokreta spomenutih iznad
  ubacujete broj prije pokreta kako bi izbrisali više znakova:

	 d   broj   pokret

  1. Pomaknite kursor na prvo slovo u riječi sa VELIKIM SLOVIMA
     označenu s --->.

  2. Otipkajte  2dw  da izbrišete dvije riječi sa VELIKIM SLOVIMA

  3. Ponovite korake 1 i 2 sa različitim brojevima da izbrišete
     uzastopne riječi sa VELIKIM SLOVIMA sa samo jednom naredbom.

---> ova ABCČĆ DĐE linija FGHI JK LMN OP riječi je RSŠ TUVZŽ popravljena.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lekcija 1.2.6: OPERIRANJE NAD LINIJAMA


	       ** Otipkajte  dd   za brisanje cijele linije. **

  Zbog učestalosti brisanja cijelih linija, dizajneri Vi-a su odlučili da
  je lakše brisati linije tipkanjem  d  dvaput.

  1. Pomaknite kursor na drugu liniju u donjoj kitici.
  2. Otipkajte  dd  kako bi izbrisali liniju.
  3. Pomaknite kursor na četvrtu liniju.
  4. Otipkajte   2dd   kako bi izbrisali dvije linije.

--->  1)  Ruže su crvene,
--->  2)  Plaža je super,
--->  3)  Ljubice su plave,
--->  4)  Imam auto,
--->  5)  Satovi ukazuju vrijeme,
--->  6)  Šećer je sladak
--->  7)  Kao i ti.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcija 1.2.7: NAREDBA PONIŠTENJA


  ** Pritisnite  u  za poništenje zadnje naredbe,  U  za cijelu liniju. **

  1. Pomaknite kursor na liniju označenu s ---> i postavite kursor na prvu
     pogrešku.
  2. Otipkajte  x  kako bi izbrisali prvi neželjeni znak.
  3. Otipkajte  u  kako bi poništili zadnju izvršenu naredbu.
  4. Ovaj put ispravite sve pogreške na liniji koristeći  x  naredbu.
  5. Sada utipkajte veliko  U  kako bi poništili sve promjene
     na liniji, vraćajući je u prijašnje stanje.
  6. Sada utipkajte  u  nekoliko puta kako bi poništili  U
     i prijašnje naredbe.
  7. Sada utipkajte CTRL-R (držeći  CTRL  tipku pritisnutom dok
     ne pritisnete  R) nekoliko puta kako bi vratili promjene
     (poništili poništenja).

---> Poopravite pogreške nna ovvoj liniji ii pooništiteee ih.

  8. Vrlo korisne naredbe.  Prijeđite na sažetak Lekcije 1.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.2 SAŽETAK


  1. Brisanje od kursora do sljedeće riječi:    dw
  2. Brisanje od kursora do kraja linije:    d$
  3. Brisanje cijele linije:    dd

  4. Za ponavljanje pokreta prethodite mu broj:   2w
  5. Oblik naredbe mijenjanja:
               operator   [broj]   pokret
     gdje je:
       operator - što napraviti, npr.   d  za brisanje
       [broj]   - neobavezan broj ponavljanja pokreta
       pokret   - kretanje po tekstu po kojem se operira,
                  kao što je: w (riječ), $ (kraj linije), itd.

  6. Postavljanje kursora na početak linije:  0

  7. Za poništenje prethodnih promjena, pritisnite:	  u  (malo u)
     Za poništenje svih promjena na liniji, pritisnite:   U  (veliko U)
     Za vraćanja promjena, utipkajte:	CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lekcija 1.3.1: NAREDBA POSTAVI


	** p  za unos prethodno izbrisanog teksta iza kursora. **

  1. Pomaknite kursor na prvu sljedeću liniju označenu s --->.

  2. Otipkajte  dd  kako bi izbrisali liniju i spremili je u Vim registar.

  3. Pomaknite kursor na liniju c), IZNAD linije koju trebate unijeti.

  4. Otipkajte  p  kako bi postavili liniju ispod kursora.

  5. Ponovite korake 2 do 4 kako bi postavili sve linije u pravilnom
     rasporedu.

---> d) Možeš li i ti naučiti?
---> b) Ljubice su plave,
---> c) Inteligencija je naučena,
---> a) Ruže su crvene,


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lekcija 1.3.2: NAREDBA PROMJENE


      ** Otipkajte  rx  za zamjenu slova ispod kursora sa slovom  x . **

  1. Pomaknite kursor na prvu sljedeću liniju označenu s --->.

  2. Pomaknite kursor tako da se nalazi na prvoj pogrešci.

  3. Otipkajte  r  i nakon toga ispravan znak na tom mjestu.

  4. Ponovite korake 2 i 3 sve dok prva
     linije ne bude istovjetna drugoj.

--->  Kede ju ovu limija tupjana, natko je protuskao kruve tupke!
--->  Kada je ova linija tipkana, netko je pritiskao krive tipke!

  5. Prijeđite na Lekciju 1.3.2.

NAPOMENA:  Prisjetite da trebate učiti vježbanjem, ne pamćenjem.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcija 1.3.3: OPERATOR MIJENJANJA


	  ** Za mijenjanje do kraja riječi, istipkajte  ce . **

  1. Pomaknite kursor na prvu sljedeću liniju označenu s --->.

  2. Postavite kursor na  a  u  lackmb.

  3. Otipkajte  ce  i ispravite riječ (u ovom slučaju otipkajte  inija ).

  4. Pritisnite <ESC> i pomaknite kursor na sljedeći znak
     kojeg je potrebno ispraviti.

  5. Ponovite korake 3 i 4 sve dok prva rečenica ne postane istovjetna
     drugoj.

---> Ova lackmb ima nekoliko rjlcah koje trfcb mijdmlfsz.
---> Ova linija ima nekoliko riječi koje treba mijenjati.

Primijetite da  ce  briše riječ i postavlja Vim u Insert mod.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	         Lekcija 1.3.4: JOŠ MIJENJANJA KORIŠTENJEM c


    ** Naredba mijenjanja se koristi sa istim pokretima kao i brisanje. **

  1. Operator mijenjanja se koristi na isti način kao i operator brisanja:

         c    [broj]   pokret

  2. Pokreti su isti, npr:   w (riječ) i  $ (kraj linije).

  3. Pomaknite kursor na prvu sljedeću liniju označenu s --->.

  4. Pomaknite kursor na prvu pogrešku.

  5. Otipkajte  c$  i utipkajte ostatak linije tako da bude istovjetna
     drugoj te pritisnite <ESC>.

---> Kraj ove linije treba pomoć tako da izgleda kao linija ispod.
---> Kraj ove linije treba ispraviti korištenjem c$ naredbe.

NAPOMENA:  Možete koristiti Backspace za ispravljanje grešaka.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.3 SAŽETAK


  1. Za postavljanje teksta koji je upravo izbrisan, pritisnite  p . Ovo
     postavlja tekst IZA kursora (ako je pak linija izbrisana tekst se
     postavlja na liniju ispod kursora).

  2. Za promjenu znaka na kojem se nalazi kursor, pritisnite  r  i nakon toga
     željeni znak.

  3. Operator mijenjanja dozvoljava promjenu teksta od kursora do pozicije do
     koje dovede pokret. tj. Otipkajte  ce  za mijenjanje od kursora do kraja
     riječi, c$  za mijenjanje od kursora do kraja linije.

  4. Oblik naredbe mijenjanja:

	 c   [broj]   pokret

Prijeđite na sljedeću lekciju.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	      Lekcija 1.4.1: POZICIJA KURSORA I STATUS DATOTEKE

  ** CTRL-G za prikaz pozicije kursora u datoteci i status datoteke.
     Pritisnite  G  za pomicanje kursora na neku liniju u datoteci. **

NAPOMENA:  Pročitajte cijelu lekciju prije izvršenja bilo kojeg koraka!!

  1. Držite Ctrl tipku pritisnutom i pritisnite  g .  Ukratko: CTRL-G.
     Vim će ispisati poruku na dnu ekrana sa imenom datoteke i pozicijom
     kursora u datoteci.  Zapamtite broj linije za 3. korak.

NAPOMENA:  Možete vidjeti poziciju kursora u donjem desnom kutu ako
           je postavka 'ruler' aktivirana (objašnjeno u 6. lekciji).

  2. Pritisnite  G  za pomicanje kursora na kraj datoteke.
     Otipkajte  gg  za pomicanje kursora na početak datoteke.

  3. Otipkajte broj linije na kojoj ste bili maloprije i zatim  G .  Kursor
     će se vratiti na liniju na kojoj se nalazio kada ste otipkali CTRL-G.

  4. Ako ste spremni, izvršite korake od 1 do 3.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lekcija 1.4.2: NAREDBE TRAŽENJA

       ** Otipkajte  /  i nakon toga izraz kojeg želite tražiti. **

  1. U Normal modu otipkajte  /  znak.  Primijetite da se znak
     pojavio zajedno sa kursorom na dnu ekrana kao kod  :  naredbe.

  2. Sada otipkajte 'grrrreška' <ENTER>.  To je riječ koju zapravo tražite.

  3. Za ponovno traženje istog izraza, otipkajte  n .
     Za traženje istog izraza ali u suprotnom smjeru, otipkajte  N .

  4. Za traženje izraza unatrag, koristite  ?  umjesto  / .

  5. Za povratak na prethodnu poziciju koristite  CTRL-O  (držite Ctrl
     pritisnutim dok ne pritisnete tipku o).  Ponavljajte sve dok se ne
     vratite na početak.  CTRL-I slično kao CTRL-O ali u suprotnom smjeru.

---> "pogrrrreška" je pogrešno; umjesto pogrrrreška treba stajati pogreška.

NAPOMENA:  Ako se traženjem dođe do kraja datoteke nastavit će se od njenog
           početka osim ako je postavka 'wrapscan' deaktivirana.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcija 1.4.3: TRAŽENJE PRIPADAJUĆE ZAGRADE


	** Otipkajte  %  za pronalazak pripadajuće ), ] ili } . **

  1. Postavite kursor na bilo koju od  ( ,  [  ili  {
     otvorenih zagrada u liniji označenoj s --->.

  2. Otipkajte znak  % .

  3. Kursor će se pomaknuti na pripadajuću zatvorenu zagradu.

  4. Otipkajte  %  kako bi pomakli kursor na drugu pripadajuću zagradu.

  5. Pomaknite kursor na neku od (,),[,],{ ili } i ponovite  %  naredbu.

---> Linija ( testiranja običnih ( [ uglatih ] i { vitičastih } zagrada.))


NAPOMENA:  Vrlo korisno u ispravljanju koda sa nepripadajućim zagradama!


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcija 1.4.4: NAREDBE ZAMIJENE


       ** Otipkajte  :s/staro/novo/g  da zamijenite 'staro' za 'novo'. **

  1. Pomaknite kursor na liniju označenu s --->.

  2. Otipkajte  :s/cvrćč/cvrč <ENTER> .  Primjetite da ova naredba zamjenjuje
     samo prvi "cvrćč" u liniji.

  3. Otipkajte  :s/cvrćč/cvrč/g .  Dodavanje  g  stavke znači da će se naredba
     izvršiti na cijeloj liniji, zamjenjivanjem svih "cvrćč" u liniji.

---> i cvrćči cvrćči cvrćčak na čvoru crne smrče.

  4. Za zamjenu svih izraza u rasponu dviju linija,
     otipkajte :#,#s/staro/novo/g   #,# su brojevi linije datoteke na kojima
                                    te između njih će se izvršiti zamjena.
     Otipkajte :%s/staro/novo/g     za zamjenu svih izraza u cijeloj datoteci.
     Otipkajte :%s/staro/novo/gc    za pronalazak svakog izraza u datoteci i
     			            potvrdu zamjene.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.4 SAŽETAK


  1. CTRL-G  prikazuje poziciju kursora u datoteci i status datoteke.
             G  postavlja kursor na zadnju liniju datoteke.
     broj    G  postavlja kursor na  broj  liniju.
            gg  postavlja kursor na prvu liniju.

  2. Tipkanje  /  sa izrazom traži UNAPRIJED taj izraz.
     Tipkanje  ?  sa izrazom traži UNATRAG taj izraz.
     Nakon naredbe traženja koristite  n  za pronalazak izraza u istom
     smjeru, i  N  za pronalazak istog izraza ali u suprotnom smjeru.
     CTRL-O vraća kursor na prethodnu poziciju, CTRL-I na sljedeću poziciju.

  3. Tipkanje  %  dok je kursor na zagradi pomiče ga na pripadajuću zagradu.

  4. Za zamjenu prvog izraza staro za izraz novo	   :s/staro/novo
     Za zamjenu svih izraza staro na cijeloj liniji	   :s/staro/novo/g
     Za zamjenu svih izraza staro u rasponu linija #,#     :#,#s/staro/novo/g
     Za zamjenu u cijeloj datoteci			   :%s/staro/novo/g
     Za potvrdu svake zamjene dodajte 'c'		   :%s/staro/novo/gc

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcija 1.5.1: IZVRŠAVANJE VANJSKIH NAREDBI


	** Otipkajte  :!  sa vanjskom naredbom koju želite izvršiti. **

  1. Otipkajte poznatu naredbu  :  kako bi kursor premjestili na dno
     ekrana.  Time omogućavate unos naredbe u naredbenoj liniji.

  2. Otipkajte znak  !  (uskličnik).  Tako omogućavate
     izvršavanje naredbe vanjske ljuske.

  3. Kao primjer otipkajte   ls   nakon ! te pritisnite <ENTER>.
     Ovo će prikazati sadržaj direktorija, kao da ste u ljusci.
     Koristite   :!dir   ako   :!ls   ne radi.

NAPOMENA:  Moguće je izvršavati bilo koju vanjsku naredbu na ovaj način,
           zajedno sa njenim argumentima.

NAPOMENA:  Sve  :  naredbe se izvršavaju nakon što pritisnete <ENTER>
	   U daljnjem tekstu to neće uvijek biti napomenuto.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Lekcija 1.5.2: VIŠE O SPREMANJU DATOTEKA

           ** Za spremanje promjena, otipkajte  :w IME_DATOTEKE. **

  1. Otipkajte   :!dir   ili   :!ls   za pregled direktorija.
     Već znate da morate pritisnuti <ENTER> na kraju tipkanja.

  2. Izaberite ime datoteke koja još ne postoji, npr. TEST.

  3. Otipkajte:	 :w TEST   (gdje je TEST ime koje ste prethodno odabrali.)

  4. Time će te spremiti cijelu datoteku (Vim Tutor) pod imenom TEST.
     Za provjeru, otipkajte ponovno   :!dir   ili   :!ls
     za pregled direktorija.

NAPOMENA:  Ako bi napustili Vim i ponovno ga pokrenuli sa vim TEST ,
           datoteka bi bila potpuna kopija ove datoteke u trenutku
           kada ste je spremili.

  5. Izbrišite datoteku tako da otipkate (MS-DOS):	:!del TEST
				       ili (Unix):	:!rm TEST

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcija 1.5.3: SPREMANJE OZNAČENOG TEKSTA


   ** Kako bi spremili dio datoteke, otipkajte  v  pokret  :w IME_DATOTEKE **

  1. Pomaknite kursor na ovu liniju.

  2. Pritisnite  v  i pomaknite kursor pet linija ispod ove.
     Primijetite promjenu, označeni tekst se razlikuje od običnog.

  3. Pritisnite  :  znak.  Na dnu ekrana pojavit će se  :'<,'> .

  4. Otipkajte  w TEST  , pritom je TEST ime datoteke koja još ne postoji.
     Provjerite da zaista piše  :'<,'>w TEST
     prije nego što pritisnite <ENTER>.

  5. Vim će spremiti označeni tekst u TEST.  Provjerite sa :!dir  ili  :!ls .
     Nemojte je još brisati!  Koristiti će te je u sljedećoj lekciji.

NAPOMENA:  Tipka  v  započinje Vizualno označavanje.  Možete pomicati kursor
           unaokolo kako bi mijenjali veličinu označenog teksta. Možete
           koristiti i operatore.  Npr,  d  će izbrisati označeni tekst.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Lekcija 1.5.4: UČITAVANJE DATOTEKA


       ** Za ubacivanje sadržaja datoteke, otipkajte  :r IME_DATOTEKE  **

  1. Postavite kursor iznad ove linije.

NAPOMENA:  Nakon što izvršite 2. korak vidjeti će te tekst iz Lekcije 1.5.3.
           Stoga pomaknite kursor DOLJE kako bi ponovno vidjeli ovu lekciju.

  2. Učitajte vašu TEST datoteku koristeći naredbu   :r TEST
     gdje je TEST ime datoteke koju ste koristili u prethodnoj lekciji.
     Sadržaj učitane datoteke je ubačen liniju ispod kursora.

  3. Kako bi provjerili da je datoteka učitana, vratite kursor unatrag i
     primijetite dvije kopije Lekcije 1.5.3, originalnu i onu iz datoteke.

NAPOMENA:  Možete također učitati ispis vanjske naredbe.  Npr,  :r !ls
           će učitati ispis  ls  naredbe i postaviti ispis liniju ispod
           kursora.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.5 SAŽETAK


  1.  :!naredba  izvršava vanjsku naredbu.

      Korisni primjeri:
	 (MS-DOS)	  (Unix)
	  :!dir		   :!ls		   -  pregled direktorija.
	  :!del DATOTEKA   :!rm DATOTEKA   -  briše datoteku DATOTEKA.

  2.  :w DATOTEKA  zapisuje trenutačnu datoteku na disk sa imenom DATOTEKA.

  3.  v  pokret  :w IME_DATOTEKE  sprema vizualno označene linije u
      datoteku IME_DATOTEKE.

  4.  :r IME_DATOTEKE  učitava datoteku IME_DATOTEKE sa diska i stavlja
      njen sadržaj liniju ispod kursora.

  5.  :r !dir  učitava ispis naredbe dir i postavlja sadržaj ispisa liniju
      ispod kursora.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Lekcija 1.6.1: NAREDBA OTVORI


	** Pritisnite  o  kako bi otvorili liniju ispod kursora
	   i prešli u Insert mod. **

  1. Pomaknite kursor na sljedeću liniju označenu s --->.

  2. Otipkajte malo  o  kako bi otvorili novu liniju ISPOD kursora
     i prešli u Insert mod.

  3. Otipkajte nešto teksta i nakon toga pritisnite <ESC>
     kako bi napustili Insert mod.

---> Nakon što pritisnete  o  kursor će preći u novu liniju u Insert mod.

  4. Za otvaranje linije IZNAD kursora, otipkajte umjesto malog o veliko O ,
     Pokušajte na donjoj liniji označenoj s --->.

---> Otvorite liniju iznad ove - otipkajte O dok je kursor na ovoj liniji.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lekcija 1.6.2: NAREDBA DODAJ


	     ** Otipkajte  a  za dodavanje teksta IZA kursora. **

  1. Pomaknite kursor na početak sljedeće linije označene s --->.
  
  2. Tipkajte  e  dok se kursor ne nalazi na kraju  li .

  3. Otipkajte  a  (malo) kako bi dodali tekst IZA kursora.

  4. Dopunite riječ kao što je na liniji ispod.
     Pritisnite <ESC> za izlaz iz Insert moda.

  5. Sa  e  prijeđite na sljedeću nepotpunu riječ i ponovite korake 3 i 4.
  
---> Ova li omogućava vje dodav teksta nekoj liniji.
---> Ova linija omogućava vježbanje dodavanja teksta nekoj liniji.

NAPOMENA:  Sa i, a, i  A  prelazite u isti Insert mod, jedina
           razlika je u poziciji od koje će se tekst ubacivati.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcija 1.6.3: DRUGI NAČIN MIJENJANJA


      ** Otipkajte veliko  R  kako bi zamijelili više od jednog znaka. **

  1. Pomaknite kursor na prvu sljedeću liniju označenu s --->.
     Pomaknite kursor na početak prvog  xxx .

  2. Pritisnite  R  i otipkajte broj koji je liniju ispod,
     tako da zamijeni xxx .

  3. Pritisnite <ESC> za izlaz iz Replace moda.
     Primijetite da je ostatak linije ostao nepromjenjen.

  5. Ponovite korake kako bi zamijenili preostali xxx.

---> Zbrajanje: 123 plus xxx je xxx.
---> Zbrajanje: 123 plus 456 je 579.

NAPOMENA:  Replace mod je kao Insert mod, ali sa bitnom razlikom,
           svaki otipkani znak briše već postojeći.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lekcija 1.6.4: KOPIRANJE I LIJEPLJENJE TEKSTA


    ** Koristite  y  operator za kopiranje a  p  za lijepljenje teksta. **

  1. Pomaknite kursor na liniju s --->  i postavite kursor nakon "a)".
  
  2. Pokrenite Visual mod sa  v  i pomaknite kursor sve do ispred "prva".
  
  3. Pritisnite  y  kako bi kopirali označeni tekst.

  4. Pomaknite kursor do kraja sljedeće linije:  j$

  5. Pritisnite  p  kako bi zalijepili tekst.  Onda utipkajte:  druga <ESC> .

  6. Koristite Visual mod kako bi označili " linija.", kopirajte:  y , kursor
     postavite na kraj sljedeće linije:  j$  i ondje zalijepite tekst:  p .

--->  a) ovo je prva linija.
      b)

NAPOMENA:  možete koristiti  y  kao operator;  yw   kopira jednu riječ.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcija 1.6.5: MIJENJANJE POSTAVKI


 ** Postavka: naredbe traženja i zamijene ne razlikuju VELIKA i mala slova **

  1. Potražite 'razlika' tipkanjem:   /razlika  <ENTER>
     Nekoliko puta ponovite pritiskanjem  n .

  2. Aktivirajte 'ic' (Ignore case) postavku:   :set ic

  3. Ponovno potražite 'razlika' tipkanjem  n
     Primijetite da su sada i RAZLIKA i Razlika pronađeni.

  4. Aktivirajte 'hlsearch' i 'incsearch' postavke:  :set hls is

  5. Otipkajte naredbu traženja i primijetite razlike:  /razlika <ENTER>

  6. Za deaktiviranje  ic  postavke koristite:  :set noic

NAPOMENA:  Za neoznačavanje pronađenih izraza otipkajte:   :nohlsearch
NAPOMENA:  Bez razlikovanja velikih i malih slova u samo jednoj naredbi
	   koristite  \c u izrazu:  /razlika\c  <ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.6 SAŽETAK

  1. Pritisnite  o  za otvaranje linije ISPOD kursora i prelazak u Insert mod.
     Pritisnite  O  za otvaranje linije IZNAD kursora.

  2. Pritisnite  a  za unos teksta IZA kursora.
     Pritisnite  A  za unos teksta na kraju linije.

  3. Naredba  e  pomiče kursor na kraj riječi.

  4. Operator  y  kopira tekst,  p  ga lijepi.

  5. Tipkanjem velikog  R  Vim prelazi u Replace mod dok ne pritisnete <ESC> .

  6. Tipkanjem ":set xxx" aktivira postavku "xxx".  Neke postavke su:
  	'ic' 'ignorecase'	ne razlikuje velika/mala slova pri traženju
	'is' 'incsearch'	traži nedovršene izraze
	'hls' 'hlsearch'	označi sve pronađene izraze
     Možete koristite dugo ili kratko ime postavke.

  7. Prethodite "no" imenu postavke za deaktiviranje iste:   :set noic

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Lekcija 1.7.1: DOBIVANJE POMOĆI


		   ** Koristite on-line sustav pomoći **

  Vim ima detaljan on-line sustav pomoći.
  Za početak, pokušajte jedno od sljedećeg:
	- pritisnite <HELP> tipku (ako je vaša tipkovnica ima)
	- pritisnite <F1> tipku (ako je vaša tipkovnica ima)
	- utipkajte   :help <ENTER>

  Pročitajte tekst u prozoru pomoći kako bi ste se znali služiti istom.
  Tipkanjem  CTRL-W CTRL-W   prelazite iz jednog prozora u drugi.
  Otipkajte    :q <ENTER>    kako bi zatvorili prozor pomoći.

  Pronaći će te pomoć o bilo kojoj temi, tako da dodate upit samoj
  ":help" naredbi.  Pokušajte (ne zaboravite pritisnuti <ENTER>):

	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lekcija 1.7.2: PRAVLJENJE SKRIPTE


		       ** Aktivirajte Vim mogućnosti **

  Vim ima mnogo više alata od Vi-ja, ali većina njih nije aktivirana.
  Kako bi mogli koristiti više mogućnosti napravite "vimrc" datoteku.

  1. Uredite "vimrc" datoteku.  Ovo ovisi o vašem sistemu:
	:e ~/.vimrc		za Unix
	:e ~/_vimrc		za MS-Windows

  2. Sada učitajte primjer sadržaja "vimrc" datoteke:
	:r $VIMRUNTIME/vimrc_example.vim

  3. Sačuvajte datoteku sa:
	:w

  Sljedećeg puta kada pokrenete Vim, bojanje sintakse teksta biti će
  aktivirano. Sve vaše postavke možete dodati u "vimrc" datoteku.
  Za više informacija otipkajte  :help vimrc-intro

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lekcija 1.7.3: AUTOMATSKO DOVRŠAVANJE


	** Dovršavanje iz naredbene linije pomoću CTRL-D i <TAB> **

  1. Provjerite da Vim nije u Vi modu:  :set nocp

  2. Pogledajte koje datoteke postoje u direktoriju:  :!ls   or  :!dir

  3. Otipkajte početak naredbe:  :e

  4. Tipkajte  CTRL-D  i prikazati će se lista naredbi koje započinju sa "e".

  5. Pritisnite <TAB>  i Vim će dopuniti unos u naredbu ":edit".

  6. Dodajte razmak i početak datoteke:  :edit FIL

  7. Pritisnite <TAB>.  Vim će nadopuniti ime datoteke (ako je jedinstveno).

NAPOMENA:  Moguće je dopuniti mnoge naredbe.  Koristite CTRL-D i <TAB>.
           Naročito je korisno za  :help  naredbe.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      Lekcija 1.7 SAŽETAK


  1. Otipkajte  :help  ili pritisnite <F1> ili <Help>  za pomoć.

  2. Otipkajte  :help naredba  kako bi dobili pomoć za  naredba .

  3. Otipkajte  CTRL-W CTRL-W  za prelazak u drugi prozor

  4. Otipkajte  :q  kako bi zatvorili prozor pomoći

  5. Napravite vimrc skriptu za podizanje kako bi u nju spremali
     vaše omiljene postavke.

  6. Kada tipkate naredbu koja započinje sa  :
     pritisnite CTRL-D kako bi vidjeli moguće valjane vrijednosti.
     Pritisnite <TAB> kako bi odabrali jednu od njih.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  Kraj.  Cilj priručnika je da pokaže kratak pregled Vim editora, tek toliko
  da omogući njegovo korištenje. Priručnik nije potpun jer Vim ima mnogo više
  naredbi. Za više informacija: ":help user-manual".

  Za čitanje i korištenje, preporučamo:
	Vim - Vi Improved - by Steve Oualline
	Izdavač: New Riders
  Prva knjiga potpuno posvećena Vim-u. Vrlo korisna za početnike.
  Sa mnogo primjera i slika.
  Posjetite https://iccf-holland.org/click5.html

  Sljedeća knjiga je nešto starija i više o Vi-u nego o Vim-u, preporučamo:
	Learning the Vi Editor - by Linda Lamb
	Izdavač: O'Reilly & Associates Inc.
  Solidna knjiga, možete saznati skoro sve što možete napraviti
  u Vi-u. Šesto izdanje ima nešto informacija i o Vim-u.

  Ovaj priručnik su napisali: Michael C. Pierce i Robert K. Ware,
  Colorado School of Mines koristeći ideje Charles Smith,
  Colorado State University.  E-pošta: <EMAIL>.

  Naknadne promjene napravio je Bram Moolenaar.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Preveo na hrvatski: Paul B. Mahol <<EMAIL>>
  Preinaka 1.42, Lipanj 2008


