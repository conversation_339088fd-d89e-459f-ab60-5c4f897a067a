===============================================================================
=      歡     迎     閱     讀   《 V I M  教  程 》   ──     版本 1.5      =
===============================================================================
     vim 是一個具有很多命令的功能非常強大的編輯器。限于篇幅，在本教程當中
     不就詳細介紹了。本教程的設計目標是講述一些必要的基本命令，而掌握好這
     些命令，您就能夠很容易將vim當作一個通用的萬能編輯器來使用了。

     完成本教程的內容大約需要25-30分鐘，取決于您訓練的時間。

     每一節的命令操作將會更改本文。推薦您復制本文的一個副本，然後在副本上
     進行訓練(如果您是通過"vimtutor"來啟動教程的，那麼本文就已經是副本了)。

     切記一點︰本教程的設計思路是在使用中進行學習的。也就是說，您需要通過
     執行命令來學習它們本身的正確用法。如果您只是閱讀而不操作，那麼您可能
     會很快遺忘這些命令的！

     好了，現在請確定您的Shift-Lock(大小寫鎖定鍵)還沒有按下，然後按鍵盤上
     的字母鍵 j 足夠多的次數來移動光標，直到第一節的內容能夠完全充滿屏幕。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第一講第一節︰移動光標


   ※※ 要移動光標，請依照說明分別按下 h、j、k、l 鍵。 ※※

	     ^
	     k		    提示︰ h 的鍵位于左邊，每次按下就會向左移動。
       < h	 l >		   l 的鍵位于右邊，每次按下就會向右移動。
	     j			   j 鍵看起來很象一支尖端方向朝下的箭頭。
	     v

  1. 請隨意在屏幕內移動光標，直至您覺得舒服為止。

  2. 按下下行鍵(j)，直到出現光標重復下行。

---> 現在您應該已經學會如何移動到下一講吧。

  3. 現在請使用下行鍵，將光標移動到第一講第二節。

提示︰如果您不敢確定您所按下的字母，請按下<ESC>鍵回到正常(Normal)模式。
      然後再次從鍵盤輸入您想要的命令。

提示︰光標鍵應當也能正常工作的。但是使用hjkl鍵，在習慣之後您就能夠快速
      地在屏幕內四處移動光標了。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第一講第二節︰VIM的進入和退出


  !! 特別提示︰敬請閱讀完整本一節的內容，然後才能執行以下所講解的命令。

  1. 請按<ESC>鍵(這是為了確保您處在正常模式)。

  2. 然後輸入︰			:q! <回車>

---> 這種方式的退出編輯器絕不會保存您進入編輯器以來所做的改動。
     如果您想保存更改再退出，請輸入︰
				:wq  <回車>

  3. 如果您看到了命令行提示符，請輸入能夠帶您回到本教程的命令，那就是︰

		vimtutor <回車>

     通常情況下您也可以用這種方式︰

		vim tutor <回車>

---> 這裡的 'vim' 表示進入vim編輯器，而 'tutor'則是您準備要編輯的文件。

  4. 如果您自信已經牢牢記住了這些步驟的話，請從步驟1執行到步驟3退出，然
     後再次進入編輯器。接著將光標移動到第一講第三節來繼續我們的教程講解。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第一講第三節︰文本編輯之刪除


   ** 在正常(Normal)模式下，可以按下 x 鍵來刪除光標所在位置的字符。**

  1. 請將光標移動到本節中下面標記有 ---> 的那一行。

  2. 為了修正輸入錯誤，請將光標移至準備刪除的字符的位置處。

  3. 然後按下 x 鍵將錯誤字符刪除掉。

  4. 重復步驟2到步驟4，直到句子修正為止。

---> The ccow jumpedd ovverr thhe mooon.

  5. 好了，該行已經修正了，下一節內容是第一講第四節。

特別提示︰在您瀏覽本教程時，不要強行記憶。記住一點︰在使用中學習。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第一講第四節︰文本編輯之插入


	 ** 在正常模式下，可以按下 i 鍵來插入文本。**

  1. 請將光標移動到本節中下面標記有 ---> 的第一行。

  2. 為了使得第一行內容雷同于第二行，請將光標移至文本第一個字符準備插入
     的位置。

  3. 然後按下 i 鍵，接著輸入必要的文本字符。

  4. 所有文本都修正完畢，請按下 <ESC> 鍵返回正常模式。
     重復步驟2至步驟4以便修正句子。

---> There is text misng this .
---> There is some text missing from this line.

  5. 如果您對文本插入操作已經很滿意，請接著閱讀下面的小結。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第一講小結


  1. 光標在屏幕文本中的移動既可以用箭頭鍵，也可以使用 hjkl 字母鍵。
	 h (左移)	j (下行)       k (上行)	    l (右移)

  2. 欲進入vim編輯器(從命令行提示符)，請輸入︰vim 文件名 <回車>

  3. 欲退出vim編輯器，請輸入以下命令放棄所有修改︰

	<ESC>   :q!	 <回車>

     或者輸入以下命令保存所有修改︰

	<ESC>   :wq	 <回車>

  4. 在正常模式下刪除光標所在位置的字符，請按︰ x

  5. 在正常模式下要在光標所在位置開始插入文本，請按︰

	 i     輸入必要文本	<ESC>

特別提示︰按下 <ESC> 鍵會帶您回到正常模式或者取消一個不期望或者部分完成
的命令。

好了，第一講到此結束。下面接下來繼續第二講的內容。


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第二講第一節︰刪除類命令


	    ** 輸入 dw 可以從光標處刪除至一個單字/單詞的末尾。**

  1. 請按下 <ESC> 鍵確保您處于正常模式。

  2. 請將光標移動到本節中下面標記有 ---> 的那一行。

  3. 請將光標移至準備要刪除的單詞的開始。

  4. 接著輸入 dw 刪除掉該單詞。

  特別提示︰您所輸入的 dw 會在您輸入的同時出現在屏幕的最後一行。如果您輸
  入有誤，請按下 <ESC> 鍵取消，然後重新再來。

---> There are a some words fun that don't belong paper in this sentence.

  5. 重復步驟3至步驟4，直至句子修正完畢。接著繼續第二講第二節內容。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第二講第二節︰其他刪除類命令


		   ** 輸入 d$ 從當前光標刪除到行末。**

  1. 請按下 <ESC> 鍵確保您處于正常模式。

  2. 請將光標移動到本節中下面標記有 ---> 的那一行。

  3. 請將光標移動到該行的尾部(也就是在第一個點號‘.’後面)。

  4. 然後輸入 d$ 從光標處刪至當前行尾部。

---> Somebody typed the end of this line twice. end of this line twice.


  5. 請繼續學習第二講第三節就知道是怎麼回事了。





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第二講第三節︰關于命令和對象


  刪除命令 d 的格式如下︰

	 [number]   d	object	    或者     d	 [number]   object

  其意如下︰
    number - 代表執行命令的次數(可選項，缺省設置為 1 )。
    d - 代表刪除。
    object - 代表命令所要操作的對象(下面有相關介紹)。

  一個簡短的對象列表︰
    w - 從當前光標當前位置直到單字/單詞末尾，包括空格。
    e - 從當前光標當前位置直到單字/單詞末尾，但是 *不* 包括空格。
    $ - 從當前光標當前位置直到當前行末。

特別提示︰
    對于勇于探索者，請在正常模式下面僅按代表相應對象的鍵而不使用命令，則
    將看到光標的移動正如上面的對象列表所代表的一樣。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		第二講第四節︰對象命令的特殊情況


	       ** 輸入 dd 可以刪除整一個當前行。 **

  鑒于整行刪除的高頻度，VIM 的設計者決定要簡化整行刪除，僅需要在同一行上
  擊打兩次 d 就可以刪除掉光標所在的整行了。

  1. 請將光標移動到本節中下面的短句段落中的第二行。
  2. 輸入 dd 刪除該行。
  3. 然後移動到第四行。
  4. 接著輸入 2dd (還記得前面講過的 number-command-object 嗎？) 刪除兩行。

      1)  Roses are red,
      2)  Mud is fun,
      3)  Violets are blue,
      4)  I have a car,
      5)  Clocks tell time,
      6)  Sugar is sweet
      7)  And so are you.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 第二講第五節︰撤消類命令


	 ** 輸入 u 來撤消最後執行的命令，輸入 U 來修正整行。**

  1. 請將光標移動到本節中下面標記有 ---> 的那一行，並將其置于第一個錯誤
     處。
  2. 輸入 x 刪除第一個不想保留的字母。
  3. 然後輸入 u 撤消最後執行的(一次)命令。
  4. 這次要使用 x 修正本行的所有錯誤。
  5. 現在輸入一個大寫的 U ，恢復到該行的原始狀態。
  6. 接著多次輸入 u 以撤消 U 以及更前的命令。
  7. 然後多次輸入 CTRL-R (先按下 CTRL 鍵不放開，接著輸入 R 鍵) ，這樣就
     可以執行恢復命令，也就是撤消掉撤消命令。

---> Fiix the errors oon thhis line and reeplace them witth undo.

  8. 這些都是非常有用的命令。下面是第二講的小結了。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第二講小結


  1. 欲從當前光標刪除至單字/單詞末尾，請輸入︰dw

  2. 欲從當前光標刪除至當前行末尾，請輸入︰d$

  3. 欲刪除整行，請輸入︰dd

  4. 在正常模式下一個命令的格式是︰

       [number]   command   object     或者     command	[number]   object
     其意是︰
       number - 代表的是命令執行的次數
       command - 代表要做的事情，比如 d 代表刪除
       object - 代表要操作的對象，比如 w 代表單字/單詞，$ 代表到行末等等。
		$ (to the end of line), etc.

  5. 欲撤消以前的操作，請輸入︰u (小寫的u)
     欲撤消在一行中所做的改動，請輸入︰U (大寫的U)
     欲撤消以前的撤消命令，恢復以前的操作結果，請輸入︰CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 第三講第一節︰置入類命令


	       ** 輸入 p 將最後一次刪除的內容置入光標之後 **

  1. 請將光標移動到本節中下面示范段落的首行。

  2. 輸入 dd 將該行刪除，這樣會將該行保存到vim的緩沖區中。

  3. 接著將光標移動到準備置入的位置的上方。記住︰是上方哦。

  4. 然後在正常模式下(<ESC>鍵進入)，輸入 p 將該行粘貼置入。

  5. 重復步驟2至步驟4，將所有的行依序放置到正確的位置上。

     d) Can you learn too?
     b) Violets are blue,
     c) Intelligence is learned,
     a) Roses are red,



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       第三講第二節︰替換類命令


	  ** 輸入 r 和一個字符替換光標所在位置的字符。**

  1. 請將光標移動到本節中下面標記有 ---> 的第一行。

  2. 請移動光標到第一個錯誤的適當位置。

  3. 接著輸入 r ，這樣就能將錯誤替換掉了。

  4. 重復步驟2和步驟3，直到第一行已經修改完畢。

--->  Whan this lime was tuoed in, someone presswd some wrojg keys!
--->  When this line was typed in, someone pressed some wrong keys!

  5. 然後我們繼續學校第三講第三節。

特別提示︰切記您要在使用中學習，而不是在記憶中學習。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第三講第三節︰更改類命令


	   ** 要改變一個單字/單詞的部分或者全部，請輸入 cw **

  1. 請將光標移動到本節中下面標記有 ---> 的第一行。

  2. 接著把光標放在單詞 lubw 的字母 u 的位置那裡。

  3. 然後輸入 cw 就可以修正該單詞了(在本例這裡是輸入 ine 。)

  4. 最後按 <ESC> 鍵，然後光標定位到下一個錯誤第一個準備更改的字母處。

  5. 重復步驟3和步驟4，直到第一個句子完全雷同第二個句子。

---> This lubw has a few wptfd that mrrf changing usf the change command.
---> This line has a few words that need changing using the change command.

提示︰請注意 cw 命令不僅僅是替換了一個單詞，也讓您進入文本插入狀態了。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       第三講第四節︰使用c指令的其他更改類命令


	     ** 更改類指令可以使用同刪除類命令所使用的對象參數。**

  1. 更改類指令的工作方式跟刪除類命令是一致的。操作格式是︰

       [number]   c   object	   或者	    c	[number]   object

  2. 對象參數也是一樣的，比如 w 代表單字/單詞，$代表行末等等。

  3. 請將光標移動到本節中下面標記有 ---> 的第一行。

  4. 接著將光標移動到第一個錯誤處。

  5. 然後輸入 c$ 使得該行剩下的部分更正得同第二行一樣。最後按 <ESC> 鍵。

---> The end of this line needs some help to make it like the second.
---> The end of this line needs to be corrected using the  c$  command.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第三講小結


  1. 要重新置入已經刪除的文本內容，請輸入小寫字母 p。該操作可以將已刪除
     的文本內容置于光標之後。如果最後一次刪除的是一個整行，那麼該行將置
     于當前光標所在行的下一行。

  2. 要替換光標所在位置的字符，請輸入小寫的 r 和要替換掉原位置字符的新字
     符即可。

  3. 更改類命令允許您改變指定的對象，從當前光標所在位置直到對象的末尾。
     比如輸入 cw 可以替換當前光標到單詞的末尾的內容；輸入 c$ 可以替換當
     前光標到行末的內容。

  4. 更改類命令的格式是︰

	 [number]   c	object	      或者	c   [number]   object

下面我們繼續學習下一講。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     第四講第一節︰定位及文件狀態


  ** 輸入 CTRL-g 顯示當前編輯文件中當前光標所在行位置以及文件狀態信息。
     輸入 SHIFT-G 則直接跳轉到文件中的某一指定行。**

  提示︰切記要先通讀本節內容，之後才可以執行以下步驟!!!

  1. 按下 CTRL 鍵不放開然後按 g 鍵。然後就會看到頁面最底部出現一個狀態信
     息行，顯示的內容是當前編輯的文件名和文件的總行數。請記住步驟3的行號。

  2. 按下 SHIFT-G 鍵可以使得當前光標直接跳轉到文件最後一行。

  3. 輸入您曾停留的行號，然後按下 SHIFT-G。這樣就可以返回到您第一次按下
     CTRL-g 時所在的行好了。注意︰輸入行號時，行號是不會在屏幕上顯示出來
     的。

  4. 如果願意，您可以繼續執行步驟1至步驟三。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第四講第二節︰搜索類命令


     ** 輸入 / 以及尾隨的字符串可以用以在當前文件中查找該字符串。**

  1. 在正常模式下輸入 / 字符。您此時會注意到該字符和光標都會出現在屏幕底
     部，這跟 : 命令是一樣的。

  2. 接著輸入 errroor <回車>。那個errroor就是您要查找的字符串。

  3. 要查找同上一次的字符串，只需要按 n 鍵。要向相反方向查找同上一次的字
     符串，請輸入 Shift-N 即可。

  4. 如果您想逆向查找字符串，請使用 ? 代替 / 進行。

---> When the search reaches the end of the file it will continue at the start.

  "errroor" is not the way to spell error;  errroor is an error.

  提示︰如果查找已經到達文件末尾，查找會自動從文件頭部繼續查找。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   第四講第三節︰配對括號的查找


	      ** 按 % 可以查找配對的括號 )、]、}。**

  1. 把光標放在本節下面標記有 --> 那一行中的任何一個 (、[ 或 { 處。

  2. 接著按 % 字符。

  3. 此時光標的位置應當是在配對的括號處。

  4. 再次按 % 就可以跳回配對的第一個括號處。

---> This ( is a test line with ('s, ['s ] and {'s } in it. ))

提示︰在程序調試時，這個功能用來查找不配對的括號是很有用的。






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第四講第四節︰修正錯誤的方法之一


		** 輸入 :s/old/new/g 可以替換 old 為 new。**

  1. 請將光標移動到本節中下面標記有 ---> 的那一行。

  2. 輸入 :s/thee/the <回車> 。請注意該命令只改變光標所在行的第一個匹配
     串。

  3. 輸入 :s/thee/the/g	則是替換全行的匹配串。

---> the best time to see thee flowers is in thee spring.

  4. 要替換兩行之間出現的每個匹配串，請輸入 :#,#s/old/new/g (#,#代表的是
     兩行的行號)。輸入 :%s/old/new/g 則是替換整個文件中的每個匹配串。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第四講小結


  1. Ctrl-g 用于顯示當前光標所在位置和文件狀態信息。Shift-G 用于將光標跳
     轉至文件最後一行。先敲入一個行號然後按 Shift-G 則是將光標移動至該行
     號代表的行。

  2. 輸入 / 然後緊隨一個字符串是則是在當前所編輯的文檔中向後查找該字符串。
     輸入問號 ? 然後緊隨一個字符串是則是在當前所編輯的文檔中向前查找該字
     符串。完成一次查找之後按 n 鍵則是重復上一次的命令，可在同一方向上查
     找下一個字符串所在；或者按 Shift-N 向相反方向查找下該字符串所在。

  3. 如果光標當前位置是括號(、)、[、]、{、}，按 % 可以將光標移動到配對的
     括號上。

  4. 在一行內替換頭一個字符串 old 為新的字符串 new，請輸入  :s/old/new
     在一行內替換所有的字符串 old 為新的字符串 new，請輸入  :s/old/new/g
     在兩行內替換所有的字符串 old 為新的字符串 new，請輸入  :#,#s/old/new/g
     在文件內替換所有的字符串 old 為新的字符串 new，請輸入  :%s/old/new/g
     進行全文替換時詢問用戶確認每個替換需添加 c 選項，請輸入 :%s/old/new/gc


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		第五講第一節︰在 VIM 內執行外部命令的方法


	   ** 輸入 :! 然後緊隨著輸入一個外部命令可以執行該外部命令。**

  1. 按下我們所熟悉的 : 命令設置光標到屏幕底部。這樣就可以讓您輸入命令了。

  2. 接著輸入感嘆號 ! 這個字符，這樣就允許您執行外部的 shell 命令了。

  3. 我們以 ls 命令為例。輸入 !ls <回車> 。該命令就會列舉出您當前目錄的
     內容，就如同您在命令行提示符下輸入 ls 命令的結果一樣。如果 !ls 沒起
     作用，您可以試試 :!dir 看看。

---> 提示︰ 所有的外部命令都可以以這種方式執行。

---> 提示︰ 所有的 : 命令都必須以 <回車> 告終。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第五講第二節︰關于保存文件的更多信息


	     ** 要將對文件的改動保存到文件中，請輸入 :w FILENAME **

  1. 輸入 :!dir 或者 :!ls 獲知當前目錄的內容。您應當已知道最後還得敲
     <回車> 吧。

  2. 選擇一個尚未存在文件名，比如 TEST 。

  3. 接著輸入 :w TEST  (此處 TEST 是您所選擇的文件名。)

  4. 該命令會以 TEST 為文件名保存整個文件 (VIM 教程)。為了確保正確保存，
     請再次輸入 :!dir 查看您的目錄列表內容。

---> 請注意︰如果您退出 VIM 然後在以文件名 TEST 為參數進入，那麼該文件內
     容應該同您保存時的文件內容是完全一樣的。

  5. 現在您可以通過輸入 :!rm TEST 來刪除 TEST 文件了。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    第五講第三節︰一個具有選擇性的保存命令


		** 要保存文件的部分內容，請輸入 :#,# w FILENAME **

  1. 再來執行一次 :!dir 或者 :!ls 獲知當前目錄的內容，然後選擇一個合適的
     不重名的文件名，比如 TEST 。

  2. 接著將光標移動至本頁的最頂端，然後按 CTRL-g 找到該行的行號。別忘了
     行號哦。

  3. 接著把光標移動至本頁的最底端，再按一次 CTRL-g 。也別忘了這個行號哦。

  4. 為了只保存文章的某個部分，請輸入 :#,# w TEST 。這裡的 #,# 就是上面
     要求您記住的行號(頂端行號,底端行號)，而 TEST 就是選定的文件名。

  5. 最後，用 :!dir 確認文件是否正確保存。但是這次先別刪除掉。




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   第五講第四節︰提取和合並文件


       ** 要向當前文件中插入另外的文件的內容，請輸入 :r FILENAME **

  1. 請鍵入 :!dir 確認您前面創建的 TEST 文件還在。

  2. 然後將光標移動至當前頁面的頂端。

特別提示︰ 執行步驟3之後您將看到第五講第三節，請屆時再往下移動回到這裡來。

  3. 接著通過 :r TEST 將前面創建的名為 TEST 的文件提取進來。

特別提示︰您所提取進來的文件將從光標所在位置處開始置入。

  4. 為了確認文件已經提取成功，移動光標回到原來的位置就可以注意有兩份第
     五講第三節，一份是原本，另外一份是來自文件的副本。



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第五講小結


  1. :!command 用于執行一個外部命令 command。

     請看一些實際例子︰
	 :!dir		-  用于顯示當前目錄的內容。
	 :!rm FILENAME	-  用于刪除名為 FILENAME 的文件。

  2. :w FILENAME  可將當前 VIM 中正在編輯的文件保存到名為 FILENAME
     的文件中。

  3. :#,#w FILENAME 可將當前編輯文件第 # 行至第 # 行的內容保存到文件
     FILENAME 中。

  4. :r FILENAME 可提取磁盤文件 FILENAME 並將其插入到當前文件的光標位置
     後面。







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 第六講第一節︰打開類命令


	 ** 輸入 o 將在光標的下方打開新的一行並進入插入模式。**

  1. 請將光標移動到本節中下面標記有 ---> 的那一行。

  2. 接著輸入小寫的 o 在光標 *下方* 打開新的一行並進入插入模式。

  3. 然後復制標記有 ---> 的行並按 <ESC> 鍵退出插入模式而進入正常模式。

---> After typing  o  the cursor is placed on the open line in Insert mode.

  4. 為了在光標 *上方* 打開新的一行，只需要輸入大寫的 O 而不是小寫的 o
     就可以了。請在下行測試一下吧。當光標處在在該行上時，按 Shift-O可以
     在該行上方新開一行。

Open up a line above this by typing Shift-O while the cursor is on this line.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			第六講第二節︰光標後插入類命令


		     ** 輸入 a 將可在光標之後插入文本。 **

  1. 請在正常模式下通過輸入 $ 將光標移動到本節中下面標記有 ---> 的第一行
     的末尾。

  2. 接著輸入小寫的 a 則可在光標之後插入文本了。大寫的 A 則可以直接在行
     末插入文本。

提示︰輸入大寫 A 的操作方法可以在行末插入文本，避免了輸入 i，光標定位到
      最後一個字符，輸入的文本，<ESC> 回復正常模式，箭頭右鍵移動光標以及
      x 刪除當前光標所在位置字符等等諸多繁雜的操作。

  3. 操作之後第一行就可以補充完整了。請注意光標後插入文本與插入模式是基
     本完全一致的，只是文本插入的位置定位稍有不同罷了。

---> This line will allow you to practice
---> This line will allow you to practice appending text to the end of a line.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    第六講第三節︰另外一個置換類命令的版本


		      ** 輸入大寫的 R 可連續替換多個字符。**

  1. 請將光標移動到本節中下面標記有 ---> 的第一行。

  2. 移動光標到第一行中不同于標有 ---> 的第二行的第一個單詞的開始，即單
     詞 last 處。

  3. 然後輸入大寫的 R 開始把第一行中的不同于第二行的剩余字符逐一輸入，就
     可以全部替換掉原有的字符而使得第一行完全雷同第二行了。

---> To make the first line the same as the last on this page use the keys.
---> To make the first line the same as the second, type R and the new text.

  4. 請注意︰如果您按 <ESC> 退出置換模式回復正常模式，尚未替換的文本將仍
     然保持原狀。





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    第六講第四節︰設置類命令的選項


		  ** 設置可使查找或者替換可忽略大小寫的選項 **


  1. 要查找單詞 ignore 可在正常模式下輸入 /ignore 。要重復查找該詞，可以
     重復按 n 鍵。

  2. 然後設置 ic 選項(ic就是英文忽略大小寫Ignore Case的首字母縮寫詞)，即
     輸入︰
	:set ic

  3. 現在可以通過鍵入 n 鍵再次查找單詞 ignore。重復查找可以重復鍵入 n 鍵。

  4. 然後設置 hlsearch 和 incsearch 這兩個選項，輸入以下內容︰
     :set hls is

  5. 現在可以再次輸入查找命令，看看會有什麼效果︰
     /ignore

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       第六講小結


  1. 輸入小寫的 o 可以在光標下方打開新的一行並將光標置于新開的行首，進入
     插入模式。
     輸入大寫的 O 可以在光標上方打開新的一行並將光標置于新開的行首，進入
     插入模式。

  2. 輸入小寫的 a 可以在光標所在位置之後插入文本。
     輸入大寫的 A 可以在光標所在行的行末之後插入文本。

  3. 輸入大寫的 R 將進入替換模式，直至按 <ESC> 鍵退出替換模式而進入正常
     模式。

  4. 輸入 :set xxx 可以設置 xxx 選項。









~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       第七講︰在線幫助命令

		      ** 使用在線幫助系統 **

  Vim 擁有一個細致全面的在線幫助系統。要啟動該幫助系統，請選擇如下三種方
  法之一︰
	- 按下 <HELP> 鍵 (如果鍵盤上有的話)
	- 按下 <F1> 鍵 (如果鍵盤上有的話)
	- 輸入	:help <回車>

  輸入 :q <回車> 可以關閉幫助窗口。

  提供一個正確的參數給":help"命令，您可以找到關于該主題的幫助。請試驗以
  下參數(可別忘了按回車鍵哦。:)︰

	  :help w <回車>
	  :help c_<T <回車>
	  :help insert-index <回車>
	  :help user-manual <回車>




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      第八講︰創建一個啟動腳本

			 ** 啟用vim的功能 **

  Vim的功能特性要比vi多得多，但大部分功能都沒有缺省激活。為了啟動更多的
  功能，您得創建一個vimrc文件。

  1. 開始編輯vimrc文件，這取決于您所使用的操作系統︰

     :edit ~/.vimrc		這是Unix系統所使用的命令
     :edit ~/_vimrc		這是Windows系統所使用的命令

  2. 接著導入vimrc范例文件︰

     :read $VIMRUNTIME/vimrc_example.vim

  3. 保存文件，命令為︰

     :write

  在下次您啟動vim的時候，編輯器就會有了語法高亮的功能。您可以繼續把您喜
  歡的其它功能設置添加到這個vimrc文件中。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  vim 教程到此結束。本教程只是為了簡明地介紹一下vim編輯器，但已足以讓您
  很容易學會使用本編輯器了。毋庸質疑，vim還有很多很多的命令，本教程所介
  紹的還差得遠著呢。所以您要精通的話，還望繼續努力哦。下一步您可以閱讀
  vim手冊，使用的命令是︰
	:help user-manual

  為了更進一步的參考和學習，以下這本書值得推薦︰

	Vim - Vi Improved - 作者︰Steve Oualline
	出版社︰New Riders

  這是第一本完全講解vim的書籍。對于初學者特別有用。其中還包含有大量實例
  和圖示。欲知詳情，請訪問 https://iccf-holland.org/click5.html

  以下這本書比較老了而且內容主要是vi而不是vim，但是也值得推薦︰

	Learning the Vi Editor - 作者︰Linda Lamb
	出版社︰O'Reilly & Associates Inc.

  這是一本不錯的書，通過它您幾乎能夠了解到全部vi能夠做到的事情。此書的第
  六個版本也包含了一些關于vim的信息。

  本教程是由來自Calorado School of Minese的Michael C. Pierce、Robert K.
  Ware 所編寫的，其中來自Colorado State University的Charles Smith提供了
  很多創意。編者通信地址是︰

	<EMAIL>

  本教程已由Bram Moolenaar專為vim進行修訂。



  譯制者附言︰
  ===========
      簡體中文教程翻譯版之譯制者為梁昌泰 <<EMAIL>>，還有
      另外一個聯系地址︰<EMAIL>。

      繁體中文教程是從簡體中文教程翻譯版使用 Debian GNU/Linux 中文項目小
      組的于廣輝先生編寫的中文漢字轉碼器  autoconvert 轉換而成的，並對轉
      換的結果做了一些細節的改動。

  變更記錄︰
  =========
      2002年08月30日 梁昌泰 <<EMAIL>>
      感謝 RMS@SMTH 的指正，將多處錯誤修正。

      2002年04月22日 梁昌泰 <<EMAIL>>
      感謝 <EMAIL> 的指正，將兩處錯別字修正。

      2002年03月18日 梁昌泰 <<EMAIL>>
      根據Bram Moolenaar先生在2002年03月16日的來信要求，將vimtutor1.4中譯
      版升級到vimtutor1.5。

      2001年11月15日 梁昌泰 <<EMAIL>>
      將vimtutor1.4中譯版提交給Bram Moolenaar和Sven Guckes。

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
