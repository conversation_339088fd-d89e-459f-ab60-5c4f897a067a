===============================================================================
=           Добре дошли в самоучителя на V I M     -     Версия 1.7           =
===============================================================================

     Vim е много мощен редактор с много команди - твърде много, за да бъдат
     обяснени в ръководство като това. Този самоучител е създаден, за да обясни
     достатъчно от тях, така че да можете да използвате Vim за всякакви цели.

     Времето, необходимо за уроците, е около 25-30 минути, в зависимост от
     това, колко време ви трябва за упражненията.
     
     ВНИМАНИЕ!
     Командите в уроците ще променят текста им. Запишете файла другаде, за да
     се упражнявате (ако сте отворили самоучителя с "vimtutor", това вече е
     направено).

     Важно е да се запомни, че този самоучител е съставен с цел да се учите
     чрез употреба. Това означава да изпълнявате командите, за да ги научите
     правилно. Ако просто четете текста, ще забравите командите!


     И така, уверете се, че клавишът CapsLock не е натиснат, и натиснете клавиша
     j няколко пъти, така че Урок 1.1 да се побере на екрана.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.1.1:  ПРИДВИЖВАНЕ НА ПОКАЗАЛЕЦА


   ** За да преместите показалеца, натискайте клавишите h,j,k,l както е указано. **
	     ^
	     k        Подсказка: Клавишът h е вляво и премества показалеца наляво.
       < h	 l >             Клавишът l е вдясно и премества показалеца надясно.
	     j                   Клавишът j прилича на стрелка, насочена надолу.
	     v
  1. Движете показалеца насам-натам по екрана, докато свикнете.

  2. Задръжте клавиша за преместване надолу (j), докато започне да повтаря
     действието си. Сега знаете как да се придвижите до следващия урок.

  3. Използвайте клавиша за движение надолу, за да стигнете до Урок 1.2.

Важно! Ако се окаже, че не сте сигурни какво сте въвели, натиснете <ESC>, за да
       отидете в нормален режим. След това въведете желаната команда отново.

Важно! Клавишите със стрелки би трябвало също да работят, но ако използвате
       hjkl ще можете да се придвижвате по-бързо, след като свикнете. Наистина!

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Урок 1.1.2: Излизане от VIM (quit)


  Важно!!! Преди да изпълните която и да е от стъпките по-долу, прочетете
  целия урок!!! 

  1. Натиснете клавиша <ESC> (за да се уверите, че сте в нормален режим).

  2. Напишете:	:q! <ENTER>.
     Така излизате от редактора без да записвате промените, които сте направили.

  3. Върнете се тук като изпълните командата, с която пуснахте този самоучител.
     Това ще да е:  vimtutor <ENTER>

  4. Ако сте сигурни, че сте запомнили стъпките от 1 до 3, изпълнете ги и
     влезте отново в редактора.

Внимание! :q! <ENTER>  отхвърля всички промени, които сте направили. След
          няколко урока ще се научите как да записвате промени във файл.

  5. Придвижете показалеца надолу до Урок 1.3.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.1.3: ПРОМЯНА НА ТЕКСТ - ИЗТРИВАНЕ (DELETE)


	   ** Натиснете x , за да изтриете буквата под показалеца. **

  1. Придвижете показалеца до реда по-долу, означен със --->.

  2. За да поправите грешките, придвижете показалеца върху буквата,
     която ще триете.

  3. Натиснете клавиша x, за да изтриете нежеланата буква.

  4. Повтаряйте стъпки от 2 до 4, докато поправите изречението.

---> Кккравата сскоочии връъъъзз ллуннатааа.

  5. След като горният ред е вече поправен, можем да отидем на Урок 1.4.

Важно! Като правите този урок, не се опитвайте да помните, учете се с правене.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Урок 1.1.4: ПРОМЯНА НА ТЕКСТ - ВЪВЕЖДАНЕ (INSERT)


                     ** Бележка на преводача **
  В упражненията нататък ще ви се налага да въвеждате текст на български. Vim
  притежава собствена система за въвеждане на не-латински букви. За да можете
  да пишете български букви, докато сте в режим за въвеждане, и едновременно с
  това командите ви да се въвеждат с латински букви, направете следното:

  Натиснете  <ESC>, за да се уверите, че не сте в режим за въвеждане. 

  Въведете ":set keymap=bulgarian-phonetic" или ":set keymap=bulgarian-bds"
  (без кавичките!), в зависимост от това коя подредба предпочитате. Забележете,
  че щом въведете : , те ще се появят в дъното на екрана. Вече можете да
  въвеждате български букви, без да ползвате системната клавиатурна подредба.

  За да превключвате между двете подредби, докато сте в режим за въвеждане,
  натискайте CTRL-^ (дръжте натиснати CTRL и SHIFT и натиснете ^).

   
            ** Натиснете i, за да въведете текст. **

  1. Придвижете показалеца до първия ред долу, означен със --->.

  2. За да направите първия ред същия като втория, придвижете показалеца върху
     първата буква СЛЕД мястото, където трябва да бъде въведен текстът.

  3. Натиснете i и напишете каквото трябва да се добави.

  4. След поправяне на всяка грешка натискайте <ESC>, за да се върнете към
     Нормален режим. Повтаряйте стъпки от 2 до 4, докато поправите изречението.

---> Част текс липс н тзи .
---> Част от текста липсва на този ред.

  5. След като усвоите въвеждането на текст, отидете на Урок 1.5.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.1.5: ПРОМЯНА НА ТЕКСТ - ДОБАВЯНЕ (APPEND)


			** Натиснете  A (SHIFT+a) , за да добавите текст. **

  1. Придвижете показалеца до реда долу, означен със --->.
     Няма значение на коя буква в реда се намира показалеца.

  2. Натиснете  A  и добавете каквото е нужно.

  3. След като сте добавили каквото е нужно, натиснете <ESC>, за да се върнете
     в Нормален режим.

  4. Придвижете показалеца до втория ред означен със ---> и повторете стъпки 2,
     и 3, за да поправите изречението.

---> Има текст, който липсва
     Има текст, които липсва на този ред.
---> Тук също има текст,
     Тук също има текст, който липсва.

  5. След като овладеете добавянето на текст, отидете на Урок 1.6.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.1.6: ПРОМЯНА НА ФАЙЛ

		    ** Използвайте  :wq  (write and quit), за да запишете файла и
            излезете. **

  Внимание! Преди да изпълните която и да е от стъпките долу, прочетете целия урок!!

  1. Излезте от самоучителя, както направихте в Урок 1.1.2:  :q!
     Или, ако имате достъп до друг терминал, направете следното там.

  2. На командния ред напишете следното и натиснете <ENTER>:  vim tutor <ENTER>
    'vim' е командата, която стартира редактора Vim, 'tutor' е името на файла,
    които искате да промените. Използвайте файл който може да бъде променян.

  3. Въвеждайте и изтривайте текст по начините, научени в предишните уроци.

  4. Запишете файла и излезте от Vim с:  :wq  <ENTER>

  5. Ако сте излезли от vimtutor в стъпка 1, пуснете го отново и се придвижете
     надолу до обобщението, което следва.

  6. След като прочетете и разберете горните стъпки, направете ги.
  
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 1 ОБОБЩЕНИЕ


  1. Показалецът се премества като използвате клавишите със стрелки или с клавишите.
	 h (наляво)	j (надолу)       k (нагоре)	    l (надясно)

  2. За да пуснете Vim от командния ред, напишете:  vim ИМЕ-НА-ФАЙЛ <ENTER>

  3. За да излезете от Vim, напишете:
  	                <ESC>   :q!	 <ENTER>  за да отхвърлите всички промени.
     ИЛИ напишете:  <ESC>   :wq	 <ENTER>  за да запишете промените.

  4. За да изтриете буква намираща се под показалеца, натиснете:  x .

  5. За да въведете или добавите текст, натиснете:
	 i   въведете текста, натиснете <ESC>. Въвежда преди показалеца.
	 A   добавете текста, натиснете <ESC>. Добавя в края на реда.

Внимание! С натискане на <ESC>  преминавате в Нормален режим или отменяте
          нежелана, недописана команда.

Сега продължете с Урок 2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.2.1: КОМАНДИ ЗА ИЗТРИВАНЕ


		       ** Въведете dw , за да изтриете дума. **

  1. Натиснете <ESC>, за да се уверите, че сте в Нормален режим.

  2. Придвижете показалеца до реда по-долу, означен със --->.

  3. Придвижете показалеца до началото на думата, която трябва да бъде изтрита.

  4. Натиснете последователно dw , и думата ще изчезне.

  Забележка! Буквата d ще се появи на последния ред от екрана, когато я
  натиснете. Vim ви чака да натиснете w. Ако видите друга буква, значи сте
  натиснали грешен клавиш. Натиснете <ESC> и започнете отначало.

---> Има някои думи хартия, които забава не са част от това изречение.

  5. Повтаряйте стъпки 3 и 4, докато поправите изречението, и преминете към
     Урок 2.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Урок 1.2.2: ОЩЕ КОМАНДИ ЗА ИЗТРИВАНЕ


	   ** Въведете d$ , за да изтриете всичко до края на реда. **

  1. Натиснете <ESC>, за да се уверите, че сте в Нормален режим.

  2. Придвижете показалеца до реда по-долу, означен със --->.

  3. Придвижете показалеца до правилния ред (СЛЕД първата .).

  4. Натиснете последователно d$ , за да изтриете всичко до края на реда.

---> Някой е въвел края на този ред двукратно. края на този ред двукратно.


  5. Отидете до Урок 2.3, за да разберете какво се случва.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.2.3: ЗА ОПЕРАТОРИТЕ И ДВИЖЕНИЯТА


  Много команди, които променят текст, се състоят от оператор и движение.
  Форматът за командата за изтриване с оператора d (delete) е както следва.

  	d   движение

  Където:
    d е операторът за изтриване.
    движение - върху какво ще се приложи операторът (списъкът долу).

  Кратък списък с движения:
    w - (word) до началото на следващата дума, като се ИЗКЛЮЧВА първата ѝ буква.
    e - (end of word) до края на текущата дума, ВКЛЮЧИТЕЛНО последната буква.
    $ - До края на реда, ВКЛЮЧИТЕЛНО последния символ.

  Така, като въведете  de, ще изтриете от мястото на показалеца до края на
  думата.

Забележка! Като натиснете само клавиша за движение, ще преместите показалеца на
           съответното място.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.2.4: ИЗПОЛЗВАНЕ НА БРОЯЧ ПРИ ДВИЖЕНИЕ


   ** Ако въведете число преди движението, то се повтаря толкова пъти
      колкото е числото. **

  1. Придвижете показалеца до началото на реда долу, означен със --->.

  2. Въведете  2w  , за да преместите показалеца с две думи напред.

  3. Въведете  3e  , за да преместите показалеца до края на третата дума
     напред.

  4. Въведете  0  (нула), за да отидете в началото на реда.

  5. Повтаряйте стъпки 2 и 3 с различни числа.

---> Това е просто ред с думи, в който можете да се движите.

  6. Отидете на Урок 2.5.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.2.5: ИЗПОЛЗВАЙТЕ БРОЯЧ, ЗА ДА ТРИЕТЕ ПОВЕЧЕ


   ** Ако въведете число преди оператор, действието се повтаря толкова пъти
      колкото е числото. **

  Както е упоменато горе, за да изтриете повече при използване на оператора за
  изтриване заедно с движение, трябва да въведете числото преди движението:
	 d   число   движение

  1. Придвижете показалеца до първата дума, изписана с ГЛАВНИ БУКВИ в реда,
     означен със --->.

  2. Въведете  d2w  , за да изтриете думите, написани с ГЛАВНИ БУКВИ.

  3. Повторете стъпки 1 и 2, за да изтриете последователните
     думи, изписани с големи букви с една команда.

--->  този АБВ ГДЕ ред ЖЗИЙ КЛ МНОП РСТ с думи УФХ ЦЧШ ЩЪЬЮЯ е почистен.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.2.6: РАБОТА С РЕДОВЕ


		   ** Въведете dd , за да изтриете цял ред. **

  Понеже често се налага да се трие цял ред, създателите на Vim са решили, че ще
  е по-лесно да се натисне два пъти d, за да се изтрие ред.

  1. Придвижете показалеца на втория ред в абзаца долу.
  2. Въведете dd , за да изтриете реда.
  3. Сега отидете на четвъртия ред.
  4. Въведете 2dd , за да изтриете два реда.

--->  1)  Розите са червени,
--->  2)  Калта е забавление,
--->  3)  Теменужките са сини,
--->  4)  Аз имам кола,
--->  5)  Часовниците показват часа,
--->  6)  Захарта е сладка,
--->  7)  Както и ти.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.2.7: ОТМЯНА


   ** Натиснете u , за да отмените (undo) последната команда; U , за
   отмяна на всички команди на текущия ред. **

  1. Придвижете показалеца до началото на реда долу, означен със --->, и го
  поставете на първата грешка.
  2. Въведете x , за да изтриете първата нежелана буква.
  3. Сега натиснете u , за да отмените последната изпълнена команда.
  4. Този път поправете всички грешки, като използвате командата x.
  5. Сега въведете главно U (SHIFT+U), за да върнете реда в първоначалния му вид.
  6. А сега натиснете u няколко пъти, за да отмените предишното U и командите
     преди него.
  7. Сега натиснете CTRL-R (redo) (дръжте клавиша CTRL натиснат, докато натискате R)
     неколкократно, за да изпълните отново командите (да отмените отмените).

---> Пооправеете грешшките нна този реди и ги заменете с отмянаа.

  8. Това са много полезни команди. Сега отидете на обобщението за Урок 2.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 2 ОБОБЩЕНИЕ


  1. За да изтриете всичко от показалеца до началото на следващата дума, въведете  dw
  2. За да изтриете всичко от показалеца до края на реда, въведете  d$
  3. За да изтриете цял ред, въведете  dd

  4. За да повторите движение въведете преди него число   2w
  5. Форматът за команда за промяна е:
               команда  [число]  движение
     където:
       оператор - това, което трябва да се направи (заповед), например  d  за изтриване
       [число] - незадължителен брой повторения на движението
       движение - придвижване в текста, върху който се работи, например  w (word),
		  $ (до края на реда) и т.н.

  6. За да се придвижите до началото на ред, натиснете нула - 0

  7. За да отмените предишни действия, натиснете  u  (малка буква u)
     За да отмените всички промени на един ред, въведете  U  (главна буква U)
     За да отмените отмените, натиснете  CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.3.1: КОМАНДАТА ЗА ПОСТАВЯНЕ (PUT)


       ** Въведете  p  , за да поставите изтрит преди това текст след
          показалеца.**

  1. Придвижете показалеца до първия ред, означен със ---> долу.

  2. Въведете  dd , за да изтриете реда и да го запишете в регистъра на Vim.

  3. Придвижете показалеца до реда, означен със c), НАД мястото, където трябва да
     се постави изтрития ред.

  4. Въведете  p  , за да поставите (put) реда под реда, на който е показалеца.

  5. Повтаряйте стъпки от 2 до 4, за да подредите правилно редовете.

---> d) Ти можеш ли да учиш?
---> b) Теменужките са сини,
---> c) Уменията се научават,
---> a) Розите са червени,



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Урок 1.3.2: КОМАНДАТА ЗА ЗАМЕСТВАНЕ (REPLACE)


       ** Въведете  rx  , за да заместите буквата под показалеца с x . **

  1. Придвижете показалеца до първия ред, означен със ---> долу.

  2. Наместете показалеца така, че да се окаже върху първата грешка.

  3. Въведете  r  и след това буквата, с която ще замествате.

  4. Повтаряйте стъпки 2 и 3 докато първият ред стане същия като втория.

--->  Катишо тизе гад и песен, никей а нарескъл гришнета бливочи!
--->  Когато този ред е писан, някой е натискал грешните клавиши!

  5. Сега отидете на урок 3.3.

Забележка! Помнете, че трябва да се учите, като се упражнявате, а не като се
           опитвате да запомните.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.3.3: ОПЕРАТОРЪТ ЗА ПРОМЯНА (CHANGE)


	   ** За да промените от мястото на показалеца до края на дума, въведете  ce . **

  1. Придвижете показалеца до първия ред долу, означен със --->.

  2. Поставете показалеца върху з в тзии.

  3. Въведете  ce  и правилния остатък от думата ( в този случай ози).

  4. Натиснете <ESC> и отидете на следващата група букви, които трябва да се променят.

  5. Повтаряйте стъпки 3 и 4, докато първото изречение стане същото като второто.

---> На тзии ред иам неклико дмуи, ктоио требав да се прмнеято като се изповлза оепртореа за промяна.
---> На този ред има няколко думи, които трябва да се променят като се използва оператора за промяна.

 Забележете, че  ce  изтрива думата и преминавате в режим за въвеждане.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Урок 1.3.4: ОЩЕ ПРОМЕНИ С ИЗПОЛЗВАНЕ НА c


     ** Операторът за промяна се използва със същите движения както при триене **

  1. Операторът за промяна работи по същия начин като операторът за триене.
     Форматът е:

         c [число] движение

  2. Движенията са същите, например: w (word) и $ (край на ред).

  3. Отидете на първия ред долу, отбелязан със --->.

  4. Придвижете показалеца до първата грешка.

  5. Въведете c$ и допишете остатъка от реда така, че да стане същият като
     долния ред. След това натиснете <ESC>.

---> Краят на този ред трябва да изглежда като долния.
---> Краят на този ред трябва да бъде поправен с командата c$.

Забележка! Можете да използвате клавиша Backspace за поправка на грешки, докато въвеждате.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 3 ОБОБЩЕНИЕ


  1. За да поставите изтрит преди това отнякъде текст, въведете  p  .
     Това поставя изтрития текст СЛЕД мястото, на което се намира показалеца. 
     Ако сте изтрили преди това цял ред, той ще бъде поставен като следващ ред.

  2. За да заместите буква, намираща се под показалеца, въведете  r  и след
     това буквата, с която искате да заместите.

  3. Операторът за промяна ви позволява да променяте текста от мястото на
     показалеца до мястото, указано от съответното движение. Например, въведете
     ce за да изтриете от мястото на показалеца до края на думата, или,
     въведете  c$ ,за да замените с нов текст до края на реда.

  4. Форматът на оператора за промяна е:

	 c   [число]   движение

Сега отидете на следващия урок.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Урок 1.4.1: МЕСТОПОЛОЖЕНИЕ НА ПОКАЗАЛЕЦА И СЪСТОЯНИЕ НА ФАЙЛА

  ** Въведете CTRL-G, за да видите къде се намирате във файла и неговото
     състояние. Въведете G , за да отидете на някой ред. **

Внимание! Прочетете целия урок, преди да изпълните стъпките в него!

  1. Задръжте натиснат клавиша Ctrl и натиснете g. Това действие го наричаме
     CTRL-G. В дъното на екрана ще се появи съобщение с името на файла и
     мястото, където се намира показалецът. Запомнете номера на реда за стъпка 3.

Забележка: Може би виждате мястото на показалеца в долния десен ъгъл на екрана.
Това се случва, когато настройката 'ruler' е зададена (вижте :help 'ruler' )

  2. Натиснете G , за да отидете в края на файла.
     Въведете gg , за да отидете в началото на файла.

  3. Въведете номера на реда, на който бяхте, и след това натиснете G. Това ще
     ви върне на мястото където бяхте, когато натиснахте CTRL-G.

  4. Ако вече се чувствате уверени, изпълнете стъпките от 1 до 3.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.4.2: КОМАНДАТА ЗА ТЪРСЕНЕ


     ** Въведете  /  , последвана от фраза, за да потърсите  фразата. **

  1. В Нормален режим въведете знака  / .  Забележете, че / (наклонената
     черта) и показалецът се появяват в дъното на екрана, както се случва при
     използването на командата  : .

  2. Сега въведете 'грешшшка' <ENTER>. Това е думата, която ще търсите.

  3. За да търсите същата дума отново, натиснете n .
     За да търсите същата дума отново, но в обратната посока, натиснете N .

  4.  За да търсите за фраза в обратната посока използвайте ? вместо / .

  5. За да се  върнете, там където сте били, натиснете  CTRL-O  (задръжте Ctrl
     натиснат докато натискате клавиша o).  Повторете, за да отидете още
     по-назад.  С CTRL-I пък отивате напред.

--->  "грешшшка" се се пише "грешка"  грешшшка е грешка.
Внимание! Когато търсенето достигне до края на файла, то ще продължи от
началото на файла, освен ако настройката 'wrapscan' е била нулирана.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Урок 1.4.3: ТЪРСЕНЕ НА СЪОТВЕТСТВАЩИ СКОБИ


	      ** Въведете  %  , за да на мерите съответната  ),], или } . **

  1. Поставете показалеца върху някоя скоба (, [, или {  в реда долу, означен със --->.

  2. Сега Въведете символа  % .

  3. Показалецът ще се премести върху съответстващата фигурна, квадратна или
     обикновена скоба.

  4. Въведете  %  , за да преместите показалеца на другата съответстваща скоба.

  5. Придвижете показалеца до друга (,),[,],{ или } скоба и вижте какво прави % .

---> Това ( е ред за проверка с различни скоби като  (, [  ] и {  } в него. ))


Забележка! Това е много полезно при откриване на грешки в програми с несъответстващи скоби.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Урок 1.4.4: КОМАНДАТА ЗА ЗАМЕСТВАНЕ (SUBSTITUTE)


	** Въведете  :s/старо/ново/g   за да заместите  'старо' със 'ново'. **

  1. Придвижете показалеца до реда долу, означен със --->.

  2. Въведете  :s/тоо/то <ENTER> . Забележете, че командата замества само
     първото съвпадение с "тоо" на реда.

  3. Сега въведете :s/тоо/то/g .  Като добавите знака g (globally) това
     означава, че искате да се заместят всички съвпадения, навсякъде в реда.

---> Най-добротоо време да сте на полетоо е лятотоо.

  4. За да заместите всяко съвпадение на дадена последователност от символи
     между два реда:
     Въведете   :#,#s/old/new/g  където #,#  са числата на редовете
                                 (първи и последен), обхватът, в който искате да
                                 стане заместването.
     Въведете   :%s/old/new/g    за да промените всяко съвпадение в целия файл.
     Въведете   :%s/old/new/gc   да бъдете питани при всяко съвпадение, дали
                                 да се замести или не.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 4 ОБОБЩЕНИЕ


  1. CTRL-G  показва къде се намирате във файл и състоянието му.
             G  ви отвежда до края на файла.
     число   G  ви отвежда до съответния ред.
            gg  ви отвежда до първия ред.

  2. Ако натиснете  /  , последвана от низ за търсене, търсите НАПРЕД.
     Ако натиснете  /  , последвана от низ за търсене, търсите НАЗАД.
     След търсене, въведете  n  , за да намерите следващо съвпадение с низа,
     който търсите в същата посока, в която търсите или  N  , за да търсите в
     обратната посока.
     CTRL-O ви отвежда назад до старо място във файла, CTRL-I обратно до
     по-нови места.

  3. Ако натиснете  %  докато показалеца се намира на (,),[,],{, или }, той
     отива до съответстващата скоба.

  4. За да заместите един низ с друг, въведете                      :s/низ/друг
     За да заместите един низ с друг навсякъде в един ред, въведете :s/низ/друг/g
     За да заместите в даден обхват от редове, въведете             :#,#s/низ/друг/g
     За да заместите всички съвпадения във файл, въведете           :%s/низ/друг/g
     За да бъдете питани при всяко съвпадение, добавете  'c'        :%s/низ/друг/gc

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Урок 1.5.1: КАК ДА ИЗПЪЛНИМ ВЪНШНА КОМАНДА


   ** Въведете  :!	, последвано от външна команда, за да я изпълните. **

  1. Въведете познатото ви вече  :  , за да поставите показалеца в дъното на
     екрана. Това ви позволява да въвеждате команда.

  2. Сега въведете !  (удивителен знак). Това ви позволява да изпълнявате
     всякакви външни команди.

  3. Например, след  !  въведете  ls  и след това натиснете  <ENTER>. Това ще
     ви покаже списък с файловете и папките точно както ако сте в терминал.
     Напишете :!dir ако  ls не работи.

Забележка: По този начин можете да изпълнявате всякакви външни команди и с аргументи.

Забележка: Всички команди, започващи с  :  завършват с натискането на  <ENTER>
           От сега нататък няма да го споменаваме постоянно.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Урок 1.5.2: ПОВЕЧЕ ЗА ЗАПИСВАНЕТО НА ФАЙЛОВЕ


     ** За да запишете промените, направени в текста въведете  :w ИМЕНАФАЙЛ. **

  1. Въведете  :!dir  или  :!ls  за да видите списък със съдържанието на
     текущата папка. Вече знаете, че трябва да натиснете <ENTER> след това.

  2. Изберете име на файла, което не съществува, например TEST.

  3. Сега въведете	 :w TEST   (където TEST  е името на файла).

  4. Това записва целия файл (Самоучителя за Vim) под името TEST.
     За да проверите, напишете  :!dir или  :!ls  отново и вижте съдържанието
     на вашата папка.

Забележете! Ако излезете от Vim и го пуснете отново, като напишете на командния
            ред  vim TEST , файлът ще бъде точно копие на самоучителя, когато
            сте го записали.

  5.  Сега изтрийте файла като напишете (в MS-DOS):    :!del TEST
				или (в какъвто и да е Unix) :!rm TEST


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Урок 1.5.3: ИЗБОР НА ТЕКСТ ЗА ЗАПИС


	** За да запишете част от файла, натиснете  v  , следвано от движение  :w FILENAME **

  1. Придвижете показалеца на този ред.

  2. Натиснете  v  и придвижете показалеца пет реда надолу. Забележете, че
     текстът се осветява.

  3. Натиснете : . В дъното на екрана ще се появи  :'<,'> .

  4. Напишете w TEST  , където TEST е име на файл, който все още не съществува.
     Уверете се, че виждате  :'<,'>w TEST  преди да натиснете <ENTER>.

  5. Vim ще запише избраните редове във файла TEST.  Използвайте  :!dir  или  :!ls ,
     за да го видите.  Не го изтривайте все още! Ще го използваме в следващия урок.

Забележете! Като натиснете  v  , започвате видимо избиране (Visual selection).  
            Може да движите показалеца наоколо, за да направите избраното
            по-голямо или по-малко. След което, можете да използвате оператор,
            за да направите нещо с текста. Например,  d  изтрива текста.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Урок 1.5.4: ИЗВЛИЧАНЕ И СЛИВАНЕ НА ФАЙЛОВЕ


       ** За да вмъкнете съдържание на файл в текущия, въведете  :r ИМЕНАФАЙЛ  **

  1. Поставете показалеца над този ред.

Важно! След като изпълните стъпка 2, ще видите текста от Урок 5.3. След това
       отидете НАДОЛУ, за да видите този урок отново. 

  2. Сега извлечете файла TEST, като използвате командата :r TEST  , където TEST
     е името на файла, което сте използвали. Файла, който извлекохте е вмъкнат
     под реда, на който се намира показалеца.

  3. За да проверите, че файла е извлечен, отидете назад и ще забележите, че
     има два урока 5.3 - оригинала и копието от извлечения файл.

Важно! Също така можете да четете изхода от външна команда.
       :r !ls  прочита показаното от ls и го поставя под показалеца.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 5 ОБОБЩЕНИЕ


  1.  :!команда  изпълнява външна команда.

      Някои полезни примери са:
	 (MS-DOS)	  (Unix)
	  :!dir		   :!ls	 -  показва съдържанието на директорията, в която
                            се намирате.
	  :!del FILENAME   :!rm FILENAME   -  изтрива файла FILENAME.

  2.  :w FILENAME  записва текущия файл под името FILENAME.

  3.  v  движение :w FILENAME  записва видимо избраните редове във файл с име
      FILENAME.

  4.  :r FILENAME  извлича съдържанието на файла с име FILENAME и го вмъква под
      мястото, където се намира показалеца

  5.  :r !dir  чете изхода на командата dir и  го поставя под мястото, на
      което се намира показалеца.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.6.1: КОМАНДАТА ЗА ОТВАРЯНЕ (OPEN)


     ** Натиснете  o  , за да отворите ред под показалеца и да преминете в
        режим за въвеждане. **

  1. Придвижете показалеца до реда долу, означен със --->.

  2. Натиснете клавиша o , за да отворите нов ред ПОД показалеца и да преминете
     в режим за въвеждане.

  3. Сега въведете някакъв текст и натиснете <ESC> , за да излезете от режима
     за въвеждане.

---> След като натиснете  o  , показалеца отива на новоотворения ред и
     преминавате в режим за въвеждане.

  4. За да отворите нов ред НАД показалеца, просто въведете главно O вместо
     малко. Пробвайте това на долния ред.

---> Отворете нов ред над този, като натиснете  O  , докато показалеца е на
     този ред.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.6.2: КОМАНДАТА ЗА ДОБАВЯНЕ (APPEND)


	     ** Натиснете  a  , за да въведете текст СЛЕД показалеца. **

  1. Придвижете показалеца до началото на реда долу, означен със --->.
  
  2. Натискайте  e  , докато показалеца отиде до края на ре .

  3. Натиснете  a  (малка буква), за да добавите текст СЛЕД показалеца.

  4. Допълнете думата както е на следващия ред. Натиснете <ESC> , за да
     излезете от режима за въвеждане.

  5. Използвайте  e  , за да се придвижите до следващата непълна дума и
     повторете стъпки 3 и 4.
  
---> Този ре ви позволява да упраж  добав на тек в ред.
---> Този ред ви позволява да упражнявате добавяне на текст в ред.

Важно! a, i и A - с всички тях отивате в режим за въвеждане. Единствената
       разлика е в това, къде се въвеждат знаците.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Урок 1.6.3: ДРУГ НАЧИН ЗА ЗАМЕСТВАНЕ


      ** Натиснете главно  R  ,  за да заместите повече от един знак. **

  1. Придвижете показалеца до първия ред долу означен със --->.  Придвижете
     показалеца до началото на първото xxx.

  2. Сега натиснете  R  и въведете числото от долния ред, така че да замести xxx . 

  3. Натиснете <ESC> , за да излезете от режима за заместване. Забележете, че
     остатъка от реда остава непроменен. 

  4. Повторете стъпките, за да заместите другото xxx.

---> Ако добавите 123 към xxx ще получите xxx.
---> Ако добавите 123 към 456 ще получите 579.

Важно! Режимът за заместване е същия като режима за въвеждане, но всеки въведен
       знак изтрива съществуващ знак.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.6.4: КОПИРАНЕ И ЗАМЕСТВАНЕ


	  ** Използвайте операторът y (yank),  за да  копирате текст и p (paste),
         за да го поставите. **

  1. Отидете до реда, означен със ---> долу и поставете показалеца след "a)".
  
  2. Преминете във режим за видимо избиране като използвате v и преместете
     показалеца точно пред "първата".
  
  3. Натиснете  y  , за да копирате (yank) осветения текст.

  4. Преместете показалеца на края на следващия ред с  j$

  5. Натиснете  p ,за да поставите (paste) текста. След това натиснете пак <ESC> .

  6. Използвайте режима за видимо избиране, за да изберете " точка.", вземете
     го с y , отидете на края на следващия ред с j$ и поставете текста с p .

--->  a) това е първата точка.
      b)

  Важно! Можете да използвате  y също и като оператор.  yw  взима цяла дума.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   Урок 1.6.5: ЗАДАВАНЕ НА НАСТРОЙКА


	  ** Задайте настройка, та при търсене и заместване, да не се различават
         големи и малки букви. **

  1. Търсете 'разли' като въведете   /разли  <ENTER>
     Повторете няколко пъти като натискате  n .

  2. Задайте настройката 'ic' (Ignore case) като въведете  :set ic

  3.Сега търсете 'разли' отново като натискате  n .
    Забележете, че сега Разлика и РАЗЛИКА също биват намерени.

  4. Задайте настройките 'hlsearch' (highlight search) 
     и 'incsearch' (incremental search):  :set hls is
     Тези настройки означават съответно "осветяване на намереното" 
     и "частично търсене".

  5. Сега въведете отново командата за търсене и вижте какво се случва:
     /разли <ENTER>

  6. За да изключите нечувствителното към регистъра на буквите търсене, въведете
  :set noic

Забележка! За да премахнете осветяването, въведете :nohlsearch
Забележка! Ако искате да не се прави разлика между главни и малки букви само
           при едно търсене, въведете  \c (латинско ц) в края на низа, който
           търсите: /разлика\c  <ENTER>

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 6 ОБОБЩЕНИЕ

  1. Натиснете  o , за да отворите нов ред ПОД показалеца и да преминете в
     режим за въвеждане.
     Натиснете  O , за да отворите ред НАД показалеца.

  2. Натиснете  a  , за да въведете текст СЛЕД показалеца.
     Натиснете  A  , за да въведете текст след края на реда.

  3. Командата e  ви отвежда в края на дума.

  4. Операторът  y  взима (yank) текст, а  p  го поставя (paste).

  5. Ако въведете  R  , докато сте в нормален режим, преминавате в режим за
     заместване, докато натиснете <ESC>.

  6. Ако напишете ":set xxx", задавате настройката "xxx".  Ето някои настройки:
  	'ic' 'ignorecase'	Търсенето не прави разлика между главни и малки букви
	'is' 'incsearch'	Показва частични съвпадения на търсеното
	'hls' 'hlsearch'	Осветява всички намерени съвпадения
     Можете да ползвате кратките или дългите наименувания на настройките

  7. Поставете "no" отпред за да изключите настройка:   :set noic

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Урок 1.7.1: КАК ДА НАМЕРИМ ПОМОЩ


		      ** Ползвайте наличната система за помощ **
  
  Vim върви с изчерпателна система за помощ. За да започнете, опитайте някоя от
  следните три възможности:
	- натиснете клавиша <HELP> (ако имате такъв на клавиатурата си)
	- натиснете клавиша <F1> (ако имате такъв на клавиатурата си)
	- напишете   :help <ENTER>

  Прочетете текста в прозореца за помощ, за да разберете как работи системата.
  Натиснете CTRL-W CTRL-W (два пъти CTRL-W), за да прескочите от един прозорец в друг.
  Въведете    :q <ENTER>  , за да затворите прозореца за помощ. 

  Можете да намерите помощ по всякакъв въпрос, като напишете
  ":help" именакоманда.  Опитайте следните (не забравяйте да натискате <ENTER>):

	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Урок 1.7.2: СЪЗДАЙТЕ СКРИПТ ЗА СТАРТИРАНЕ


			  ** Включване на възможностите на Vim **

  Vim има много повече възможности от Vi, но по подразбиране повечето от тях не
  са включени. За да започнете да ползвате тези възможности, трябва да
  създадете файл, наречен "vimrc".

  1. Създайте вашия файл "vimrc". В зависимост от вашата операционна система:
	:e ~/.vimrc	за всеки вид Unix
	:e ~/_vimrc	за MS-Windows

  2. Сега прочетете съдържанието на примерния файл "vimrc":
	:r $VIMRUNTIME/vimrc_example.vim

  3. Запишете файла с:
	:w

  Следващият път като пуснете Vim той ще осветява текста във файловете, които
  отваряте в зависимост от синтаксиса им. Можете да добавите всичките си
  предпочитани настройки в този файл. За повече информация, въведете  
  :help vimrc-intro

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     Урок 1.7.3: ДОВЪРШВАНЕ


	      ** Довършване на команди с CTRL-D и <TAB> **

  1. Уверете се, че Vim е в несъвместим режим:  :set nocp

  2. Вижте какви файлове има в папката ви:  :!ls   или  :!dir

  3. Въведете началото на команда:  :e

  4. Натиснете  CTRL-D  и Vim ще ви покаже команди, започващи с "e".

  5. Натиснете <TAB>  и Vim ще допълни командата до ":edit".

  6. Сега добавете празно пространство и началото на името на съществуващ файл:
     :edit FIL

  7. Натиснете <TAB>.  Vim ще допълни името (ако е единствено).

Важно! Допълването работи за много команди. Просто натиснете CTRL-D и/или 
       <TAB>.  Особено полезно е при намиране на помощ  :help .

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       Урок 7 ОБОБЩЕНИЕ


  1. Напишете  :help  или натиснете <F1> или <Help>  за да отворите помощния
     прозорец.

  2. Напишете  :help cmd  , за да намерите помощ за  cmd .

  3. Натиснете  CTRL-W CTRL-W  , за да прескочите в друг прозорец.
  4. Напишете  :q  , за да затворите помощния прозорец.

  5. Създайте файл за стартиране vimrc, за да запазите предпочитаните от вас
     настройки.

  6. Когато въвеждате команда след  :  , натиснете CTRL-D , за да видите
     възможностите за допълване. Натиснете  <TAB> , за да използвате някоя от
     предложените възможности за допълване.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  С това завършва Самоучителят на Vim. Той бе предназначен да даде кратък
  преглед на текстовия редактор Vim. Съвсем достатъчно, за да можете да
  ползвате редактора лесно. Самоучителят е доста непълен, понеже Vim има много
  повече команди. Сега прочете наръчника за потребителя: ":help user-manual".

  Препоръчваме следната книга за по-нататъшно четене:
	Vim - Vi Improved - от Steve Oualline
	Издател: New Riders
  Това е първата книга, изцяло посветена на Vim. Особено полезна е за
  начинаещи. В нея ще намерите много примери и картинки.
  Вижте https://iccf-holland.org/click5.html

  Следната книга е по-стара и по-скоро за Vi отколкото за Vim, но също се препоръчва:
	Learning the Vi Editor - от Linda Lamb
  Издател: O'Reilly & Associates Inc.
  Това е книга, която ще ви запознае с почти всичко във Vi.
  Шестото издание включва и информация за Vim.

  Този самоучител е написан от Michael C. Pierce и Robert K. Ware,
  Colorado School of Mines, като използва идеи предоставени от Charles Smith,
  Colorado State University.  E-mail: <EMAIL>.

  Променен за Vim от Bram Moolenaar.

  Превод от Красимир Беров <<EMAIL>>, юли 2016.
  Този превод е подарък за сина ми Павел и е посветен на българските деца.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

