===============================================================================
версия 1.7      =  ДОБРО  ПОЖАЛОВАТЬ  НА  ЗАНЯТИЯ  ПО  РЕДАКТОРУ  Vim  =
===============================================================================
=				ГЛАВА  ПЕРВАЯ				      =
===============================================================================

   Программа Vim -- это очень мощный текстовый редактор, имеющий множество
   команд, и все их просто невозможно описать в рамках этого учебника.
   Данный же учебник призван объяснить те команды, которые позволят вам с
   лёгкостью использовать программу Vim в качестве редактора общего назначения.
   На освоение материалов этого учебника потребуется около 30 минут, но это
   зависит от того, сколько времени вы посвятите практическим занятиям.

   Внимание! Выполняя задания уроков, вы будете изменять текст в этом файле,
   поэтому прежде чем продолжить, создайте копию файла. Тогда можно будет
   практиковаться столько, сколько это потребуется. Если вы воспользовались
   командой "vimtutor" для открытия этого учебника, значит, копия уже создана.

   Важно помнить, что этот учебник предназначен для практического обучения.
   Это означает, что вы должны применять команды для того, чтобы как следует
   их изучить. Если вы просто прочитаете этот текст, то не запомните команды!
   Теперь, убедившись, что не включена клавиша <CapsLock>, нажмите клавишу  j
   несколько раз, так, чтобы урок 1.1.1 полностью поместился на экране.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.1.1. ПЕРЕМЕЩЕНИЕ КАРЕТКИ

** Чтобы перемещать каретку в указанных направлениях, нажмите клавиши h,j,k,l **
	     ^		   Подсказка.
	     k		      Клавиша h слева и удобна для перемещения влево.
       < h	 l >	      Клавиша l справа и удобна для перемещения вправо.
	     j		      Клавиша j похожа на стрелку "вниз".
	     v
  1. Перемещайте каретку в разных направлениях, пока не ощутите уверенность.

  2. Удерживайте нажатой клавишу "вниз" (j) для беспрерывного перемещения
	каретки. Теперь вы знаете, как перейти к следующему уроку.

  3. Используя клавишу "вниз", то есть  j  , перейдите к уроку 1.1.2.

Совет.
    Если вы не уверены в правильности набранного текста, нажмите клавишу <ESC>,
	чтобы переключить редактор в режим команд. После этого повторите набор.

Примечание.
    Клавиши управления курсором (стрелки) также должны работать. Но учтите, что
	выполнять перемещение каретки клавишами h j k l намного быстрее, стоит
	только немного потренироваться.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.1.2. ЗАВЕРШЕНИЕ РАБОТЫ ПРОГРАММЫ

 ВНИМАНИЕ! Перед выполнением описанных ниже действий, прочтите урок полностью!

  1. Нажмите клавишу  <ESC>  (чтобы быть уверенным, что программа находится в
	режиме команд).

  2. Наберите		:q! <ENTER>
     Это означает, что надо набрать три символа  :q!  и нажать клавишу <ВВОД>
     Исполнение этой команды вызовет завершение работы редактора
	БЕЗ СОХРАНЕНИЯ любых сделанных изменений.

  3. В приглашении командной оболочки наберите команду, которой вы открывали
	этот учебник. Это может быть  vimtutor <ENTER>

  4. Если уверены в том, что поняли смысл вышесказанного, выполните шаги
	с 1 до 3, чтобы завершить работу и снова запустить редактор.

Примечание.
    По команде  :q! <ENTER>  будут сброшены любые сделанные изменения. Через
	несколько уроков вы узнаете, как сохранять изменения в файл.

  5. Переместите каретку вниз к уроку 1.1.3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Урок 1.1.3. РЕДАКТИРОВАНИЕ - УДАЛЕНИЕ ТЕКСТА

	  ** Чтобы удалить символ под кареткой, нажмите клавишу  x  **

  1. Переместите каретку к строке помеченной --->.

  2. Чтобы исправить ошибки, перемещайте каретку, пока она не окажется над
	удаляемым символом.

  3. Нажмите клавишу  x  для удаления требуемого символа.

  4. Повторите шаги со 2 по 4, пока строка не будет исправлена.


---> От тттопота копытт пппыль ппо ппполю леттитт.

  5. Теперь, когда строка исправлена, переходите к уроку 1.1.4.

Примечание.
    В ходе этих занятий не пытайтесь сразу всё запоминать, учитесь в процессе
	работы.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Урок 1.1.4. РЕДАКТИРОВАНИЕ - ВСТАВКА ТЕКСТА

		** Чтобы вставить текст, нажмите клавишу  i  **

  1. Переместите каретку к первой строке помеченной --->.

  2. Чтобы сделать первую строку идентичной второй, установите каретку на тот
	символ, ПЕРЕД которым следует вставить текст.

  3. Нажмите клавишу  i  и наберите текст, который требуется вставить.

  4. После исправления каждого ошибочного слова, нажмите клавишу <ESC>
	для переключения в режим команд.
     Повторите шаги со 2 по 4, пока предложение не будет исправлено полностью.


---> Часть текта в строке бесследно .
---> Часть текста в этой строке бесследно пропало.


  5. Когда освоите вставку текста, переходите к уроку 1.1.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Урок 1.1.5. РЕДАКТИРОВАНИЕ - ДОБАВЛЕНИЕ ТЕКСТА

		** Чтобы добавить текст, нажмите клавишу  A  **

  1. Переместите каретку к первой строке помеченной --->.
     Сейчас неважно, на каком символе расположена каретка в этой строке.

  2. Нажмите клавишу  A   и наберите текст, который требуется добавить.

  3. После добавления текста нажмите клавишу <ESC> для возврата в режим команд.

  4. Переместите каретку на следующую строку, помеченную --->
	и повторите шаги со 2 по 3 для исправления этой строки.

---> Часть текста в этой строке бессле
     Часть текста в этой строке бесследно пропало.
---> Здесь также недостаёт час
     Здесь также недостаёт части текста.

  5. Когда освоите добавление текста, переходите к уроку 1.1.6.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  УРОК 1.1.6. РЕДАКТИРОВАНИЕ И ЗАПИСЬ ФАЙЛА

    ** Чтобы сохранить файл и закрыть редактор, используйте команды  :wq  **

 ВНИМАНИЕ! Перед выполнением описанных ниже действий, прочтите урок полностью!

  1. Завершите работу редактора Vim, как указано в уроке 1.1.2  -  :q!
     Если есть доступ к другому терминалу, то там можете сделать следующее:

  2. В приглашении командной оболочки введите команду  vim tutor <ENTER>
	где vim - команда для запуска редактора Vim, а tutor - наименование
	файла для редактирования. Укажите такой файл, который можно изменять.

  3. Вставляйте и удаляйте текст, как описано в предыдущих уроках.

  4. Сохраните этот изменённый файл и завершите работу программы Vim,
	набрав команду  :wq <ENTER>

  5. Если вы вышли из vimtutor на шаге 1, перезапустите vimtutor и переходите
	далее к резюме.

  6. После того как вы прочли и поняли вышесказанное, выполните описанные шаги.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.1

  1. Каретку можно перемещать либо клавишами со стрелками, либо клавишами hjkl.
	h (влево)	j (вниз)	k (вверх)	l (вправо)

  2. Чтобы запустить редактор Vim из приглашения командной оболочки, наберите
	vim ФАЙЛ <ENTER>

  3. Чтобы завершить работу редактора Vim, выполните одно из следующих:
	<ESC>   :q!  <ENTER>	по этой команде не будут сохранены изменения;
     или
	<ESC>   :wq  <ENTER>	по этой команде будут сохранены изменения.

  4. Чтобы удалить символ под кареткой, нажмите клавишу  x  в режиме команд.

  5. Чтобы вставить текст перед кареткой - i  наберите вставляемый текст  <ESC>
     Чтобы добавить текст в конце строки - A  наберите добавляемый текст  <ESC>

Примечание.
    По нажатию клавиши <ESC> будет выполнено переключение редактора в режим
	команд с прерыванием обработки любой ранее набранной команды.

Теперь переходите к уроку 1.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   Урок 1.2.1. КОМАНДЫ УДАЛЕНИЯ

	** Чтобы удалить слово под кареткой, используйте команду  dw  **

  1. Переключите редактор в режим команд, нажав клавишу <ESC>.

  2. Переместите каретку к строке помеченной --->.

  3. Установите каретку на начало слова, которое следует удалить.

  4. Наберите  dw  для удаления этого слова.

Примечание.
    При наборе буквы  d  она отобразится справа в самой нижней строке, и
	программа будет ожидать ввода следующей команды, в данном случае -  w
    Если что-то не получается, нажмите клавишу <ESC> и начните сначала.

---> Несколько слов рафинад в этом предложении автокран излишни.

  5. Повторите шаги 3 и 4, пока не исправите все ошибки, и переходите к
	уроку 1.2.2


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Урок 1.2.2. ЕЩЁ ОДНА КОМАНДА УДАЛЕНИЯ


      ** Чтобы удалить текст до конца строки, используйте команду  d$  **

  1. Переключите редактор в режим команд, нажав клавишу <ESC>.

  2. Переместите каретку к строке помеченной --->.

  3. Установите каретку в конце корректного предложения (ПОСЛЕ первой точки).

  4. Наберите  d$  для удаления остатка строки.


---> Кто-то набрал окончание этой строки дважды. окончание этой строки дважды.


  5. Чтобы лучше разобраться в том как это происходит, обратитесь к уроку 1.2.3.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.2.3. ОПЕРАТОРЫ И ОБЪЕКТЫ

  Многие команды, изменяющие текст, являются составными и формируются из
    оператора и объекта, к которому применяется этот оператор.
  Так, например, формат команды удаления с оператором  d  следующий:

  	d   объект
  где
    d      - оператор удаления;
    объект - область текста (указаны ниже), к которой будет применён оператор.

  Краткий перечень объектов:
    w - от позиции каретки до конца слова, включая последующий пробел;
    e - от позиции каретки до конца слова, исключая последующий пробел;
    $ - от позиции каретки до конца строки, включая последний символ.

  Таким образом, ввод команды  de  вызовет удаление текста от позиции каретки
    до конца слова.

Примечание.
    Если в режиме команд, без ввода оператор, нажать клавишу с символом,
	с которым ассоциирован объект, то каретка будет перемещена так, как
	указано в перечне объектов.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	      Урок 1.2.4. ПРИМЕНЕНИЕ СЧЁТЧИКА СОВМЕСТНО С ОБЪЕКТАМИ

      ** Чтобы перемещение каретка выполнялось необходимое количество раз,
		укажите перед объектом требуемое число **


  1. Установите каретку на начало строки помеченной --->.

  2. Наберите  2w  для перемещения каретки вперёд к началу второго слова.

  3. Наберите  3e  для перемещения каретки вперёд к концу третьего слова.

  4. Наберите  0  (ноль) для перемещения каретки к началу строки.

  5. Повторите шаги 2 и 3 с различными значениями чисел.


---> Обычная строка из слов, чтобы вы на ней потренировались перемещать каретку.


  6. Когда освоите это, переходите к уроку 1.2.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	   Урок 1.2.5. ПРИМЕНЕНИЕ СЧЁТЧИКА ДЛЯ МНОЖЕСТВЕННОГО УДАЛЕНИЯ

 ** Чтобы применить оператор несколько раз, укажите число требуемых повторов **

  Используя приведённые ранее составные команды удаления и перемещения, укажите
    перед объектом число повторов выполнения операции удаления.

	 d   число   объект

  1. Установите каретку на первом слове из прописных букв в строке со --->

  2. Наберите  d2w  для удаления двух идущих друг за другом слов из прописных
	букв.

  3. Повторите шаги 1 и 2 с указанием других числовых значений, чтобы удалить
	группы слов из прописных букв одной командой.


---> эта АБВ ГД строка ЕЖЗИ КЛ МНО очищена от П РС ТУФ лишних слов.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.2.6. ОПЕРАЦИИ СО СТРОКАМИ

	  ** Чтобы удалить строку целиком, используйте команду  dd **

  Так как часто требуется выполнять удаление всей строки целиком, создатели
    редактора решили облегчить этот процесс, и предложили для этого просто
    дважды нажать на клавишу с буквой d.

  1. Переместите каретку к строке номер два, помеченной --->.
  2. Наберите  dd  для удаления строки.
  3. Теперь переместите каретку к строке номер четыре, помеченной --->.
  4. Наберите  2dd  для удаления двух строк подряд.

--->  1)  Летом я хожу на стадион,
--->  2)  О, как внезапно кончился диван!
--->  3)  Я болею за "Зенит", "Зенит" - чемпион!
--->  4)  Печально я гляжу на наше поколенье!
--->  5)  Его грядущее - иль пусто, иль темно...
--->  6)  Я сижу на скамейке в ложе "Б"
--->  7)  И играю на большой жестяной трубе.

Дублирование оператора для обработки целой строки применяется и с другими
    операторами, о которых говорится далее.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Урок 1.2.7. КОМАНДА ОТМЕНЫ

   ** Чтобы отменить результат действия предыдущей команды, нажмите клавишу u
	  Чтобы отменить правки для всей строки, нажмите клавишу U **

  1. Установите каретку на первой ошибке, в строке помеченной --->
  2. Нажмите клавишу  x  для удаления первого ошибочного символа.
  3. Теперь нажмите клавишу  u  для отмены последней выполненной команды.
  4. Исправьте все ошибки в строке, используя команду  x .
  5. Теперь нажмите клавишу  U  , чтобы вернуть всю строку в исходное состояние.
  6. Нажмите клавишу  u  несколько раз для отмены команды  U
	и предыдущих команд.
  7. Теперь нажмите клавиши CTRL-R (т. е. удерживая нажатой клавишу CTRL,
	нажмите клавишу r) несколько раз для возврата действий команд.


---> Испрравьте оошибки в этойй строке и вернитте их сс помощьью "отмены".


  8. Это очень нужные и полезные команды.

Далее переходите к резюме урока 1.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.2

  1. Чтобы удалить слово, установите каретку в его начало и наберите	dw
  2. Чтобы удалить текст от позиции каретки до конца слова, наберите	de
  3. Чтобы удалить текст от позиции каретки до конца строки, наберите	d$
  4. Чтобы удалить всю строку целиком, наберите				dd

  5. Чтобы переместить каретку за один раз на некоторое количество объектов,
	укажите их число, например,  2w
  6. Формат команд изменения:
            оператор	[число]   объект
     где
      оператор - необходимые действия, например,  d  для удаления;
      [число]  - количество подпадающих под действие оператора объектов,
		    если не указано, то один объект;
      объект   - на что воздействует оператор, например,  w  (слово),
		    $ (всё, что есть до конца строки) и т. п.

  7. Чтобы переместить каретку к началу строки, нажмите клавишу  0  (ноль)

  8. Чтобы отменить предшествующие действия, нажмите    u  (строчная буква u)
     Чтобы отменить все изменения в строке, нажмите	U  (прописная буква U)
     Чтобы вернуть отменённые изменения, нажмите	CTRL-R
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   Урок 1.3.1. КОМАНДА ВСТАВКИ

      ** Чтобы вставить последний удалённый текст, наберите команду  p  **

  1. Переместите каретку к первой строке помеченной --->.

  2. Наберите  dd  , чтобы удалить строку, при этом она будет автоматически
	помещена в специальный регистр редактора Vim.

  3. Установите каретку на строку ВЫШЕ той, в которой следует вставить
	удалённую строку.

  4. Убедитесь, что программа в режиме команд и нажмите клавишу  p  для вставки
	строки ниже позиции каретки.

  5. Повторите шаги со 2 по 4, пока не расставите все строки в нужном порядке.

---> г) И лучше выдумать не мог.
---> б) Когда не в шутку занемог,
---> в) Он уважать себя заставил
---> а) Мой дядя самых честных правил


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Урок 1.3.2. КОМАНДА ЗАМЕНЫ

   ** Чтобы заменить символ под кареткой, наберите  r  и заменяющий символ **

  1. Переместите каретку к первой строке помеченной --->.

  2. Установите каретку так, чтобы она находилась над первым ошибочным символом.

  3. Нажмите клавишу  r  и затем наберите символ, исправляющий ошибку.

  4. Повторите шаги 2 и 3, пока первая строка не будет соответствовать второй.


--->  В момегт набтра этой чтроки кое0кто с трудом попвдал по клваишам!
--->  В момент набора этой строки кое-кто с трудом попадал по клавишам!


  5. Теперь переходите к уроку 1.3.3.

Примечание.
    Помните, что вы должны учиться в процессе работы, а не просто зубрить.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Урок 1.3.3. ОПЕРАТОР ИЗМЕНЕНИЯ

	  ** Чтобы изменить окончание слова, наберите команду  ce  **

  1. Переместите каретку к первой строке помеченной --->.

  2. Установите каретку над буквой  o  в слове  "сола".

  3. Наберите команду  ce  и исправьте слово (в данном случае, наберите "лов").

  4. Нажмите клавишу <ESC> и переместите каретку к следующей ошибке (к первому
	символу, начиная с которого надо изменить окончание слова).

  5. Повторите шаги 3 и 4 пока первая строка не будет соответствовать второй.

---> Несколько сола в эьгц строке тпгшцбь редалзкуюиесвх.
---> Несколько слов в этой строке требуют редактирования.

Примечание.
    Обратите внимание, что по команде  ce  не только удаляется часть слова,
	но и происходит переключение редактора в режим вставки.
    По команде  cc  будет выполнятся то же самое, но для целой строки.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
       УРОК 1.3.4. ЕЩЁ НЕСКОЛЬКО СПОСОБОВ РАБОТЫ С ОПЕРАТОРОМ ИЗМЕНЕНИЯ  c

** К оператору изменения применимы те же объекты, что и к оператору удаления **

  1. Оператор изменения работает аналогично оператору удаления. Формат команды:

       c   [число]   объект

  2. Объекты - это то же самое, что и ранее: w (слово), $ (конец строки) и т. п.

  3. Переместите каретку к первой строке помеченной --->.

  4. Установите каретку на первой ошибке.

  5. Наберите  c$  и отредактируйте первую строку так, чтобы она совпадала со
	второй, после чего нажмите клавишу <ESC>.

---> Окончание этой строки нужно сделать похожим как во второй строке.
---> Окончание этой строки нужно исправить командой  c$ .

Примечание.
    Клавиша <BACKSPACE> может использоваться для исправления при наборе текста.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.3

  1. Чтобы вставить текст, который был только что удалён, наберите команду  p .
	Текст будет вставлен ПОСЛЕ позиции каретки (если была удалена строка,
	то она будет помещена в строке ниже строки с кареткой).

  2. Чтобы заменить символ под кареткой, наберите команду  r  и затем
	заменяющий символ.

  3. Операторы изменения изменяют указанный объект текста от позиции каретки
	до конечной точки перемещения.
     Например, по команде  ce  можно изменить текст от позиции каретки до конца
	слова, а по команде  c$  - до конца строки.

  4. Формат команд изменения:

	 c   [число]   объект

     где c - оператор изменения;
	 [число] - количество изменяемых объектов (необязательная часть);
         объект - объект текста, который будет изменён.

Теперь переходите к следующему уроку.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 УРОК 1.4.1. ИНФОРМАЦИЯ О ФАЙЛЕ И ПОЗИЦИЯ КАРЕТКИ

   ** Чтобы получить информацию о файле и позиции каретки, нажмите  CTRL-g  .
       Чтобы переместить каретку к заданной строке в файле, нажмите  SHIFT-G **

  ВНИМАНИЕ! Прочитайте весь урок, прежде чем выполнять любые действия!

  1. Удерживая клавишу  CTRL  , нажмите клавишу  g  . Внизу экрана появится
	сообщение с наименованием файла и номером строки, в которой находится
	каретка. Запомните этот номер строки, он потребуется на шаге 3.

  Примечание.
    Позиция каретки может отображаться в правом нижнем углу окна программы,
	если установлен параметр 'ruler' (см. :help 'ruler').

  2. Нажмите клавиши SHIFT-G для перемещения каретки на последнюю строку файла.
     Теперь наберите  gg  для перемещения каретки на первую строку файла.

  3. Наберите номер строки, которой был получен на шаге 1, и нажмите клавиши
	SHIFT-G. Каретка будет перемещена в ту строку, где она находилась,
	когда в первый раз были нажаты клавиши CTRL-g.

  4. Если вы запомнили всё вышесказанное, выполните шаги с 1 по 3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Урок 1.4.2. КОМАНДЫ ПОИСКА

   ** Чтобы что-то найти, наберите команду / и затем введите искомую фразу **

  1. В режиме команд наберите символ  /  . Обратите внимание, что этот символ
	будет отображаться внизу экрана. Так же, как и при наборе команды  :

  2. Теперь наберите ошшшибка <ENTER>. Это то слово, которое требуется найти.

  3. Чтобы повторить поиск искомого слова, просто нажмите клавишу  n  .
     Чтобы искать это слово в обратном направлении, нажмите клавиши  SHIFT-N  .

  4. Если требуется сразу выполнить поиск в обратном направлении, используйте
	команду  ?  вместо команды  /  .

  5. Чтобы вернуться туда, откуда был начат поиск, нажмите несколько раз
	клавиши  CTRL-O  . Для перехода вперёд, используйте команду  CTRL-I  .

---> "ошшшибка" это не способ написания слова "ошибка";  ошшшибка это ошибка.

Примечание.
    Если будет достигнут конец файла, то поиск будет продолжен от начала файла.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.4.3. ПОИСК ПАРНЫХ СКОБОК

      ** Чтобы найти парную скобку для (, [ или {, наберите команду  %  **

  1. Поместите каретку на любой из скобок (, [ или { в строке помеченной --->.

  2. Теперь нажмите на клавиатуре клавишу с символом  %  .

  3. Каретка будет перемещена на парную скобку для той скобки, на которой
	установлена каретка.

  4. Наберите  %  для возврата каретки назад к первой парной скобке.


---> В этой ( строке есть такие (, такие [ ] и { такие } скобки. ))


Примечание.
    Это очень удобно при отладке программ, когда в коде пропущены скобки!




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Урок 1.4.4. СПОСОБ ЗАМЕНЫ СЛОВ

     ** Чтобы "что-то" заменить "чем-то", наберите команду :s/что/чем/g **

  1. Переместите каретку к строке помеченной --->.

  2. Наберите  :s/уводю/увожу <ENTER> . Обратите внимание на то, что по этой
	команде будет замена только первого найденного вхождение в строке.

  3. Теперь наберите  :s/уводю/увожу/g , добавленный флаг  'g'  означает
	замена во всей строке. Будет выполнена замена всех найденных в строке
	совпадений.

---> Я уводю к отверженным селеньям, я уводю сквозь вековечный стон, я уводю
     к забытым поколеньям.

  4. Чтобы заменить все вхождения искомого слова в каком-то диапазоне строк,
     наберите  :#,#s/что/чем/g	  где #,# - номер начальной и конечной строки
				  диапазона, в котором будет выполнена замена.
     Наберите  :%s/что/чем/g	чтобы заменить все вхождения во всём файле.
     Наберите  :%s/что/чем/gc	чтобы выдавался запрос подтверждения
				перед каждой заменой.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.4

  1. По приведённым ниже командам будет выполнено:
     CTRL-g - вывод информации о файле и текущей позиции каретки в этом файле
     SHIFT-G - переход на последнюю строку файла
     номер и SHIFT-G - переход к строке с указанным номером
     gg - переход на первую строку файла
  2. При вводе символа  /  с последующим набором слова, будет выполнен поиск
	этого слова ВПЕРЁД по тексту.
     При вводе символа  ?  с последующим набором слова, будет выполнен поиск
	этого слова НАЗАД по тексту.
     После показа первого совпадения, нажмите  n  для перехода к следующему
	слову в том же направлении поиска или SHIFT-N для поиска в
	противоположном направлении.
     При нажатии клавиш  CTRL-O  будет возврат к предыдущему слову, а при
	нажатии клавиш  CTRL-I  будет переход к ранее найденному слову.
  3. При нажатии  %  , когда каретка на одной из скобок ( ), [ ] или { },
	будет найдена её парная скобка.
  4. Чтобы заменить первое найденное слово в строке, наберите	:s/что/чем
     Чтобы заменить все найденные слова в строке, наберите	:s/что/чем/g
     Чтобы заменить в указанными интервале строк, наберите	:#,#s/что/чем/g
     Чтобы заменить все найденные слова в файле, наберите	:%s/что/чем/g
     Чтобы запрашивалось подтверждение, добавьте флаг 'c'	:%s/что/чем/gc
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	       Урок 1.5.1. КАК ВЫЗВАТЬ ИЗ РЕДАКТОРА ВНЕШНЮЮ КОМАНДУ

** Чтобы была выполнена команда командной оболочки, наберите в редакторе  :! **

  1. Наберите уже знакомую команду  :  , чтобы установить каретку в командной
	строке редактора и ввести необходимую команду.

  2. Теперь наберите символ  !  (восклицательный знак). По этой команде будет
	вызвана указанная следующей внешняя команда командной оболочки.

  3. Например, наберите  ls  сразу после  !  и нажмите <ENTER>. Будет выведен
	перечень файлов в текущем каталоге. То есть будет выполнено точно то же
	самое, как если бы ввести команду ls в приглашении командной оболочки.
	Если в системе не поддерживается команда ls, то наберите команду  :!dir

Примечание.
    Таким способом можно выполнить любую внешнюю команду, в том числе и с
	указанием необходимых аргументов этой команды.

Важно.
    После ввода команды, начинающейся с  : , должна быть нажата клавиша <ENTER>
    В дальнейшем это может не указываться отдельно, но подразумеваться.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Урок 1.5.2. КАК ЗАПИСАТЬ ФАЙЛ

   ** Чтобы сохранить файл со всеми изменениями в тексте, наберите  :w ФАЙЛ **

  1. Наберите  :!dir  или  :!ls  для получения перечня файлов в текущем
	каталоге.  Как вы помните, после набора команды нажмите клавишу <ENTER>

  2. Придумайте название для файла, которое ещё не существует, например, TEST.

  3. Теперь наберите  :w TEST  (здесь TEST - это придуманное название файла).

  4. По этой команде будет полностью сохранён текущий файл ("tutor") под новым
	название "TEST". Чтобы проверить это, снова наберите команду :!dir  или
	:!ls  и просмотрите содержимое каталога.

Примечание.
    Если завершить работу редактора Vim и затем запустить его снова с файлом
	TEST (т. е. набрать команду  vim TEST ), этот файл будет точной копией
	учебника в тот момент, когда он был сохранён.

  5. Теперь удалите этот файл, набрав в редакторе команду  :!del TEST
	(для ОС Windows) или  :!rm TEST  (для UNIX-подобных ОС)

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Урок 1.5.3. ВЫБОРОЧНАЯ ЗАПИСЬ СТРОК

     ** Чтобы сохранить часть файла, нажмите клавишу  v  , выделите строки
			 и наберите команду  :w ФАЙЛ **

  1. Переместите каретку на эту строку.

  2. Нажмите клавишу  v  и переместите каретку ниже к строке с пятым пунктом.
	Обратите внимание, что текст подсвечен.

  3. Нажмите клавишу с символом  :  и внизу экрана появится  :'<,'>  .

  4. Наберите команду  w TEST  (здесь TEST - файл, который ещё не существует).
	В командной строке должно быть  :'<,'>w TEST  и нажмите клавишу <ENTER>

  5. По этой команде выбранные строки будут записаны в файл TEST. Убедитесь в
	наличии этого файла, воспользовавшись командой  :!dir  или  :!ls  .
     Не удаляйте этот файл, он потребуется на следующем уроке.
Примечание.
    По нажатию клавиши  v  выполняется переключение в визуальный режим. Чтобы
	изменить размер выбранной области, нужно переместить каретку.
    К выделенному фрагменту можно применить любой оператор, например,  d
	для его удаления.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Урок 1.5.4. СЧИТЫВАНИЕ И ОБЪЕДИНЕНИЕ ФАЙЛОВ

      ** Чтобы вставить содержащийся в файле текст, наберите  :r ФАЙЛ  **

  1. Установите каретку над этой строкой.

Внимание!
    После выполнения описанного в пункте 2 вы увидите текст из урока 1.5.3.
	Переместите каретку вниз по тексту до текущего урока.

  2. Теперь считайте содержимое файла TEST, используя команду  :r TEST , здесь
	TEST - это наименование файла.

  3. Для проверки, что содержимое файла было вставлено, переместите каретку
	вверх по тексту и удостоверьтесь, что теперь здесь два урока 1.5.3. -
	исходный и из файла TEST.

Примечание.
    Вставить можно и результат внешней команды. Например, по команде  :r !ls
	будет получен вывод команды ls и вставлен ниже позиции каретки.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.5

  1. По команде  :!command  будет исполнена указанная внешняя команда.

      Некоторые полезные примеры:
      	(Windows)	(UNIX)
	 :!dir		 :!ls	    - вывести перечень файлов в каталоге;
	 :!del ФАЙЛ	 :!rm ФАЙЛ  - удалить файл с указанным наименованием.

  2. По команде  :w ФАЙЛ  , текущий редактируемый файл будет записан
	с указанным наименованием.

  3. Используя команды  v  , перемещение каретки и  :w ФАЙЛ  можно сохранить
	визуально выделенные строки в файл с указанным наименованием.

  4. По команде  :r ФАЙЛ  будет прочитан файл с указанным наименованием
	и его содержимое помещено ниже позиции каретки.

  5. По команде  :r !dir  будет получен вывод команды dir и помещён ниже
	позиции каретки.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      УРОК 1.6.1. КОМАНДЫ ДЛЯ СОЗДАНИЯ СТРОК

 ** Чтобы открыть новую строку с переключением в режим вставки, наберите  o  **

  1. Переместите каретку вниз, к первой строке помеченной --->.

  2. Нажмите клавишу  o   для того, чтобы создать пустую строку НИЖЕ позиции
	каретки и переключить редактор в режим вставки.

  3. Теперь наберите какой-нибудь текст и нажмите клавишу <ESC> для выхода из
	режима вставки.

---> После нажатия  o  ниже будет открыта новая пустая строка в режиме вставки.


  4. Для создания строки ВЫШЕ позиции каретки, наберите прописную букву  O ,
	вместо строчной буквы  o . Попробуйте это сделать для строки ниже.


---> Создайте новую строку над этой, поместив сюда каретку и нажав  SHIFT-O.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    УРОК 1.6.2. КОМАНДА ДЛЯ ДОБАВЛЕНИЯ ТЕКСТА

	 ** Чтобы вставить текст после позиции каретки, наберите  a  **

  1. Переместите каретку вниз, в начало первой строки помеченной --->.
  2. Нажмите клавишу  e  , пока каретка не окажется на последнем символе слова
	"стро".

  3. Нажмите клавишу  a  для добавления текста ПОСЛЕ символа, находящегося под
	кареткой.

  4. Допишите слово как в строке ниже. Нажмите клавишу <ESC> для выхода из
	режима вставки.

  5. Используйте клавишу  e  для перехода к следующему незавершённому слову
	и повторите действия, описанные в пунктах 3 и 4.

---> На этой стро вы можете попрактиков в добавле текста.
---> На этой строке вы можете попрактиковаться в добавлении текста.

Примечание.
    По команде  a , i  и  A  будет выполнено переключение в один и тот же режим
	вставки, различие только в том, где вставляются символы.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Урок 1.6.3. ЕЩЁ ОДИН СПОСОБ ЗАМЕНЫ

	 ** Чтобы заменить несколько символов в строке, наберите R  **

  1. Переместите каретку в начало первого слова xxx в строке помеченной --->

  2. Теперь нажмите  SHIFT-R  и введите число, указанное ниже во второй строке,
	чтобы заменить символы  xxx.

  3. Нажмите клавишу <ESC> для выхода из режима замены. Заметьте, что остаток
	строки не был изменён.

  4. Повторите эти шаги для замены оставшихся слов  xxx.

---> При сложении числа 123 с числом xxx сумма будет xxx.
---> При сложении числа 123 с числом 456 сумма будет 579.


Примечание.
    Режим замены похож на режим вставки, но каждый введённый символ удаляет
	существующий символ в строке.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Урок 1.6.4. КОПИРОВАНИЕ И ВСТАВКА ТЕКСТА

** Чтобы копировать, используйте оператор  y  , чтобы вставить - команду  p  **

  1. Установите каретку после символов "а)" в строке, помеченной --->.
  2. Переключите редактор в визуальный режим командой  v  и переместите каретку
	вперёд до слова "первый".
  3. Нажмите клавишу  y  для копирования подсвеченного текста.
  4. Переместите каретку в конец следующей строки, набрав команду  j$ .
  5. Нажмите клавишу  p  для вставки текста. Затем наберите команду  a  ,
	напечатайте слово "второй" и нажмите клавишу <ESC>.
  6. Повторите шаги с 1 по 4, только установите каретку после слова "первый",
	выделите, скопируйте и вставьте слово " пункт.".

--->  а) Это первый пункт.
      б)

Примечание.
    Можно воспользоваться командой  yw  (оператор  y  и объект  w) для
	копирования одного слова.
    По команде  yy  будет скопирована целая строка, а по команде  p  вставлена.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Урок 1.6.5. УСТАНОВКА ПАРАМЕТРОВ

	 ** Чтобы при поиске или замене не учитывался регистр символов,
		      задайте соответствующие настройки **

  1. Найдите слово "игнорировать", набрав команду  /игнорировать <ENTER>.
     Повторите поиск несколько раз, нажимая клавишу  n  .

  2. Установите параметр 'ic' (игнорировать регистр), набрав команду  :set ic

  3. Ещё несколько раз повторите поиск слова "игнорировать", нажимая клавишу  n
     Заметьте, что теперь будут найдены слова "Игнорировать" и "ИГНОРИРОВАТЬ".

  4. Установите параметры 'hlsearch' и 'incsearch' командой  :set hls is

  5. Повторно введите команду поиска и посмотрите, что получится  /игнорировать

  6. Для возврата учёта регистра при поиске, введите команду  :set noic
Примечание.
    Для отключения подсветки совпадений, наберите команду  :nohlsearch
Примечание.
    Если требуется не учитывать регистр символов только единоразово, используйте
	ключ  \c  в команде поиска, например,  /игнорировать\c  <ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.6

  1. По команде  o  будет создана пустая строка ниже строки с кареткой
	и редактор будет переключен в режим вставки
     По команде  O  будет создана пустая строка выше строки с кареткой
	и редактор будет переключен в режим вставки
  2. По команде  a  выполняется вставки текста ПОСЛЕ позиции каретки.
     По команде  A  выполняется вставки текста в конце строки.

  3. По команде  e  выполняется установка каретки в конце слова.
  4. Оператор  y  используется для копирования текста, а по команде  p
	происходит вставка скопированного текста.

  5. При нажатии клавиш  SHIFT-R  выполняется переключение в режим замены,
	а отключение - нажатием клавиши  <ESC> .

  6. Наберите :set xxx для установки параметра 'xxx'.
 Вот некоторые параметры (можно указывать полные или сокращённые наименования):
	'ic'	'ignorecase'	игнорирование регистра символов при поиске
	'is'	'incsearch'	отображение частичных совпадений при поиске
	'hls'	'hlsearch'	подсветка всех совпадений при поиске

  7. Для сброса параметра, добавьте приставку "no" к его названию  :set noic
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    УРОК 1.7.1. ВСТРОЕННАЯ СПРАВОЧНАЯ СИСТЕМА

		** Используйте встроенную справочную систему **

  В редакторе Vim имеется мощная встроенная справочная система, и чтобы начать
    ей пользоваться, воспользуйтесь одним из трёх вариантов:
	- нажмите клавишу <HELP> (если она есть на клавиатуре)
	- нажмите клавишу <F1> (если она есть на клавиатуре)
	- наберите  :help <ENTER>

  Ознакомьтесь с информацией в окне справочной системы, чтобы получить
    представление о том, как работать с документацией.

  Нажмите  CTRL-w CTRL-w  для перемещения каретки из одного окна в другое окно.
  Наберите   :q <ENTER>  , чтобы закрыть окно справочной системы (когда каретка
    находится в этом окне).

  Можно найти описание для любого понятия или команды, задав соответствующий
    аргумент команде :help. Попробуйте следующее (не забудьте нажать <ENTER>):
	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Урок 1.7.2. СОЗДАНИЕ СТАРТОВОГО КОМАНДНОГО ФАЙЛА

		       ** Включим все возможности Vim **

  Редактор Vim более функционален по сравнению с редактором Vi, но большинство
    из этих возможностей отключены при запуске программы. Чтобы активировать
    весь потенциала редактора, необходимо создать файл "vimrc".

  1. Создайте новый файл "vimrc". Его расположение зависит от используемой
	системы:
	    :e ~/.vimrc			для UNIX
	    :e $VIM/_vimrc		для MS Windows

  2. Теперь добавьте в этот файл содержимое шаблонного файла "vimrc"
	    :r $VIMRUNTIME/vimrc_example.vim

  3. Запишите созданный вами файл "vimrc"
	    :w

  Теперь при следующем запуске редактора Vim будет включена подсветка
    синтаксиса. Все необходимые вам настройки могут быть добавлены в файл
    "vimrc".
  Чтобы получить подробную информацию, наберите  :help vimrc-intro
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  УРОК 1.7.3. ПОДСТАНОВКА КОМАНД

** Подстановка в командной строке выполняется нажатием клавиш CTRL-D и <TAB> **

  1. Отключите совместимость с редактором Vi
	    :set nocp
  2. Посмотрите, какие файлы есть в каталоге, набрав команду
	    :!ls  или  :!dir
  3. Наберите начало команды для открытия файла на редактирование  :e
  4. Нажмите клавиши  CTRL-D  , и будет показан перечень команд редактора Vim
	начинающихся с буквы "e".
  5. Нажмите клавиши  d<TAB>  , и будет подставлено полное название команды
	"edit".
  6. Теперь напечатайте пробел и начало наименования существующего файла
	    :edit TE
  7. Нажмите клавишу  <TAB>  и будет подставлено наименование файла, если оно
	уникальное.

Примечание.
    Подстановка работает для множества команд. Просто попробуйте нажать клавиши
	CTRL-D  и  <TAB>  для любой из команд редактора. Это особенно полезно
	для команды  :help  .

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 РЕЗЮМЕ УРОКА 1.7


  1. Чтобы открыть окно встроенной справочной системы редактора, наберите
	команду  :help  или нажмите клавишу <F1>, или клавишу <HELP>.

  2. Чтобы найти справочную информацию о какой-либо команде,
	наберите  :help cmd  (вместо "cmd" укажите наименование команды).

  3. Чтобы переместить каретку в другое окно, нажмите клавиши  CTRL-w CTRL-w  .

  4. Чтобы закрыть окна справочной системы (если оно активно), наберите  :q  .

  5. Чтобы при запуске всегда применялись необходимые вам настройки, создайте
	стартовый командный файл vimrc.

  6. При наборе команды, начинающейся с символа  :  , нажмите клавиши CTRL-D,
	чтобы просмотреть возможные варианты подстановки. Нажмите клавишу <TAB>
	для подстановки необходимого варианта.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  На этом можно завершить первую часть занятий посвящённых редактору Vim.
  Далее вы можете ознакомиться со второй частью занятий.

  Целью данного курса было дать краткий обзор редактора Vim, достаточный для
  того, чтобы не возникало сложностей при его использовании. Это далеко не
  полный обзор, поскольку в редакторе Vim есть ещё много-много команд.

  Чтобы расширить свои познания, ознакомьтесь с руководством пользователя,
  набрав команду  :help user-manual.

  Для дальнейшего чтения рекомендуется книга
	"Vim - Vi Improved", автор Steve Oualline, издательство New Riders.
  Она полностью посвящена редактору Vim и будет особенно полезна новичкам.
  В книге имеется множество примеров и иллюстраций.
  См. https://iccf-holland.org/click5.html

  Ещё одна книга более почтенного возраста и посвящена больше редактору Vi,
  чем редактору Vim, однако также рекомендуется к прочтению
	"Learning the Vi Editor", автор Linda Lamb,
				издательство O'Reilly & Associates Inc.
  Это хорошая книга, чтобы узнать всё, что только можно сделать в редакторе Vi.
  Шестое издание этой книги включает информацию о редакторе Vim.

  Эти уроки были составлены Michael C. Pierce и Robert K. Ware из Colorado
  School of Mines с учётом идей, предложенных Charles Smith из Colorado State
  University. E-mail: <EMAIL> (теперь недоступен).

   Для использования в редакторе Vim уроки были доработаны Bram Moolenaar

    Андрей Киселёв, перевод на русский язык, 2002, <<EMAIL>>
    Сергей Алёшин, перевод на русский язык, 2014, <<EMAIL>>
    Restorer, редактура, 2022, <<EMAIL>>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
