===============================================================================
= V e l k o m m e n   t i l   i n n f ø r i n g e n   i   V i m  --  Ver. 1.7 =
===============================================================================

     Vim er en meget kraftig editor med mange kommandoer, alt for mange til å
     kunne gå gjennom alle i en innføring som denne. Den er beregnet på å
     sette deg inn i bruken av nok kommandoer så du vil være i stand til lett
     å kunne bruke Vim som en editor til alle formål.

     Tiden som kreves for å gå gjennom denne innføringen tar ca. 25-30
     minutter, avhengig av hvor mye tid du bruker til eksperimentering.

     MERK:
     Kommandoene i leksjonene vil modifisere teksten. Lag en kopi av denne
     filen som du kan øve deg på (hvis du kjørte «vimtutor»-kommandoen, er
     dette allerede en kopi).

     Det er viktig å huske at denne innføringen er beregnet på læring gjennom
     bruk. Det betyr at du må utføre kommandoene for å lære dem skikkelig.
     Hvis du bare leser teksten, vil du glemme kommandoene!

     Først av alt, sjekk at «Caps Lock» IKKE er aktiv og trykk «j»-tasten for
     å flytte markøren helt til leksjon 1.1.1 fyller skjermen.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Leksjon 1.1.1:  FLYTTING AV MARKØREN


       ** For å flytte markøren, trykk tastene h, j, k, l som vist. **
	     ^
	     k		Tips: h-tasten er til venstre og flytter til venstre.
       < h	 l >	      l-tasten er til høyre og flytter til høyre.
	     j		      j-tasten ser ut som en pil som peker nedover.
	     v
  1. Flytt markøren rundt på skjermen til du har fått det inn i fingrene.

  2. Hold inne nedovertasten (j) til den repeterer.
     Nå vet du hvordan du beveger deg til neste leksjon.

  3. Gå til leksjon 1.1.2 ved hjelp av nedovertasten.

Merk: Hvis du blir usikker på noe du har skrevet, trykk <ESC> for å gå til
      normalmodus. Skriv deretter kommandoen du ønsket på nytt.

Merk: Piltastene skal også virke. Men ved å bruke hjkl vil du være i stand til
      å bevege markøren mye raskere når du er blitt vant til det. Helt sant!

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Leksjon 1.1.2: AVSLUTTE VIM


  !! MERK: Før du utfører noen av punktene nedenfor, les hele leksjonen!!

  1. Trykk <ESC>-tasten (for å forsikre deg om at du er i normalmodus).

  2. Skriv:	:q! <ENTER>.
     Dette avslutter editoren og FORKASTER alle forandringer som du har gjort.

  3. Når du ser kommandolinjen i skallet, skriv kommandoen som startet denne
     innføringen. Den er:   vimtutor <ENTER>

  4. Hvis du er sikker på at du husker dette, utfør punktene 1 til 3 for å
     avslutte og starte editoren på nytt.

MERK:  :q! <ENTER>  forkaster alle forandringer som du gjorde. I løpet av noen
       få leksjoner vil du lære hvordan du lagrer forandringene til en fil.

  5. Flytt markøren ned til leksjon 1.1.3.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Leksjon 1.1.3: REDIGERING AV TEKST -- SLETTING


	     ** Trykk  x  for å slette tegnet under markøren. **

  1. Flytt markøren til den første linjen merket med  --->.

  2. For å ordne feilene på linjen, flytt markøren til den er oppå tegnet som
     skal slettes.

  3. Trykk tasten  x  for å slette det uønskede tegnet.

  4. Repeter punkt 2 til 4 til setningen er lik den som er under.

---> Hessstennnn brrråsnudddde ii gaaata.
---> Hesten bråsnudde i gata.

  5. Nå som linjen er korrekt, gå til leksjon 1.1.4.

MERK: Når du går gjennom innføringen, ikke bare prøv å huske kommandoene, men
      bruk dem helt til de sitter.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Leksjon 1.1.4: REDIGERING AV TEKST -- INNSETTING


		    ** Trykk  i  for å sette inn tekst. **

  1. Flytt markøren til den første linjen som er merket med --->.

  2. For å gjøre den første linjen lik den andre, flytt markøren til den står
     på tegnet ETTER posisjonen der teksten skal settes inn.

  3. Trykk  i  og skriv inn teksten som mangler.

  4. Etterhvert som hver feil er fikset, trykk <ESC> for å returnere til
     normalmodus. Repeter punkt 2 til 4 til setningen er korrekt.

---> Det er tkst som mnglr .
---> Det er ganske mye tekst som mangler her.

  5. Når du føler deg komfortabel med å sette inn tekst, gå til oppsummeringen
     nedenfor.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Leksjon 1.1.5: REDIGERING AV TEKST -- LEGGE TIL


		    ** Trykk  A  for å legge til tekst. **

  1. Flytt markøren til den første linjen nedenfor merket --->.
     Det har ikke noe å si hvor markøren er plassert på den linjen.

  2. Trykk  A  og skriv inn det som skal legges til.

  3. Når teksten er lagt til, trykk <ESC> for å returnere til normalmodusen.

  4. Flytt markøren til den andre linjen markert med ---> og repeter steg 2 og
     3 for å reparere denne setningen.

---> Det mangler noe tekst p
     Det mangler noe tekst på denne linjen.
---> Det mangler også litt tek
     Det mangler også litt tekst på denne linjen.

  5. Når du føler at du behersker å legge til tekst, gå til leksjon 1.1.6.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Leksjon 1.1.6: REDIGERE EN FIL


	       ** Bruk	:wq  for å lagre en fil og avslutte. **

  !! MERK: Før du utfører noen av stegene nedenfor, les hele denne leksjonen!!

  1. Avslutt denne innføringen som du gjorde i leksjon 1.1.2:  :q!

  2. Skriv denne kommandoen på kommandolinja:  vim tutor <ENTER>
     «vim» er kommandoen for å starte Vim-editoren, «tutor» er navnet på fila
     som du vil redigere. Bruk en fil som kan forandres.

  3. Sett inn og slett tekst som du lærte i de foregående leksjonene.

  4. Lagre filen med forandringene og avslutt Vim med:	:wq <ENTER>

  5. Start innføringen på nytt og flytt ned til oppsummeringen som følger.

  6. Etter å ha lest og forstått stegene ovenfor: Sett i gang.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.1


  1. Markøren beveges ved hjelp av piltastene eller hjkl-tastene.
	 h (venstre)	 j (ned)     k (opp)	 l (høyre)

  2. For å starte Vim fra skall-kommandolinjen, skriv:	vim FILNAVN <ENTER>

  3. For å avslutte Vim, skriv:  <ESC> :q! <ENTER>  for å forkaste endringer.
		   ELLER skriv:  <ESC> :wq <ENTER>  for å lagre forandringene.

  4. For å slette tegnet under markøren, trykk:  x

  5. For å sette inn eller legge til tekst, trykk:
	 i    skriv innsatt tekst  <ESC>	sett inn før markøren
	 A    skriv tillagt tekst  <ESC>	legg til på slutten av linjen

MERK: Når du trykker <ESC> går du til normalmodus eller du avbryter en uønsket
      og delvis fullført kommando.

  Nå kan du gå videre til leksjon 1.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Leksjon 1.2.1: SLETTEKOMMANDOER


		    ** Trykk  dw  for å slette et ord. **

  1. Trykk <ESC> for å være sikker på at du er i normalmodus.

  2. Flytt markøren til den første linjen nedenfor merket --->.

  3. Flytt markøren til begynnelsen av ordet som skal slettes.

  4. Trykk  dw	og ordet vil forsvinne.

MERK: Bokstaven  d  vil komme til syne på den nederste linjen på skjermen når
      du skriver den. Vim venter på at du skal skrive w . Hvis du ser et annet
      tegn enn	d  har du skrevet noe feil; trykk <ESC> og start på nytt.

---> Det er agurk tre ord eple som ikke hører pære hjemme i denne setningen.
---> Det er tre ord som ikke hører hjemme i denne setningen.

  5. Repeter punkt 3 og 4 til den første setningen er lik den andre. Gå
     deretter til leksjon 1.2.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Leksjon 1.2.2: FLERE SLETTEKOMMANDOER


	     ** Trykk  d$  for å slette til slutten av linjen. **

  1. Trykk <ESC> for å være sikker på at du er i normalmodus.

  2. Flytt markøren til linjen nedenfor merket --->.

  3. Flytt markøren til punktet der linjen skal kuttes (ETTER første punktum).

  4. Trykk  d$	for å slette alt til slutten av linjen.

---> Noen skrev slutten på linjen en gang for mye. linjen en gang for mye.

  5. Gå til leksjon 1.2.3 for å forstå hva som skjer.






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Leksjon 1.2.3: OM OPERATORER OG BEVEGELSER


  Mange kommandoer som forandrer teksten er laget ut i fra en operator og en
  bevegelse. Formatet for en slettekommando med sletteoperatoren  d  er:

	d   bevegelse

  Der:
    d	      - er sletteoperatoren.
    bevegelse - er hva operatoren vil opere på (listet nedenfor).

  En kort liste med bevegelser:
    w - til starten av det neste ordet, UNNTATT det første tegnet.
    e - til slutten av det nåværende ordet, INKLUDERT det siste tegnet.
    $ - til slutten av linjen, INKLUDERT det siste tegnet.

  Ved å skrive	de  vil altså alt fra markøren til slutten av ordet bli
  slettet.

MERK:  Ved å skrive kun bevegelsen i normalmodusen uten en operator vil
       markøren flyttes som spesifisert.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 LEKSJON 1.2.4: BRUK AV TELLER FOR EN BEVEGELSE


 ** Ved å skrive et tall foran en bevegelse repeterer den så mange ganger. **

  1. Flytt markøren til starten av linjen markert ---> nedenfor.

  2. Skriv  2w	for å flytte markøren to ord framover.

  3. Skriv  3e	for å flytte markøren framover til slutten av det tredje
     ordet.

  4. Skriv  0  (null) for å flytte til starten av linjen.

  5. Repeter steg 2 og 3 med forskjellige tall.

---> Dette er en linje med noen ord som du kan bevege deg rundt på.

  6. Gå videre til leksjon 1.2.5.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Leksjon 1.2.5: BRUK AV ANTALL FOR Å SLETTE MER


     ** Et tall sammen med en operator repeterer den så mange ganger. **

  I kombinasjonen med sletteoperatoren og en bevegelse nevnt ovenfor setter du
  inn antall før bevegelsen for å slette mer:
	 d  nummer  bevegelse

  1. Flytt markøren til det første ordet med STORE BOKSTAVER på linjen markert
     med --->.

  2. Skriv  2dw  for å slette de to ordene med store bokstaver.

  3. Repeter steg 1 og 2 med forskjelling antall for å slette de etterfølgende
     ordene som har store bokstaver.

---> Denne ABC DE linjen FGHI JK LMN OP er nå Q RS TUV litt mer lesbar.

MERK: Et antall mellom operatoren  d  og bevegelsen virker på samme måte som å
      bruke bevegelsen uten en operator.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Leksjon 1.2.6: OPERERE PÅ LINJER


		 ** Trykk  dd  for å slette en hel linje. **

  På grunn av at sletting av linjer er mye brukt, fant utviklerne av Vi ut at
  det vil være lettere å rett og slett trykke to d-er for å slette en linje.

  1. Flytt markøren til den andre linjen i verset nedenfor.
  2. Trykk  dd	 å slette linjen.
  3. Flytt deretter til den fjerde linjen.
  4. Trykk  2dd  for å slette to linjer.

--->  1) Roser er røde,
--->  2) Gjørme er gøy,
--->  3) Fioler er blå,
--->  4) Jeg har en bil,
--->  5) Klokker viser tiden,
--->  6) Druer er søte
--->  7) Og du er likeså.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Leksjon 1.2.7: ANGRE-KOMMANDOEN


  ** Trykk  u  for å angre siste kommando,  U  for å fikse en hel linje. **

  1. Flytt markøren til linjen nedenfor merket ---> og plasser den på den
     første feilen.
  2. Trykk  x  for å slette det første uønskede tegnet.
  3. Trykk så  u  for å angre den siste utførte kommandoen.
  4. Deretter ordner du alle feilene på linjene ved å bruke kommandoen	x  .
  5. Trykk nå en stor  U  for å sette linjen tilbake til det den var
     originalt.
  6. Trykk  u  noen ganger for å angre	U  og foregående kommandoer.
  7. Deretter trykker du  CTRL-R  (hold CTRL nede mens du trykker R) noen
     ganger for å gjenopprette kommandoene (omgjøre angrekommandoene).

---> RReparer feiilene påå denne linnnjen oog erssstatt dem meed angre.

  8. Dette er meget nyttige kommandoer. Nå kan du gå til oppsummeringen av
     leksjon 1.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.2


  1. For å slette fra markøren fram til det neste ordet, trykk:  dw
  2. For å slette fra markøren til slutten av en linje, trykk:	d$
  3. For å slette en hel linje, trykk:	dd

  4. For å repetere en bevegelse, sett et nummer foran:  2w
  5. Formatet for en forandringskommando er:
	       operator  [nummer]  bevegelse
     der:
       operator  - hva som skal gjøres, f.eks.	d  for å slette
       [nummer]  - et valgfritt antall for å repetere bevegelsen
       bevegelse - hva kommandoen skal operere på, eksempelvis	w  (ord),
		   $  (til slutten av linjen) og så videre.

  6. For å gå til starten av en linje, bruk en null:  0

  7. For å angre tidligere endringer, skriv:		u  (liten u)
     For å angre alle forandringer på en linje, skriv:	U  (stor U)
     For å omgjøre angringen, trykk:			CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Leksjon 1.3.1: «LIM INN»-KOMMANDOEN


    ** Trykk  p  for å lime inn tidligere slettet tekst etter markøren **

  1. Flytt markøren til den første linjen med ---> nedenfor.

  2. Trykk  dd	for å slette linjen og lagre den i et Vim-register.

  3. Flytt markøren til c)-linjen, OVER posisjonen linjen skal settes inn.

  4. Trykk  p  for å legge linjen under markøren.

  5. Repeter punkt 2 til 4 helt til linjene er i riktig rekkefølge.

---> d) Kan du også lære?
---> b) Fioler er blå,
---> c) Intelligens må læres,
---> a) Roser er røde,



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Leksjon 1.3.2: «ERSTATT»-KOMMANDOEN


	 ** Trykk  rx  for å erstatte tegnet under markøren med x. **

  1. Flytt markøren til den første linjen nedenfor merket --->.

  2. Flytt markøren så den står oppå den første feilen.

  3. Trykk  r  og deretter tegnet som skal være der.

  4. Repeter punkt 2 og 3 til den første linjen er lik den andre.

---> Da dfnne lynjxn ble zkrevet, var det nøen som tjykket feite taster!
---> Da denne linjen ble skrevet, var det noen som trykket feile taster!

  5. Gå videre til leksjon 1.3.2.

MERK: Husk at du bør lære ved å BRUKE, ikke pugge.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Leksjon 1.3.3: «FORANDRE»-OPERATOREN


	   ** For å forandre til slutten av et ord, trykk  ce . **

  1. Flytt markøren til den første linjen nedenfor som er merket --->.

  2. Plasser markøren på  u  i «lubjwr».

  3. Trykk  ce	og det korrekte ordet (i dette tilfellet, skriv «injen»).

  4. Trykk <ESC> og gå til det neste tegnet som skal forandres.

  5. Repeter punkt 3 og 4 helt til den første setningen er lik den andre.

---> Denne lubjwr har noen wgh som må forkwåp med «forækzryas»-kommandoen.
---> Denne linjen har noen ord som må forandres med «forandre»-kommandoen.

Vær oppmerksom på at  ce  sletter ordet og går inn i innsettingsmodus.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Leksjon 1.3.4: FLERE FORANDRINGER VED BRUK AV c


 ** Forandringskommandoen blir brukt med de samme bevegelser som «slett». **

  1. Forandringsoperatoren fungerer på samme måte som «slett». Formatet er:

	 c    [nummer]	 bevegelse

  2. Bevegelsene er de samme, som for eksempel	w  (ord) og  $	(slutten av en
     linje).

  3. Gå til den første linjen nedenfor som er merket --->.

  4. Flytt markøren til den første feilen.

  5. Skriv  c$	og skriv resten av linjen lik den andre og trykk <ESC>.

---> Slutten på denne linjen trenger litt hjelp for å gjøre den lik den neste.
---> Slutten på denne linjen trenger å bli rettet ved bruk av c$-kommandoen.

MERK: Du kan bruke slettetasten for å rette feil mens du skriver.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.3


  1. For å legge tilbake tekst som nettopp er blitt slettet, trykk  p  . Dette
     limer inn den slettede teksten ETTER markøren (hvis en linje ble slettet
     vil den bli limt inn på linjen under markøren).

  2. For å erstatte et tegn under markøren, trykk  r  og deretter tegnet som
     du vil ha der.

  3. Forandringsoperatoren lar deg forandre fra markøren til dit bevegelsen
     tar deg. Det vil si, skriv  ce  for å forandre fra markøren til slutten
     av ordet,	c$  for å forandre til slutten av linjen.

  4. Formatet for «forandre» er:

	 c   [nummer]	bevegelse

Nå kan du gå til neste leksjon.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	     Leksjon 1.4.1: POSISJONERING AV MARKØREN OG FILSTATUS

	** Trykk CTRL-G for å vise posisjonen i filen og filstatusen.
	   Trykk  G  for å gå til en spesifikk linje i filen. **

  Merk: Les hele leksjonen før du utfører noen av punktene!

  1. Hold nede Ctrl-tasten og trykk  g	. Vi kaller dette CTRL-G. En melding
     vil komme til syne på bunnen av skjermen med filnavnet og posisjonen i
     filen. Husk linjenummeret for bruk i steg 3.

Merk: Du kan se markørposisjonen i nederste høyre hjørne av skjermen. Dette
      skjer når «ruler»-valget er satt (forklart i leksjon 6).

  2. Trykk  G  for å gå til bunnen av filen.
     Skriv  gg	for å gå til begynnelsen av filen.

  3. Skriv inn linjenummeret du var på og deretter  G . Dette vil føre deg
     tilbake til linjen du var på da du først trykket CTRL-G.

  4. Utfør steg 1 til 3 hvis du føler deg sikker på prosedyren.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Leksjon 1.4.2: SØKEKOMMANDOEN

      ** Skriv	/  etterfulgt av en søkestreng som du vil lete etter. **

  1. Trykk  /  når du er i normalmodusen. Legg merke til at skråstreken og
     markøren kommer til syne på bunnen av skjermen i likhet med
     «:»-kommandoene.

  2. Skriv «feeeiil» og trykk <ENTER>. Dette er teksten du vil lete etter.

  3. For å finne neste forekomst av søkestrengen, trykk  n .
     For å lete etter samme søketeksten i motsatt retning, trykk  N .

  4. For å lete etter en tekst bakover i filen, bruk  ?  istedenfor  /	.

  5. For å gå tilbake til der du kom fra, trykk  CTRL-O  (Hold Ctrl nede mens
     du trykker bokstaven  o ). Repeter for å gå enda lengre tilbake. CTRL-I
     går framover.

---> «feeeiil» er ikke måten å skrive «feil» på, feeeiil er helt feil.
Merk: Når søkingen når slutten av filen, vil den fortsette fra starten unntatt
      hvis «wrapscan»-valget er resatt.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Leksjon 1.4.3: FINN SAMSVARENDE PARENTESER


	  ** Trykk  %  for å finne en samsvarende ), ] eller } . **

  1. Plasser markøren på en (, [ eller { på linjen nedenfor merket --->.

  2. Trykk  %  .

  3. Markøren vil gå til den samsvarende parentesen eller hakeparentesen.

  4. Trykk  %  for å flytte markøren til den andre samsvarende parentesen.

  5. Flytt markøren til en annen (, ), [, ], { eller } og se hva  %  gjør.

---> Dette ( er en testlinje med (, [ ] og { } i den )).

Merk: Dette er veldig nyttig til feilsøking i programmer som har ubalansert
      antall parenteser!



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Leksjon 1.4.4: ERSTATT-KOMMANDOEN


	** Skriv  :s/gammel/ny/g  for å erstatte «gammel» med «ny». **

  1. Flytt markøren til linjen nedenfor som er merket med --->.

  2. Skriv  :s/deen/den/ <ENTER>  . Legg merke til at denne kommandoen bare
     forandrer den første forekomsten av «deen» på linjen.

  3. Skriv  :s/deen/den/g . Når g-flagget legges til, betyr dette global
     erstatning på linjen og erstatter alle forekomster av «deen» på linjen.

---> deen som kan kaste deen tyngste steinen lengst er deen beste

  4. For å erstatte alle forekomster av en tekststreng mellom to linjer,
     skriv  :#,#s/gammel/ny/g  der #,# er linjenumrene på de to linjene for
			       linjeområdet erstatningen skal gjøres.
     Skriv  :%s/gammel/ny/g    for å erstatte tekst i hele filen.
     Skriv  :%s/gammel/ny/gc   for å finne alle forekomster i hele filen, og
			       deretter spørre om teksten skal erstattes eller
			       ikke.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.4


  1. Ctrl-G viser nåværende posisjon i filen og filstatusen.
	     G	går til slutten av filen.
     nummer  G	går til det linjenummeret.
	    gg	går til den første linjen.

  2. Skriv  /  etterfulgt av en søketekst for å lete FRAMOVER etter teksten.
     Skriv  ?  etterfulgt av en søketekst for å lete BAKOVER etter teksten.
     Etter et søk kan du trykke  n  for å finne neste forekomst i den samme
     retningen eller  N  for å lete i motsatt retning.
     CTRL-O tar deg tilbake til gamle posisjoner, CTRL-I til nyere posisjoner.

  3. Skriv  %  når markøren står på en (, ), [, ], { eller } for å finne den
     som samsvarer.

  4. Erstatte «gammel» med første «ny» på en linje:  :s/gammel/ny
     Erstatte alle «gammel» med «ny» på en linje:    :s/gammel/ny/g
     Erstatte tekst mellom to linjenumre:	     :#,#s/gammel/ny/g
     Erstatte alle forekomster i en fil:	     :%s/gammel/ny/g
     For å godkjenne hver erstatning, legg til «c»:  :%s/gammel/ny/gc
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	       Leksjon 1.5.1: HVORDAN UTFØRE EN EKSTERN KOMMANDO


    ** Skriv  :!  etterfulgt av en ekstern kommando for å utføre denne. **

  1. Skriv den velkjente kommandoen  :	 for å plassere markøren på bunnen av
     skjermen. Dette lar deg skrive en kommandolinjekommando.

  2. Nå kan du skrive tegnet  !  . Dette lar deg utføre en hvilken som helst
     ekstern kommando.

  3. Som et eksempel, skriv  ls  etter utropstegnet og trykk <ENTER>. Du vil
     nå få en liste over filene i katalogen, akkurat som om du hadde kjørt
     kommandoen direkte fra kommandolinjen i skallet. Eller bruk  :!dir  hvis
     «ls» ikke virker.

MERK: Det er mulig å kjøre alle eksterne kommandoer på denne måten, også med
      parametere.

MERK: Alle «:»-kommandoer må avsluttes med <ENTER>. Fra dette punktet er det
      ikke alltid vi nevner det.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Leksjon 1.5.2: MER OM LAGRING AV FILER


      ** For å lagre endringene gjort i en tekst, skriv  :w FILNAVN. **

  1. Skriv  :!dir  eller  :!ls	for å få en liste over filene i katalogen. Du
     vet allerede at du må trykke <ENTER> etter dette.

  2. Velg et filnavn på en fil som ikke finnes, som for eksempel  TEST .

  3. Skriv  :w TEST  (der TEST er filnavnet du velger).

  4. Dette lagrer hele filen (denne innføringen) under navnet TEST . For å
     sjekke dette, skriv  :!dir  eller	:!ls  igjen for å se innholdet av
     katalogen.

Merk: Hvis du nå hadde avsluttet Vim og startet på nytt igjen med «vim TEST»,
      ville filen vært en eksakt kopi av innføringen da du lagret den.

  5. Fjern filen ved å skrive  :!rm TEST  hvis du er på et Unix-lignende
     operativsystem, eller  :!del TEST	hvis du bruker MS-DOS.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Leksjon 1.5.3: VELGE TEKST SOM SKAL LAGRES


     ** For å lagre en del av en fil, skriv  v	bevegelse  :w FILNAVN **

  1. Flytt markøren til denne linjen.

  2. Trykk  v  og flytt markøren til det femte elementet nedenfor. Legg merke
     til at teksten blir markert.

  3. Trykk  :  (kolon). På bunnen av skjermen vil  :'<,'>  komme til syne.

  4. Trykk  w TEST  , der TEST er et filnavn som ikke finnes enda. Kontroller
     at du ser	:'<,'>w TEST  før du trykker Enter.

  5. Vim vil skrive de valgte linjene til filen TEST. Bruk  :!dir  eller  :!ls
     for å se den. Ikke slett den enda! Vi vil bruke den i neste leksjon.

MERK: Ved å trykke  v  startes visuelt valg. Du kan flytte markøren rundt for
      å gjøre det valgte området større eller mindre. Deretter kan du bruke en
      operator for å gjøre noe med teksten. For eksempel sletter  d  teksten.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Leksjon 1.5.4: HENTING OG SAMMENSLÅING AV FILER


 ** For å lese inn en annen fil inn i nåværende buffer, skriv  :r FILNAVN  **

  1. Plasser markøren like over denne linjen.

MERK: Etter å ha utført steg 2 vil du se teksten fra leksjon 1.5.3. Gå deretter
      NED for å se denne leksjonen igjen.

  2. Hent TEST-filen ved å bruke kommandoen  :r TEST  der TEST er navnet på
     filen du brukte. Filen du henter blir plassert nedenfor markørlinjen.

  3. For å sjekke at filen ble hentet, gå tilbake og se at det er to kopier av
     leksjon 1.5.3, originalen og denne versjonen.

MERK: Du kan også lese utdataene av en ekstern kommando. For eksempel,	:r !ls
      leser utdataene av ls-kommandoen og legger dem nedenfor markøren.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.5


  1.  :!kommando  utfører en ekstern kommandio.

      Noen nyttige eksempler er:
	 (MS-DOS)	  (Unix)
	  :!dir		   :!ls		  - List filene i katalogen.
	  :!del FILNAVN    :!rm FILNAVN   - Slett filen FILNAVN.

  2.  :w FILNAVN  skriver den nåværende Vim-filen disken med navnet FILNAVN .

  3.  v  bevegelse  :w FILNAVN	lagrer de visuelt valgte linjene til filen
     FILNAVN.

  4.  :r FILNAVN  henter filen FILNAVN og legger den inn nedenfor markøren.

  5.  :r !dir  leser utdataene fra «dir»-kommandoen og legger dem nedenfor
     markørposisjonen.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Leksjon 1.6.1: «ÅPNE LINJE»-KOMMANDOEN


    ** Skriv  o  for å «åpne opp» for en ny linje etter markøren og gå til
       innsettingsmodus **

  1. Flytt markøren til linjen nedenfor merket --->.

  2. Skriv  o  (liten o) for å åpne opp en linje NEDENFOR markøren og gå inn i
     innsettingsmodus.

  3. Skriv litt tekst og trykk <ESC> for å gå ut av innsettingsmodusen.

---> Etter at  o  er skrevet blir markøren plassert på den tomme linjen.

  4. For å åpne en ny linje OVER markøren, trykk rett og slett en stor	O
     istedenfor en liten  o . Prøv dette på linjen nedenfor.

---> Lag ny linje over denne ved å trykke O mens markøren er på denne linjen.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Leksjon 1.6.2: «LEGG TIL»-KOMMANDOEN


	    ** Skriv  a  for å legge til tekst ETTER markøren. **

  1. Flytt markøren til starten av linjen merket ---> nedenfor.

  2. Trykk  e  til markøren er på slutten av «li».

  3. Trykk  a  (liten a) for å legge til tekst ETTER markøren.

  4. Fullfør ordet sånn som på linjen nedenfor. Trykk <ESC> for å gå ut av
     innsettingsmodusen.

  5. Bruk  e  for å gå til det neste ufullstendige ordet og repeter steg 3 og
     4.

---> Denne li lar deg øve på å leg til tek på en linje.
---> Denne linjen lar deg øve på å legge til tekst på en linje.

Merk: a, i og A går alle til den samme innsettingsmodusen, den eneste
      forskjellen er hvor tegnene blir satt inn.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Leksjon 1.6.3: EN ANNEN MÅTE Å ERSTATTE PÅ


	   ** Skriv en stor  R	for å erstatte mer enn ett tegn. **

  1. Flytt markøren til den første linjen nedenfor merket --->. Flytt markøren
     til begynnelsen av den første «xxx»-en.

  2. Trykk  R  og skriv inn tallet som står nedenfor på den andre linjen så
     det erstatter xxx.

  3. Trykk <ESC> for å gå ut av erstatningsmodusen. Legg merke til at resten
     av linjen forblir uforandret.

  4. Repeter stegene for å erstatte den gjenværende xxx.

---> Ved å legge 123 til xxx får vi xxx.
---> Ved å legge 123 til 456 får vi 579.

MERK: Erstatningsmodus er lik insettingsmodus, men hvert tegn som skrives
      erstatter et eksisterende tegn.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Leksjon 1.6.4: KOPIERE OG LIME INN TEKST


    ** Bruk y-operatoren for å kopiere tekst og  p  for å lime den inn **

  1. Gå til linjen merket ---> nedenfor og plasser markøren etter «a)».

  2. Gå inn i visuell modus med  v  og flytt markøren til like før «første».

  3. Trykk  y  for å kopiere (engelsk: «yank») den uthevede teksten.

  4. Flytt markøren til slutten av den neste linjen:  j$

  5. Trykk  p  for å lime inn teksten. Trykk deretter:	a andre <ESC> .

  6. Bruk visuell modus for å velge « valget.», kopier det med	y , gå til
     slutten av den neste linjen med  j$  og legg inn teksten der med  p .

---> a) Dette er det første valget.
     b)

Merk: Du kan også bruke  y  som en operator;  yw  kopierer ett ord.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Leksjon 1.6.5: SETT VALG


  ** Sett et valg så søk eller erstatning ignorerer store/små bokstaver. **

  1. Let etter «ignore» ved å skrive:  /ignore <ENTER>
     Repeter flere ganger ved å trykke	n .

  2. Sett «ic»-valget (Ignore Case) ved å skrive:  :set ic

  3. Søk etter «ignore» igjen ved å trykke  n .
     Legg merke til at både «Ignore» og «IGNORE» blir funnet.

  4. Sett «hlsearch»- og «incsearch»-valgene:  :set hls is

  5. Skriv søkekommandoen igjen og se hva som skjer:  /ignore <ENTER>

  6. For å slå av ignorering av store/små bokstaver, skriv:  :set noic

Merk: For å fjerne uthevingen av treff, skriv:	:nohlsearch
Merk: Hvis du vil ignorere store/små bokstaver for kun en søkekommando, bruk
      \c  i uttrykket:	/ignore\c <ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.6

  1. Trykk  o  for å legge til en linje NEDENFOR markøren og gå inn i
     innsettingsmodus.
     Trykk  O  for å åpne en linje OVER markøren.

  2. Skriv  a  for å sette inn tekst ETTER markøren.
     Skriv  A  for å sette inn tekst etter slutten av linjen.

  3. Kommandoen  e  går til slutten av et ord.

  4. Operatoren  y  («yank») kopierer tekst,  p  («paste») limer den inn.

  5. Ved å trykke  R  går du inn i erstatningsmodus helt til  <ESC>  trykkes.

  6. Skriv «:set xxx» for å sette valget «xxx». Noen valg er:
	«ic» «ignorecase»	ignorer store/små bokstaver under søk
	«is» «incsearch»	vis delvise treff for en søketekst
	«hls» «hlsearch»	uthev alle søketreff

  7. Legg til «no» foran valget for å slå det av:  :set noic

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Leksjon 1.7.1: FÅ HJELP


		   ** Bruk det innebygde hjelpesystemet. **

  Vim har et omfattende innebygget hjelpesystem. For å starte det, prøv en av
  disse måtene:
    - Trykk Hjelp-tasten (hvis du har en)
    - Trykk F1-tasten (hvis du har en)
    - Skriv  :help <ENTER>

  Les teksten i hjelpevinduet for å finne ut hvordan hjelpen virker.
  Skriv  CTRL-W CTRL-W	for å hoppe fra et vindu til et annet
  Skriv  :q <ENTER>	for å lukke hjelpevinduet.

  Du kan få hjelp for omtrent alle temaer om Vim ved å skrive et parameter til
  «:help»-kommandoen. Prøv disse (ikke glem å trykke <ENTER>):

    :help w
    :help c_CTRL-D
    :help insert-index
    :help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Leksjon 1.7.2: LAG ET OPPSTARTSSKRIPT


			** Slå på funksjoner i Vim **

  Vim har mange flere funksjoner enn Vi, men flesteparten av dem er slått av
  som standard. For å begynne å bruke flere funksjoner må du lage en
  «vimrc»-fil.

  1. Start redigeringen av «vimrc»-filen. Dette avhenger av systemet ditt:
	:e ~/.vimrc	  for Unix
	:e ~/_vimrc	  for MS Windows

  2. Les inn eksempelfilen for «vimrc»:
	:r $VIMRUNTIME/vimrc_example.vim

  3. Lagre filen med:
	:w

  Neste gang du starter Vim vil den bruke syntaks-utheving. Du kan legge til
  alle dine foretrukne oppsett i denne «vimrc»-filen.
  For mer informasjon, skriv  :help vimrc-intro
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   Leksjon 1.7.3: FULLFØRING


	      ** Kommandolinjefullføring med CTRL-D og <TAB> **

  1. Vær sikker på at Vim ikke er i Vi-kompatibel modus:  :set nocp

  2. Se hvilke filer som er i katalogen:  :!ls	eller  :!dir

  3. Skriv starten på en kommando:  :e

  4. Trykk  CTRL-D  og Vim vil vise en liste over kommandoer som starter med
     «e».

  5. Trykk  <TAB>  og Vim vil fullføre kommandonavnet til «:edit».

  6. Legg til et mellomrom og starten på et eksisterende filnavn:  :edit FIL

  7. Trykk <TAB>. Vim vil fullføre navnet (hvis det er unikt).

MERK: Fullføring fungerer for mange kommandoer. Prøv ved å trykke CTRL-D og
      <TAB>. Det er spesielt nyttig for bruk sammen med  :help .
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  OPPSUMMERING AV LEKSJON 1.7


  1. Skriv  :help  eller trykk <F1> eller <Help> for å åpne et hjelpevindu.

  2. Skriv  :help kommando  for å få hjelp om  kommando .

  3. Trykk  CTRL-W CTRL-W  for å hoppe til et annet vindu.

  4. Trykk  :q	for å lukke hjelpevinduet.

  5. Opprett et vimrc-oppstartsskript for å lagre favorittvalgene dine.

  6. Når du skriver en «:»-kommando, trykk CTRL-D for å se mulige
     fullføringer. Trykk <TAB> for å bruke en fullføring.







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Her slutter innføringen i Vim. Den var ment som en rask oversikt over
  editoren, akkurat nok til å la deg sette i gang med enkel bruk. Den er på
  langt nær komplett, da Vim har mange flere kommandoer. Les bruksanvisningen
  ved å skrive	:help user-manual  .

  For videre lesing og studier, kan denne boken anbefales:
      «Vim - Vi Improved» av Steve Oualline
      Utgiver: New Riders
  Den første boken som er fullt og helt dedisert til Vim. Spesielt nyttig for
  nybegynnere. Inneholder mange eksempler og illustrasjoner.
  Se https://iccf-holland.org/click5.html

  Denne boken er eldre og handler mer om Vi enn Vim, men anbefales også:
      «Learning the Vi Editor» av Linda Lamb
      Utgiver: O'Reilly & Associates Inc.
  Det er en god bok for å få vite omtrent hva som helst om Vi.
  Den sjette utgaven inneholder også informasjon om Vim.

  Denne innføringen er skrevet av Michael C. Pierce og Robert K. Ware,
  Colorado School of Mines med idéer av Charles Smith, Colorado State
  University. E-mail: <EMAIL> .

  Modifisert for Vim av Bram Moolenaar.
  Oversatt av Øyvind A. Holm. E-mail: vimtutor _AT_ sunbase.org
  Id: tutor.no 406 2007-03-18 22:48:36Z sunny

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
vim: set ts=8 :
