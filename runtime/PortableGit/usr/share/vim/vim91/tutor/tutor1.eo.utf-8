==============================================================================
=  B o n v e n o n   al   la   I n s t r u i l o  de  V I M  -  Versio 1.7   =
==============================================================================

   Vim estas tre potenca redaktilo, kiu havas multajn komandojn, tro da ili
   por ĉion klarigi en instruilo kiel ĉi tiu. Ĉi tiu instruilo estas
   fasonita por priskribi sufiĉajn komandojn, por ke vi kapablu uzi Vim
   kun sufiĉa facileco.

   La tempo bezonata por plenumi la kurson estas 30 minutoj, kaj dependas
   de kiom da tempo estas uzata por eksperimenti.

   ATENTU:
   La komandoj en la lecionoj ŝanĝos la tekston. Kopiu tiun ĉi dosieron
   por ekzerci vin (se vi lanĉis "vimtutor", tiam estas jam kopio).

   Gravas memori, ke ĉi tiu instruilo estas organizata por instrui per
   la uzo. Tio signifas, ke vi devas plenumi la komandojn por bone lerni
   ilin. Se vi nur legas la tekston, vi forgesos la komandojn!

   Nun, certigu, ke la majuskla baskulo NE estas en reĝimo majuskla,
   kaj premu la klavon  j  sufiĉe da fojoj por movi la kursoron, kaj por
   ke la leciono 1.1 plenigu la ekranon.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Leciono 1.1.1:  MOVI LA KURSORON


  ** Por movi la kursoron, premu la h,j,k,l klavojn kiel montrite. **
         ^
         k        Konsilo: La klavo h estas la plej liva kaj movas liven.
   < h       l >           La klavo l estas la plej dekstra kaj movas dekstren.
         j                 La klavo j aspektas kiel malsuprena sago.
         v
  1. Movu la kursoron sur la ekrano ĝis kiam vi sentas vin komforta.

  2. Premu la klavon (j) ĝis kiam ĝi ripetas.
     Vi nun scias, kiel moviĝi al la sekvanta leciono

  3. Uzante la malsuprenan klavon, moviĝu al la leciono 1.2.

RIMARKO: Se vi dubas pri tio, kion vi premis, premu <ESK> por reiri al
         la normala reĝimo. Tiam repremu la deziratan komandon.

RIMARKO: La klavoj de la kursoro devus ankaŭ funkcii. Sed uzante hjkl,
         vi kapablos moviĝi pli rapide post kiam vi kutimiĝos.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                           Leciono 1.1.2:  ELIRI EL VIM


  !! RIMARKO: Antaŭ ol plenumi iujn subajn paŝojn ajn, legu la tutan lecionon!!

  1. Premu la klavon <ESK> (por certigi, ke vi estas en normala reĝimo).

  2. Tajpu:       :q! <Enenklavo>.
     Tio eliras el la rekdaktilo, SEN konservi la ŝanĝojn, kiujn vi faris.

  3. Kiam vi vidas la ŝelinviton, tajpu la komandon kiun vi uzis por eniri
     en ĉi tiu instruilo. Tio estus:   vimtutor <Enenklavo>

  4. Se vi memoris tiujn paŝojn kaj sentas vin memfida, plenumu la paŝojn
     1 ĝis 3 por eliri kaj reeniri la redaktilon.

RIMARKO: :q! <Enenklavo> eliras sen konservi la ŝanĝojn, kiujn vi faris.
         Post kelkaj lecionoj, vi lernos kiel konservi la ŝanĝojn al dosiero.

  5. Movu la kursoron suben ĝis la leciono 1.3.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Leciono 1.1.3:  REDAKTO DE TEKSTO - FORVIŜO


             ** Premu  x  por forviŝi la signon sub la kursoro. **

  1. Movu la kursoron al la suba linio markita per --->.

  2. Por korekti la erarojn, movu la kursoron ĝis kiam ĝi estas sur la
     forviŝenda signo.

  3. Premu la klavon  x  por forviŝi la nedeziratan signon.

  4. Ripetu paŝojn 2 ĝis 4 ĝis kiam la frazo estas ĝusta.


---> La boovinno saaltiss ssur laa luuno.

  5. Post kiam la linio estas ĝusta, iru al la leciono 1.4

RIMARKO: Trairante la instruilon, ne provu memori, lernu per la uzo.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leciono 1.1.4:  REDAKTO DE TEKSTO - ENMETO


                      ** Premu  i  por enmeti tekston. **

  1. Movu la kursoron al la unua suba linio markita per --->.

  2. Por igi la unuan linion sama kiel la dua, movu la kursoron sur la unuan
     signon antaŭ kie la teksto estas enmetenda.

  3. Premu  i  kaj tajpu la bezonatajn aldonojn.

  4. Premu <ESK> kiam la eraroj estas korektitaj por reiri al la normala
     reĝimo. Ripetu la paŝojn 2 ĝis 4 por korekti la frazon.

---> Mank en ĉi linio.
---> Mankas teksto en ĉi tiu linio.

  5. Kiam vi sentas vin komforta pri enmeto de teksto, moviĝu al la
     leciono 1.5.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                  Leciono 1.1.5:  REDAKTO DE TEKSTO - POSTALDONO


                    ** Premu  A  por postaldoni tekston. **

  1. Movu la kursoron al la unua suba linio markita per --->.
     Ne gravas sur kiu signo estas la kursoro.

  2. Premu majusklan  A  kaj tajpu la bezonatajn aldonojn.

  3. Post kiam la teksto estas aldonita, premu <ESK> por reiri al la normala
     reĝimo.

  4. Movu la kursoron al la dua linio markita per ---> kaj ripetu la
     paŝojn 2 kaj 3 por korekti la frazon.

---> Mankas teksto el ti
     Mankas teksto el tiu linio.
---> Mankas ankaŭ teks
     Mankas ankaŭ teksto ĉi tie.

  5 Kiam vi sentas vin komforta pri postaldono de teksto, moviĝu al la
    leciono 1.6


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Leciono 1.1.6:  REDAKTI DOSIERON

                ** Uzu  :wq  por konservi dosieron kaj eliri. **

  !! RIMARKO: Antaŭ ol plenumi iun suban paŝon ajn, legu la tutan lecionon!!

  1. Eliru el la instruilo kiel vi faris en la leciono 1.1.2:  :q!
     Aŭ, se vi havas atingon al alia terminalo, faru tion, kio sekvas tie.

  2. Ĉe la ŝelinvito, tajpu ĉi tiun komandon:  vim tutor <Enenklavo>
     'vim' estas la komando por lanĉi la redaktilon Vim, 'tutor' estas la
     dosiernomo de la dosiero, kiun vi volas redakti.  Uzu dosieron, kiu
     ŝanĝeblas.

  3. Enmetu kaj forviŝu tekston, kiel vi lernis en la antaŭaj lecionoj.

  4. Konservu la dosieron kun ŝanĝoj kaj eliru el Vim per:  :wq  <Enenklavo>

  5. Se vi eliris el la instruilo vimtutor en paŝo 1, restartigu la instruilon
     vimtutor kaj moviĝu suben al la sekvanta resumo.

  6. Post kiam vi legis la suprajn paŝojn, kaj komprenis ilin: faru ilin.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.1 RESUMO


  1. La kursoro moviĝas aŭ per la sagoklavoj, aŭ per la klavoj hjkl.
        h (liven)     j (suben)      k (supren)     l (dekstren)

  2. Por lanĉi Vim el la ŝelinvito, tajpu:  vim DOSIERNOMO <Enenklavo>

  3. Por eliri el Vim, tajpu:  <ESK>  :q! <Enenklavo>  por rezigni la ŝanĝojn

  4. Por forviŝi la signojn ĉe la pozicio de la kursoro, tajpu:  x

  5. Por enmeti aŭ postaldoni tekston, tajpu:
          i  tajpu enmetendan tekston        <ESK>
             enmetas tekston antaŭ la kursoro

          A  tajpu la postaldonendan tekston <ESK>
             postaldonas post la kursoro

RIMARKO: Premo de <ESK> iras al la normala reĝimo, aŭ rezignas la
         nedeziratan aŭ parte plenumita komando.

Nun daŭrigu al la leciono 2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Leciono 1.2.1: KOMANDOJ DE FORVIŜO


                      ** Tajpu  dw  por forviŝi vorton. **

  1. Premu  <ESK>  por certigi, ke vi estas en normala reĝimo.

  2. Movu la kursoron al la suba linio markita per --->.

  3. Movu la kursoron al la komenco de vorto, kiu forviŝendas.

  4. Tajpu   dw   por forviŝi la vorton.

  RIMARKO: La litero  d  aperos en la lasta linio sur la ekrano kiam vi
           tajpas ĝin. Vim atendas ĝis kiam vi tajpas  w .  Se vi vidas
           alian signon ol  d  vi tajpis ion mise; premu  <ESK>  kaj
           rekomencu.

---> Estas iuj vortoj kiuj Zamenhof ne devus esti akuzativo en ĉi tiu frazo.

  5. Ripetu paŝojn 3 kaj 4 ĝis kiam la frazo estas ĝusta kaj moviĝu al la
     leciono 2.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leciono 1.2.2: PLIAJ KOMANDOJ DE FORVIŜO


               ** Tajpu  d$  por forviŝi la finon de la linio. **

  1. Premu  <ESK>  por certigi, ke vi estas en normala reĝimo.

  2. Movu la kursoron al la suba linio markita per --->.

  3. Movu la kursoron ĉe la fino de la ĝusta linio (POST la unua . ).

  4. Tajpu   d$   por foriviŝi ĝis la fino de la linio.

---> Iu tajpis la finon de ĉi tiu linio dufoje. fino de ĉi tiu linio dufoje.


  5. Moviĝu al la leciono 2.3 por kompreni kio okazas.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leciono 1.2.3: PRI OPERATOROJ KAJ MOVOJ


  Multaj komandoj, kiuj ŝanĝas la tekston, estas faritaj de operatoro kaj
  movo. La formato de komando de forviŝo per la operatoro de forviŝo  d
  estas kiel sekvas:

       d   movo

  Kie:
    d      - estas la operatoro de movo
    movo   - estas tio, pri kio la operatoro operacios (listigita sube)

  Mallonga listo de movoj:
    w - ĝis la komenco de la sekvanta vorto, krom ĝia unua signo.
    e - ĝis la fino de la nuna vorto, krom la lasta signo.
    $ - ĝis la fino de la linio, krom la lasta signo.

  Do tajpo de   'de'   forviŝos ekde la kursoro ĝis la fino de la vorto.

RIMARKO: Premo de nur la movo en Normala reĝimo sen operatoro movos
         la kursoron kiel specifite.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leciono 1.2.4: UZI NOMBRON POR MOVO

             ** Tajpo de nombro antaŭ movo ripetas ĝin laŭfoje. **

  1. Movu la kursoron ĉe la komenco de la suba linio markita per --->.

  2. Tajpu  2w  por movi la kursoron je du vortoj antaŭen.

  3. Tajpu  3e  por movi la kursoron ĉe la fino de la tria vorto antaŭen.

  4. Tajpu  0  (nul) por moviĝi ĉe la komenco de la linio.


  5. Ripetu paŝojn 2 ĝis 3 kun malsamaj nombroj.

---> Tio estas nur linio kun vortoj, kie vi povas moviĝi.

  6. Moviĝu al la leciono 2.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leciono 1.2.5: UZI NOMBRON POR FORVIŜI PLI


            ** Tajpo de nombro kun operatoro ripetas ĝin laŭfoje. **

  En la kombinaĵo de la operatoro de forviŝo, kaj movo kiel menciita
  ĉi-supre, eblas aldoni nombron antaŭ la movo por pli forviŝi:
        d  nombro   movo

  1. Movu la kursoron ĉe la unua MAJUSKLA vorto en la linio markita per --->.

  2. Tajpu  d2w  por forviŝi la du MAJUSKLAJN vortojn.

  3. Ripetu paŝojn 1 ĝis 2 per malsama nombro por forviŝi la sinsekvajn
     MAJUSKLAJN vortojn per unu komando.

---> Tiu AB CDE linio FGHI JK LMN OP de vortoj estas Q RS TUV purigita.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leciono 1.2.6: OPERACII SUR LINIOJ


                   ** Tajpu  dd  por forviŝi tutan linion. **

  Pro la ofteco de forviŝo de tuta linio, la verkisto de Vi decidis, ke
  estus pli facile simple tajpi du d-ojn  por forviŝi linion.

  1. Movu la kursoron sur la duan linion en la suba frazo.
  2. Tajpu  dd  por forviŝi la linion.
  3. Nun moviĝu al la kvara linio.
  4. Tajpu   2dd   por forviŝi du liniojn.

---> 1)  Rozoj estas ruĝaj,
---> 2)  Ŝlimo estas amuza,
---> 3)  Violoj estas bluaj,
---> 4)  Mi havas aŭton,
---> 5)  Horloĝoj diras kioma horo estas,
---> 6)  Sukero estas dolĉa,
---> 7)  Kaj tiel vi estas.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leciono 1.2.7: LA KOMANDO DE MALFARO


 ** Premu u por malfari la lastajn komandojn, U por ripari la tutan linion. **

  1. Movu la kursoron ĉe la suba linio markita per ---> kaj metu ĝin sur
     la unuan eraron.
  2. Tajpu  x  por forviŝi la unuan nedeziratan signon.
  3. Nun tajpu  u  por malfari la lastan plenumitan komandon.
  4. Ĉi-foje, riparu ĉiujn erarojn en la linio kaj ĝia originala stato.
  5. Nun tajpu majusklan  U  por igi la linion al ĝia antaŭa stato.
  6. Nun tajpu  u  kelkfoje por malfari la  U  kaj antaŭajn komandojn.
  7. Nun tajpu CTRL-R (premante la CTRL klavon dum vi premas R) kelkfoje
     por refari la komandojn (malfari la malfarojn).

---> Koorektii la erarojn sur tiuu ĉi liniio kaj remettu illlin per malfaro.

  8. Tiuj estas tre utilaj komandoj.  Nun moviĝu al la leciono 1.2 RESUMO.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.2 RESUMO


  1. Por forviŝi ekde la kursoro ĝis la sekvanta vorto, tajpu:    dw
  2. Por forviŝi ekde la kursoro ĝis la fino de la linio, tajpu:  d$
  3. Por forviŝi tutan linion, tajpu:                             dd

  4. Por ripeti movon, antaŭmetu nombron:                         2w
  5. La formato de ŝanĝa komando estas:
           operatoro   [nombro]   movo

     kie:
       operatoro - estas tio, kio farendas, kiel  d  por forviŝi
       [nombro]  - estas opcia nombro por ripeti la movon
       movo      - movas sur la teksto por operacii, kiel ekzemple  w (vorto),
                   $ (ĝis fino de linio), ktp.

  6. Por moviĝi al la komenco de la linio, uzu nul:  0

  7. Por malfari antaŭajn agojn, tajpu:               u (minuskla u)
     Por malfari ĉiujn ŝanĝojn sur la linio, tajpu:   U (majuskla U)
     Por refari la malfarojn, tajpu:                  CTRL-R


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Leciono 3.1 LA KOMANDO DE METO


    ** Tajpu  p  por meti tekston forviŝitan antaŭe post la kursoro. **

  1. Movu la kursoron ĉe la unua suba linio markita per --->.

  2. Tajpu  dd  por forviŝi la linion kaj konservi ĝin ene de reĝistro de Vim.

  3. Movu la kursoron ĉe la linio c), SUPER kie la forviŝita linio devus esti.

  4. Tajpu  p  por meti la linion sub la kursoron.

  5. Ripetu la paŝojn 2 ĝis 4 por meti ĉiujn liniojn en la ĝusta ordo.

---> d) Ĉu ankaŭ vi povas lerni?
---> b) Violoj estas bluaj,
---> c) Inteligenteco lerneblas,
---> a) Rozoj estas ruĝaj,


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leciono 3.2 LA KOMANDO DE ANSTATAŬIGO


       ** Tajpu  rx  por anstataŭigi la signon ĉe la kursoro per  x . **


  1. Movu la kursoron ĉe la unua suba linio markita per --->.

  2. Movu la kursoron ĝis la unua eraro.

  3. Tajpu  r  kaj la signon, kiu devus esti tie.

  4. Ripetu paŝojn 2 kaj 3 ĝis kiam la unua linio egalas la duan.

---> Kiem tiu lanio estis tajpita, iu pramis la naĝuftajn klovojn!
---> Kiam tiu linio estis tajpita, iu premis la neĝustajn klavojn!

  5. Nun moviĝu al la leciono 3.3.

RIMARKO: Memoru, ke vi devus lerni per uzo, kaj ne per memorado.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leciono 3.3 LA OPERATORO DE ŜANĜO


              ** Por ŝanĝi ĝis la fino de la vorto, tajpu  ce . **

  1. Movu la kursoron ĉe la unua suba linio markita per --->.

  2. Metu la kursoron sur la  d  en  lduzw

  3. Tajpu  ce  kaj la ĝustan vorton (en tiu ĉi kazo, tajpu inio ).

  4. Premu <ESK> kaj moviĝu al la sekvanta signo, kiu bezonas ŝanĝon.

  5. Ripetu la paŝojn 3 kaj 4 ĝis kiam la unua frazo egalas la duan.

---> Tiu lduzw havas kelkajn vortojn, kiii bezas ŝanĝon per la ŝanĝooto.
---> Tiu linio havas kelkajn vortojn, kiuj bezonas ŝanĝon per la ŝanĝoperatoro.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Leciono 3.4 PLIAJ ŜANĜOJ PER c


       ** La operatoro de ŝanĝo uzeblas kun la sama movo kiel forviŝo. **

  1. La operatoro de ŝanĝo funkcias sammaniere kiel forviŝo. La formato estas:

       c    [nombro]   movo

  2. La movoj estas samaj, kiel ekzemple   w (vorto) kaj $ (fino de linio).

  3. Moviĝu ĉe la unua suba linio markita per --->.

  4. Movu la kursoron al la unua eraro.

  5. Tajpu  c$  kaj tajpu la reston de la linio kiel la dua kaj premu <ESK>.

---> La fino de ĉi tiu linio bezonas helpon por igi ĝin same kiel la dua.
---> La fino de ĉi tiu linio bezonas korektojn per uzo de la komando  c$

RIMARKO:  Vi povas uzi la klavon Retropaŝo por korekti erarojn dum vi tajpas.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.3 RESUMO


  1. Por remeti tekston, kiun vi ĵus forviŝis, tajpu  p. Tio metas la
     forviŝitan tekston POST la kursoro (se linio estis forviŝita, ĝi
     iros en la linion sub la kursoro).

  2. Por anstataŭigi la signon sub la kursoro, tajpu  r  kaj tiam la signon
     kion vi deziras havi tie.

  3. La operatoro de ŝanĝo ebligas al vi ŝanĝi ekde la kursoro, ĝis kie
     la movo iras.  Ekz. tajpu  ce  por ŝanĝi ekde la kursoro ĝis la fino
     de la vorto,  c$  por ŝanĝi ĝis la fino de la linio.

  4. La formato de ŝanĝo estas:

         c    [nombro]   movo

Nun daŭrigu al la sekvanta leciono.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
              Leciono 1.4.1: POZICIO DE KURSORO KAJ STATO DE DOSIERO


  ** Tajpu CTRL-G por montri vian pozicion en la dosiero kaj la dosierstaton.
     Tajpu  G  por moviĝi al linio en la dosiero. **

  RIMARKO: Legu la tutan lecionon antaŭ ol plenumi iun paŝon ajn!!

  1. Premu la klavon Ctrl kaj premu  g . Oni nomas tion CTRL-G.
     Mesaĝo aperos ĉe la suba parto de la paĝo kun la dosiernomo kaj la
     pozicio en la dosiero. Memoru la numeron de la linio por paŝo 3.

  RIMARKO: Vi eble vidas la pozicion de la kursoro ĉe la suba dekstra
           angulo de la ekrano. Tio okazas kiam la agordo 'ruler' estas
           ŝaltita (vidu  :help 'ruler')

  2. Premu  G  por moviĝi ĉe la subo de la dosiero.
     Tajpu gg  por moviĝi ĉe la komenco de la dosiero.

  3. Tajpu la numeron de la linio kie vi estis kaj poste G .  Tio removos
     vin al la linio, kie vi estis kiam vi unue premis CTRL-G.

  4. Se vi sentas vin komforta, plenumu paŝojn 1 ĝis 3.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Leciono 1.4.2: LA KOMANDO DE SERĈO


             ** Tajpu  /  kaj poste frazon por serĉi la frazon. **

  1. En normala reĝimo, tajpu la  /  signon.  Rimarku, ke ĝi kaj la kursoro
     aperas ĉe la suba parto de la ekrano kiel por la  :  komando.

  2. Nun tajpu 'errarro' <Enenklavo>.
     Tio estas la vorto, kion vi volas serĉi.

  3. Por serĉi la saman frazon denove, simple tajpu  n .
     Por serĉi la saman frazon denove en la retrodirekto, tajpu  N .

  4. Por serĉi frazon en la retrodirekto, uzu  ?  anstataŭ  / .

  5. Por reiri tien, el kie vi venis, premu  CTRL-O (Premu Ctrl kaj o
     literon o).  Ripetu por pli retroiri.  CTRL-I iras antaŭen.

---> "errarro" ne estas maniero por literumi eraro; errarro estas eraro.

RIMARKO: Kiam la serĉo atingas la finon de la dosiero, ĝi daŭras ĉe la
         komenco, krom se la agordo 'wrapscan' estas malŝaltita.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leciono 1.4.3: SERĈO DE KONGRUAJ KRAMPOJ


                 ** Tajpu  %  por trovi kongruan ), ] aŭ  } **

  1. Poziciu la kursoron sur iun (, [ aŭ { en la linio markita per --->.

  2. Nun tajpu la  %  signon.

  3. La kursoro moviĝas al la kongrua krampo.

  4. Tajpu  %  por movi la kursoron al la alia kongrua krampo.

  5. Movu la kursoron al la alia (, ), [, ], {, } kaj observu tion,
     kion  %  faras.

---> Ĉi tiu ( estas testa linio kun (-oj, [-oj, ]-oj kaj {-oj, }-oj en ĝi. ))


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leciono 1.4.4: LA KOMANDO DE ANSTATAŭIGO


     ** Tajpu  :s/malnova/nova/g  por anstataŭigi 'nova' per 'malnova'. **

  1. Movu la kursoron al la suba linio markita per --->.

  2. Tajpu  :s/laa/la <Enenklavo> .  Rimarku, ke la komando ŝanĝas nur la
     unuan okazaĵon de "laa" en la linio.

  3. Nun tajpu  :s/laa/la/g .  Aldono de  g  opcio signifas mallokan
     anstataŭigon en la linio. Ĝi ŝanĝas ĉiujn okazaĵojn de "laa" en la
     linio.

---> laa plej bona tempo por vidi florojn estas en laa printempo.

  4. Por ŝanĝi ĉiujn okazaĵojn de iu ĉena signo inter du linioj,
     tajpu    :#,#s/malnova/nova/g   kie #,# estas la numeroj de linioj de la
                                     intervalo de la linioj kie la anstataŭigo
                                     okazos.
     Tajpu    :%s/malnova/nova/g     por ŝanĝi ĉiujn okazaĵojn en la tuta
                                     dosiero.
     Tajpu    :s/malnova/nova/gc     por trovi ĉiujn okazaĵojn en la tuta
                                     dosiero, kun invitilo ĉu anstataŭigi
                                     aŭ ne.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.4 RESUMO

  1. CTRL-G  vidigas vian pozicion en la dosiero kaj la staton de la dosiero.
            G  movas la kursoron al la fino de la dosiero.
     numero G  movas la kursoron al numero de tiu linio.
           gg  movas la kursoron al la unua linio.

  2. Tajpo de  /  kaj frazon serĉas la frazon antaŭen.
     Tajpo de  ?  kaj frazon serĉas la frazon malantaŭen.
     Post serĉo, tajpu n por trovi la sekvantan okazaĵon en la sama direkto aŭ
     N por serĉi en la mala direkto.
     CTRL-O movas vin al la antaŭaj pozicioj, CTRL-I al la novaj pozicioj.

  3. Tajpo de  %  kiam la kursoro estas sur (,),[,],{ aŭ } moviĝas al ĝia
     kongruo.

  4. Por anstataŭigi 'nova' en la unua 'malnova' en linio :s/malnova/nova
     Por anstataŭigi 'nova' en ĉiuj 'malnova'-oj en linio :s/malnova/nova/g
     Por anstataŭigi frazon inter du #-aj linioj          :#,#s/malnova/nova/g
     Por anstataŭigi ĉiujn okazaĵojn en la dosiero        :%s/malnova/nova/g
     Por demandi konfirmon ĉiu-foje, aldonu 'c'           :%s/malnova/nova/gc


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Leciono 1.5.1: KIEL PLENUMI EKSTERAN KOMANDON


     ** Tajpu  :!  sekvata de ekstera komando por plenumi la komandon. **

  1. Tajpu la konatan komandon  :  por pozicii la kursoron ĉe la suba parto
     de la ekrano. Tio ebligas tajpadon de komando en komanda linio.

  2. Nun tajpu la  !  (krisigno) signon. Tio ebligas al vi plenumi iun
     eksteran ŝelan komandon ajn.

  3. Ekzemple, tajpu  ls  post ! kaj tajpu <Enenklavo>. Tio listigos la
     enhavon de la dosierujo, same kiel se vi estis en ŝela invito.
     Aŭ uzu  :!dir  se ls ne funkcias.

RIMARKO: Eblas plenumi iun eksteran komandon ajn tiamaniere, ankaŭ kun
         argumentoj.

RIMARKO: Ĉiuj  :  komandoj devas finiĝi per tajpo de <Enenklavo>
         Ekde nun, ni ne plu mencios tion.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leciono 1.5.2: PLI PRI KONSERVO DE DOSIERO


   ** Por konservi la faritajn ŝanĝojn en la teksto, tajpu  :w DOSIERNOMO. **

  1. Tajpu  !dir  aŭ  !ls  por akiri liston de via dosierujo.
     Vi jam scias, ke vi devas tajpi <Enenklavo> post tio.

  2. Elektu dosieron, kiu ankoraŭ ne ekzistas, kiel ekzemple TESTO.

  3. Nun tajpu:   :w TESTO   (kie TESTO estas la elektita dosiernomo)

  4. Tio konservas la tutan dosieron (instruilon de Vim) kun la nomo TESTO.
     Por kontroli tion, tajpu   :!dir   aŭ   :!ls   denove por vidigi vian
     dosierujon.

RIMARKO: Se vi volus eliri el Vim kaj restartigi ĝin denove per  vim TESTO,
         la dosiero estus precize same kiel kopio de la instruilo kiam vi
         konservis ĝin.

  5. Nun forviŝu la dosieron tajpante (VINDOZO):    :!del TESTO
                                   aŭ (UNIKSO):     :!rm TESTO


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leciono 1.5.3: APARTIGI KONSERVENDAN TESTON


    ** Por konservi parton de la dosiero, tajpu   v  movo  :w DOSIERNOMO **

  1. Movu la kursoron al tiu linio.

  2. Premu  v  kaj movu la kursoron al la kvina suba ero.  Rimarku, ke la
     teksto emfaziĝas.

  3. Premu la  :  signon.  Ĉe la fino de la ekrano  :'<,'>  aperos.

  4. Tajpu  w TESTO  , kie TESTO estas dosiernomo, kiu ankoraŭ ne ekzistas.
     Kontrolu, ke vi vidas  :'<,'>w TESTO  antaŭ ol premi <Enenklavo>.

  5. Vim konservos la apartigitajn liniojn al la dosiero TESTO.  Uzu  :dir
     aŭ  :!ls  por vidigi ĝin.  Ne forviŝu ĝin.  Ni uzos ĝin en la sekvanta
     leciono.

RIMARKO: Premo de  v  komencas Viduman apartigon.  Vi povas movi la kursoron
         por pligrandigi aŭ malpligrandigi la apartigon. Tiam vi povas uzi
         operatoron por plenumi ion kun la teksto.  Ekzemple,  d  forviŝas
         la tekston.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Leciono 1.5.4: AKIRI KAJ KUNFANDI DOSIEROJN


         ** Por enmeti la enhavon de dosiero, tajpu  :r DOSIERNOMON **

  1. Movu la kursoron tuj super ĉi tiu linio.

RIMARKO: Post plenumo de paŝo 2, vi vidos tekston el la leciono 5.3.  Tiam
         moviĝu SUBEN por vidi tiun lecionon denove.

  2. Nun akiru vian dosieron TESTO uzante la komandon   :r TESTO   kie TESTO
     estas la nomo de la dosiero, kiun vi uzis.
     La dosiero, kion vi akiras, estas metita sub la linio de la kursoro.

  3. Por kontroli, ĉu la dosiero akiriĝis, retromovu la kursoron kaj rimarku,
     ke estas nun du kopioj de la leciono 5.3, la originala kaj la versio mem
     de la dosiero.

RIMARKO: Vi nun povas legi la eliron de ekstera komando. Ekzemple,
        :r !ls  legas la eliron de la komando ls kaj metas ĝin sub la
        kursoron.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.5 RESUMO


  1.  :!komando  plenumas eksteran komandon.

      Iuj utilaj ekzemploj estas:
        (VINDOZO)          (UNIKSO)
         :!dir              :!ls            - listigas dosierujon
         :!del DOSIERNOMO   :!rm DOSIERNOMO - forviŝas la dosieron DOSIERNOMO

  2.  :w DOSIERNOMO  konservas la nunan dosieron de Vim al disko kun la
      nomo DOSIERNOMO.

  3.  v  movo  :w DOSIERNOMO  konservas la Viduman apartigon de linioj en
      dosiero DOSIERNOMO.

  4. :r DOSIERNOMO  akiras la dosieron DOSIERNOMO el la disko kaj metas
     ĝin sub la pozicion de la kursoro.

  5. :r !dir  legas la eligon de la komando dir kaj metas ĝin sub la
     pozicion de la kursoro.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leciono 1.6.1: LA KOMANDO DE MALFERMO


  ** Tajpu o por malfermi linion sub la kursoro kaj eniri Enmetan reĝimon. **

  1. Movu la kursoron al la suba linio markita per --->.

  2. Tajpu la minusklan literon  o  por malfermi linion SUB la kursoro kaj
     eniri la Enmetan reĝimon.

  3. Nun tajpu tekston kaj premu <ESK> por eliri el la Enmeta reĝimo.

---> Post tajpo de  o  la kursoro moviĝas al la malfermata linio en
     Enmeta reĝimo.

  4. Por malfermi linion SUPER la kursoro, nur tajpu majusklan  O  ,
     anstataŭ minusklan  o.  Provu tion per la suba linio.

---> Malfermu linion SUPER tiu tajpante O dum la kursoro estas sur tiu linio.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Leciono 1.6.2: LA KOMANDO DE POSTALDONO


                  ** Tajpu  a  por enmeti POST la kursoro. **

  1. Movu la kursoron ĉe la komenco de la linio markita per --->.

  2. Premu  e  ĝis kiam la kursoro estas ĉe la fino de  li.

  3. Tajpu  a  (minuskle) por aldoni tekston POST la kursoro.

  4. Kompletigu la vorton same kiel la linio sub ĝi.  Premu <ESK> por
     eliri el la Enmeta reĝimo.

  5. Uzu  e  por moviĝi al la sekvanta nekompleta vorto kaj ripetu
     paŝojn 3 kaj 4.

---> Ĉi tiu lin ebligos vin ekz vin postal tekston al linio.
---> Ĉi tiu linio ebligos vin ekzerci vin postaldoni tekston al linio.

RIMARKO: Ĉiu  a, i kaj A  iras al la sama Enmeta reĝimo, la nura malsamo
         estas tie, kie la signoj estas enmetitaj.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leciono 1.6.3: ALIA MANIERO POR ANSTATAŬIGI


          ** Tajpu majusklan  R  por anstataŭigi pli ol unu signo. **

  1. Movu la kursoron al la unua suba linio markita per --->.  Movu la
     kursoron al la komenco de la unua  xxx .

  2. Nun premu  R  kaj tajpu la nombron sub ĝi en la dua linio, por ke ĝi
     anstataŭigu la xxx .

  3. Premu <ESK> por foriri el la Anstataŭiga reĝimo. Rimarku, ke la cetera
     parto de la linio restas neŝanĝata.

  4. Ripetu la paŝojn por anstataŭigi la restantajn xxx.

---> Aldono de 123 al xxx donas al vi xxx.
---> Aldono de 123 al 456 donas al vi 579.

RIMARKO: Anstataŭiga reĝimo estas same kiel Enmeta reĝimo, sed ĉiu signo
         tajpita forviŝas ekzistan signon.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Leciono 1.6.4: KOPII KAJ ALGLUI TEKSTON


     ** Uzu la  y  operatoron por kopii tekston, kaj  p  por alglui ĝin **


  1. Iru al la suba linio markita per ---> kaj poziciu la kursoron post "a)".

  2. Komencu la Viduman reĝimon per  v  kaj movu la kursoron tuj antaŭ "unua".

  3. Tajpu  y  por kopii la emfazitan tekston.

  4. Movu la kursoron ĉe la fino de la linio:  j$

  5. Tajpu  p  por alglui la tekston.  Tiam tajpu:  a dua <ESK> .

  6. Uzu Viduman reĝimon por apartigi " ero.", kopiu ĝin per  y , moviĝu
     ĉe la fino de la sekvanta linio per  j$  kaj algluu la tekston tie
     per  p .

---> a) tio estas la unua ero.
     b)

RIMARKO: vi povas ankaŭ uzi  y  kiel operatoro;  yw  kopias unu vorton.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                           Leciono 1.6.5: AGORDI OPCION


         ** Agordu opcion por ke serĉo aŭ anstataŭigo ignoru usklecon **

  1. Serĉu 'ignori' per tajpo de /ignori <Enenklavo>
     Ripetu plurfoje premante  n .

  2. Ŝaltu la opcion 'ic' (ignori usklecon) per:   :set ic

  3. Nun serĉu 'ignori' denove premante  n
     Rimarku, ke Ignori kaj IGNORI estas nun troveblas.

  4. Ŝaltu la opciojn 'hlsearch' kaj 'incsearch':   :set hls is

  5. Nun retajpu la serĉan komandon kaj vidu kio okazas:  /ignore <Enenklavo>

  6. Por malŝalti ignoron de uskleco:  :set noic

RIMARKO: Por forigi emfazon de kongruo, tajpu:   :nohlsearch
RIMARKO: Se vi deziras ignori usklecon por nur unu serĉa komando, uzu  \c
         en la frazo:  /ignore\c  <Enenklavo>


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.6 RESUMO

  1. Tajpu  o  por malfermi linion SUB la kursoro kaj eki en Enmeta reĝimo.
  1. Tajpu  O  por malfermi linion SUPER la kursoro.

  2. Tajpu  a  por enmeti tekston POST la kursoro.
     Tajpu  A  por enmeti tekston post la fino de la linio.

  3. La  e  komando movas la kursoron al la fino de vorto.

  4. la  y  operatoro kopias tekston,  p  algluas ĝin.

  5. Tajpo de majuskla  R  eniras la Anstataŭigan reĝimon ĝis kiam
     <ESK> estas premita.

  6. Tajpo de ":set xxx" ŝaltas la opcion "xxx".  Iuj opcioj estas:
        'ic' 'ignorecase'     ignori usklecon dum serĉo
        'is' 'incsearch'      montru partan kongruon dum serĉo
        'hls' 'hlsearch'      emfazas ĉiujn kongruajn frazojn
     Vi povas uzi aŭ la longan, aŭ la mallongan nomon de opcio.

  7. Antaŭaldonu "no" por malŝalti la opcion:  :set noic


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                            Leciono 1.7.1: AKIRI HELPON


                          ** Uzu la helpan sistemon **

  Vim havas ampleksan helpan sistemon. Por komenciĝi, provu unu el la tiuj
  tri:
        - premu la klavon <HELPO> (se vi havas ĝin)
        - premu la klavon <F1> (se vi havas ĝin)
        - tajpu   :help <Enenklavo>

  Legu la tekston en la helpfenestro por trovi kiel helpo funkcias.
  Tajpu  CTRL-W CTRL-W      por salti de unu fenestro al la alia.
  Tajpu    :q <Enenklavo>   por fermi la helpan fenestron.

  Vi povas trovi helpon pri io ajn aldonante argumenton al la komando
  ":help".  Provu tiujn (ne forgesu premi <Enenklavo>):

        :help w
        :help c_CTRL-D
        :help insert-index
        :help user-manual


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leciono 1.7.2: KREI STARTAN SKRIPTON


                         ** Ebligu kapablojn de Vim **

  Vim havas multe pli da kapabloj ol Vi, sed la plej multaj estas defaŭlte
  malŝaltitaj.  Por ekuzi la kapablojn, vi devas krei dosieron "vimrc".

  1. Ekredaktu la dosieron "vimrc".  Tio dependas de via sistemo:
      :e ~/.vimrc          por Unikso
      :e ~/_vimrc          por Vindozo

  2. Nun legu la enhavon de la ekzempla "vimrc"
      :r $VIMRUNTIME/vimrc_example.vim

  3. Konservu la dosieron per:
      :w

  La sekvantan fojon, kiam vi lanĉas Vim, ĝi uzos sintaksan emfazon.
  Vi povas aldoni ĉiujn viajn preferatajn agordojn al tiu dosiero "vimrc".
  Por pli da informoj, tajpu  :help vimrc-intro


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             Leciono 1.7.3: KOMPLETIGO


             ** Kompletigo de komanda linio per CTRL-D kaj <TAB> **

  1. Certigu ke Vim estas en kongrua reĝimo:  :set nocp

  2. Rigardu tiujn dosierojn, kiuj ekzistas en la dosierujo:  :!ls  aŭ  :!dir

  3. Tajpu la komencon de komando:  :e

  4. Premu  CTRL-D  kaj Vim montros liston de komandoj, kiuj komencas per "e".

  5. Premu d<TAB>  kaj Vim kompletigos la nomon de la komando al ":edit".

  6. Nun aldonu spaceton kaj la komencon de ekzistanta nomo:  :edit DOSI

  7. Premu d<TAB>.  Vim kompletigos la nomon (se ĝi estas unika)

RIMARKO: Kompletigo funkcias por multaj komandoj. Nur provu premi CTRL-D kaj
         <TAB>.  Estas aparte utila por  :help .

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                                Leciono 1.7 RESUMO


  1. Tajpu  :help  aŭ premu <F1> aŭ <Helpo>  por malfermi helpan fenestron.

  2. Tajpu  :help kmd  por trovi helpon pri  kmd.

  3. Tajpu  CTRL-W CTRL-W  por salti al alia fenestro.

  4. Tajpu  :q  to fermi la helpan fenestron.

  5. Kreu komencan skripton vimrc por konservi viajn agordojn.

  6. Kiam vi tajpas  :  komandon, premu CTRL-D por vidi ĉiujn kompleteblojn.
     Premu <TAB> por uzi unu kompletigon.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Tio konkludas la instruilon de Vim.  Ĝi celis doni mallongan superrigardon
  de la redaktilo Vim, nur tion kio sufiĉas por ebligi al vi facilan uzon de
  la redaktilo. Estas nepre nekompleta, ĉar Vim havas multajn multajn pliajn
  komandojn. Legu la manlibron: ":help user-manual".

  Tiu instruilo estis verkita de Michael C. Pierce kaj Robert K. Ware,
  el la Koloradia Lernejo de Minejoj (Colorado School of Mines) uzante
  ideojn provizitajn de Charles Smith el la Stata Universitato de Koloradio
  (Colorado State University)

  Retpoŝto: <EMAIL>.

  Modifita por Vim de Bram Moolenaar.

  Esperantigita fare de Dominique Pellé, 2008-04-01
  Retpoŝto: <EMAIL>
  Lasta ŝanĝo: 2020-07-19

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
