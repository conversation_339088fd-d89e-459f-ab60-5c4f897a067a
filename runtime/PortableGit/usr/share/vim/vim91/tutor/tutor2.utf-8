===============================================================================
=    W e l c o m e   t o   t h e   V I M   T u t o r    -    Version 1.7      =
===============================================================================
=			    C H A P T E R   TWO				      =
===============================================================================

     Hic Sunt Dracones: if this is your first exposure to vim and you
     intended to avail yourself of the introductory chapter, kindly type
     :q<enter> and try again.

     The approximate time required to complete this chapter is 8-10 minutes,
     depending upon how much time is spent with experimentation.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Lesson 2.1.1: THE NAMED REGISTERS


         ** Store two yanked words concurrently and then paste them **

  1. Move the cursor to the line below marked --->

  2. Navigate to any point on the word 'Edward' and type   "ayiw

MNEMONIC: into register(") named (a) (y)ank (i)nner (w)ord

  3. Navigate forward to the word 'cookie' (fk or 2fc or $2b or /co<enter>)
     and type   "byiw

  4. Navigate to any point on the word 'Vince' and type   ciw<C-r>a<ESC>

MNEMONIC: (c)hange (i)nner (w)ord with <contents of (r)egister> named (a)

  5. Navigate to any point on the word 'cake' and type   ciw<C-r>b<ESC>

--->  a) Edward will henceforth be in charge of the cookie rations
      b) In this capacity, Vince will have sole cake discretionary powers

NOTE: Delete also works into registers, i.e. "sdiw will delete the word under
      the cursor into register s.

REFERENCE: 	Registers 	:h registers
		Named Registers :h quotea
		Motion 		:h motion.txt<enter> /inner<enter>
		CTRL-R		:h insert<enter> /CTRL-R<enter>

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

		     Lesson 2.1.2: THE EXPRESSION REGISTER


	     ** Insert the results of calculations on the fly **

  1. Move the cursor to the line below marked --->

  2. Navigate to any point on the supplied number

  3. Type ciw<C-r>=60*60*24<enter>

  4. On the next line, enter insert mode and add today's date with 
     <C-r>=system('date')<enter>

NOTE: All calls to system are OS dependent, e.g. on Windows use 
      system('date /t')   or  :r!date /t

---> I have forgotten the exact number of seconds in a day, is it 84600?
     Today's date is: 

NOTE: the same can be achieved with :pu=system('date')
      or, with fewer keystrokes :r!date

REFERENCE: 	Expression Register 	:h quote=

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

		      Lesson 2.1.3: THE NUMBERED REGISTERS


	** Press  yy and dd to witness their effect on the registers **

  1. Move the cursor to the line below marked --->

  2. yank the zeroth line, then inspect registers with :reg<enter>

  3. delete line 0. with "cdd, then inspect registers
     (Where do you expect line 0 to be?)

  4. continue deleting each successive line, inspecting :reg as you go

NOTE: You should notice that old full-line deletions move down the list
      as new full-line deletions are added

  5. Now (p)aste the following registers in order; c, 7, 4, 8, 2. i.e. "7p

---> 0. This
     9. wobble
     8. secret
     7. is
     6. on
     5. axis
     4. a
     3. war
     2. message
     1. tribute

NOTE: Whole line deletions (dd) are much longer lived in the numbered registers
      than whole line yanks, or deletions involving smaller movements

REFERENCE: 	Numbered Registers 	:h quote0


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

		      Lesson 2.1.4: THE BEAUTY OF MARKS


	           ** Code monkey arithmetic avoidance **

NOTE: a common conundrum when coding is moving around large chunks of code.
      The following technique helps avoid number line calculations associated
      with operations like   "a147d   or   :945,1091d a   or even worse using
      i<C-r>=1091-945<enter>   first

  1. Move the cursor to the line below marked --->

  2. Go to the first line of the function and mark it with   ma

NOTE: exact position on line is NOT important!

  3. Navigate to the end of the line and then the end of the code block 
     with   $%

  4. Delete the block into register a with   "ad'a

MNEMONIC: into register(") named (a) put the (d)eletion from the cursor to the
          LINE containing mark(') (a)

  5. Paste the block between BBB and CCC   "ap

NOTE: practice this operation multiple times to become fluent   ma$%"ad'a

---> AAA
     function itGotRealBigRealFast() {
       if ( somethingIsTrue ) {
         doIt()
       }
       // the taxonomy of our function has changed and it
       // no longer makes alphabetical sense in its current position

       // imagine hundreds of lines of code

       // naively you could navigate to the start and end and record or
       // remember each line number
     }
     BBB
     CCC

NOTE: marks and registers do not share a namespace, therefore register a is
      completely independent of mark a. This is not true of registers and
      macros.

REFERENCE: 	Marks 		:h marks
		Mark Motions 	:h mark-motions  (difference between ' and `)

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

			       Lesson 2.1 SUMMARY


  1. To store (yank, delete) text into, and retrieve (paste) from, a total of
     26 registers (a-z) 
  2. Yank a whole word from anywhere within a word:   yiw
  3. Change a whole word from anywhere within a word:   ciw
  4. Insert text directly from registers in insert mode:   (C-r)a

  5. Insert the results of simple arithmetic operations: (C-r)=60*60<enter>
     in insert mode
  6. Insert the results of system calls: (C-r)=system('ls -1')
     in insert mode

  7. Inspect registers with   :reg
  8. Learn the final destination of whole line deletions: dd in the numbered
     registers, i.e. descending from register 1 - 9.  Appreciate that whole
     line deletions are preserved in the numbered registers longer than any
     other operation
  9. Learn the final destination of all yanks in the numbered registers and
     how ephemeral they are

 10. Place marks from command mode   m[a-zA-Z0-9]
 11. Move line-wise to a mark with   '

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  This concludes chapter two of the Vim Tutor. It is a work in progress.

  This chapter was written by Paul D. Parker.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
