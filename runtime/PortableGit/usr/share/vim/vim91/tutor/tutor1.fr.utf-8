===============================================================================
=    B i e n v e n u e  dans  l e  T u t o r i e l  de  V I M  -  Version 1.7 =
===============================================================================

     Vim est un éditeur très puissant qui a trop de commandes pour pouvoir
     toutes les expliquer dans un cours comme celui-ci, qui est conçu pour en
     décrire suffisamment afin de vous permettre d'utiliser simplement Vim.

     Le temps requis pour suivre ce cours est d'environ 25 à 30 minutes, selon
     le temps que vous passerez à expérimenter.

     ATTENTION :
     Les commandes utilisées dans les leçons modifieront le texte. Faites une
     copie de ce fichier afin de vous entraîner dessus (si vous avez lancé
     "vimtutor" ceci est déjà une copie).

     Il est important de garder en tête que ce cours est conçu pour apprendre
     par la pratique. Cela signifie que vous devez exécuter les commandes
     pour les apprendre correctement. Si vous vous contentez de lire le texte,
     vous oublierez les commandes !

     Maintenant, vérifiez que votre clavier n'est PAS verrouillé en
     majuscules, et appuyez la touche  j  le nombre de fois suffisant pour
     que la Leçon 1.1.1 remplisse complètement l'écran.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Leçon 1.1.1 : DÉPLACEMENT DU CURSEUR


  ** Pour déplacer le curseur, appuyez les touches h,j,k,l comme indiqué. **
          ^
          k        Astuce :  La touche h est à gauche et déplace à gauche.
    < h       l >            La touche l est à droite et déplace à droite.
          j                  La touche j ressemble à une flèche vers le bas.
          v
  1. Déplacez le curseur sur l'écran jusqu'à vous sentir à l'aise.

  2. Maintenez la touche Bas (j) enfoncée jusqu'à ce qu'elle se répète.
     Maintenant vous êtes capable de vous déplacer jusqu'à la leçon suivante.

  3. En utilisant la touche Bas, allez à la Leçon 1.1.2.

NOTE : Si jamais vous doutez de ce que vous venez de taper, appuyez <Échap>
       pour revenir en mode Normal. Puis retapez la commande que vous vouliez.

NOTE : Les touches fléchées devraient également fonctionner. Mais en utilisant
       hjkl vous pourrez vous déplacer beaucoup plus rapidement, une fois que
       vous aurez pris l'habitude.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leçon 1.1.2 : SORTIR DE VIM


 !! NOTE : Avant d'effectuer les étapes ci-dessous, lisez toute cette leçon !!

  1. Appuyez la touche  <Échap>  (pour être sûr d'être en mode Normal).

  2. Tapez :  :q! <Entrée>
     Ceci quitte l'éditeur SANS enregistrer les changements que vous avez
     faits.

  3. Revenez ici en tapant la commande qui vous a mené à ce tutoriel.
     Cela pourrait être :    vimtutor <Entrée>

  4. Si vous avez mémorisé ces étapes et êtes confiant, effectuez les étapes
     1 à 3 pour sortir puis rentrer dans l'éditeur.

NOTE :  :q! <Entrée> annule tous les changements que vous avez faits. Dans
         quelques leçons, vous apprendrez à enregistrer les changements.

  5. Déplacez le curseur à la Leçon 1.1.3.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                  Leçon 1.1.3 : ÉDITION DE TEXTE - EFFACEMENT


         ** Appuyez   x   pour effacer le caractère sous le curseur. **

  1. Déplacez le curseur sur la ligne marquée ---> ci-dessous.

  2. Pour corriger les erreurs, déplacez le curseur jusqu'à ce qu'il soit
     sur un caractère à effacer.

  3. Appuyez la touche  x  pour effacer le caractère redondant.

  4. Répétez les étapes 2 à 4 jusqu'à ce que la phrase soit correcte.

---> La vvache a sautéé au-ddessus dde la luune.

  5. Maintenant que la ligne est correcte, passez à la Leçon 1.1.4.

NOTE : En avançant dans ce cours, n'essayez pas de mémoriser, apprenez par
       la pratique.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leçon 1.1.4 : ÉDITION DE TEXTE - INSERTION


                    ** Appuyez  i  pour insérer du texte. **

  1. Déplacez le curseur sur la première ligne marquée ---> ci-dessous.

  2. Pour rendre la première ligne identique à la seconde, mettez le curseur
     sur le premier caractère APRÈS l'endroit où insérer le texte.

  3. Appuyez  i  et tapez les caractères qui manquent.

  4. Une fois qu'une erreur est corrigée, appuyez <Échap> pour revenir en mode
     Normal. Répétez les étapes 2 à 4 pour corriger la phrase.

---> Il mnqe caractères cette .
---> Il manque des caractères dans cette ligne.

  5. Une fois que vous êtes à l'aise avec l'insertion de texte, allez à la
     Leçon 1.1.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leçon 1.1.5 : ÉDITION DE TEXTE - AJOUTER


                    ** Appuyez  A  pour ajouter du texte. **

  1. Déplacez le curseur sur la première ligne ci-dessous marquée --->.
     Peu importe sur quel caractère se trouve le curseur sur cette ligne.

  2. Appuyez  A  et tapez les ajouts nécessaires.

  3. Quand le texte a été ajouté, appuyez <Échap> pour revenir en mode
     Normal.

  4. Déplacez le curseur sur la seconde ligne marquée ---> et répétez les
     étapes 2 et 3 pour corriger la phrase.

---> Il manque du texte à partir de cet
     Il manque du texte à partir de cette ligne.
---> Il manque aussi du te
     Il manque aussi du texte ici.

  5. Quand vous vous sentez suffisamment à l'aise pour ajouter du texte,
     allez à la Leçon 1.1.6.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Leçon 1.1.6 : ÉDITER UN FICHIER


          ** Utilisez  :wq  pour enregistrer un fichier et sortir. **

!! NOTE : Lisez toute la leçon avant d'exécuter les instructions ci-dessous !!

  1. Sortez de ce tutoriel comme vous l'avez fait dans la Leçon 1.1.2 :  :q!
     Ou, si vous avez accès à un autre terminal, exécutez-y les actions
     qui suivent.

  2. À l'invite du shell, tapez cette commande :  vim tutor <Entrée>
     'vim' est la commande pour démarrer l'éditeur Vim, 'tutor' est le
     nom du fichier que vous souhaitez éditer. Utilisez un fichier qui peut
     être modifié.

  3. Insérez et effacez du texte comme vous l'avez appris dans les leçons
     précédentes.

  4. Enregistrez le fichier avec les changements et sortez de Vim avec :
       :wq <Entrée>

  5. Si vous avez quitté vimtutor à l'étape 1, recommencez vimtutor et
     déplacez-vous en bas vers le résumé suivant.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             RÉSUMÉ DE LA LEÇON 1.1


  1. Le curseur se déplace avec les touches fléchées ou les touches hjkl.
           h (gauche)      j (bas)      k (haut)      l (droite)

  2. Pour démarrer Vim à l'invite du shell tapez :  vim FICHIER <Entrée>

  3. Pour quitter Vim tapez :  <Échap> :q! <Entrée>  pour perdre tous les
                                                     changements.
                   OU tapez :  <Échap> :wq <Entrée>  pour enregistrer les
                                                     changements.

  4. Pour effacer un caractère sous le curseur tapez :  x

  5. Pour insérer ou ajouter du texte tapez :
         i   tapez le texte à insérer avant le curseur   <Échap>
         A   tapez le texte à ajouter en fin de ligne    <Échap>

NOTE : Appuyer  <Échap>  vous place en mode Normal ou annule une commande
       partiellement tapée dont vous ne voulez plus.

Passez maintenant à la leçon 2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leçon 1.2.1 : COMMANDES D'EFFACEMENT


                     ** Tapez  dw  pour effacer un mot. **

  1. Appuyez  <Échap>  pour être sûr d'être en mode Normal.

  2. Déplacez le curseur sur la ligne marquée ---> ci-dessous.

  3. Placez le curseur sur le début d'un mot qui a besoin d'être effacé.

  4. Tapez  dw  pour faire disparaître ce mot.

NOTE : La lettre  d  apparaîtra sur la dernière ligne de l'écran lors de
       votre frappe. Vim attend que vous tapiez  w . Si vous voyez un autre
       caractère que  d  vous avez tapé autre chose ; appuyez <Échap> et
       recommencez.

---> Il y a quelques drôle mots qui n'ont rien à faire papier sur cette ligne.

  5. Répétez les étapes 3 et 4 jusqu'à ce que la phrase soit correcte et allez
     à la Leçon 1.2.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                  Leçon 1.2.2 : PLUS DE COMMANDES D'EFFACEMENTS


          ** Tapez  d$  pour effacer jusqu'à la fin de la ligne. **

  1. Appuyez  <Échap>  pour être sûr d'être en mode Normal.

  2. Déplacez le curseur sur la ligne marquée ---> ci-dessous.

  3. Déplacez le curseur jusqu'à la fin de la ligne correcte (APRÈS le
     premier . ).

  4. Tapez  d$  pour effacer jusqu'à la fin de la ligne.

---> Quelqu'un a tapé la fin de cette ligne deux fois. cette ligne deux fois.

  5. Allez à la Leçon 1.2.3 pour comprendre ce qui se passe.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
             Leçon 1.2.3 : À PROPOS DES OPÉRATEURS ET DES MOUVEMENTS


  Plusieurs commandes qui changent le texte sont constituées d'un opérateur
  et d'un mouvement. Le format pour une commande d'effacement avec l'opérateur
  d  d'effacement est le suivant :

        d   mouvement

  Où :
    d         - est l'opérateur d'effacement
    mouvement - est le mouvement sur lequel agit l'opérateur (listés
                ci-dessous)

  Une courte liste de mouvements :
    w - jusqu'au début du prochain mot, en EXCLUANT son premier caractère.
    e - jusqu'à la fin du mot courant, en EXCLUANT son dernier caractère.
    $ - jusqu'à la fin de la ligne, en INCLUANT son dernier caractère.

  Ainsi, taper  de  va effacer depuis le curseur jusqu'à la fin du mot.

NOTE : Le seul appui d'un mouvement en mode Normal, sans commande, déplace le
       curseur comme indiqué.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            Leçon 1.2.4 : UTILISER UN QUANTIFICATEUR AVEC UN MOUVEMENT


       ** Taper un nombre avant un mouvement le répète autant de fois. **

  1. Déplacez le curseur au début de la ligne marquée ---> ci-dessous.

  2. Tapez  2w  pour déplacer le curseur de 2 mots vers l'avant.

  3. Tapez  3e  pour déplacer le curseur à la fin du troisième mot vers
     l'avant.

  4. Tapez  0  (zéro) pour déplacer au début de la ligne.

  5. Répétez les étapes 2 et 3 avec des quantificateurs différents.

---> Ceci est juste une ligne avec des mots où vous pouvez vous déplacer.

  6. Déplacez-vous à la Leçon 1.2.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            Leçon 1.2.5 : UTILISER UN QUANTIFICATEUR POUR EFFACER PLUS


       ** Taper un nombre avec un opérateur le répète autant de fois. **

  Outre la combinaison de l'opérateur d'effacement avec un déplacement
  mentionné ci-dessus, vous pouvez insérer un nombre (quantificateur)
  pour effacer encore plus :
       d   nombre   déplacement

  1. Déplacez le curseur vers le premier mot en MAJUSCULES dans la ligne
     marquée --->.

  2. Tapez  d2w  pour effacer les deux mots en MAJUSCULES.

  3. Répétez les étapes 1 et 2 avec des quantificateurs différents pour
     effacer les mots suivants en MAJUSCULES à l'aide d'une commande.

---> Cette ABC DE ligne FGHI JK LMN OP de mots est Q RS TUV nettoyée.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leçon 1.2.6 : OPÉREZ SUR DES LIGNES


              ** Tapez   dd   pour effacer une ligne complète. **

  Vu le nombre de fois où l'on efface des lignes complètes, les concepteurs
  de Vi ont décidé qu'il serait plus facile de taper simplement deux  d
  pour effacer une ligne.

  1. Placez le curseur sur la seconde ligne de la phrase ci-dessous.
  2. Tapez  dd  pour effacer la ligne.
  3. Maintenant allez à la quatrième ligne.
  4. Tapez   2dd   pour effacer deux lignes.

--->  1)  Les roses sont rouges,
--->  2)  La boue c'est drôle,
--->  3)  Les violettes sont bleues,
--->  4)  J'ai une voiture,
--->  5)  Les horloges donnent l'heure,
--->  6)  Le sucre est doux
--->  7)  Tout comme vous.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                            Leçon 1.2.7 : L'ANNULATION


            ** Tapez  u  pour annuler les dernières commandes. **
               ** Tapez  U  pour récupérer toute une ligne. **

  1. Déplacez le curseur sur la ligne marquée ---> ci-dessous et placez-le sur
     la première erreur.
  2. Tapez  x  pour effacer le premier caractère redondant.
  3. Puis tapez  u  pour annuler la dernière commande exécutée.
  4. Cette fois, corrigez toutes les erreurs de la ligne avec la commande  x .
  5. Puis tapez un  U  majuscule pour remettre la ligne dans son état initial.
  6. Puis tapez  u  deux-trois fois pour annuler le  U  et les commandes
     précédentes.
  7. Maintenant tapez  CTRL-R  (maintenez la touche CTRL enfoncée pendant que
     vous appuyez R) deux-trois fois pour refaire les commandes (annuler
     les annulations).

---> Coorrigez les erreurs suur ccette ligne et reemettez-les avvec 'annuler'.

  8. Ce sont des commandes très utiles. Maintenant, allez au résumé de la
     Leçon 1.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             RÉSUMÉ DE LA LEÇON 1.2


  1. Pour effacer du curseur jusqu'au mot suivant tapez :         dw

  2. Pour effacer du curseur jusqu'à la fin d'une ligne tapez :   d$

  3. Pour effacer toute une ligne tapez :                         dd

  4. Pour répéter un déplacement ajoutez un quantificateur :      2w

  5. Le format d'une commande de changement est :

       opérateur   [nombre]   déplacement

     Où :
       opérateur   - est ce qu'il faut faire, comme  d  pour effacer.
       [nombre]    - un quantificateur optionnel pour répéter le déplacement.
       déplacement - déplace le long du texte à opérer, tel que  w  (mot),
                     $ (jusqu'à la fin de ligne), etc.

  6. Pour se déplacer au début de ligne, utilisez un zéro :  0

  5. Pour annuler des actions précédentes, tapez :            u (u minuscule)
     Pour annuler tous les changements sur une ligne tapez :  U (U majuscule)
     Pour annuler l'annulation tapez :                        CTRL-R


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             Leçon 1.3.1 : LE COLLAGE


   ** Tapez  p  pour placer après le curseur ce qui vient d'être effacé. **

  1. Placez le curseur sur la première ligne ci-dessous marquée --->.

  2. Tapez  dd  pour effacer la ligne et la placer dans un registre de Vim.

  3. Déplacez le curseur sur la ligne c) au-dessus où vous voulez remettre la
     ligne effacée.

  4. En mode Normal, tapez   p   pour remettre la ligne en dessous du curseur.

  5. Répétez les étapes 2 à 4 pour mettre toutes les lignes dans le bon ordre.

---> d) Et vous, qu'apprenez-vous ?
---> b) Les violettes sont bleues,
---> c) L'intelligence s'apprend,
---> a) Les roses sont rouges,


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leçon 1.3.2 : LA COMMANDE DE REMPLACEMENT


     ** Tapez  rx  pour remplacer un caractère sous le curseur par  x . **

  1. Déplacez le curseur sur la première ligne marquée ---> ci-dessous.

  2. Placez le curseur de manière à ce qu'il surplombe la première erreur.

  3. Tapez  r  suivi du caractère qui doit corriger l'erreur.

  4. Répétez les étapes 2 et 3 jusqu'à ce que la première ligne soit égale
     à la seconde.

--->  Quand cette ligne a été sauvie, quelqu'un a lait des faunes de frappe !
--->  Quand cette ligne a été saisie, quelqu'un a fait des fautes de frappe !

  5. Maintenant, allez à la Leçon 1.3.3.

NOTE : N'oubliez pas que vous devriez apprendre par la pratique, pas par
       mémorisation.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leçon 1.3.3 : L'OPÉRATEUR DE CHANGEMENT


            ** Pour changer jusqu'à la fin d'un mot, tapez  ce .**

  1. Déplacez le curseur sur la première ligne marquée ---> ci-dessous.

  2. Placez le curseur sur le  u  de luhko.

  3. Tapez  ce  et corrigez le mot (dans notre cas, tapez  'igne'.)

  4. Appuyez <Échap> et placez-vous sur le prochain caractère qui doit
     être changé.

  5. Répétez les étapes 3 et 4 jusqu'à ce que la première phrase soit
     identique à la seconde.

---> Cette luhko contient quelques myqa qui ont ricne d'être chantufip.
---> Cette ligne contient quelques mots qui ont besoin d'être changés.

Notez que  ce  efface le mot et vous place ensuite en mode Insertion.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leçon 1.3.4 : PLUS DE CHANGEMENTS AVEC c


    ** L'opérateur de changement fonctionne avec les mêmes déplacements
       que l'effacement. **

  1. L'opérateur de changement fonctionne de la même manière que
     l'effacement. Le format est :

         c   [nombre]   déplacement

  2. Les déplacements sont identiques :  w (mot) et  $ (fin de ligne).

  3. Déplacez-vous sur la première ligne marquée ---> ci-dessous.

  4. Placez le curseur sur la première erreur.

  5. Tapez  c$  et tapez le reste de la ligne afin qu'elle soit identique
     à la seconde ligne, puis tapez <Échap>.

---> La fin de cette ligne doit être rendue identique à la seconde.
---> La fin de cette ligne doit être corrigée avec la commande  c$ .

NOTE :  Vous pouvez utiliser la touche Retour Arrière pour corriger les
        erreurs lorsque vous tapez.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             RÉSUMÉ DE LA LEÇON 1.3


  1. Pour remettre le texte qui a déjà été effacé, tapez  p . Cela Place le
     texte effacé APRÈS le curseur (si une ligne complète a été effacée, elle
     sera placée sous la ligne du curseur).

  2. Pour remplacer le caractère sous le curseur, tapez  r  suivi du caractère
     qui remplacera l'original.

  3. L'opérateur de changement vous permet de changer depuis la position du
     curseur jusqu'où le déplacement vous amène. Par exemple, tapez  ce
     pour changer du curseur jusqu'à la fin du mot,  c$  pour changer jusqu'à
     la fin d'une ligne.

  4. Le format pour le changement est :

         c   [nombre]   déplacement

Passez maintenant à la leçon suivante.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
               Leçon 1.4.1 : POSITION DU CURSEUR ET ÉTAT DU FICHIER


  ** Tapez CTRL-G pour afficher votre position dans le fichier et son état.
     Tapez  G  pour vous rendre à une ligne donnée du fichier. **

NOTE : Lisez toute cette leçon avant d'effectuer l'une des étapes !!

  1. Maintenez enfoncée la touche CTRL et appuyez sur  g . On appelle cela
     CTRL-G. Une ligne d'état va apparaître en bas de l'écran avec le nom
     du fichier et le numéro de la ligne où vous êtes. Notez ce numéro, il
     servira lors de l'étape 3.

NOTE : Vous pouvez peut-être voir le curseur en bas à droite de l'écran.
       Ceci arrive quand l'option 'ruler' est activée (voir  :help 'ruler')

  2. Tapez  G   pour vous déplacer à la fin du fichier.
     Tapez  gg  pour vous déplacer au début du fichier.

  3. Tapez le numéro de la ligne où vous étiez suivi de   G . Cela vous
     ramènera à la ligne où vous étiez au départ quand vous aviez appuyé
     CTRL-G.

  4. Si vous vous sentez prêt à faire ceci, effectuez les étapes 1 à 3.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                            Leçon 1.4.2 : LA RECHERCHE


          ** Tapez  /  suivi d'un texte pour rechercher ce texte. **

  1. Tapez le caractère  /  en mode Normal. Notez que celui-ci et le curseur
     apparaissent en bas de l'écran, comme lorsque l'on utilise  : .

  2. Puis tapez 'errreuur' <Entrée>. C'est le mot que vous voulez rechercher.

  3. Pour rechercher à nouveau le même texte, tapez simplement  n .
     Pour rechercher le même texte dans la direction opposée, tapez  N .

  4. Pour rechercher une phrase dans la direction opposée, utilisez  ?
     au lieu de  / .

---> erreur ne s'écrit pas "errreuur" ; errreuur est une erreur.

NOTE : Quand la recherche atteint la fin du fichier, elle reprend au début
       sauf si l'option 'wrapscan' est désactivée.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            Leçon 1.4.3 : RECHERCHE DES PARENTHÈSES CORRESPONDANTES


           ** Tapez  %  pour trouver des ), ] ou } correspondants. **

  1. Placez le curseur sur l'un des (, [ ou { de la ligne marquée --->
     ci-dessous.

  2. Puis tapez le caractère  % .

  3. Le curseur se déplacera sur la parenthèse ou crochet correspondant.

  4. Tapez  %  pour replacer le curseur sur la parenthèse ou crochet
     correspondant.

  5. Déplacez le curseur sur un autre (,),[,],{ ou } et regardez ce que
     fait  % .

---> Voici ( une ligne de test contenant des (, des [ ] et des { } )).

NOTE : Cette fonctionnalité est très utile lors du débogage d'un programme qui
       contient des parenthèses déséquilibrées !


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leçon 1.4.4 : LA COMMANDE DE SUBSTITUTION


    ** Tapez  :s/ancien/nouveau/g  pour remplacer 'ancien' par 'nouveau'. **

  1. Déplacez le curseur sur la ligne marquée ---> ci-dessous.

  2. Tapez  :s/lee/le <Entrée> . Notez que cette commande change seulement la
     première occurrence de "lee" dans la ligne.

  3. Puis tapez  :s/lee/le/g . L'ajout du drapeau  g  ordonne de faire une
     substitution globale sur la ligne, et change toutes les occurrences de
     "lee" sur la ligne.

---> lee meilleur moment pour regarder lees fleurs est pendant lee printemps.

  4. Pour changer toutes les occurrences d'un texte, entre deux lignes,
     tapez  :#,#s/ancien/nouveau/g  où #,# sont les numéros de lignes de la
                                    plage où la substitution doit être faite.
     Tapez  :%s/ancien/nouveau/g    pour changer toutes les occurrences dans
                                    tout le fichier.
     Tapez  :%s/ancien/nouveau/gc   pour trouver toutes les occurrences dans
                                    tout le fichier avec une invite pour
                                    confirmer ou infirmer chaque substitution.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             RÉSUMÉ DE LA LEÇON 1.4


  1. CTRL-G       affiche la position dans le fichier et l'état de celui-ci.
               G  déplace à la fin du fichier.
     nombre    G  déplace au numéro de ligne.
              gg  déplace à la première ligne.

  2. Taper  /  suivi d'un texte recherche ce texte vers l'AVANT.
     Taper  ?  suivi d'un texte recherche ce texte vers l'ARRIÈRE.
     Après une recherche tapez  n  pour trouver l'occurrence suivante dans la
     même direction ou  Maj-N  pour rechercher dans la direction opposée.

  3. Taper  %  lorsque le curseur est sur  (, ), [, ], { ou }  déplace
     celui-ci sur le caractère correspondant.

  4. Pour remplacer le premier aa par bb sur une ligne tapez     :s/aa/bb
     Pour remplacer tous les aa par bb sur une ligne tapez       :s/aa/bb/g
     Pour remplacer du texte entre deux numéros de ligne tapez   :#,#s/aa/bb/g
     Pour remplacer toutes les occurrences dans le fichier tapez :%s/aa/bb/g
     Pour demander une confirmation à chaque fois ajoutez 'c'    :%s/aa/bb/gc


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
              Leçon 1.5.1 : COMMENT EXÉCUTER UNE COMMANDE EXTERNE


 ** Tapez  :!  suivi d'une commande externe pour exécuter cette commande. **

  1. Tapez le  :  familier pour mettre le curseur en bas de l'écran. Cela vous
     permet de saisir une commande.

  2. Puis tapez un  !  (point d'exclamation). Cela vous permet d'exécuter
     n'importe quelle commande valide pour votre interpréteur (shell).

  3. Par exemple, tapez  ls  après le  !  et appuyez <Entrée>. Ceci affichera
     la liste des fichiers du répertoire courant, comme si vous aviez tapé la
     commande à l'invite du shell. Utilisez  :!dir  si  :!ls  ne marche pas.

NOTE :  Il est possible d'exécuter n'importe quelle commande externe de cette
        manière, avec ou sans argument.

NOTE :  Toutes les commandes  :  doivent finir par la frappe de <Entrée>.
        À partir de maintenant, nous ne le mentionnerons plus.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
         Leçon 1.5.2 : PLUS DE DÉTAILS SUR L'ENREGISTREMENT DE FICHIERS


 ** Pour enregistrer les changements faits au texte, tapez  :w FICHIER . **

  1. Tapez  :!dir  ou  :!ls  pour avoir la liste des fichiers dans le
     répertoire courant. Vous savez déjà qu'il faut appuyer <Entrée> après
     cela.

  2. Choisissez un nom de fichier qui n'existe pas encore, par exemple TEST.

  3. Puis tapez  :w TEST  (où TEST est le nom que vous avez choisi).

  4. Cela enregistre tout le fichier (Tutoriel Vim) sous le nom TEST.
     Pour le vérifier, tapez  :!dir  ou  :!ls  de nouveau pour revisualiser
     votre répertoire.

NOTE : Si vous quittez Vim et le redémarrez de nouveau avec le fichier TEST,
       celui-ci sera une copie exacte de ce cours au moment où vous l'avez
       enregistré.

  5. Maintenant, effacez le fichier en tapant (Windows) :   :!del TEST
                                           ou (Unix) :      :!rm TEST


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                  Leçon 1.5.3 : SÉLECTION DU TEXTE À ENREGISTRER


                  ** Pour enregistrer une portion du fichier,
                      tapez :   v  déplacement  :w FICHIER  **

  1. Déplacez le curseur sur cette ligne.

  2. Appuyez  v  et déplacez le curseur vers la cinquième ligne plus bas.
     Remarquez que le texte est en surbrillance.

  3. Appuyez  :  . En bas de l'écran  :'<,'> va apparaître.

  4. Tapez   w TEST  , où TEST est un nom de fichier qui n'existe pas.
     Vérifiez que vous voyez  :'<,'>w TEST  avant d'appuyer sur <Entrée>.

  5. Vim va enregistrer les lignes sélectionnées dans le fichier TEST.
     Utilisez  :!dir  ou  :!ls pour le voir. Ne l'effacez pas encore !
     Nous allons l'utiliser dans la leçon suivante.

NOTE : L'appui de  v  démarre la sélection Visuelle. Vous pouvez déplacer le
       curseur pour agrandir ou rétrécir la sélection. Puis vous pouvez
       utiliser un opérateur pour faire quelque chose sur le texte. Par
       exemple,  d  efface le texte.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Leçon 1.5.4 : RÉCUPÉRATION ET FUSION DE FICHIERS


        ** Pour insérer le contenu d'un fichier, tapez  :r FICHIER **

  1. Placez le curseur juste au-dessus de cette ligne.

NOTE :  Après avoir exécuté l'étape 2 vous verrez du texte de la Leçon 1.5.3.
        Puis déplacez-vous vers le bas pour voir cette leçon à nouveau.

  2. Maintenant récupérez votre fichier TEST en utilisant la commande  :r TEST
     où TEST est le nom de votre fichier.
     Le fichier que vous récupérez est placé au-dessous de la ligne du curseur.

  3. Pour vérifier que le fichier a bien été inséré, remontez et vérifiez
     qu'il y a maintenant deux copies de la Leçon 1.5.3, l'originale et celle
     contenue dans le fichier.

NOTE :  Vous pouvez aussi lire la sortie d'une commande externe. Par exemple,
        :r !ls  lit la sortie de la commande ls et la place sous la ligne du
        curseur.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             RÉSUMÉ DE LA LEÇON 1.5


  1. :!commande  exécute une commande externe.

     Quelques exemples pratiques :
      (Windows)       (Unix)
       :!dir           :!ls          affiche le contenu du répertoire courant.
       :!del FICHIER   :!rm FICHIER  efface FICHIER.

  2. :w FICHIER  enregistre le fichier Vim courant sur le disque avec pour
     nom FICHIER.

  3. v  déplacement  :w FICHIER sauvegarde les lignes de la sélection Visuelle
     dans le fichier FICHIER.

  4. :r FICHIER  récupère le contenu du fichier FICHIER et l'insère sous la
     position du curseur.

  5. :r !dir  lit la sortie de la commande dir et l'insère sous la position
     du curseur.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Leçon 1.6.1 : LA COMMANDE D'OUVERTURE


** Tapez  o  pour ouvrir une ligne sous le curseur et y aller en Insertion. **

  1. Déplacez le curseur sur la ligne marquée ---> ci-dessous.

  2. Tapez la lettre   o   minuscule pour ouvrir une ligne SOUS le curseur et
     vous y placer en mode Insertion.

  3. Puis tapez du texte et appuyez <Échap> pour sortir du mode Insertion.

---> En tapant  o  le curseur se met sur la ligne ouverte, en mode Insertion.

  4. Pour ouvrir une ligne au-DESSUS du curseur, tapez simplement un  O
     majuscule, plutôt qu'un  o  minuscule. Faites un essai sur la ligne
     ci-dessous.

---> Ouvrez une ligne ci-dessus en tapant O lorsque le curseur est ici.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Leçon 1.6.2 : LA COMMANDE D'AJOUT


            ** Tapez  a  pour insérer du texte APRÈS le curseur. **

  1. Placez le curseur au début de la ligne marquée ---> ci-dessous.

  2. Appuyez  e  jusqu'à ce que le curseur soit sur la fin de  li .

  3. Appuyez  a  (minuscule) pour ajouter du texte APRÈS le curseur.

  4. Complétez le mot comme dans la ligne dessous. Appuyez <Échap> pour
     sortir du mode Insertion.

  5. Utilisez  e  pour vous déplacer vers le mot incomplet suivant et
     répétez les étapes 3 et 4.

---> Cette li vous perm de pratiq l'ajout de t dans une ligne.
---> Cette ligne vous permet de pratiquer l'ajout de texte dans une ligne.

NOTE :  a, i, A vont tous dans le même mode Insertion, la seule différence
        est l'endroit où les caractères sont insérés.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Leçon 1.6.3 : UNE AUTRE MANIÈRE DE REMPLACER


       ** Tapez un  R  majuscule pour remplacer plus d'un caractère. **

  1. Déplacez le curseur sur la première ligne marquée ---> ci-dessous.
     Déplacez le curseur sur le début du premier xxx .

  2. Appuyez maintenant  R  et tapez le nombre dessous dans la deuxième ligne,
     de manière à remplacer le xxx .

  3. Appuyez <Échap> pour quitter le mode Remplacement. Notez que le reste de
     la ligne demeure inchangé.

  4. Répétez les étapes pour remplacer les xxx restants.


---> L'ajout de 123 à xxx donne xxx.
---> L'ajout de 123 à 456 donne 579.

NOTE : Le mode Remplacement est comme le mode Insertion, mais tous les
       caractères tapés effacent un caractère existant.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Leçon 1.6.4 : COPIER ET COLLER DU TEXTE


   ** Utilisez l'opérateur  y  pour copier du texte et  p  pour le coller **

  1. Allez à la ligne marquée ---> ci-dessous et placez le curseur après "a)".

  2. Démarrez le mode Visuel avec  v  et déplacez le curseur juste devant
     "premier".

  3. Tapez  y  pour copier le texte en surbrillance.

  4. Déplacez le curseur à la fin de la ligne suivante :   j$

  5. Tapez  p  pour coller le texte. Puis tapez :  un second <Échap> .

  6. Utilisez le mode Visuel pour sélectionner "élément", copiez-le avec  y  ,
     déplacez-vous à la fin de la ligne suivante avec  j$  et collez le texte
     à cet endroit avec  p .

--->  a) ceci est le premier élément.
      b)

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Leçon 1.6.5 : RÉGLAGE DES OPTIONS


    ** Réglons une option afin que la recherche et la substitution ignorent la
       casse des caractères. **

  1. Recherchez 'ignore' en tapant :   /ignore <Entrée>
     Répétez ceci plusieurs fois en utilisant la touche  n .

  2. Activez l'option 'ic' (ignorer casse) en tapant  :set ic .

  3. Puis cherchez 'ignore' de nouveau en utilisant  n .
     Remarquez que Ignore et IGNORE sont maintenant aussi trouvés.

  4. Activez les options 'hlsearch' et 'incsearch' avec  :set hls is .

  5. Puis recommencez une recherche, et faites bien attention à ce qui se
     produit :  /ignore <Entrée>

  6. Pour désactiver 'ignorer casse', entrez :  :set noic

NOTE : Pour enlever la surbrillance des résultats, entrez :   :nohlsearch

NOTE : Si vous voulez ignorer la casse uniquement pour une recherche, utilisez
       \c  dans la phrase :   /ignore\c  <Entrée>


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                             RÉSUMÉ DE LA LEÇON 1.6


  1. Taper  o  ouvre une ligne SOUS le curseur et démarre le mode Insertion.
     Taper  O  ouvre une ligne au-DESSUS du curseur.

  2. Taper  a  pour insérer du texte APRÈS le curseur.
     Taper  A  pour insérer du texte après la fin de ligne.

  3. Taper  e  déplace à la fin du mot.

  4. Taper  y  copie du texte,  p  le colle.

  5. Taper  R  majuscule active le mode Remplacement jusqu'à ce qu' <Échap>
     soit appuyé.

  6. Taper  ":set xxx"  active l'option "xxx". Quelques options sont :
        'ic'  'ignorecase' pour ignorer la casse lors des recherches.
        'is'  'incsearch'  pour montrer les appariements partiels.
        'hls' 'hlsearch'   pour mettre en surbrillance les appariements.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Leçon 1.7.1 : OBTENIR DE L'AIDE


                  ** Utiliser le système d'aide en ligne. **

  Vim a un système complet d'aide en ligne. Pour y accéder, essayez l'une de
  ces trois méthodes :
        - appuyez la touche <Help> (si vous en avez une)
        - appuyez la touche <F1> (si vous en avez une)
        - tapez  :help <Entrée>


  Lisez le texte dans la fenêtre d'aide pour savoir comment fonctionne l'aide.
  Tapez  CTRL-W CTRL-W   pour sauter d'une fenêtre à l'autre.
  Tapez  :q <Entrée>     pour fermer la fenêtre d'aide.

  Vous pouvez accéder à l'aide sur à peu près n'importe quel sujet en donnant
  des arguments à la commande  :help . Essayez par exemple (n'oubliez pas
  d'appuyer sur <Entrée>) :

        :help w
        :help c_CTRL-D
        :help c_<T
        :help insert-index
        :help user-manual


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Leçon 1.7.2 : CRÉER UN SCRIPT DE DÉMARRAGE

                   ** Activer les fonctionnalités de Vim. **

  Vim a beaucoup plus de fonctionnalités que Vi, mais la plupart de celles-ci
  sont désactivées par défaut. Pour commencer à les utiliser, vous devez
  créer un fichier "vimrc".

  1. Commencez à éditer le fichier "vimrc". Ceci dépend de votre système :
        :edit ~/.vimrc         pour Unix
        :edit ~/_vimrc         pour Windows

  2. Lisez maintenant le fichier d'exemple "vimrc" :
        :r $VIMRUNTIME/vimrc_example.vim

  3. Enregistrez le fichier avec :
        :w

  La prochaine fois que vous démarrerez Vim, la coloration syntaxique sera
  activée. Vous pouvez ajouter tous vos réglages préférés dans ce fichier
  "vimrc". Pour plus d'informations, tapez  :help vimrc-intro


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                            Leçon 1.7.3 : COMPLÈTEMENT


          ** Complètement de ligne de commande avec CTRL-D et <TAB> **

  1. Mettez Vim soit en mode non compatible :   set nocp

  2. Regardez quels fichiers existent dans le répertoire :  !ls  ou  !dir

  3. Tapez le début d'une commande :   :e

  4. Appuyez  CTRL-D  et Vim affichera une liste de commandes qui commencent
     par "e".

  5. Appuyez  d<TAB>  et Vim complétera le nom de la commande :  ":edit"

  6. Ajoutez maintenant un espace et le début d'un fichier existant :
     :edit  FIC

  7  Appuyez  <TAB>. Vim va compléter le nom (s'il est unique).

NOTE : Le complètement fonctionne pour de nombreuses commandes. Essayez
       d'appuyer CTRL-D et <TAB>. C'est utile en particulier pour  :help .


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                              RÉSUMÉ DE LA LEÇON 1.7


  1. Tapez  :help  ou appuyez <F1> ou <Aide> pour ouvrir la fenêtre d'aide.

  2. Tapez  :help cmd  pour trouver l'aide sur  cmd .

  3. Tapez  CTRL-W CTRL-W  pour sauter à une autre fenêtre.

  4. Tapez  :q  pour fermer la fenêtre d'aide.

  5. Créez un script de démarrage vimrc pour conserver vos réglages préférés.

  6. Quand vous tapez une commande  :  appuyez CTRL-D pour voir les
     complètements possibles. Appuyez <TAB> pour utiliser un complètement.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Ceci conclut le Tutoriel Vim. Le but était de vous donner un bref aperçu de
  l'éditeur Vim, juste assez pour vous permettre d'utiliser l'éditeur
  relativement facilement. Il est loin d'être complet, vu que Vim a beaucoup
  plus de commandes. Un Manuel de l'utilisateur est disponible en anglais :
    :help user-manual

  Pour continuer à découvrir et à apprendre Vim, il existe un livre traduit en
  français. Il parle plus de Vi que de Vim, mais pourra vous être utile.
        L'éditeur Vi - Collection Précis et concis - par Arnold Robbins
        Éditeur : O'Reilly France
        ISBN : 2-84177-102-4

  Deux livres en anglais sont également mentionnés dans la version originale
  de ce tutoriel, dont un qui traite spécifiquement de Vim. Merci de vous y
  référer si vous êtes intéressés.

  Ce tutoriel a été écrit par Michael C. Pierce et Robert K. Ware de l'École
  des Mines du Colorado et reprend des idées fournies par Charles Smith,
  Université d'État du Colorado. E-mail : <EMAIL>.

  Modifié pour Vim par Bram Moolenaar.
  Traduit en français par Adrien Beau, en avril 2001.
  Dernières mises à jour par Dominique Pellé.

  E-mail :      <EMAIL>
  Last Change : 2018 Dec 2
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
