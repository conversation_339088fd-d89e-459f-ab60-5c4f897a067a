===============================================================================
=    W i t a j   w   t u t o r i a l u   V I M - a      -    Wersja  1.7.     =
===============================================================================

     Vim to potężny edytor, który posiada wiele poleceń, zbyt dużo, by
     wy<PERSON><PERSON><PERSON><PERSON> je wszystkie w tym tutorialu. Ten przewodnik ma nauczyć
     Cię posługiwać się wystarczająco wieloma komendami, byś mógł łatwo
     używać Vima jako edytora ogólnego przeznaczenia.

     Czas potrzebny na ukończenie tutoriala to 25 do 30 minut i zależy
     od tego jak wiele czasu spędzisz na eksperymentowaniu.

	 UWAGA:
	 Polecenia wykonywane w czasie lekcji zmodyfikują tekst. Zrób
	 wcześniej kopię tego pliku do ćwiczeń (jeśli zacząłeś komendą
	 "vimtutor", to już pracujesz na kopii).

	 Pamiętaj, że przewodnik ten został zaprojektowany do nauki poprzez
	 ćwiczenia. Oznacza to, że musisz wykonywać polecenia, by nauczyć się ich
	 prawidłowo. Jeśli będziesz jedynie czytał tekst, szybko zapomnisz wiele
	 poleceń!

     Teraz upewnij się, że nie masz wciśniętego Caps Locka i wciskaj  j
     tak długo dopóki Lekcja 1.1.1. nie wypełni całkowicie ekranu.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcja 1.1.1.: PORUSZANIE SIĘ KURSOREM

       ** By wykonać ruch kursorem, wciśnij h, j, k, l jak pokazano. **

	       ^
	       k		      Wskazówka:  h jest po lewej
	  < h	  l >				  l jest po prawej
	       j				  j wygląda jak strzałka w dół
	       v
  1. Poruszaj kursorem dopóki nie będziesz pewien, że pamiętasz polecenia.

  2. Trzymaj  j  tak długo aż będzie się powtarzał.
     Teraz wiesz jak dojść do następnej lekcji.

  3. Używając strzałki w dół przejdź do następnej lekcji.

Uwaga: Jeśli nie jesteś pewien czegoś co wpisałeś, wciśnij <ESC>, by wrócić do
       trybu Normal. Wtedy powtórz polecenie.

Uwaga: Klawisze kursora także powinny działać, ale używając  hjkl  będziesz
       w stanie poruszać się o wiele szybciej, jak się tylko przyzwyczaisz.
       Naprawdę!

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcja 1.1.2.: WYCHODZENIE Z VIM-a

 !! UWAGA: Przed wykonaniem jakiegokolwiek polecenia przeczytaj całą lekcję !!

  1. Wciśnij <ESC> (aby upewnić się, że jesteś w trybie Normal).
  2. Wpisz:			:q!<ENTER>.
     To spowoduje wyjście z edytora PORZUCAJĄC wszelkie zmiany, jakie
     zdążyłeś zrobić. Jeśli chcesz zapamiętać zmiany i wyjść,
     wpisz:			:wq<ENTER>

  3. Kiedy widzisz znak zachęty powłoki wpisz komendę, żeby wrócić
     do tutoriala. Czyli:	vimtutor<ENTER>

  4. Jeśli chcesz zapamiętać polecenia, wykonaj kroki 1. do 3., aby
     wyjść i wrócić do edytora.

UWAGA: :q!<ENTER> porzuca wszelkie zmiany jakie zrobiłeś. W następnych
       lekcjach dowiesz się jak je zapamiętywać.

  5. Przenieś kursor do lekcji 1.1.3.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Lekcja 1.1.3.: EDYCJA TEKSTU - KASOWANIE

	    ** Wciśnij  x  aby usunąć znak pod kursorem. **

  1. Przenieś kursor do linii poniżej oznaczonej --->.

  2. By poprawić błędy, naprowadź kursor na znak do usunięcia.

  3. Wciśnij  x  aby usunąć niechciany znak.

  4. Powtarzaj kroki 2. do 4. dopóki zdanie nie jest poprawne.

---> Kkrowa prrzeskoczyła prrzez ksiiężycc.

  5. Teraz, kiedy zdanie jest poprawione, przejdź do Lekcji 1.1.4.

UWAGA: Ucz się przez ćwiczenie, nie wkuwanie.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	   Lekcja 1.1.4.: EDYCJA TEKSTU - INSERT (wprowadzanie)


		  ** Wciśnij  i  aby wstawić tekst. **

  1. Przenieś kursor do pierwszej linii poniżej oznaczonej --->.

  2. Aby poprawić pierwszy wiersz, ustaw kursor na pierwszym znaku PO tym,
     gdzie tekst ma być wstawiony.

  3. Wciśnij  i  a następnie wpisz konieczne poprawki.

  4. Po poprawieniu błędu wciśnij <ESC>, by wrócić do trybu Normal.
     Powtarzaj kroki 2. do 4., aby poprawić całe zdanie.

---> W tej brkje trochę .
---> W tej linii brakuje trochę tekstu.

  5. Kiedy czujesz się swobodnie wstawiając tekst, przejdź do
     podsumowania poniżej.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	   Lekcja 1.1.5.: EDYCJA TEKSTU - APPENDING (dodawanie)


		   ** Wciśnij  A  by dodać tekst. **

  1. Przenieś kursor do pierwszej linii poniżej oznaczonej --->.
     Nie ma znaczenia, który to będzie znak.

  2. Wciśnij  A  i wpisz odpowiednie dodatki.

  3. Kiedy tekst został dodany, wciśnij <ESC> i wróć do trybu Normalnego.

  4. Przenieś kursor do drugiej linii oznaczonej ---> i powtórz kroki 2. i 3.,
     aby poprawić zdanie.

---> Brakuje tu tro
     Brakuje tu trochę tekstu.
---> Tu też trochę bra
     Tu też trochę brakuje.

  5. Kiedy już utrwaliłeś ćwiczenie, przejdź do lekcji 1.1.6.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Lekcja 1.1.6.: EDYCJA PLIKU

		  ** Użyj  :wq  aby zapisać plik i wyjść. **

   !! UWAGA: zanim wykonasz jakiekolwiek polecenia przeczytaj całą lekcję !!

  1. Zakończ tutorial tak jak w lekcji 1.1.2.:  :q!
     lub, jeśli masz dostęp do innego terminala, wykonaj kolejne kroki tam.

  2. W powłoce wydaj polecenie:  vim tutor<ENTER>
     "vim" jest poleceniem uruchamiającym edytor Vim. 'tutor' to nazwa pliku,
     jaki chcesz edytować. Użyj pliku, który może zostać zmieniony.

  3. Dodaj i usuń tekst tak, jak się nauczyłeś w poprzednich lekcjach.

  4. Zapisz plik ze zmianami i opuść Vima:  :wq<ENTER>

  5. Jeśli zakończyłeś vimtutor w kroku 1., uruchom go ponownie i przejdź
     do podsumowania poniżej.

  6. Po przeczytaniu wszystkich kroków i ich zrozumieniu: wykonaj je.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     LEKCJA 1.1. PODSUMOWANIE

  1. Poruszasz kursorem używając "strzałek" i klawiszy  hjkl .
       h (w lewo)	 j (w dół)	 k (do góry)		l (w prawo)

  2. By wejść do Vima, (z powłoki) wpisz:
			    vim NAZWA_PLIKU<ENTER>

  3. By wyjść z Vima, wpisz:
			    <ESC> :q!<ENTER>  by usunąć wszystkie zmiany.
	     LUB:	    <ESC> :wq<ENTER>  by zmiany zachować.

  4. By usunąć znak pod kursorem, wciśnij:  x

  5. By wstawić tekst przed kursorem lub dodać:
	i   wpisz tekst   <ESC>         wstawi przed kursorem
	A   wpisz tekst   <ESC>         doda na końcu linii

UWAGA: Wciśnięcie <ESC> przeniesie Cię z powrotem do trybu Normal
       lub odwoła niechciane lub częściowo wprowadzone polecenia.

Teraz możemy kontynuować i przejść do Lekcji 1.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcja 1.2.1.: POLECENIE DELETE (usuwanie)


		      ** Wpisz  dw  by usunąć wyraz. **

  1. Wciśnij  <ESC>, by upewnić się, że jesteś w trybie Normal.

  2. Przenieś kursor do linii poniżej oznaczonej --->.

  3. Przesuń kursor na początek wyrazu, który chcesz usunąć.

  4. Wpisz   dw   by usunąć wyraz.

  UWAGA: Litera  d  pojawi się na dole ekranu. Vim czeka na wpisanie  w .
	 Jeśli zobaczysz inny znak, oznacza to, że wpisałeś coś źle; wciśnij
	 <ESC> i zacznij od początku.

---> Jest tu parę papier wyrazów, które kamień nie należą do nożyce tego zdania.

  5. Powtarzaj kroki 3. i 4. dopóki zdanie nie będzie poprawne, potem
  przejdź do Lekcji 1.2.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcja 1.2.2.: WIĘCEJ POLECEŃ USUWAJĄCYCH


	      ** Wpisz	d$  aby usunąć tekst do końca linii. **

  1. Wciśnij  <ESC>  aby się upewnić, że jesteś w trybie Normal.

  2. Przenieś kursor do linii poniżej oznaczonej --->.

  3. Przenieś kursor do końca poprawnego zdania (PO pierwszej  . ).

  4. Wpisz  d$  aby usunąć resztę linii.

---> Ktoś wpisał koniec tego zdania dwukrotnie. zdania dwukrotnie.


  5. Przejdź do Lekcji 1.2.3., by zrozumieć co się stało.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lekcja 1.2.3.: O OPERATORACH I RUCHACH


  Wiele poleceń zmieniających tekst jest złożonych z operatora i ruchu.
  Format dla polecenia usuwającego z operatorem  d  jest następujący:

	    d  ruch

  gdzie:
   d      - operator usuwania.
   ruch   - na czym polecenie będzie wykonywane (lista poniżej).

  Krótka lista ruchów:
    w - do początku następnego wyrazu WYŁĄCZAJĄC pierwszy znak.
    e - do końca bieżącego wyrazu, WŁĄCZAJĄC ostatni znak.
    $ - do końca linii, WŁĄCZAJĄC ostatni znak.

W ten sposób wpisanie  de  usunie znaki od kursora do końca wyrazu.

UWAGA: Wpisanie tylko ruchu w trybie Normal bez operatora przeniesie kursor
       tak, jak to określono.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcja 1.2.4.: UŻYCIE MNOŻNIKA DLA RUCHU


   ** Wpisanie liczby przed ruchem powtarza ruch odpowiednią ilość razy. **

  1. Przenieś kursor na początek linii poniżej zaznaczonej --->.

  2. Wpisz  2w  aby przenieść kursor o dwa wyrazy do przodu.

  3. Wpisz  3e  aby przenieść kursor do końca trzeciego wyrazu w przód.

  4. Wpisz  0  (zero), aby przenieść kursor na początek linii.

  5. Powtórz kroki 2. i 3. z innymi liczbami.


 ---> To jest zwykły wiersz z wyrazami, po których możesz się poruszać.

  6. Przejdź do lekcji 1.2.5.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lekcja 1.2.5.: UŻYCIE MNOŻNIKA, BY WIĘCEJ USUNĄĆ


    ** Wpisanie liczby z operatorem powtarza go odpowiednią ilość razy. **

  W wyżej wspomnianej kombinacji operatora usuwania i ruchu podaj mnożnik
  przed ruchem, by więcej usunąć:
	d  liczba  ruch

  1. Przenieś kursor do pierwszego wyrazu KAPITALIKAMI w linii zaznaczonej --->.

  2. Wpisz  2dw  aby usunąć dwa wyrazy KAPITALIKAMI.

  3. Powtarzaj kroki 1. i 2. z innymi mnożnikami, aby usunąć kolejne wyrazy
     KAPITALIKAMI jednym poleceniem

---> ta ASD WE linia QWE ASDF ZXCV FG wyrazów została ERT FGH CF oczyszczona.

UWAGA:  Mnożnik pomiędzy operatorem  d  i ruchem działa podobnie do ruchu bez
        operatora.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcja 1.2.6.: OPEROWANIE NA LINIACH


		   ** Wpisz  dd  aby usunąć całą linię. **

  Z powodu częstości usuwania całych linii, projektanci Vi zdecydowali, że
  będzie łatwiej wpisać dwa razy  d  aby usunąć linię.

  1. Przenieś kursor do drugiego zdania z wierszyka poniżej.
  2. Wpisz  dd  aby usunąć wiersz.
  3. Teraz przenieś się do czwartego wiersza.
  4. Wpisz  2dd  aby usunąć dwa wiersze.

--->  1)  Róże są czerwone,
--->  2)  Błoto jest fajne,
--->  3)  Fiołki są niebieskie,
--->  4)  Mam samochód,
--->  5)  Zegar podaje czas,
--->  6)  Cukier jest słodki,
--->  7)  I ty też.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcja 1.2.7.: POLECENIE UNDO (cofnij)


	  ** Wciśnij  u  aby cofnąć skutki ostatniego polecenia.
		 U zaś, by cofnąć skutki dla całej linii. **

  1. Przenieś kursor do zdania poniżej oznaczonego ---> i umieść go na
     pierwszym błędzie.
  2. Wpisz  x  aby usunąć pierwszy niechciany znak.
  3. Teraz wciśnij  u  aby cofnąć skutki ostatniego polecenia.
  4. Tym razem popraw wszystkie błędy w linii używając polecenia  x .
  5. Teraz wciśnij wielkie  U  aby przywrócić linię do oryginalnego stanu.
  6. Teraz wciśnij  u  kilka razy, by cofnąć  U  i poprzednie polecenia.
  7. Teraz wpisz CTRL-R (trzymaj równocześnie wciśnięte klawisze CTRL i R)
     kilka razy, by cofnąć cofnięcia.

---> Poopraw błędyyy w teej liniii i zaamiień je prrzez coofnij.

  8. To są bardzo pożyteczne polecenia.

     Przejdź teraz do podsumowania Lekcji 1.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     LEKCJA 1.2. PODSUMOWANIE


  1. By usunąć znaki od kursora do następnego wyrazu, wpisz:   dw
  2. By usunąć znaki od kursora do końca linii, wpisz:    d$
  3. By usunąć całą linię:    dd
  4. By powtórzyć ruch, poprzedź go liczbą:    2w
  5. Format polecenia zmiany to:
                operator  [liczba]  ruch
  gdzie:
   operator  - to, co trzeba zrobić (np.  d  dla usuwania)
   [liczba]  - opcjonalne, ile razy powtórzyć ruch
   ruch      - przenosi nad tekstem do operowania, takim jak  w (wyraz),
	       $  (do końca linii) etc.

  6. By przejść do początku linii, użyj zera:  0
  7. By cofnąć poprzednie polecenie, wpisz:	  u  (małe u)
     By cofnąć wszystkie zmiany w linii, wpisz:	  U  (wielkie U)
     By cofnąć cofnięcie, wpisz:			  CTRL-R



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Lekcja 1.3.1.: POLECENIE PUT (wstaw)


	  ** Wpisz  p  by wstawić ostatnie usunięcia za kursorem. **

  1. Przenieś kursor do pierwszej linii ---> poniżej.

  2. Wpisz  dd  aby usunąć linię i przechować ją w rejestrze Vima.

  3. Przenieś kursor do linii c), POWYŻEJ tej, gdzie usunięta linia powinna
     się znajdować.

  4. Wciśnij  p  by wstawić linię poniżej kursora.

  5. Powtarzaj kroki 2. do 4. aż znajdą się w odpowiednim porządku.

---> d) Jak dwa aniołki.
---> b) Na dole fiołki,
---> c) A my się kochamy,
---> a) Na górze róże,


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcja 1.3.2.: POLECENIE REPLACE (zastąp)


	   ** Wpisz  rx  aby zastąpić znak pod kursorem na  x . **

  1. Przenieś kursor do pierwszej linii poniżej oznaczonej --->

  2. Ustaw kursor na pierwszym błędzie.

  3. Wpisz  r  a potem znak jaki powinien go zastąpić.

  4. Powtarzaj kroki 2. i 3. dopóki pierwsza linia nie będzie taka, jak druga.

--->  Kjedy ten wiersz bił wstókiwany, ktoś wciznął perę złych klawirzy!
--->  Kiedy ten wiersz był wstukiwany, ktoś wcisnął parę złych klawiszy!

  5. Teraz czas na Lekcję 1.3.3.


UWAGA: Pamiętaj, by uczyć się ćwicząc, a nie pamięciowo.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lekcja 1.3.3.: OPERATOR CHANGE (zmień)

		 ** By zmienić do końca wyrazu, wpisz  ce . **

  1. Przenieś kursor do pierwszej linii poniżej oznaczonej --->.

  2. Umieść kursor na  u  w lunos.

  3. Wpisz  ce  i popraw wyraz (w tym wypadku wstaw  inia ).

  4. Wciśnij <ESC> i przejdź do następnej planowanej zmiany.

  5. Powtarzaj kroki 3. i 4. dopóki pierwsze zdanie nie będzie takie same,
     jak drugie.

---> Ta lunos ma pire słów, które tżina zbnic użifajonc pcmazu zmień.
---> Ta linia ma parę słów, które trzeba zmienić używając polecenia zmień.

  Zauważ, że  ce  nie tylko zamienia wyraz, ale także zmienia tryb na
  Insert (wprowadzanie).


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcja 1.3.4.: WIĘCEJ ZMIAN UŻYWAJĄC c


	** Polecenie change używa takich samych ruchów, jak delete. **

  1. Operator change działa tak samo, jak delete. Format wygląda tak:

	    c   [liczba]   ruch

  2. Ruchy są także takie same, np.:  w  (wyraz),  $  (koniec linii) etc.

  3. Przenieś się do pierwszej linii poniżej oznaczonej --->

  4. Ustaw kursor na pierwszym błędzie.

  5. Wpisz  c$ , popraw koniec wiersza i wciśnij <ESC>.

---> Koniec tego wiersza musi być poprawiony, aby wyglądał tak, jak drugi.
---> Koniec tego wiersza musi być poprawiony używając polecenia  c$ .

UWAGA:  Możesz używać <BS> aby poprawiać błędy w czasie pisania.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     LEKCJA 1.3. PODSUMOWANIE


  1. Aby wstawić tekst, który został wcześniej usunięty wciśnij  p . To
     polecenie wstawia skasowany tekst PO kursorze (jeśli cała linia
     została usunięta, zostanie ona umieszczona w linii poniżej kursora).

  2. By zamienić znak pod kursorem, wciśnij  r  a potem znak, który ma zastąpić
     oryginalny.

  3. Operator change pozwala Ci na zastąpienie od kursora do miejsca, gdzie
     zabrałby Cię ruch. Np. wpisz  ce  aby zamienić tekst od kursora do końca
     wyrazu,  c$  aby zmienić tekst do końca linii.

  4. Format do polecenia change (zmień):

	c   [liczba]   obiekt

     Teraz przejdź do następnej lekcji.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	       Lekcja 1.4.1.: POŁOŻENIE KURSORA ORAZ STATUS PLIKU

       ** Naciśnij CTRL-G aby zobaczyć swoje położenie w pliku i status
	  pliku. Naciśnij  G  aby przejść do linii w pliku. **

  UWAGA: Przeczytaj całą lekcję zanim wykonasz jakieś polecenia!!!

  1. Przytrzymaj klawisz CTRL i wciśnij  g . Używamy notacji CTRL-G.
     Na dole strony pojawi się pasek statusu z nazwą pliku i pozycją w pliku.
     Zapamiętaj numer linii dla potrzeb kroku 3.

UWAGA: Możesz też zobaczyć pozycję kursora w prawym, dolnym rogu ekranu.
       Dzieje się tak kiedy ustawiona jest opcja 'ruler' (więcej w lekcji 6.).

  2. Wciśnij G aby przejść na koniec pliku.
     Wciśnij  gg  aby przejść do początku pliku.

  3. Wpisz numer linii, w której byłeś a potem  G . To przeniesie Cię
     z powrotem do linii, w której byłeś kiedy wcisnąłeś CTRL-G.

  4. Jeśli czujesz się wystarczająco pewnie, wykonaj kroki 1-3.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Lekcja 1.4.2.: POLECENIE SZUKAJ


	     ** Wpisz  /  a następnie wyrażenie, aby je znaleźć. **

  1. W trybie Normal wpisz  / . Zauważ, że znak ten oraz kursor pojawią
     się na dole ekranu tak samo, jak polecenie  : .

  2. Teraz wpisz  błond<ENTER> .  To jest słowo, którego chcesz szukać.

  3. By szukać tej samej frazy ponownie, po prostu wciśnij  n .
     Aby szukać tej frazy w przeciwnym, kierunku wciśnij  N .

  4. Jeśli chcesz szukać frazy do tyłu, użyj polecenia  ?  zamiast  / .

  5. Aby wrócić gdzie byłeś, wciśnij  CTRL-O. Powtarzaj, by wrócić dalej. CTRL-I
     idzie do przodu.

Uwaga:  'błond' to nie jest metoda, by przeliterować błąd; 'błond' to błąd.
Uwaga:  Kiedy szukanie osiągnie koniec pliku, będzie kontynuowane od początku
        o ile opcja 'wrapscan' nie została przestawiona.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lekcja 1.4.3.: W POSZUKIWANIU PARUJĄCYCH NAWIASÓW


	       ** Wpisz  %  by znaleźć parujący ), ], lub } . **

  1. Umieść kursor na którymś z (, [, lub { w linii poniżej oznaczonej --->.

  2. Teraz wpisz znak  % .

  3. Kursor powinien się znaleźć na parującym nawiasie.

  4. Wciśnij  %  aby przenieść kursor z powrotem do parującego nawiasu.

  5. Przenieś kursor do innego (,),[,],{ lub } i zobacz co robi  % .

---> To ( jest linia testowa z (, [, ] i {, } . ))

Uwaga: Ta funkcja jest bardzo użyteczna w debuggowaniu programu
       z niesparowanymi nawiasami!



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcja 1.4.4.: POLECENIE SUBSTITUTE (zamiana)


	 ** Wpisz  :s/stary/nowy/g  aby zamienić 'stary' na 'nowy'. **

  1. Przenieś kursor do linii poniżej oznaczonej --->.

  2. Wpisz  :s/czaas/czas<ENTER> .  Zauważ, że to polecenie zmienia
     tylko pierwsze wystąpienie 'czaas' w linii.

  3. Teraz wpisz  :s/czaas/czas/g  . Dodane  g  oznacza zamianę (substytucję)
     globalnie w całej linii.  Zmienia wszystkie wystąpienia 'czaas' w linii.

---> Najlepszy czaas na zobaczenie najładniejszych kwiatów to czaas wiosny.

  4. Aby zmienić wszystkie wystąpienia łańcucha znaków pomiędzy dwoma liniami,
     wpisz: :#,#s/stare/nowe/g gdzie #,# są numerami linii ograniczających
                               region, gdzie ma nastąpić zamiana.
     wpisz  :%s/stare/nowe/g   by zmienić wszystkie wystąpienia w całym pliku.
     wpisz  :%s/stare/nowe/gc  by zmienić wszystkie wystąpienia w całym
                               pliku, prosząc o potwierdzenie za każdym razem.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     LEKCJA 1.4. PODSUMOWANIE

  1. CTRL-G   pokaże Twoją pozycję w pliku i status pliku.  SHIFT-G przenosi
	      Cię do końca pliku.
     G        przenosi do końca pliku.
     liczba G przenosi do linii [liczba].
     gg       przenosi do pierwszej linii.

  2. Wpisanie  /  a następnie łańcucha znaków szuka łańcucha DO PRZODU.
     Wpisanie  ?  a następnie łańcucha znaków szuka łańcucha DO TYŁU.
     Po wyszukiwaniu wciśnij  n  by znaleźć następne wystąpienie szukanej
     frazy w tym samym kierunku lub  N  by szukać w kierunku przeciwnym.
     CTRL-O przenosi do starszych pozycji, CTRL-I do nowszych.

  3. Wpisanie  %  gdy kursor znajduje się na (,),[,],{, lub } lokalizuje
     parujący znak.

  4. By zamienić pierwszy stary na nowy w linii, wpisz      :s/stary/nowy
     By zamienić wszystkie stary na nowy w linii, wpisz     :s/stary/nowy/g
     By zamienić frazy pomiędzy dwoma liniami # wpisz      :#,#s/stary/nowy/g
     By zamienić wszystkie wystąpienia w pliku, wpisz       :%s/stary/nowy/g
     By Vim prosił Cię o potwierdzenie, dodaj 'c'	   :%s/stary/nowy/gc
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Lekcja 1.5.1.: JAK WYKONAĆ POLECENIA ZEWNĘTRZNE?


	** Wpisz  :!  a następnie zewnętrzne polecenie, by je wykonać. **

  1. Wpisz znajome polecenie  :  by ustawić kursor na dole ekranu. To pozwala
     na wprowadzenie komendy linii poleceń.

  2. Teraz wstaw  !  (wykrzyknik). To umożliwi Ci wykonanie dowolnego
     zewnętrznego polecenia powłoki.

  3. Jako przykład wpisz  ls  za  !  a następnie wciśnij <ENTER>. To polecenie
     pokaże spis plików w Twoim katalogu, tak jakbyś był przy znaku zachęty
     powłoki. Możesz też użyć  :!dir  jeśli  ls  nie działa.

Uwaga:  W ten sposób można wykonać wszystkie polecenia powłoki.
Uwaga:  Wszystkie polecenia  :  muszą być zakończone <ENTER>.
        Od tego momentu nie zawsze będziemy o tym wspominać.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcja 1.5.2.: WIĘCEJ O ZAPISYWANIU PLIKÓW


	   ** By zachować zmiany w tekście, wpisz :w NAZWA_PLIKU . **

  1. Wpisz  :!dir  lub  :!ls  by zobaczyć spis plików w katalogu.
     Już wiesz, że musisz po tym wcisnąć <ENTER>.

  2. Wybierz nazwę pliku, jaka jeszcze nie istnieje, np. TEST.

  3. Teraz wpisz:   :w TEST   (gdzie TEST jest nazwą pliku jaką wybrałeś.)

  4. To polecenie zapamięta cały plik (Vim Tutor) pod nazwą TEST.
     By to sprawdzić, wpisz  :!dir  lub  :!ls  żeby znowu zobaczyć listę plików.

Uwaga: Zauważ, że gdybyś teraz wyszedł z Vima, a następnie wszedł ponownie
       poleceniem  vim TEST , plik byłby dokładną kopią tutoriala, kiedy go
       zapisywałeś.

  5. Teraz usuń plik wpisując (MS-DOS):		   :!del TEST
                          lub (Unix):              :!rm TEST

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Lekcja 1.5.3.: WYBRANIE TEKSTU DO ZAPISU


	  ** By zachować część pliku, wpisz  v ruch :w NAZWA_PLIKU **

  1. Przenieś kursor do tego wiersza.

  2. Wciśnij  v  i przenieś kursor do punktu 5. Zauważ, że tekst został
     podświetlony.

  3. Wciśnij znak  : . Na dole ekranu pojawi się  :'<,'> .

  4. Wpisz  w TEST , gdzie TEST to nazwa pliku, który jeszcze nie istnieje.
     Upewnij się, że widzisz  :'<,'>w TEST zanim wciśniesz Enter.

  5. Vim zapisze wybrane linie do pliku TEST. Użyj  :!dir  lub  :!ls , żeby to
     zobaczyć. Jeszcze go nie usuwaj! Użyjemy go w następnej lekcji.

UWAGA: Wciśnięcie  v  zaczyna tryb Wizualny. Możesz poruszać kursorem, by
       zmienić rozmiary zaznaczenia. Możesz też użyć operatora, by zrobić coś
       z tekstem. Na przykład  d  usuwa tekst.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Lekcja 1.5.4.: WSTAWIANIE I ŁĄCZENIE PLIKÓW


	    ** By wstawić zawartość pliku, wpisz   :r NAZWA_PLIKU **

  1. Umieść kursor tuż powyżej tej linii.

UWAGA: Po wykonaniu kroku 2. zobaczysz tekst z Lekcji 1.5.3. Potem przejdź
       do DOŁU, by zobaczyć ponownie tę lekcję.

  2. Teraz wczytaj plik TEST używając polecenia  :r TEST , gdzie TEST
     jest nazwą pliku.
     Wczytany plik jest umieszczony poniżej linii z kursorem.

  3. By sprawdzić czy plik został wczytany, cofnij kursor i zobacz, że
     teraz są dwie kopie Lekcji 1.5.3., oryginał i kopia z pliku.

UWAGA: Możesz też wczytać wyjście zewnętrznego polecenia. Na przykład
       :r !ls  wczytuje wyjście polecenia ls i umieszcza je pod poniżej
       kursora.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     LEKCJA 1.5. PODSUMOWANIE


  1.  :!polecenie wykonuje polecenie zewnętrzne.

      Użytecznymi przykładami są:

	  :!dir  -  pokazuje spis plików w katalogu.

	  :!rm NAZWA_PLIKU  -  usuwa plik NAZWA_PLIKU.

  2.  :w NAZWA_PLIKU  zapisuje obecny plik Vima na dysk z nazwą NAZWA_PLIKU.

  3.  v ruch :w NAZWA_PLIKU  zapisuje Wizualnie wybrane linie do NAZWA_PLIKU.

  4.  :r NAZWA_PLIKU  wczytuje z dysku plik NAZWA_PLIKU i wstawia go do
      bieżącego pliku poniżej kursora.

  5.  :r !dir  wczytuje wyjście polecenia dir i umieszcza je poniżej kursora.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Lekcja 1.6.1.: POLECENIE OPEN (otwórz)


      ** Wpisz  o  by otworzyć linię poniżej kursora i przenieść się do
	 trybu Insert (wprowadzanie). **

  1. Przenieś kursor do linii poniżej oznaczonej --->.

  2. Wpisz  o  (małe), by otworzyć linię PONIŻEJ kursora i przenieść się
     do trybu Insert (wprowadzanie).

  3. Wpisz trochę tekstu i wciśnij <ESC> by wyjść z trybu Insert (wprowadzanie).

---> Po wciśnięciu  o  kursor znajdzie się w otwartej linii w trybie Insert.

  4. By otworzyć linię POWYŻEJ kursora, wciśnij wielkie  O  zamiast małego
     o . Wypróbuj to na linii poniżej.

---> Otwórz linię powyżej wciskając SHIFT-O gdy kursor będzie na tej linii.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Lekcja 1.6.2.: POLECENIE APPEND (dodaj)


		  ** Wpisz  a  by dodać tekst ZA kursorem. **

  1. Przenieś kursor do początku pierwszej linii poniżej oznaczonej --->

  2. Wciskaj  e  dopóki kursor nie będzie na końcu li .

  3. Wpisz  a  (małe), aby dodać tekst ZA znakiem pod kursorem.

  4. Dokończ wyraz tak, jak w linii poniżej. Wciśnij <ESC> aby opuścić tryb
     Insert.

  5. Użyj  e  by przejść do kolejnego niedokończonego wyrazu i powtarzaj kroki
     3. i 4.

---> Ta li poz Ci ćwi dodaw teks do koń lin
---> Ta linia pozwoli Ci ćwiczyć dodawanie tekstu do końca linii.

Uwaga:  a ,  i  oraz  A  prowadzą do trybu Insert, jedyną różnicą jest miejsce,
       gdzie nowe znaki będą dodawane.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Lekcja 1.6.3.: INNA WERSJA REPLACE (zamiana)


	   ** Wpisz wielkie  R  by zamienić więcej niż jeden znak. **

  1. Przenieś kursor do pierwszej linii poniżej oznaczonej --->. Przenieś
     kursor do pierwszego  xxx .

  2. Wciśnij  R  i wpisz numer poniżej w drugiej linii, tak, że zastąpi on
     xxx.

  3. Wciśnij <ESC> by opuścić tryb Replace. Zauważ, że reszta linii pozostaje
     niezmieniona.

  5. Powtarzaj kroki by wymienić wszystkie xxx.

---> Dodanie 123 do xxx daje xxx.
---> Dodanie 123 do 456 daje 579.

UWAGA: Tryb Replace jest jak tryb Insert, ale każdy znak usuwa istniejący
       znak.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Lekcja 1.6.4.: KOPIOWANIE I WKLEJANIE TEKSTU


       ** użyj operatora  y  aby skopiować tekst i  p  aby go wkleić **

  1. Przejdź do linii oznaczonej ---> i umieść kursor za "a)".

  2. Wejdź w tryb Wizualny  v  i przenieś kursor na początek "pierwszy".

  3. Wciśnij  y  aby kopiować (yankować) podświetlony tekst.

  4. Przenieś kursor do końca następnej linii:  j$

  5. Wciśnij  p  aby wkleić (wpakować) tekst.  Dodaj:  a drugi<ESC> .

  6. Użyj trybu Wizualnego, aby wybrać " element.", yankuj go  y , przejdź do
     końca następnej linii  j$  i upakuj tam tekst z  p .

--->  a) to jest pierwszy element.
      b)
Uwaga: możesz użyć  y  jako operatora;  yw  kopiuje jeden wyraz.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Lekcja 1.6.5.: USTAWIANIE OPCJI


** Ustawianie opcji tak, by szukaj lub substytucja ignorowały wielkość liter **

  1. Szukaj 'ignore' wpisując:    /ignore<ENTER>
     Powtórz szukanie kilka razy naciskając klawisz  n .

  2. Ustaw opcję 'ic' (Ignore case -- ignoruj wielkość liter) poprzez
     wpisanie:		:set ic

  3. Teraz szukaj 'ignore' ponownie wciskając:  n
     Zauważ, że Ignore i IGNORE także są teraz znalezione.

  4. Ustaw opcje 'hlsearch' i 'incsearch':    :set hls is

  5. Teraz wprowadź polecenie szukaj ponownie i zobacz co się zdarzy:
     /ignore<ENTER>

  6. Aby wyłączyć ignorowanie wielkości liter:  :set noic

Uwaga: Aby usunąć podświetlanie dopasowań, wpisz:   :nohlsearch
Uwaga: Aby ignorować wielkość liter dla jednego wyszukiwania: /ignore\c<ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			     LEKCJA 1.6. PODSUMOWANIE


  1. Wpisanie  o  otwiera linię PONIŻEJ kursora.
     Wpisanie  O  otwiera linię POWYŻEJ kursora.

  2. Wpisanie  a  wstawia tekst ZA znakiem, na którym jest kursor.
     Wpisanie  A  dodaje tekst na końcu linii.

  3. Polecenie  e  przenosi do końca wyrazu.
  4. Operator  y  yankuje (kopiuje) tekst,  p  pakuje (wkleja) go.
  5. Wpisanie wielkiego  R  wprowadza w tryb Replace (zamiana) dopóki
     nie zostanie wciśnięty <ESC>.
  6. Wpisanie ":set xxx" ustawia opcję "xxx". Niektóre opcje:
	'ic'  'ignorecase'	ignoruj wielkość znaków
	'is'  'incsearch'	pokaż częściowe dopasowania
	'hls' 'hlsearch'	podświetl wszystkie dopasowania
     Możesz użyć zarówno długiej, jak i krótkiej formy.
  7. Dodaj "no", aby wyłączyć opcję:   :set noic





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 LEKCJA 1.7.1.: JAK UZYSKAĆ POMOC?

		      ** Użycie systemu pomocy on-line **

  Vim posiada bardzo dobry system pomocy on-line. By zacząć, spróbuj jednej
  z trzech możliwości:
	- wciśnij klawisz <HELP> (jeśli taki masz)
	- wciśnij klawisz <F1> (jeśli taki masz)
	- wpisz   :help<ENTER>

  Przeczytaj tekst w oknie pomocy, aby dowiedzieć się jak działa pomoc.
  wpisz CTRL-W CTRL-W    aby przeskoczyć z jednego okna do innego
  wpisz :q<ENTER>        aby zamknąć okno pomocy.

  Możesz też znaleźć pomoc na każdy temat podając argument polecenia ":help".
  Spróbuj tych (nie zapomnij wcisnąć <ENTER>):

  :help w
  :help c_CTRL-D
  :help insert-index
  :help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   LEKCJA 1.7.2.: TWORZENIE SKRYPTU STARTOWEGO

			  ** Włącz możliwości Vima **

  Vim ma o wiele więcej możliwości niż Vi, ale większość z nich jest domyślnie
  wyłączona. Jeśli chcesz włączyć te możliwości na starcie musisz utworzyć
  plik "vimrc".

  1. Początek edycji pliku "vimrc" zależy od Twojego systemu:
     :edit ~/.vimrc	     dla Uniksa
     :edit ~/_vimrc          dla MS-Windows
  2. Teraz wczytaj przykładowy plik "vimrc":
     :read $VIMRUNTIME/vimrc_example.vim
  3. Zapisz plik:
     :w

  Następnym razem, gdy zaczniesz pracę w Vimie będzie on używać podświetlania
  składni. Możesz dodać wszystkie swoje ulubione ustawienia do tego pliku
  "vimrc".
  Aby uzyskać więcej informacji, wpisz     :help vimrc-intro

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Lekcja 1.7.3.: UZUPEŁNIANIE


	      ** Uzupełnianie linii poleceń z CTRL-D i <TAB> **

  1. Upewnij się, że Vim nie jest w trybie kompatybilności:   :set nocp

  2. Zerknij, jakie pliki są w bieżącym katalogu:   :!ls   lub   :!dir

  3. Wpisz początek polecenia:   :e

  4. Wciśnij  CTRL-D  i Vim pokaże listę poleceń, jakie zaczynają się na "e".

  5. Wciśnij  <TAB>  i Vim uzupełni polecenie do ":edit".

  6. Dodaj spację i zacznij wpisywać nazwę istniejącego pliku:   :edit FIL

  7. Wciśnij <TAB>. Vim uzupełni nazwę (jeśli jest niepowtarzalna).

UWAGA: Uzupełnianie działa dla wielu poleceń. Spróbuj wcisnąć CTRL-D i <TAB>.
       Użyteczne zwłaszcza przy  :help .
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Lekcja 1.7. PODSUMOWANIE


  1. Wpisz  :help  albo wciśnij <F1> lub <Help> aby otworzyć okno pomocy.

  2. Wpisz  :help cmd  aby uzyskać pomoc o  cmd .

  3. Wpisz  CTRL-W CTRL-W  aby przeskoczyć do innego okna.

  4. Wpisz  :q  aby zamknąć okno pomocy.

  5. Utwórz plik startowy vimrc aby zachować wybrane ustawienia.

  6. Po poleceniu  : , wciśnij CTRL-D aby zobaczyć możliwe uzupełnienia.
     Wciśnij <TAB> aby użyć jednego z nich.






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Tutaj się kończy tutorial Vima. Został on pomyślany tak, aby dać krótki
  przegląd jego możliwości, wystarczający byś mógł go używać. Jest on
  daleki od kompletności, ponieważ Vim ma o wiele, wiele więcej poleceń.

  Dla dalszej nauki rekomendujemy książkę:
	Vim - Vi Improved - autor Steve Oualline
	Wydawca: New Riders
  Pierwsza książka całkowicie poświęcona Vimowi. Użyteczna zwłaszcza dla
  początkujących. Zawiera wiele przykładów i ilustracji.
  Zobacz https://iccf-holland.org./click5.html

  Starsza pozycja i bardziej o Vi niż o Vimie, ale także warta
  polecenia:
	Learning the Vi Editor - autor Linda Lamb
	Wydawca: O'Reilly & Associates Inc.
  To dobra książka, by dowiedzieć się niemal wszystkiego, co chciałbyś zrobić
  z Vi. Szósta edycja zawiera też informacje o Vimie.

  Po polsku wydano:
	Edytor vi. Leksykon kieszonkowy - autor Arnold Robbins
	Wydawca: Helion 2001 (O'Reilly).
	ISBN: 83-7197-472-8
	http://helion.pl/ksiazki/vilek.htm
  Jest to książeczka zawierająca spis poleceń vi i jego najważniejszych
  klonów (między innymi Vima).

	Edytor vi - autorzy Linda Lamb i Arnold Robbins
	Wydawca: Helion 2001 (O'Reilly) - wg 6. ang. wydania
	ISBN: 83-7197-539-2
	http://helion.pl/ksiazki/viedyt.htm
  Rozszerzona wersja Learning the Vi Editor w polskim tłumaczeniu.

  Ten tutorial został napisany przez Michaela C. Pierce'a i Roberta K. Ware'a,
  Colorado School of Mines korzystając z pomocy Charlesa Smitha,
  Colorado State University.
  E-mail: <EMAIL>.

  Zmodyfikowane dla Vima przez Brama Moolenaara.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Przetłumaczone przez Mikołaja Machowskiego,
  Sierpień 2001,
  rev. Marzec 2002
  2nd rev. Wrzesień 2004
  3rd rev. Marzec 2006
  4th rev. Grudzień 2008
  Wszelkie uwagi proszę kierować na: <EMAIL>
