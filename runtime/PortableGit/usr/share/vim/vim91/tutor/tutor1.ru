===============================================================================
������ 1.7      =  �����  ����������  ��  �������  ��  ���������  Vim  =
===============================================================================
=				�����  ������				      =
===============================================================================

   ��������� Vim -- ��� ����� ������ ��������� ��������, ������� ���������
   ������, � ��� �� ������ ���������� ������� � ������ ����� ��������.
   ������ �� ������� ������� ��������� �� �������, ������� �������� ��� �
   ̣������� ������������ ��������� Vim � �������� ��������� ������ ����������.
   �� �������� ���������� ����� �������� ����������� ����� 30 �����, �� ���
   ������� �� ����, ������� ������� �� ��������� ������������ ��������.

   ��������! �������� ������� ������, �� ������ �������� ����� � ���� �����,
   ������� ������ ��� ����������, �������� ����� �����. ����� ����� �����
   �������������� �������, ������� ��� �����������. ���� �� ���������������
   �������� "vimtutor" ��� �������� ����� ��������, ������, ����� ��� �������.

   ����� �������, ��� ���� ������� ������������ ��� ������������� ��������.
   ��� ��������, ��� �� ������ ��������� ������� ��� ����, ����� ��� �������
   �� �������. ���� �� ������ ���������� ���� �����, �� �� ��������� �������!
   ������, ����������, ��� �� �������� ������� <CapsLock>, ������� �������  j
   ��������� ���, ���, ����� ���� 1.1.1 ��������� ���������� �� ������.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ���� 1.1.1. ����������� �������

** ����� ���������� ������� � ��������� ������������, ������� ������� h,j,k,l **
	     ^		   ���������.
	     k		      ������� h ����� � ������ ��� ����������� �����.
       < h	 l >	      ������� l ������ � ������ ��� ����������� ������.
	     j		      ������� j ������ �� ������� "����".
	     v
  1. ����������� ������� � ������ ������������, ���� �� ������� �����������.

  2. ����������� ������� ������� "����" (j) ��� ������������� �����������
	�������. ������ �� ������, ��� ������� � ���������� �����.

  3. ��������� ������� "����", �� ����  j  , ��������� � ����� 1.1.2.

�����.
    ���� �� �� ������� � ������������ ���������� ������, ������� ������� <ESC>,
	����� ����������� �������� � ����� ������. ����� ����� ��������� �����.

����������.
    ������� ���������� �������� (�������) ����� ������ ��������. �� ������, ���
	��������� ����������� ������� ��������� h j k l ������� �������, �����
	������ ������� ���������������.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     ���� 1.1.2. ���������� ������ ���������

 ��������! ����� ����������� ��������� ���� ��������, �������� ���� ���������!

  1. ������� �������  <ESC>  (����� ���� ���������, ��� ��������� ��������� �
	������ ������).

  2. ��������		:q! <ENTER>
     ��� ��������, ��� ���� ������� ��� �������  :q!  � ������ ������� <����>
     ���������� ���� ������� ������� ���������� ������ ���������
	��� ���������� ����� ��������� ���������.

  3. � ����������� ��������� �������� �������� �������, ������� �� ���������
	���� �������. ��� ����� ����  vimtutor <ENTER>

  4. ���� ������� � ���, ��� ������ ����� ��������������, ��������� ����
	� 1 �� 3, ����� ��������� ������ � ����� ��������� ��������.

����������.
    �� �������  :q! <ENTER>  ����� �������� ����� ��������� ���������. �����
	��������� ������ �� �������, ��� ��������� ��������� � ����.

  5. ����������� ������� ���� � ����� 1.1.3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   ���� 1.1.3. �������������� - �������� ������

	  ** ����� ������� ������ ��� ��������, ������� �������  x  **

  1. ����������� ������� � ������ ���������� --->.

  2. ����� ��������� ������, ����������� �������, ���� ��� �� �������� ���
	��������� ��������.

  3. ������� �������  x  ��� �������� ���������� �������.

  4. ��������� ���� �� 2 �� 4, ���� ������ �� ����� ����������.


---> �� �������� ������ ������ ��� ������ �������.

  5. ������, ����� ������ ����������, ���������� � ����� 1.1.4.

����������.
    � ���� ���� ������� �� ��������� ����� �ӣ ����������, ������� � ��������
	������.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   ���� 1.1.4. �������������� - ������� ������

		** ����� �������� �����, ������� �������  i  **

  1. ����������� ������� � ������ ������ ���������� --->.

  2. ����� ������� ������ ������ ���������� ������, ���������� ������� �� ���
	������, ����� ������� ������� �������� �����.

  3. ������� �������  i  � �������� �����, ������� ��������� ��������.

  4. ����� ����������� ������� ���������� �����, ������� ������� <ESC>
	��� ������������ � ����� ������.
     ��������� ���� �� 2 �� 4, ���� ����������� �� ����� ���������� ���������.


---> ����� ����� � ������ ��������� .
---> ����� ������ � ���� ������ ��������� �������.


  5. ����� ������� ������� ������, ���������� � ����� 1.1.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  ���� 1.1.5. �������������� - ���������� ������

		** ����� �������� �����, ������� �������  A  **

  1. ����������� ������� � ������ ������ ���������� --->.
     ������ �������, �� ����� ������� ����������� ������� � ���� ������.

  2. ������� �������  A   � �������� �����, ������� ��������� ��������.

  3. ����� ���������� ������ ������� ������� <ESC> ��� �������� � ����� ������.

  4. ����������� ������� �� ��������� ������, ���������� --->
	� ��������� ���� �� 2 �� 3 ��� ����������� ���� ������.

---> ����� ������ � ���� ������ ������
     ����� ������ � ���� ������ ��������� �������.
---> ����� ����� ��������� ���
     ����� ����� ��������� ����� ������.

  5. ����� ������� ���������� ������, ���������� � ����� 1.1.6.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  ���� 1.1.6. �������������� � ������ �����

    ** ����� ��������� ���� � ������� ��������, ����������� �������  :wq  **

 ��������! ����� ����������� ��������� ���� ��������, �������� ���� ���������!

  1. ��������� ������ ��������� Vim, ��� ������� � ����� 1.1.2  -  :q!
     ���� ���� ������ � ������� ���������, �� ��� ������ ������� ���������:

  2. � ����������� ��������� �������� ������� �������  vim tutor <ENTER>
	��� vim - ������� ��� ������� ��������� Vim, � tutor - ������������
	����� ��� ��������������. ������� ����� ����, ������� ����� ��������.

  3. ���������� � �������� �����, ��� ������� � ���������� ������.

  4. ��������� ���� ����Σ���� ���� � ��������� ������ ��������� Vim,
	������ �������  :wq <ENTER>

  5. ���� �� ����� �� vimtutor �� ���� 1, ������������� vimtutor � ����������
	����� � ������.

  6. ����� ���� ��� �� ������ � ������ �������������, ��������� ��������� ����.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.1

  1. ������� ����� ���������� ���� ��������� �� ���������, ���� ��������� hjkl.
	h (�����)	j (����)	k (�����)	l (������)

  2. ����� ��������� �������� Vim �� ����������� ��������� ��������, ��������
	vim ���� <ENTER>

  3. ����� ��������� ������ ��������� Vim, ��������� ���� �� ���������:
	<ESC>   :q!  <ENTER>	�� ���� ������� �� ����� ��������� ���������;
     ���
	<ESC>   :wq  <ENTER>	�� ���� ������� ����� ��������� ���������.

  4. ����� ������� ������ ��� ��������, ������� �������  x  � ������ ������.

  5. ����� �������� ����� ����� �������� - i  �������� ����������� �����  <ESC>
     ����� �������� ����� � ����� ������ - A  �������� ����������� �����  <ESC>

����������.
    �� ������� ������� <ESC> ����� ��������� ������������ ��������� � �����
	������ � ����������� ��������� ����� ����� ��������� �������.

������ ���������� � ����� 1.2.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   ���� 1.2.1. ������� ��������

	** ����� ������� ����� ��� ��������, ����������� �������  dw  **

  1. ����������� �������� � ����� ������, ����� ������� <ESC>.

  2. ����������� ������� � ������ ���������� --->.

  3. ���������� ������� �� ������ �����, ������� ������� �������.

  4. ��������  dw  ��� �������� ����� �����.

����������.
    ��� ������ �����  d  ��� ����������� ������ � ����� ������ ������, �
	��������� ����� ������� ����� ��������� �������, � ������ ������ -  w
    ���� ���-�� �� ����������, ������� ������� <ESC> � ������� �������.

---> ��������� ���� ������� � ���� ����������� �������� �������.

  5. ��������� ���� 3 � 4, ���� �� ��������� ��� ������, � ���������� �
	����� 1.2.2


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      ���� 1.2.2. ��� ���� ������� ��������


      ** ����� ������� ����� �� ����� ������, ����������� �������  d$  **

  1. ����������� �������� � ����� ������, ����� ������� <ESC>.

  2. ����������� ������� � ������ ���������� --->.

  3. ���������� ������� � ����� ����������� ����������� (����� ������ �����).

  4. ��������  d$  ��� �������� ������� ������.


---> ���-�� ������ ��������� ���� ������ ������. ��������� ���� ������ ������.


  5. ����� ����� ����������� � ��� ��� ��� ����������, ���������� � ����� 1.2.3.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ���� 1.2.3. ��������� � �������

  ������ �������, ���������� �����, �������� ���������� � ����������� ��
    ��������� � �������, � �������� ����������� ���� ��������.
  ���, ��������, ������ ������� �������� � ����������  d  ���������:

  	d   ������
  ���
    d      - �������� ��������;
    ������ - ������� ������ (������� ����), � ������� ����� �����Σ� ��������.

  ������� �������� ��������:
    w - �� ������� ������� �� ����� �����, ������� ����������� ������;
    e - �� ������� ������� �� ����� �����, �������� ����������� ������;
    $ - �� ������� ������� �� ����� ������, ������� ��������� ������.

  ����� �������, ���� �������  de  ������� �������� ������ �� ������� �������
    �� ����� �����.

����������.
    ���� � ������ ������, ��� ����� ��������, ������ ������� � ��������,
	� ������� ������������ ������, �� ������� ����� ���������� ���, ���
	������� � ������� ��������.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	      ���� 1.2.4. ���������� �������� ��������� � ���������

      ** ����� ����������� ������� ����������� ����������� ���������� ���,
		������� ����� �������� ��������� ����� **


  1. ���������� ������� �� ������ ������ ���������� --->.

  2. ��������  2w  ��� ����������� ������� ���ң� � ������ ������� �����.

  3. ��������  3e  ��� ����������� ������� ���ң� � ����� �������� �����.

  4. ��������  0  (����) ��� ����������� ������� � ������ ������.

  5. ��������� ���� 2 � 3 � ���������� ���������� �����.


---> ������� ������ �� ����, ����� �� �� ��� ��������������� ���������� �������.


  6. ����� ������� ���, ���������� � ����� 1.2.5.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	   ���� 1.2.5. ���������� �������� ��� �������������� ��������

 ** ����� ��������� �������� ��������� ���, ������� ����� ��������� �������� **

  ��������� �����ģ���� ����� ��������� ������� �������� � �����������, �������
    ����� �������� ����� �������� ���������� �������� ��������.

	 d   �����   ������

  1. ���������� ������� �� ������ ����� �� ��������� ���� � ������ �� --->

  2. ��������  d2w  ��� �������� ���� ������ ���� �� ������ ���� �� ���������
	����.

  3. ��������� ���� 1 � 2 � ��������� ������ �������� ��������, ����� �������
	������ ���� �� ��������� ���� ����� ��������.


---> ��� ��� �� ������ ���� �� ��� ������� �� � �� ��� ������ ����.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ���� 1.2.6. �������� �� ��������

	  ** ����� ������� ������ �������, ����������� �������  dd **

  ��� ��� ����� ��������� ��������� �������� ���� ������ �������, ���������
    ��������� ������ ��������� ���� �������, � ���������� ��� ����� ������
    ������ ������ �� ������� � ������ d.

  1. ����������� ������� � ������ ����� ���, ���������� --->.
  2. ��������  dd  ��� �������� ������.
  3. ������ ����������� ������� � ������ ����� ������, ���������� --->.
  4. ��������  2dd  ��� �������� ���� ����� ������.

--->  1)  ����� � ���� �� �������,
--->  2)  �, ��� �������� �������� �����!
--->  3)  � ����� �� "�����", "�����" - �������!
--->  4)  �������� � ����� �� ���� ���������!
--->  5)  ��� �������� - ��� �����, ��� �����...
--->  6)  � ���� �� �������� � ���� "�"
--->  7)  � ����� �� ������� �������� �����.

������������ ��������� ��� ��������� ����� ������ ����������� � � �������
    �����������, � ������� ��������� �����.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    ���� 1.2.7. ������� ������

   ** ����� �������� ��������� �������� ���������� �������, ������� ������� u
	  ����� �������� ������ ��� ���� ������, ������� ������� U **

  1. ���������� ������� �� ������ ������, � ������ ���������� --->
  2. ������� �������  x  ��� �������� ������� ���������� �������.
  3. ������ ������� �������  u  ��� ������ ��������� ����������� �������.
  4. ��������� ��� ������ � ������, ��������� �������  x .
  5. ������ ������� �������  U  , ����� ������� ��� ������ � �������� ���������.
  6. ������� �������  u  ��������� ��� ��� ������ �������  U
	� ���������� ������.
  7. ������ ������� ������� CTRL-R (�.��. ��������� ������� ������� CTRL,
	������� ������� r) ��������� ��� ��� �������� �������� ������.


---> ���������� ������� � ����� ������ � �������� �� �� �������� "������".


  8. ��� ����� ������ � �������� �������.

����� ���������� � ������ ����� 1.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.2

  1. ����� ������� �����, ���������� ������� � ��� ������ � ��������	dw
  2. ����� ������� ����� �� ������� ������� �� ����� �����, ��������	de
  3. ����� ������� ����� �� ������� ������� �� ����� ������, ��������	d$
  4. ����� ������� ��� ������ �������, ��������				dd

  5. ����� ����������� ������� �� ���� ��� �� ��������� ���������� ��������,
	������� �� �����, ��������,  2w
  6. ������ ������ ���������:
            ��������	[�����]   ������
     ���
      �������� - ����������� ��������, ��������,  d  ��� ��������;
      [�����]  - ���������� ����������� ��� �������� ��������� ��������,
		    ���� �� �������, �� ���� ������;
      ������   - �� ��� ������������ ��������, ��������,  w  (�����),
		    $ (�ӣ, ��� ���� �� ����� ������) � �. �.

  7. ����� ����������� ������� � ������ ������, ������� �������  0  (����)

  8. ����� �������� �������������� ��������, �������    u  (�������� ����� u)
     ����� �������� ��� ��������� � ������, �������	U  (��������� ����� U)
     ����� ������� ����Σ���� ���������, �������	CTRL-R
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			   ���� 1.3.1. ������� �������

      ** ����� �������� ��������� ���̣���� �����, �������� �������  p  **

  1. ����������� ������� � ������ ������ ���������� --->.

  2. ��������  dd  , ����� ������� ������, ��� ���� ��� ����� �������������
	�������� � ����������� ������� ��������� Vim.

  3. ���������� ������� �� ������ ���� ���, � ������� ������� ��������
	���̣���� ������.

  4. ���������, ��� ��������� � ������ ������ � ������� �������  p  ��� �������
	������ ���� ������� �������.

  5. ��������� ���� �� 2 �� 4, ���� �� ���������� ��� ������ � ������ �������.

---> �) � ����� �������� �� ���.
---> �) ����� �� � ����� �������,
---> �) �� ������� ���� ��������
---> �) ��� ���� ����� ������� ������


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    ���� 1.3.2. ������� ������

   ** ����� �������� ������ ��� ��������, ��������  r  � ���������� ������ **

  1. ����������� ������� � ������ ������ ���������� --->.

  2. ���������� ������� ���, ����� ��� ���������� ��� ������ ��������� ��������.

  3. ������� �������  r  � ����� �������� ������, ������������ ������.

  4. ��������� ���� 2 � 3, ���� ������ ������ �� ����� ��������������� ������.


--->  � ������ ������ ���� ������ ���0��� � ������ ������� �� ��������!
--->  � ������ ������ ���� ������ ���-��� � ������ ������� �� ��������!


  5. ������ ���������� � ����� 1.3.3.

����������.
    �������, ��� �� ������ ������� � �������� ������, � �� ������ �������.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  ���� 1.3.3. �������� ���������

	  ** ����� �������� ��������� �����, �������� �������  ce  **

  1. ����������� ������� � ������ ������ ���������� --->.

  2. ���������� ������� ��� ������  o  � �����  "����".

  3. �������� �������  ce  � ��������� ����� (� ������ ������, �������� "���").

  4. ������� ������� <ESC> � ����������� ������� � ��������� ������ (� �������
	�������, ������� � �������� ���� �������� ��������� �����).

  5. ��������� ���� 3 � 4 ���� ������ ������ �� ����� ��������������� ������.

---> ��������� ���� � ���� ������ ������� ��������������.
---> ��������� ���� � ���� ������ ������� ��������������.

����������.
    �������� ��������, ��� �� �������  ce  �� ������ ��������� ����� �����,
	�� � ���������� ������������ ��������� � ����� �������.
    �� �������  cc  ����� ���������� �� �� �����, �� ��� ����� ������.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
       ���� 1.3.4. ��� ��������� �������� ������ � ���������� ���������  c

** � ��������� ��������� ��������� �� �� �������, ��� � � ��������� �������� **

  1. �������� ��������� �������� ���������� ��������� ��������. ������ �������:

       c   [�����]   ������

  2. ������� - ��� �� �� �����, ��� � �����: w (�����), $ (����� ������) � �. �.

  3. ����������� ������� � ������ ������ ���������� --->.

  4. ���������� ������� �� ������ ������.

  5. ��������  c$  � �������������� ������ ������ ���, ����� ��� ��������� ��
	������, ����� ���� ������� ������� <ESC>.

---> ��������� ���� ������ ����� ������� ������� ��� �� ������ ������.
---> ��������� ���� ������ ����� ��������� ��������  c$ .

����������.
    ������� <BACKSPACE> ����� �������������� ��� ����������� ��� ������ ������.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.3

  1. ����� �������� �����, ������� ��� ������ ��� ���̣�, �������� �������  p .
	����� ����� �������� ����� ������� ������� (���� ���� ������� ������,
	�� ��� ����� �������� � ������ ���� ������ � ��������).

  2. ����� �������� ������ ��� ��������, �������� �������  r  � �����
	���������� ������.

  3. ��������� ��������� �������� ��������� ������ ������ �� ������� �������
	�� �������� ����� �����������.
     ��������, �� �������  ce  ����� �������� ����� �� ������� ������� �� �����
	�����, � �� �������  c$  - �� ����� ������.

  4. ������ ������ ���������:

	 c   [�����]   ������

     ��� c - �������� ���������;
	 [�����] - ���������� ���������� �������� (�������������� �����);
         ������ - ������ ������, ������� ����� ����Σ�.

������ ���������� � ���������� �����.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 ���� 1.4.1. ���������� � ����� � ������� �������

   ** ����� �������� ���������� � ����� � ������� �������, �������  CTRL-g  .
       ����� ����������� ������� � �������� ������ � �����, �������  SHIFT-G **

  ��������! ���������� ���� ����, ������ ��� ��������� ����� ��������!

  1. ��������� �������  CTRL  , ������� �������  g  . ����� ������ ��������
	��������� � ������������� ����� � ������� ������, � ������� ���������
	�������. ��������� ���� ����� ������, �� ����������� �� ���� 3.

  ����������.
    ������� ������� ����� ������������ � ������ ������ ���� ���� ���������,
	���� ���������� �������� 'ruler' (��. :help 'ruler').

  2. ������� ������� SHIFT-G ��� ����������� ������� �� ��������� ������ �����.
     ������ ��������  gg  ��� ����������� ������� �� ������ ������ �����.

  3. �������� ����� ������, ������� ��� ������� �� ���� 1, � ������� �������
	SHIFT-G. ������� ����� ���������� � �� ������, ��� ��� ����������,
	����� � ������ ��� ���� ������ ������� CTRL-g.

  4. ���� �� ��������� �ӣ �������������, ��������� ���� � 1 �� 3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    ���� 1.4.2. ������� ������

   ** ����� ���-�� �����, �������� ������� / � ����� ������� ������� ����� **

  1. � ������ ������ �������� ������  /  . �������� ��������, ��� ���� ������
	����� ������������ ����� ������. ��� ��, ��� � ��� ������ �������  :

  2. ������ �������� �������� <ENTER>. ��� �� �����, ������� ��������� �����.

  3. ����� ��������� ����� �������� �����, ������ ������� �������  n  .
     ����� ������ ��� ����� � �������� �����������, ������� �������  SHIFT-N  .

  4. ���� ��������� ����� ��������� ����� � �������� �����������, �����������
	�������  ?  ������ �������  /  .

  5. ����� ��������� ����, ������ ��� ����� �����, ������� ��������� ���
	�������  CTRL-O  . ��� �������� ���ң�, ����������� �������  CTRL-I  .

---> "��������" ��� �� ������ ��������� ����� "������";  �������� ��� ������.

����������.
    ���� ����� ��������� ����� �����, �� ����� ����� ��������� �� ������ �����.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ���� 1.4.3. ����� ������ ������

      ** ����� ����� ������ ������ ��� (, [ ��� {, �������� �������  %  **

  1. ��������� ������� �� ����� �� ������ (, [ ��� { � ������ ���������� --->.

  2. ������ ������� �� ���������� ������� � ��������  %  .

  3. ������� ����� ���������� �� ������ ������ ��� ��� ������, �� �������
	����������� �������.

  4. ��������  %  ��� �������� ������� ����� � ������ ������ ������.


---> � ���� ( ������ ���� ����� (, ����� [ ] � { ����� } ������. ))


����������.
    ��� ����� ������ ��� ������� ��������, ����� � ���� ��������� ������!




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  ���� 1.4.4. ������ ������ ����

     ** ����� "���-��" �������� "���-��", �������� ������� :s/���/���/g **

  1. ����������� ������� � ������ ���������� --->.

  2. ��������  :s/�����/����� <ENTER> . �������� �������� �� ��, ��� �� ����
	������� ����� ������ ������ ������� ���������� ��������� � ������.

  3. ������ ��������  :s/�����/�����/g , ����������� ����  'g'  ��������
	������ �� ���� ������. ����� ��������� ������ ���� ��������� � ������
	����������.

---> � ����� � ����������� ��������, � ����� ������ ���������� ����, � �����
     � ������� ����������.

  4. ����� �������� ��� ��������� �������� ����� � �����-�� ��������� �����,
     ��������  :#,#s/���/���/g	  ��� #,# - ����� ��������� � �������� ������
				  ���������, � ������� ����� ��������� ������.
     ��������  :%s/���/���/g	����� �������� ��� ��������� �� �ӣ� �����.
     ��������  :%s/���/���/gc	����� ��������� ������ �������������
				����� ������ �������.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.4

  1. �� �����ģ���� ���� �������� ����� ���������:
     CTRL-g - ����� ���������� � ����� � ������� ������� ������� � ���� �����
     SHIFT-G - ������� �� ��������� ������ �����
     ����� � SHIFT-G - ������� � ������ � ��������� �������
     gg - ������� �� ������ ������ �����
  2. ��� ����� �������  /  � ����������� ������� �����, ����� �������� �����
	����� ����� ����� �� ������.
     ��� ����� �������  ?  � ����������� ������� �����, ����� �������� �����
	����� ����� ����� �� ������.
     ����� ������ ������� ����������, �������  n  ��� �������� � ����������
	����� � ��� �� ����������� ������ ��� SHIFT-N ��� ������ �
	��������������� �����������.
     ��� ������� ������  CTRL-O  ����� ������� � ����������� �����, � ���
	������� ������  CTRL-I  ����� ������� � ����� ���������� �����.
  3. ��� �������  %  , ����� ������� �� ����� �� ������ ( ), [ ] ��� { },
	����� ������� ţ ������ ������.
  4. ����� �������� ������ ��������� ����� � ������, ��������	:s/���/���
     ����� �������� ��� ��������� ����� � ������, ��������	:s/���/���/g
     ����� �������� � ���������� ��������� �����, ��������	:#,#s/���/���/g
     ����� �������� ��� ��������� ����� � �����, ��������	:%s/���/���/g
     ����� ������������� �������������, �������� ���� 'c'	:%s/���/���/gc
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	       ���� 1.5.1. ��� ������� �� ��������� ������� �������

** ����� ���� ��������� ������� ��������� ��������, �������� � ���������  :! **

  1. �������� ��� �������� �������  :  , ����� ���������� ������� � ���������
	������ ��������� � ������ ����������� �������.

  2. ������ �������� ������  !  (��������������� ����). �� ���� ������� �����
	������� ��������� ��������� ������� ������� ��������� ��������.

  3. ��������, ��������  ls  ����� �����  !  � ������� <ENTER>. ����� �������
	�������� ������ � ������� ��������. �� ���� ����� ��������� ����� �� ��
	�����, ��� ���� �� ������ ������� ls � ����������� ��������� ��������.
	���� � ������� �� �������������� ������� ls, �� �������� �������  :!dir

����������.
    ����� �������� ����� ��������� ����� ������� �������, � ��� ����� � �
	��������� ����������� ���������� ���� �������.

�����.
    ����� ����� �������, ������������ �  : , ������ ���� ������ ������� <ENTER>
    � ���������� ��� ����� �� ����������� ��������, �� ���������������.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  ���� 1.5.2. ��� �������� ����

   ** ����� ��������� ���� �� ����� ����������� � ������, ��������  :w ���� **

  1. ��������  :!dir  ���  :!ls  ��� ��������� ������� ������ � �������
	��������.  ��� �� �������, ����� ������ ������� ������� ������� <ENTER>

  2. ���������� �������� ��� �����, ������� �ݣ �� ����������, ��������, TEST.

  3. ������ ��������  :w TEST  (����� TEST - ��� ����������� �������� �����).

  4. �� ���� ������� ����� ��������� �����Σ� ������� ���� ("tutor") ��� �����
	�������� "TEST". ����� ��������� ���, ����� �������� ������� :!dir  ���
	:!ls  � ����������� ���������� ��������.

����������.
    ���� ��������� ������ ��������� Vim � ����� ��������� ��� ����� � ������
	TEST (�. �. ������� �������  vim TEST ), ���� ���� ����� ������ ������
	�������� � ��� ������, ����� �� ��� �����Σ�.

  5. ������ ������� ���� ����, ������ � ��������� �������  :!del TEST
	(��� �� Windows) ���  :!rm TEST  (��� UNIX-�������� ��)

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       ���� 1.5.3. ���������� ������ �����

     ** ����� ��������� ����� �����, ������� �������  v  , �������� ������
			 � �������� �������  :w ���� **

  1. ����������� ������� �� ��� ������.

  2. ������� �������  v  � ����������� ������� ���� � ������ � ����� �������.
	�������� ��������, ��� ����� ���������.

  3. ������� ������� � ��������  :  � ����� ������ ��������  :'<,'>  .

  4. �������� �������  w TEST  (����� TEST - ����, ������� �ݣ �� ����������).
	� ��������� ������ ������ ����  :'<,'>w TEST  � ������� ������� <ENTER>

  5. �� ���� ������� ��������� ������ ����� �������� � ���� TEST. ��������� �
	������� ����� �����, ���������������� ��������  :!dir  ���  :!ls  .
     �� �������� ���� ����, �� ����������� �� ��������� �����.
����������.
    �� ������� �������  v  ����������� ������������ � ���������� �����. �����
	�������� ������ ��������� �������, ����� ����������� �������.
    � ����������� ��������� ����� ��������� ����� ��������, ��������,  d
	��� ��� ��������.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   ���� 1.5.4. ���������� � ����������� ������

      ** ����� �������� ������������ � ����� �����, ��������  :r ����  **

  1. ���������� ������� ��� ���� �������.

��������!
    ����� ���������� ���������� � ������ 2 �� ������� ����� �� ����� 1.5.3.
	����������� ������� ���� �� ������ �� �������� �����.

  2. ������ �������� ���������� ����� TEST, ��������� �������  :r TEST , �����
	TEST - ��� ������������ �����.

  3. ��� ��������, ��� ���������� ����� ���� ���������, ����������� �������
	����� �� ������ � ��������������, ��� ������ ����� ��� ����� 1.5.3. -
	�������� � �� ����� TEST.

����������.
    �������� ����� � ��������� ������� �������. ��������, �� �������  :r !ls
	����� ������� ����� ������� ls � �������� ���� ������� �������.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.5

  1. �� �������  :!command  ����� ��������� ��������� ������� �������.

      ��������� �������� �������:
      	(Windows)	(UNIX)
	 :!dir		 :!ls	    - ������� �������� ������ � ��������;
	 :!del ����	 :!rm ����  - ������� ���� � ��������� �������������.

  2. �� �������  :w ����  , ������� ������������� ���� ����� �������
	� ��������� �������������.

  3. ��������� �������  v  , ����������� ������� �  :w ����  ����� ���������
	��������� ���������� ������ � ���� � ��������� �������������.

  4. �� �������  :r ����  ����� �������� ���� � ��������� �������������
	� ��� ���������� �������� ���� ������� �������.

  5. �� �������  :r !dir  ����� ������� ����� ������� dir � ����ݣ� ����
	������� �������.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      ���� 1.6.1. ������� ��� �������� �����

 ** ����� ������� ����� ������ � ������������� � ����� �������, ��������  o  **

  1. ����������� ������� ����, � ������ ������ ���������� --->.

  2. ������� �������  o   ��� ����, ����� ������� ������ ������ ���� �������
	������� � ����������� �������� � ����� �������.

  3. ������ �������� �����-������ ����� � ������� ������� <ESC> ��� ������ ��
	������ �������.

---> ����� �������  o  ���� ����� ������� ����� ������ ������ � ������ �������.


  4. ��� �������� ������ ���� ������� �������, �������� ��������� �����  O ,
	������ �������� �����  o . ���������� ��� ������� ��� ������ ����.


---> �������� ����� ������ ��� ����, �������� ���� ������� � �����  SHIFT-O.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    ���� 1.6.2. ������� ��� ���������� ������

	 ** ����� �������� ����� ����� ������� �������, ��������  a  **

  1. ����������� ������� ����, � ������ ������ ������ ���������� --->.
  2. ������� �������  e  , ���� ������� �� �������� �� ��������� ������� �����
	"����".

  3. ������� �������  a  ��� ���������� ������ ����� �������, ������������ ���
	��������.

  4. �������� ����� ��� � ������ ����. ������� ������� <ESC> ��� ������ ��
	������ �������.

  5. ����������� �������  e  ��� �������� � ���������� �������ۣ����� �����
	� ��������� ��������, ��������� � ������� 3 � 4.

---> �� ���� ���� �� ������ ����������� � ������� ������.
---> �� ���� ������ �� ������ ���������������� � ���������� ������.

����������.
    �� �������  a , i  �  A  ����� ��������� ������������ � ���� � ��� �� �����
	�������, �������� ������ � ���, ��� ����������� �������.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			���� 1.6.3. ��� ���� ������ ������

	 ** ����� �������� ��������� �������� � ������, �������� R  **

  1. ����������� ������� � ������ ������� ����� xxx � ������ ���������� --->

  2. ������ �������  SHIFT-R  � ������� �����, ��������� ���� �� ������ ������,
	����� �������� �������  xxx.

  3. ������� ������� <ESC> ��� ������ �� ������ ������. ��������, ��� �������
	������ �� ��� ����Σ�.

  4. ��������� ��� ���� ��� ������ ���������� ����  xxx.

---> ��� �������� ����� 123 � ������ xxx ����� ����� xxx.
---> ��� �������� ����� 123 � ������ 456 ����� ����� 579.


����������.
    ����� ������ ����� �� ����� �������, �� ������ ���ģ���� ������ �������
	������������ ������ � ������.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     ���� 1.6.4. ����������� � ������� ������

** ����� ����������, ����������� ��������  y  , ����� �������� - �������  p  **

  1. ���������� ������� ����� �������� "�)" � ������, ���������� --->.
  2. ����������� �������� � ���������� ����� ��������  v  � ����������� �������
	���ң� �� ����� "������".
  3. ������� �������  y  ��� ����������� ������������� ������.
  4. ����������� ������� � ����� ��������� ������, ������ �������  j$ .
  5. ������� �������  p  ��� ������� ������. ����� �������� �������  a  ,
	����������� ����� "������" � ������� ������� <ESC>.
  6. ��������� ���� � 1 �� 4, ������ ���������� ������� ����� ����� "������",
	��������, ���������� � �������� ����� " �����.".

--->  �) ��� ������ �����.
      �)

����������.
    ����� ��������������� ��������  yw  (��������  y  � ������  w) ���
	����������� ������ �����.
    �� �������  yy  ����� ����������� ����� ������, � �� �������  p  ���������.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 ���� 1.6.5. ��������� ����������

	 ** ����� ��� ������ ��� ������ �� ���������� ������� ��������,
		      ������� ��������������� ��������� **

  1. ������� ����� "������������", ������ �������  /������������ <ENTER>.
     ��������� ����� ��������� ���, ������� �������  n  .

  2. ���������� �������� 'ic' (������������ �������), ������ �������  :set ic

  3. �ݣ ��������� ��� ��������� ����� ����� "������������", ������� �������  n
     ��������, ��� ������ ����� ������� ����� "������������" � "������������".

  4. ���������� ��������� 'hlsearch' � 'incsearch' ��������  :set hls is

  5. �������� ������� ������� ������ � ����������, ��� ���������  /������������

  6. ��� �������� �ޣ�� �������� ��� ������, ������� �������  :set noic
����������.
    ��� ���������� ��������� ����������, �������� �������  :nohlsearch
����������.
    ���� ��������� �� ��������� ������� �������� ������ �����������, �����������
	����  \c  � ������� ������, ��������,  /������������\c  <ENTER>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.6

  1. �� �������  o  ����� ������� ������ ������ ���� ������ � ��������
	� �������� ����� ���������� � ����� �������
     �� �������  O  ����� ������� ������ ������ ���� ������ � ��������
	� �������� ����� ���������� � ����� �������
  2. �� �������  a  ����������� ������� ������ ����� ������� �������.
     �� �������  A  ����������� ������� ������ � ����� ������.

  3. �� �������  e  ����������� ��������� ������� � ����� �����.
  4. ��������  y  ������������ ��� ����������� ������, � �� �������  p
	���������� ������� �������������� ������.

  5. ��� ������� ������  SHIFT-R  ����������� ������������ � ����� ������,
	� ���������� - �������� �������  <ESC> .

  6. �������� :set xxx ��� ��������� ��������� 'xxx'.
 ��� ��������� ��������� (����� ��������� ������ ��� �����ݣ���� ������������):
	'ic'	'ignorecase'	������������� �������� �������� ��� ������
	'is'	'incsearch'	����������� ��������� ���������� ��� ������
	'hls'	'hlsearch'	��������� ���� ���������� ��� ������

  7. ��� ������ ���������, �������� ��������� "no" � ��� ��������  :set noic
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    ���� 1.7.1. ���������� ���������� �������

		** ����������� ���������� ���������� ������� **

  � ��������� Vim ������� ������ ���������� ���������� �������, � ����� ������
    �� ������������, �������������� ����� �� �ң� ���������:
	- ������� ������� <HELP> (���� ��� ���� �� ����������)
	- ������� ������� <F1> (���� ��� ���� �� ����������)
	- ��������  :help <ENTER>

  ������������ � ����������� � ���� ���������� �������, ����� ��������
    ������������� � ���, ��� �������� � �������������.

  �������  CTRL-w CTRL-w  ��� ����������� ������� �� ������ ���� � ������ ����.
  ��������   :q <ENTER>  , ����� ������� ���� ���������� ������� (����� �������
    ��������� � ���� ����).

  ����� ����� �������� ��� ������ ������� ��� �������, ����� ���������������
    �������� ������� :help. ���������� ��������� (�� �������� ������ <ENTER>):
	:help w
	:help c_CTRL-D
	:help insert-index
	:help user-manual
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 ���� 1.7.2. �������� ���������� ���������� �����

		       ** ������� ��� ����������� Vim **

  �������� Vim ����� ������������ �� ��������� � ���������� Vi, �� �����������
    �� ���� ������������ ��������� ��� ������� ���������. ����� ������������
    ���� ���������� ���������, ���������� ������� ���� "vimrc".

  1. �������� ����� ���� "vimrc". ��� ������������ ������� �� ������������
	�������:
	    :e ~/.vimrc			��� UNIX
	    :e $VIM/_vimrc		��� MS Windows

  2. ������ �������� � ���� ���� ���������� ���������� ����� "vimrc"
	    :r $VIMRUNTIME/vimrc_example.vim

  3. �������� ��������� ���� ���� "vimrc"
	    :w

  ������ ��� ��������� ������� ��������� Vim ����� �������� ���������
    ����������. ��� ����������� ��� ��������� ����� ���� ��������� � ����
    "vimrc".
  ����� �������� ��������� ����������, ��������  :help vimrc-intro
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  ���� 1.7.3. ����������� ������

** ����������� � ��������� ������ ����������� �������� ������ CTRL-D � <TAB> **

  1. ��������� ������������� � ���������� Vi
	    :set nocp
  2. ����������, ����� ����� ���� � ��������, ������ �������
	    :!ls  ���  :!dir
  3. �������� ������ ������� ��� �������� ����� �� ��������������  :e
  4. ������� �������  CTRL-D  , � ����� ������� �������� ������ ��������� Vim
	������������ � ����� "e".
  5. ������� �������  d<TAB>  , � ����� ����������� ������ �������� �������
	"edit".
  6. ������ ����������� ������ � ������ ������������ ������������� �����
	    :edit TE
  7. ������� �������  <TAB>  � ����� ����������� ������������ �����, ���� ���
	����������.

����������.
    ����������� �������� ��� ��������� ������. ������ ���������� ������ �������
	CTRL-D  �  <TAB>  ��� ����� �� ������ ���������. ��� �������� �������
	��� �������  :help  .

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
				 ������ ����� 1.7


  1. ����� ������� ���� ���������� ���������� ������� ���������, ��������
	�������  :help  ��� ������� ������� <F1>, ��� ������� <HELP>.

  2. ����� ����� ���������� ���������� � �����-���� �������,
	��������  :help cmd  (������ "cmd" ������� ������������ �������).

  3. ����� ����������� ������� � ������ ����, ������� �������  CTRL-w CTRL-w  .

  4. ����� ������� ���� ���������� ������� (���� ��� �������), ��������  :q  .

  5. ����� ��� ������� ������ ����������� ����������� ��� ���������, ��������
	��������� ��������� ���� vimrc.

  6. ��� ������ �������, ������������ � �������  :  , ������� ������� CTRL-D,
	����� ����������� ��������� �������� �����������. ������� ������� <TAB>
	��� ����������� ������������ ��������.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  �� ���� ����� ��������� ������ ����� ������� �����ݣ���� ��������� Vim.
  ����� �� ������ ������������ �� ������ ������ �������.

  ����� ������� ����� ���� ���� ������� ����� ��������� Vim, ����������� ���
  ����, ����� �� ��������� ���������� ��� ��� �������������. ��� ������ ��
  ������ �����, ��������� � ��������� Vim ���� �ݣ �����-����� ������.

  ����� ��������� ���� ��������, ������������ � ������������ ������������,
  ������ �������  :help user-manual.

  ��� ����������� ������ ������������� �����
	"Vim - Vi Improved", ����� Steve Oualline, ������������ New Riders.
  ��� ��������� ��������� ��������� Vim � ����� �������� ������� ��������.
  � ����� ������� ��������� �������� � �����������.
  ��. https://iccf-holland.org/click5.html

  �ݣ ���� ����� ����� ���������� �������� � ��������� ������ ��������� Vi,
  ��� ��������� Vim, ������ ����� ������������� � ���������
	"Learning the Vi Editor", ����� Linda Lamb,
				������������ O'Reilly & Associates Inc.
  ��� ������� �����, ����� ������ �ӣ, ��� ������ ����� ������� � ��������� Vi.
  ������ ������� ���� ����� �������� ���������� � ��������� Vim.

  ��� ����� ���� ���������� Michael C. Pierce � Robert K. Ware �� Colorado
  School of Mines � �ޣ��� ����, ������������ Charles Smith �� Colorado State
  University. E-mail: <EMAIL> (������ ����������).

   ��� ������������� � ��������� Vim ����� ���� ���������� Bram Moolenaar

    ������ ����̣�, ������� �� ������� ����, 2002, <<EMAIL>>
    ������ �̣���, ������� �� ������� ����, 2014, <<EMAIL>>
    Restorer, ���������, 2022, <<EMAIL>>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
