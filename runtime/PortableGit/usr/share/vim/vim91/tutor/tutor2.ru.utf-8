===============================================================================
версия 1.7      =  ДОБРО  ПОЖАЛОВАТЬ  НА  ЗАНЯТИЯ  ПО  РЕДАКТОРУ  Vim  =
===============================================================================
=				ГЛАВА  ВТОРАЯ				      =
===============================================================================

  Что‐то неожиданное и непонятное?
  Если это ваше первое знакомство с редактором Vim и вы планировали начать
  с вводной главы учебника, не расстраивайтесь и сделайте вот что.
  Наберите на клавиатуре команду  :q! , нажмите клавишу <ENTER>, и попробуйте
  ещё раз, набрав в командной оболочке такую команду
      vimtutor --chapter 1 ru

  Приблизительное время, необходимое для изучения второй главы учебника
  составляет около 8–10 минут, и зависит от того, сколько времени вы посвятите
  выполнению заданий.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

		 Урок 2.1.1. ИМЕНОВАННЫЕ РЕГИСТРЫ В РЕДАКТОРЕ Vim

** Копирование с сохранением двух разных слов и последующая их вставка в текст **

  1. Переместите каретку к строке помеченной --->

  2. Установите каретку на любом символе слова «Эдуард» и наберите команду
     "ayiw

Эта команда означает следующее:
    в регистр(") с названием(a) скопировать(y) только(i) слово(w)

  3. Сдвиньте каретку вперёд на слово «печенье» (это можно сделать одним из
     следующих способов: fп или 3fч, или $, или /пе <ENTER>) и наберите команду
     "byiw

  4. Переместите каретку на любой символ слова «Виктор» и наберите на клавиатуре
     ciw<C-r>a<ESC>

Эта команда означает следующее:
    изменить(c) только(i) слово(w) на <содержимое регистра(r)> с названием(a)

  5. Установите каретку на любой символ слова «тортов» и наберите
     ciw<C-r>b<ESC>

--->  а) Отныне Эдуард будет отвечать за раздачу печенья
      б) Таким образом Виктор имеет единоличные права по распределению тортов

Примечание.
    Регистры можно использовать также и для вырезания текста, например,
    по команде  "sdiw  будет выполнено удаление слова под кареткой в регистр
    с названием «s».

Разделы документации:
	регистры		:h registers
	именованные регистры	:h quote_alpha
	перемещение		:h text-objects
	CTRL-R			:h i_CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

		   Урок 2.1.2. РЕГИСТР РЕЗУЛЬТАТА ВЫЧИСЛЕНИЙ

	      ** Вставка результатов вычислений напрямую в текст **

  1. Переместите каретку к строке помеченной --->

  2. Установите каретку на любой цифре приведённого числа

  3. Наберите на клавиатуре  ciw<C-r>=60*60*24<ENTER>

  4. Переместите каретку в конец следующей строки, переключите редактор в режим
     вставки, и добавьте сегодняшнюю дату с помощью следующей команды
     <C-r>=system('date')<ENTER>

Примечание.
    Результат вызова функции system() зависит от текущей операционной системы,
    например, в ОС Windows необходимо использовать такую команду
    system('date /t')    или    :r!date /t

---> Правильно ли я помню, что в сутках 84600 секунд?
     Сегодняшняя дата 

Примечание.
    Тот же результат можно получить с помощью такой команды  :pu=system('date')
    или более короткой команды  :r!date

Разделы документации:
	регистр результата вычислений	:h quote=

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

	       Урок 2.1.3. НУМЕРОВАННЫЕ РЕГИСТРЫ В РЕДАКТОРЕ Vim

	  ** Как команды  yy  и  dd  влияют на содержимое регистров **

  1. Переместите каретку к строке помеченной --->

  2. Скопируйте эту строку и проверьте состояние регистров с помощью команды
     :reg<ENTER>

  3. Удалите строку, начинающуюся с цифры 0, с помощью команды  "cdd  и ещё раз
     проверьте состояние регистров (где будет строка, начинающаяся с цифры 0?)

  4. Продолжайте удалять все последующие нумерованные строки, проверяя состояние
     регистров после каждой операции.

Примечание.
    В ходе этих действий вы заметите, что ранее удалённые строки смещаются вниз
    по мере того, как новые удалённые строки добавляются в перечень регистров.

  5. Теперь вставьте содержимое регистров в следующем порядке: c, 7, 4, 8, 2.
     То есть наберите команды  "cp , "7p  и так далее.

---> 0. Здесь
     9. шататься
     8. секретное
     7. будет
     6. на
     5. шесте
     4. это
     3. войны
     2. послание
     1. наградой

Примечание.
    Целые строки, удалённые по команде  dd  , дольше сохраняются в нумерованных
    регистрах, чем строки, которые были скопированы или когда с оператором
    удаления указывается объект текста для перемещения каретки.

Разделы документации:
	нумерованные регистры		:h quote_number

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

			 Урок 2.1.4. ИЗЯЩЕСТВО ЗАКЛАДОК

	       ** Избегайте действий, свойственных для дятлокодеров **

Примечание.
    При написании программ часто возникает необходимость перемещения больших
    фрагментов кода. Приём, приведённый далее, поможет избежать подсчёта номеров
    строк, требуемых для операций вроде  "a147d  или  :945,1091d a  , или даже
    хуже — i<C-r>=1091-935<ENTER> , как первое действие.

  1. Переместите каретку к строке помеченной --->

  2. Установите каретку на следующую строку, где начинается описание функции,
     и поставьте закладку, воспользовавшись командой  ma

Примечание.
    Неважно где будет находиться каретка в этой строке.

  3. С помощью следующей команды  $%  установите каретку на последний символ
     в этой строке с последующим перемещением на окончание блока кода

  4. Удалите весь это блок кода в регистр с названием «a» с помощью команды
     "ad'a

Эта команда означает следующее:
    в регистр(") с названием (a) поместить удалённые строки от позиции каретки
    до строки, в которой установлена закладка(') с названием (a)

  5. Вставьте удалённый блок между символами BBB и CCC с помощь команды
     "ap

---> AAA
     function itGotRealBigRealFast() {
       if ( somethingIsTrue ) {
         doIt()
       }
       // таксономия нашей функции изменилась, и её текущее положение больше
       // не имеет привязки к алфавитному порядку

       // а теперь представьте, что здесь сотни строк кода

       // было бы глупо искать начальную и конечную строку этого блока кода,
       // чтобы записывать или запоминать номер строки для каждой из них
     }
     BBB
     CCC

Примечание.
    Пространство именования закладок и регистров не пересекаются между собой,
    поэтому регистр «a» полностью независим от закладки с таким же названием «a».
    Это правило не распространяется на регистры и макросы.

Разделы документации:
	закладки			:h marks
	перемещение к закладкам		:h mark-motions  (различие между ` и ')

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

				Резюме урока 2.1

  1. Чтобы сохранить (при удалении или копировании) текст для последующей
     вставки, используйте имеющиеся 26 именованных регистра (a-z).
  2. Чтобы скопировать целое слово при нахождении каретки на любом символе
     в этом слове, воспользуйтесь командой  yiw
  3. Чтобы изменить целое слово при нахождении каретки на любом символе в этом
     слове, воспользуйтесь командой  ciw
  4. Чтобы в режиме вставки вставить текст непосредственно из регистра,
     воспользуйтесь командой  <C-r>a

  5. Чтобы в режиме вставки вставить результат вычисления простых математических
     операций, воспользуйтесь командой  <C-r>=60*60<ENTER>
  6. Чтобы в режиме вставки вставить результат выполнения команд системы,
     воспользуйтесь командой  <C-r>=system('ls -l')

  7. Чтобы просмотреть содержимое регистров, воспользуйтесь командой  :reg
  8. Учитывайте распределение удалённых целиком строк по команде  dd  — это
     нумерованные регистры в порядке убывания, т. е. от 1 до 9.
     Помните, что в нумерованных регистрах дольше хранятся те строки, которые
     были уделены целиком, в отличие от любых других операций
  9. Учитывайте, что в нумерованных регистрах кратковременно сохраняется всё
     что скопировано.

 10. Чтобы установить закладку в режиме команд, воспользуйтесь командой
     m[a-zA-Z0-9]
 11. Чтобы переместить каретку на строку в которой установлена закладка,
     воспользуйтесь командой  '

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  На этом пока заканчивается вторая глава учебника по редактору Vim.
  Работа над этой главой будет продолжена.

  Вторая глава учебника была написана Полом Д. Паркером (Paul D. Parker).

    Restorer, перевод на русский язык, 2025, <EMAIL>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
