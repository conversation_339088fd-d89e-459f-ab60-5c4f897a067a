===============================================================================
=    Xin chào mừng bạn đến với Hướng dẫn dùng Vim    -    Phiên bản 1.5      =
===============================================================================
	Vim là một trình soạn thảo rất mạnh. Vim có rất nhiều câu lệnh,
	chính vì thế không thể trình bày hết được trong cuốn hướng dẫn này.
	Cuốn hướng dẫn chỉ đưa ra những câu lệnh để giúp bạn sử dụng Vim
	được dễ dàng hơn. Đây cũng chính là mục đich của sách

	Cần khoảng 25-30 phút để hoàn thành bài học, phụ thuộc vào thời
	gian thực hành.

	Các câu lệnh trong bài học sẽ thay đổi văn bản này. Vì thế hãy tạo
	một bản sao của tập tin này để thực hành (nếu bạn dùng "vimtutor"
	thì đây đã là bản sao).

	Hãy nhớ rằng hướng dẫn này viết với nguyên tắc "học đi đôi với hành".
	Có nghĩa là bạn cần chạy các câu lệnh để học chúng. Nếu chỉ đọc, bạn
	sẽ quên các câu lệnh!

	Bây giờ, cần chắc chắn là phím Shift KHÔNG bị nhấn và hãy nhấn phím
	j đủ số lần cần thiết (di chuyển con trỏ) để Bài 1.1.1 hiện ra đầy đủ
	trên màn hình.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Bài 1.1.1:  DI CHUYỂN CON TRỎ


   ** Để di chuyển con trỏ, nhấn các phím h,j,k,l như đã chỉ ra. **
	     ^
	     k		    Gợi ý:  phím h ở phía trái và di chuyển sang trái.
	< h	l >		   phím l ở bên phải và di chuyển sang phải.
	     j			   phím j trong như một mũi tên chỉ xuống
	     v
  1. Di chuyển con trỏ quanh màn hình cho đến khi bạn quen dùng.

  2. Nhấn và giữ phím (j) cho đến khi nó lặp lại.
---> Bây giờ bạn biết cách chuyển tới bài học thứ hai.

  3. Sử dụng phím di chuyển xuống bài 1.1.2.

Chú ý: Nếu bạn không chắc chắn về những gì đã gõ, hãy nhấn <ESC> để chuyển vào
	 chế độ Câu lệnh, rồi gõ lại những câu lệnh mình muốn.

Chú ý: Các phím mũi tên cũng làm việc. Nhưng một khi sử dụng thành thạo hjkl,
	 bạn sẽ di chuyển con trỏ nhanh hơn so với các phím mũi tên.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Bài 1.1.2: VÀO VÀ THOÁT VIM


  !! CHÚ Ý: Trước khi thực hiện bất kỳ lệnh nào, xin hãy đọc cả bài học này!!

  1. Nhấn phím <ESC> (để chắc chắn là bạn đang ở chế độ Câu lệnh).

  2. Gõ:			:q! <ENTER>.

---> Lệnh này sẽ thoát trình soạn thảo mà KHÔNG ghi nhớ bất kỳ thay đổi nào mà bạn đã làm.
     Nếu bạn muốn ghi nhớ những thay đổi đó và thoát thì hãy gõ:
				:wq  <ENTER>

  3. Khi thấy dấu nhắc shell, hãy gõ câu lệnh đã đưa bạn tới hướng dẫn này. Có
	 thể là lệnh:	vimtutor vi <ENTER>
     Thông thường bạn dùng:	vim tutor.vi<ENTER>

---> 'vim' là trình soạn thảo vim, 'tutor.vi' là tập tin bạn muốn soạn thảo.

  4. Nếu bạn đã nhớ và nắm chắc những câu lệnh trên, hãy thực hiện các bước từ
	 1 tới 3 để thoát và quay vào trình soạn thảo. Sau đó di chuyển con trỏ
	 tới Bài 1.1.3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Bài 1.1.3: SOẠN THẢO VĂN BẢN - XÓA


** Trong chế độ Câu lệnh nhấn	x  để xóa ký tự nằm dưới con trỏ. **

  1. Di chuyển con trỏ tới dòng có dấu --->.

  2. Để sửa lỗi, di chuyển con trỏ để nó nằm trên ký tự sẽ bị
	 xóa.

  3. Nhấn phím	x  để xóa ký tự không mong muốn.

  4. Lặp lại các bước từ 2 tới 4 để sửa lại câu.

---> Emm xiinh em đứnng chỗ nào cũnkg xinh.

  5. Câu trên đã sửa xong, hãy chuyển tới Bài 1.1.4.

Chú ý: Khi học theo cuốn hướng dẫn này đừng cố nhớ, mà học từ thực hành.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Bài 1.1.4: SOẠN THẢO VĂN BẢN - CHÈN


	 ** Trong chế độ Câu lệnh nhấn  i  để chèn văn bản. **

  1. Di chuyển con trỏ tới dòng có dấu ---> đầu tiên.

  2. Để dòng thứ nhất giống hệt với dòng thứ hai, di chuyển con trỏ lên ký tự
	 đầu tiên NGAY SAU chỗ muốn chèn văn bản.

  3. Nhấn	i	và gõ văn bản cần thêm.

  4. Sau mỗi lần chèn từ còn thiếu nhấn <ESC> để trở lại chế dộ Câu lệnh.
     Lặp lại các bước từ 2 tới 4 để sửa câu này.

---> Mot lam chang nen , ba cay chum lai hon cao.
---> Mot cay lam chang nen non, ba cay chum lai nen hon nui cao.

  5. Sau khi thấy quen với việc chèn văn bản hãy chuyển tới phần tổng kết
	 ở dưới.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       TỔNG KẾT BÀI 1.1


  1. Con trỏ được di chuyển bởi các phím mũi tên hoặc các phím hjkl.
	 h (trái)	j (xuống)       k (lên)	    l (phải)

  2. Để vào Vim (từ dấu nhắc %) gõ:  vim TÊNTẬPTIN <ENTER>

  3. Muốn thoát Vim gõ:	   <ESC>   :q!	 <ENTER>  để vứt bỏ mọi thay đổi.
	     HOẶC gõ:	   <ESC>   :wq	 <ENTER>  để ghi nhớ thay đổi.

  4. Để xóa bỏ ký tự nằm dưới con trỏ trong chế độ Câu lệnh gõ:  x

  5. Để chèn văn bản tại vị trí con trỏ trong chế độ Câu lệnh gõ:
	 i     văn bản sẽ nhập	<ESC>

CHÚ Ý: Nhấn <ESC> sẽ đưa bạn vào chế độ Câu lệnh hoặc sẽ hủy bỏ một câu lệnh
	 hay đoạn câu lệnh không mong muốn.

Bây giờ chúng ta tiếp tục với Bài 1.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Bài 1.2.1: CÁC LỆNH XÓA


	    ** Gõ	dw	để xóa tới cuối một từ. **

  1. Nhấn  <ESC>  để chắc chắn là bạn đang trong chế độ Câu lệnh.

  2. Di chuyển con trỏ tới dòng có dấu --->.

  3. Di chuyển con trỏ tới ký tự đầu của từ cần xóa.

  4. Gõ   dw	 để làm từ đó biến mất.

  CHÚ Ý: các ký tự dw sẽ xuất hiện trên dòng cuối cùng của màn hình khi bạn gõ
	 chúng. Nếu bạn gõ nhầm, hãy nhấn <ESC> và làm lại từ đầu.

---> Khi trái tỉm tìm tim ai như mùa đông giá lạnh lanh
	 Anh đâu thành cánh én nhỏ trùng khơi.

  5. Lặp lại các bước cho đến khi sửa xong câu thơ rồi chuyển tới Bài 1.2.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Bài 1.2.2: CÁC CÂU LỆNH XÓA KHÁC


	   ** gõ  d$	để xóa tới cuối một dòng. **

  1. Nhấn  <ESC>  để chắc chắn là bạn đang trong chế độ Câu lệnh.

  2. Di chuyển con trỏ tới dòng có dấu --->.

  3. Di chuyển con trỏ tới cuối câu đúng (SAU dấu . đầu tiên).

  4. Gõ    d$    để xóa tới cuối dòng.

---> Đã qua đi những tháng năm khờ dại. thừa thãi.


  5. Chuyển tới Bài 1.2.3 để hiểu cái gì đang xảy ra.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Bài 1.2.3: CÂU LỆNH VÀ ĐỐI TƯỢNG


  Câu lệnh xóa	d	có dạng như sau:

	 [số]   d	đối_tượng	    HOẶC	     d	 [số]   đối_tượng
  Trong đó:
    số - là số lần thực hiện câu lệnh (không bắt buộc, mặc định=1).
    d - là câu lệnh xóa.
    đối_tượng - câu lệnh sẽ thực hiện trên chúng (liệt kê phía dưới).

  Danh sách ngắn của đối tượng:
    w - từ con trỏ tới cuối một từ, bao gồm cả khoảng trắng.
    e - từ con trỏ tới cuối một từ, KHÔNG bao gồm khoảng trắng.
    $ - từ con trỏ tới cuối một dòng.

CHÚ Ý:  Dành cho những người ham tìm hiểu, chỉ nhấn đối tượng trong chế độ Câu
	 lệnh mà không có câu lệnh sẽ di chuyển con trỏ như trong danh sách trên.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Bài 1.2.4: TRƯỜNG HỢP NGOẠI LỆ CỦA QUY LUẬT  'CÂU LỆNH-ĐỐI TƯỢNG'


	       ** Gõ	 dd   để xóa cả một dòng. **

  Người dùng thường xuyên xóa cả một dòng, vì thế các nhà phát triển Vi đã
  quyết định dùng hai chữ d để đơn giản hóa thao tác này.

  1. Di chuyển con trỏ tới dòng thứ hai trong cụm phía dưới.
  2. Gõ dd để xóa dòng này.
  3. Bây giờ di chuyển tới dòng thứ  tư.
  4. Gõ   2dd   (hãy nhớ lại bộ ba  số-câu lệnh-đối tượng) để xóa hai dòng.

	 1) Trong tim em khắc sâu bao kỉ niệm
	 2) Tình yêu chân thành em dành cả cho anh
	 3) Dẫu cuộc đời như bể dâu thay đổi
	 4) Anh mãi là ngọn lửa ấm trong đêm
	 5) Đã qua đi những tháng năm khờ dại
	 7) Hãy để tự em lau nước mắt của mình
	 8) Lặng lẽ sống những đêm dài bất tận
	 9) Bao khổ đau chờ tia nắng bình minh

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Bài 1.2.5: CÂU LỆNH "HỦY THAO TÁC"


   ** Nhấn  u	để hủy bỏ những câu lệnh cuối cùng,   U	 để sửa cả một dòng. **

  1. Di chuyển con trỏ tới dòng có dấu ---> và đặt con trỏ trên từ có lỗi
	 đầu tiên
  2. Gõ  x  để xóa chữ cái gây ra lỗi đầu tiên.
  3. Bây giờ gõ  u  để hủy bỏ câu lệnh vừa thự hiện (xóa chữ cái).
  4. Dùng câu lệnh	x	để sửa lỗi cả dòng này.
  5. Bây giờ gõ chữ  U  hoa để phục hồi trạng thái ban đầu của dòng.
  6. Bây giờ gõ  u  vài lần để hủy bỏ câu lệnh  U  và các câu lệnh trước.
  7. Bây giờ gõ CTRL-R (giữ phím CTRL và gõ R) và lầu để thực hiện
     lại các câu lệnh (hủy bỏ các câu lệnh hủy bỏ).

---> Câyy ccó cộii, nuước csó nguuồn.

  8. Đây là những câu lệnh rất hữu ích.  Bây giờ chuyển tới Tổng kết Bài 1.2.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       TỔNG KẾT BÀI 1.2


  1. Để xóa từ con trỏ tới cuối một từ gõ:    dw

  2. Để xóa từ con trỏ tới cuối một dòng gõ:    d$

  3. Để xóa cả một dòng gõ:    dd

  4. Một câu lệnh trong chế độ Câu lệnh có dạng:

       [số]   câu_lệnh   đối_tượng     HOẶC     câu_lệnh	[số]   đối_tượng
     trong đó:
		số - là số lần thực hiện câu lệnh (không bắt buộc, mặc định=1).
		câu_lệnh - là những gì thực hiện, ví dụ	d	dùng để xóa.
		đối_tượng - câu lệnh sẽ thực hiện trên chúng, ví dụ	w (từ),
       	$ (tới cuối một dòng), v.v...

  5. Để hủy bỏ thao tác trước, gõ:	     u	 (chữ u thường)
     Để hủy bỏ tất cả các thao tác trên một dòng, gõ: U	 (chữ U hoa)
     Để hủy bỏ các câu lệnh hủy bỏ, gõ:		     CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Bài 1.3.1: CÂU LỆNH DÁN


       ** Gõ	p  để dán những gì vừa xóa tới sau con trỏ. **

  1. Di chuyển con trỏ tới dòng đầu tiên trong cụm ở dưới.

  2. Gõ  dd  để xóa và ghi lại một dòng trong bộ nhớ đệm của Vim.

  3. Di chuyển con trỏ tới dòng Ở TRÊN chỗ cần dán.

  4. Trong chế độ Câu lệnh, gõ    p	 để thay thế dòng.

  5. Lặp lại các bước từ 2 tới 4 để đặt các dòng theo đúng thứ tự của chúng.

     d) Niềm vui như gió xưa bay nhè nhẹ
     b) Em vẫn mong anh sẽ đến với em
     c) Đừng để em mất đi niềm hy vọng đó
     a) Ai sẽ giúp em vượt qua sóng gió
	 e) Dễ ra đi khó giữ lại bên mình


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Bài 1.3.2: CÂU LỆNH THAY THẾ


  ** Gõ  r  và một ký tự để thay thế ký tự nằm dưới con trỏ. **

  1. Di chuyển con trỏ tới dòng có dấu --->.

  2. Di chuyển con trỏ tới ký tự gõ sai đầu tiên.

  3. Gõ   r	và ký tự đúng.

  4. Lặp lại các bước từ 2 đến 4 để sửa cả dòng.

--->  "Trên đời nài làm gì có đườmg, người to đi mãi rồi thànk đường là tHôi"
--->  "Trên đời này làm gì có đường, người ta đi mãi rồi thành đường mà thôi"

  5. Bây giờ chuyển sang Bài 1.3.3.

CHÚ Ý: Hãy nhớ rằng bạn cần thực hành, không nên "học vẹt".



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Bài 1.3.3: CÂU LỆNH THAY ĐỔI


	   ** Để thay đổi một phần hay cả một từ, gõ  cw . **

  1. Di chuyển con trỏ tới dòng có dấu --->.

  2. Đặt con trỏ trên chữ trong.

  3. Gõ  cw  và sửa lại từ (trong trường hợp này, gõ  'ine'.)

  4. Gõ <ESC> và chuyển tới lỗi tiếp theo (chữ cái đầu tiên trong số cần thay.)

  5. Lặp lại các bước 3 và 4 cho tới khi thu được dòng như dòng thứ hai.

---> Trên dùgn này có một dầy từ cần tyays đổi, sử dunk câu lệnh thay đổi.
---> Trên dong này có một vai từ cần thay đổi, sử dung câu lệnh thay đổi.

Chú ý rằng  cw  không chỉ thay đổi từ, nhưng còn đưa bạn vào chế độ chèn.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Bài 1.3.4: TIẾP TỤC THAY ĐỔI VỚI c


     ** Câu lệnh thay đổi được sử dụng với cùng đối tượng như câu lệnh xóa. **

  1. Câu lệnh thay đổi làm việc tương tự như câu lệnh xóa. Định dạng như sau:

        [số]   c   đối_tượng     HOẶC     c	[số]   đối_tượng

  2. Đối tượng cũng giống như ở trên, ví dụ   w (từ), $ (cuối dòng), v.v...

  3. Di chuyển con trỏ tới dòng có dấu --->.

  4. Di chuyển con trỏ tới dòng có lỗi đầu tiên.

  5. Gõ  c$  để sửa cho giống với dòng thứ hai và gõ <ESC>.

---> Doan cuoi dong nay can sua de cho giong voi dong thu hai.
---> Doan cuoi dong nay can su dung cau lenh c$ de sua.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       TỔNG KẾT BÀI 1.3


  1. Để dán đoạn văn bản vừa xóa, gõ p. Câu lệnh này sẽ đặt đoạn văn bản này
	 PHÍA SAU con trỏ (nếu một dòng vừa bị xóa, dòng này sẽ được đặt vào dòng
	 nằm dưới con trỏ).

  2. Để thay thế ký tự dưới con trỏ, gõ   r   và sau đó gõ
     ký tự muốn thay vào.

  3. Câu lệnh thay đổi cho phép bạn thay đổi đối tượng chỉ ra từ con
     trỏ tới cuối đối tượng.  vd. Gõ  cw  để thay đổi từ
     con trỏ tới cuối một từ, c$	để thay đổi tới cuối một dòng.

  4. Định dạng để thay đổi:

	[số]   c   đối_tượng     HOẶC     c	[số]   đối_tượng

Bây giờ chúng ta tiếp tục bài học mới.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Bài 1.4.1: THÔNG TIN VỀ TẬP TIN VÀ VỊ TRÍ TRONG TẬP TIN


  ** Gõ CTRL-g để hiển thị vị trí của bạn trong tập tin và thông tin về tập tin.
     Gõ SHIFT-G để chuyển tới một dòng trong tập tin. **

  Chú ý: Đọc toàn bộ bài học này trước khi thực hiện bất kỳ bước nào!!

  1. Giữ phím Ctrl và nhấn  g .  Một dòng thông tin xuất hiện tại cuối trang
     với tên tập tin và dòng mà bạn đang nằm trên.  Hãy nhớ số dòng này
     Cho bước số 3.

  2. Nhấn shift-G để chuyển tới cuối tập tin.

  3. Gõ số dòng mà bạn đã nằm trên và sau đó shift-G.  Thao tác này sẽ đưa bạn
     trở lại dòng mà con trỏ đã ở trước khi nhấn tổ hợp Ctrl-g.
     (Khi bạn gõ số, chúng sẽ KHÔNG hiển thị trên màn hình.)

  4. Nếu bạn cảm thấy đã hiểu rõ, hãy thực hiện các bước từ 1 tới 3.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Bài 1.4.2: CÂU LỆNH TÌM KIẾM


     ** Gõ  /  và theo sau là cụm từ muốn tìm kiếm. **

  1. Trong chế độ Câu lệnh gõ ký tự  /  .Chú ý rằng ký tự này  và con trỏ sẽ
	 xuất hiện tại cuối màn hình giống như câu lệnh  :	.

  2. Bây giờ gõ 'loiiiii' <ENTER>.  Đây là từ bạn muốn tìm.

  3. Để tìm kiếm cụm từ đó lần nữa, đơn giản gõ  n .
     Để tìm kiếm cụm từ theo hướng ngược lại, gõ  Shift-N .

  4. Nếu bạn muối tìm kiếm cụm từ theo hướng ngược lại đầu tập tin, sử dụng
     câu lệnh  ?  thay cho /.

---> "loiiiii" là những gì không đúng lắm;  loiiiii thường xuyên xảy ra.

Chú ý: Khi tìm kiếm đến cuối tập tin, việc tìm kiếm sẽ tiếp tục từ đầu
      tập tin này.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Bài 1.4.3: TÌM KIẾM CÁC DẤU NGOẶC SÁNH ĐÔI


	      ** Gõ  %  để tìm kiếm ),], hay } . **

  1. Đặt con trỏ trên bất kỳ một (, [, hay { nào trong dòng có dấu --->.

  2. Bây giờ gõ ký tự  %  .

  3. Con trỏ sẽ di chuyển đến dấu ngoặc tạo cặp (dấu đóng ngoặc).

  4. Gõ  %  để chuyển con trỏ trở lại dấu ngoặc đầu tiên (dấu mở ngoặc).

---> Đây là ( một dòng thử nghiệm với các dấu ngoặc (, [ ] và { } . ))

Chú ý: Rất có ích khi sửa lỗi chương trình, khi có các lỗi thừa thiếu dấu ngoặc!






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Bài 1.4.4: MỘT CÁCH SỬA LỖI


	** Gõ  :s/cũ/mới/g  để thay thế 'mới' vào 'cũ'. **

  1. Di chuyển con trỏ tới dòng có dấu --->.

  2. Gõ  :s/duou/ruou <ENTER> .  Chú ý rằng câu lệnh này chỉ thay đổi từ tìm
     thấy đầu tiên trên dòng (từ 'duou' đầu dòng).

  3. Bây giờ gõ	 :s/duou/ruou/g	   để thực hiện thay thế trên toàn bộ dòng.
     Lệnh này sẽ thay thế tất cả những từ ('duou') tìm thấy trên dòng.

---> duou ngon phai co ban hie. Khong duou cung khong hoa.

  4. Để thay thế thực hiện trong đoạn văn bản giữa hai dòng,
     gõ   :#,#s/cũ/mới/g    trong đó #,# là số thứ tự của hai dòng.
     Gõ   :%s/cũ/mới/g    để thực hiện thay thế trong toàn bộ tập tin.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       TỔNG KẾT BÀI 1.4


  1. Ctrl-g  vị trí của con trỏ trong tập tin và thông tin về tập tin.
     Shift-G  di chuyển con trỏ tới cuối tập tin.  Số dòng và theo sau
     là  Shift-G  di chuyển con trỏ tới dòng đó.

  2. Gõ  /	và cụm từ theo sau để tìm kiếm cụm từ VỀ PHÍA TRƯỚC.
     Gõ  ?	và cụm từ theo sau để tìm kiếm cụm từ NGƯỢC TRỞ LẠI.
     Sau một lần tìm kiếm gõ  n  để tìm kiếm cụm từ lại một lần nữa theo hướng
     đã tìm hoặc  Shift-N  để tìm kiếm theo hướng ngược lại.

  3. Gõ  %	khi con trỏ nằm trên một  (,),[,],{, hay }  sẽ chỉ ra vị trí của
     dấu ngoặc còn lại trong cặp.

  4. Để thay thế 'mới' cho 'cũ' đầu tiên trên dòng, gõ    :s/cũ/mới
     Để thay thế 'mới' cho tất cả 'cũ' trên dòng, gõ	   :s/cũ/mới/g
     Để thay thế giữa hai dòng, gõ	   :#,#s/cũ/mới/g
     Để thay thế trong toàn bộ tập tin, gõ	   :%s/cũ/mới/g
     Để chương trình hỏi lại trước khi thay thế, thêm 'c'	:%s/cũ/mới/gc


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Lesson 1.5.1: CÁCH THỰC HIỆN MỘT CÂU LỆNH NGOẠI TRÚ


   ** Gõ  :!	theo sau là một câu lệnh ngoại trú để thực hiện câu lệnh đó. **

  1. Gõ câu lệnh quen thuộc	:  để đặt con trỏ tại cuối màn hình.
     Thao tác này cho phép bạn nhập một câu lệnh.

  2. Bây giờ gõ ký tự  !  (chấm than).  Ký tự này cho phép bạn
     thực hiện bất kỳ một câu lệnh shell nào.

  3. Ví dụ gõ   ls   theo sau dấu ! và gõ <ENTER>.  Lệnh này
	 sẽ hiển thị nội dung của thư mục hiện thời, hoặc sử dụng
     lệnh   :!dir	nếu ls không làm việc.

Chú ý:  Có thể thực hiện bất kỳ câu lệnh ngoại trú nào theo cách này.

Chú ý:  Tất cả các câu lệnh  :  cần kết thúc bởi phím <ENTER>




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Bài 1.5.2: GHI LẠI CÁC TẬP TIN


     ** Để ghi lại các thay đổi, gõ  :w TÊNTỆPTIN. **

  1. Gõ  :!dir  hoặc  :!ls  để lấy bảng liệt kê thư mục hiện thời.
     Như bạn đã biết, bạn cần gõ <ENTER> để thực hiện.

  2. Chọn một tên tập tin chưa có, ví dụ TEST.

  3. Bây giờ gõ:	 :w TEST   (trong đó TEST là tên tập tin bạn đã chọn.)

  4. Thao tác này ghi toàn bộ tập tin	(Hướng dẫn dùng Vim) dưới tên TEST.
     Để kiểm tra lại, gõ    :!dir   một lần nữa để liệt kê thư mục.

Chú ý: Nếu bạn thoát khỏi Vim và quay trở lại với tên tập tin TEST, thì tập
      tin sẽ là bản sao của hướng dẫn tại thời điểm bạn ghi lại.

  5. Bây giờ xóa bỏ tập tin (MS-DOS):    :!del TEST
				hay (Unix):	:!rm TEST


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Bài 1.5.3: CÂU LỆNH GHI CHỌN LỌC


	** Để ghi một phần của tập tin, gõ   :#,# w TÊNTẬPTIN **

  1. Gõ lại một lần nữa  :!dir  hoặc  :!ls  để liệt kê nội dung thư mục
     rồi chọn một tên tập tin thích hợp, ví dụ TEST.

  2. Di chuyển con trỏ tới đầu trang này, rồi gõ  Ctrl-g  để tìm ra số thứ
     tự của dòng đó.  HÃY NHỚ SỐ THỨ TỰ NÀY!

  3. Bây giờ di chuyển con trỏ tới dòng cuối trang và gõ lại Ctrl-g lần nữa.
	 HÃY NHỚ CẢ SỐ THỨ TỰ NÀY!

  4. Để CHỈ ghi lại một phần vào một tập tin, gõ   :#,# w TEST   trong đó #,#
     là hai số thứ tự bạn đã nhớ (đầu,cuối) và TEST là tên tập tin.

  5. Nhắc lại, xem tập tin của bạn có ở đó không với  :!dir  nhưng ĐỪNG xóa.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Bài 1.5.4: ĐỌC VÀ KẾT HỢP CÁC TẬP TIN


       ** Để chèn nội dung của một tập tin, gõ   :r TÊNTẬPTIN **

  1. Gõ   :!dir   để chắc chắn là có tệp tin TEST.

  2. Đặt con trỏ tại đầu trang này.

CHÚ Ý:  Sau khi thực hiện Bước 3 bạn sẽ thấy Bài 1.5.3. Sau đó cần di chuyển
	 XUỐNG bài học này lần nữa.

  3. Bây giờ dùng câu lệnh   :r TEST   để đọc tập tin TEST, trong đó TEST là
     tên của tập tin.

CHÚ Ý:  Tập tin được đọc sẽ đặt bắt đầu từ vị trí của con trỏ.

  4. Để kiểm tra lại, di chuyển con trỏ ngược trở lại và thấy rằng bây giờ
     có hai Bài 1.5.3, bản gốc và bản vừa chèn.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			      TỔNG KẾT BÀI 1.5


  1.  :!câulệnh  thực hiện một câu lệnh ngoại trú

      Một vài ví dụ hữu ích:
	 (MS-DOS)	  (Unix)
	  :!dir		   :!ls		   -  liệt kê nội dung một thư mục.
	  :!del TÊNTẬPTIN   :!rm TÊNTẬPTIN   -  xóa bỏ tập tin TÊNTẬPTIN.

  2.  :w TÊNTẬPTIN  ghi tập tin hiện thời của Vim lên đĩa với tên TÊNTẬPTIN.

  3.  :#,#w TÊNTẬPTIN  ghi các dòng từ # tới # vào tập tin TÊNTẬPTIN.

  4.  :r TÊNTẬPTIN  đọc tập tin trên đĩa TÊNTẬPTIN và chèn nội dung của nó vào
      tập tin hiện thời sau vị trí của con trỏ.






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Bài 1.6.1: CÂU LỆNH TẠO DÒNG


 ** Gõ  o  để mở một dòng phía dưới con trỏ và chuyển vào chế độ Soạn thảo. **

  1. Di chuyển con trỏ tới dòng có dấu --->.

  2. Gõ  o (chữ thường) để mở một dòng BÊN DƯỚI con trỏ và chuyển vào chế độ
     Soạn thảo.

  3. Bây giờ sao chép dòng có dấu ---> và nhấn <ESC> để thoát khỏi chế độ Soạn
	 thảo.

---> Sau khi gõ  o  con trỏ sẽ đặt trên dòng vừa mở trong chế độ Soạn thảo.

  4. Để mở một dòng Ở TRÊN con trỏ, đơn giản gõ một chữ	O hoa, thay cho
     chữ  o thường.  Hãy thử thực hiện trên dòng dưới đây.
Di chuyển con trỏ tới dòng này, rồi gõ Shift-O sẽ mở một dòng trên nó.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Bài 1.6.2: CÂU LỆNH THÊM VÀO


	     ** Gõ  a  để chèn văn bản vào SAU con trỏ. **

  1. Di chuyển con trỏ tới cuối dòng đầu tiên có ký hiệu --->
     bằng cách gõ  $	trong chế độ câu lệnh.

  2. Gõ  a  (chữ thường) để thêm văn bản vào SAU ký tự dưới con trỏ.
     (Chữ  A  hoa thêm văn bản vào cuối một dòng.)

Chú ý: Lệnh này thay cho việc gõ  i , ký tự cuối cùng, văn bản muốn chèn,
     <ESC>, mũi tên sang phải, và cuối cùng, x , chỉ để thêm vào cuối dòng!

  3. Bây giờ thêm cho đủ dòng thứ nhất. Chú ý rằng việc thêm giống hệt với
     việc chèn, trừ vị trí chèn văn bản.

---> Dong nay cho phep ban thuc hanh
---> Dong nay cho phep ban thuc hanh viec them van ban vao cuoi dong.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Bài 1.6.3: MỘT CÁCH THAY THẾ KHÁC


      ** Gõ chữ cái  R  hoa để thay thế nhiều ký tự. **

  1. Di chuyển con trỏ tới cuối dòng đầu tiên có ký hiệu --->.

  2. Đặt con trỏ tại chữ cái đầu của từ đầu tiên khác với dòng có dấu
     ---> tiếp theo (từ 'tren').

  3. Bây giờ gõ R và thay thế phần còn lại của dòng thứ nhất bằng cách gõ
     đè lên văn bản cũ để cho hai dòng giống nhau.

---> De cho dong thu nhat giong voi dong thu hai tren trang nay.
---> De cho dong thu nhat giong voi dong thu hai, go R va van ban moi.

  4. Chú ý rằng khi bạn nhấn <ESC> để thoát, đoạn văn bản không sửa đổi sẽ
	 được giữ nguyên.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			    Bài 1.6.4: THIẾT LẬP CÁC THAM SỐ

	  ** Thiết lập một tùy chọn để việc tìm kiếm hay thay thế lờ đi kiểu chữ **

  1. Tìm kiếm từ 'lodi' bằng cách gõ:
     /lodi
     Lặp lại vài lần bằng phím n.

  2. Đặt tham số 'ic' (Lodi - ignore case) bằng cách gõ:
     :set ic

  3. Bây giờ thử lại tìm kiếm 'lodi' bằng cách gõ: n
     Lặp lại vài lần bằng phím n.

  4. Đặt các tham số 'hlsearch' và 'incsearch':
     :set hls is

  5. Bây giờ nhập lại câu lệnh tìm kiếm một lần nữa và xem cái gì xảy ra:
     /lodi

  6. Để xóa bỏ việc hiện sáng từ tìm thấy, gõ:
     :nohlsearch
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       TỔNG KẾT BÀI 1.6


  1. Gõ  o	mở một dòng phía DƯỚI con trỏ và đặt con trỏ trên dòng vừa mở
     trong chế độ Soạn thảo.
     Gõ một chữ  O  hoa để mở dòng phía TRÊN dòng của con trỏ.

  2. Gõ  a  để chèn văn bản vào SAU ký tự nằm dưới con trỏ.
     Gõ một chữ  A  hoa tự động thêm văn bản vào cuối một dòng.

  3. Gõ một chữ  R  hoa chuyển vào chế độ Thay thế cho đến khi nhấn  <ESC>.

  4. Gõ ":set xxx" sẽ đặt tham số "xxx"









~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Bài 1.7: CÂU LỆNH TRỢ GIÚP


		      ** Sử dụng hệ thống trợ giúp có sẵn **

  Vim có một hệ thống trợ giúp đầy đủ. Để bắt đầu, thử một trong ba
  lệnh sau:
	- nhấn phím <HELP> (nếu bàn phím có)
	- nhấn phím <F1> (nếu bàn phím có)
	- gõ   :help <ENTER>

  Gõ   :q <ENTER>   để đóng cửa sổ trợ giúp.

  Bạn có thể tìm thấy trợ giúp theo một đề tài, bằng cách đưa tham số tới
  câu lệnh ":help".  Hãy thử (đừng quên gõ <ENTER>):

	:help w
	:help c_<T
	:help insert-index
	:help user-manual


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       Bài 1.8: TẠO MỘT SCRIPT KHỞI ĐỘNG

			  ** Bật các tính năng của Vim **

  Vim có nhiều tính năng hơn Vi, nhưng hầu hết chúng bị tắt theo mặc định.
  Để sử dụng các tính năng này bạn cần phải tạo một tập tin "vimrc".

  1. Soạn thảo tệp tin "vimrc", phụ thuộc vào hệ thống của bạn:
	:edit ~/.vimrc		đối với Unix
	:edit ~/_vimrc		đối với MS-Windows

  2. Bây giờ đọc tập tin "vimrc" ví dụ:

	:read $VIMRUNTIME/vimrc_example.vim

  3. Ghi lại tập tin:

	:write

  Trong lần khởi động tiếp theo, Vim sẽ sử dụng việc hiện sáng cú pháp.
  Bạn có thể thêm các thiết lập ưa thích vào tập tin "vimrc" này.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Bài học hướng dẫn sử dụng Vim (Vim Tutor) kết thúc tại đây.  Bài học đưa ra
  cái nhìn tổng quát về trình soạn thảo Vim, chỉ đủ để bạn có thể sử dụng
  trình soạn thảo một cách dễ dàng. Bài học còn rất xa để có thể nói là đầy
  đủ vì Vim có rất rất nhiều câu lệnh. Tiếp theo xin hãy đọc hướng dẫn người
  dùng: ":help user-manual".

  Cuốn sách sau được khuyên dùng cho việc nghiên cứu sâu hơn:
	Vim - Vi Improved - Tác giả: Steve Oualline
	Nhà xuất bản: New Riders
  Cuốn sách đầu tiên dành hoàn toàn cho Vim. Đặc biệt có ích cho người mới.
  Có rất nhiều ví dụ và tranh ảnh.
  Hãy xem: https://iccf-holland.org/click5.html

  Cuốn sách tiếp theo này xuất bản sớm hơn và nói nhiều về Vi hơn là Vim,
  nhưng cũng rất nên đọc:
	Learning the Vi Editor - Tác giả: Linda Lamb
	Nhà xuất bản: O'Reilly & Associates Inc.
  Đây là một cuốn sách hay và cho bạn biết tất cả cách thực hiện những gì muốn
  làm với Vi. Lần xuất bản thứ sáu đã thêm thông tin về Vim.

  Bài học hướng dẫn này viết bởi Michael C. Pierce và Robert K. Ware,
  Colorado School of Mines sử dụng ý tưởng của Charles Smith,
  Colorado State University.  E-mail: <EMAIL>.

  Sửa đổi cho Vim bởi Bram Moolenaar.

  Dịch bởi: Phan Vĩnh Thịnh <<EMAIL>>, 2005
  Translator: Phan Vinh Thịnh <<EMAIL>>, 2005
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
