" Back to Qwerty keyboard after using Dvorak.

iunmap a
iunmap b
iunmap c
iunmap d
iunmap e
iunmap f
iunmap g
iunmap h
iunmap i
iunmap j
iunmap k
iunmap l
iunmap m
iunmap n
iunmap o
iunmap p
iunmap q
iunmap r
iunmap s
iunmap t
iunmap u
iunmap v
iunmap w
iunmap x
iunmap y
iunmap z
iunmap ;
iunmap '
iunmap "
iunmap ,
iunmap .
iunmap /
iunmap A
iunmap B
iunmap C
iunmap D
iunmap E
iunmap F
iunmap G
iunmap H
iunmap I
iunmap J
iunmap K
iunmap L
iunmap M
iunmap N
iunmap O
iunmap P
iunmap Q
iunmap R
iunmap S
iunmap T
iunmap U
iunmap V
iunmap W
iunmap X
iunmap Y
iunmap Z
iunmap <
iunmap >
iunmap ?
iunmap :
iunmap [
iunmap ]
iunmap {
iunmap }
iunmap -
iunmap _
iunmap =
iunmap +
