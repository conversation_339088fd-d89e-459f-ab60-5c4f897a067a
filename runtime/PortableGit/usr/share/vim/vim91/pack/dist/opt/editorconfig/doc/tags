:EditorConfigReload	editorconfig.txt	/*:EditorConfigReload*
EditorConfig#AddNewHook()	editorconfig.txt	/*EditorConfig#AddNewHook()*
b:EditorConfig_disable	editorconfig.txt	/*b:EditorConfig_disable*
editorconfig-advanced	editorconfig.txt	/*editorconfig-advanced*
editorconfig-commands	editorconfig.txt	/*editorconfig-commands*
editorconfig-contents	editorconfig.txt	/*editorconfig-contents*
editorconfig-hook	editorconfig.txt	/*editorconfig-hook*
editorconfig-installation	editorconfig.txt	/*editorconfig-installation*
editorconfig-license	editorconfig.txt	/*editorconfig-license*
editorconfig-overview	editorconfig.txt	/*editorconfig-overview*
editorconfig-settings	editorconfig.txt	/*editorconfig-settings*
editorconfig.txt	editorconfig.txt	/*editorconfig.txt*
g:EditorConfig_core_mode	editorconfig.txt	/*g:EditorConfig_core_mode*
g:EditorConfig_enable_for_new_buf	editorconfig.txt	/*g:EditorConfig_enable_for_new_buf*
g:EditorConfig_exclude_patterns	editorconfig.txt	/*g:EditorConfig_exclude_patterns*
g:EditorConfig_exec_path	editorconfig.txt	/*g:EditorConfig_exec_path*
g:EditorConfig_max_line_indicator	editorconfig.txt	/*g:EditorConfig_max_line_indicator*
g:EditorConfig_preserve_formatoptions	editorconfig.txt	/*g:EditorConfig_preserve_formatoptions*
g:EditorConfig_softtabstop_space	editorconfig.txt	/*g:EditorConfig_softtabstop_space*
g:EditorConfig_softtabstop_tab	editorconfig.txt	/*g:EditorConfig_softtabstop_tab*
g:EditorConfig_verbose	editorconfig.txt	/*g:EditorConfig_verbose*
