" Dvorak keyboard, only in Insert mode.
"
" Change "inoremap" to "map!" to also use in Ex mode.
" Also change disable.vim then: "iunmap" to "unmap!".
"
" You may want to add a list of map's too.

inoremap a a
inoremap b x
inoremap c j
inoremap d e
inoremap e .
inoremap f u
inoremap g i
inoremap h d
inoremap i c
inoremap j h
inoremap k t
inoremap l n
inoremap m m
inoremap n b
inoremap o r
inoremap p l
inoremap q '
inoremap r p
inoremap s o
inoremap t y
inoremap u g
inoremap v k
inoremap w ,
inoremap x q
inoremap y f
inoremap z ;
inoremap ; s
inoremap ' -
inoremap " _
inoremap , w
inoremap . v
inoremap / z
inoremap A A
inoremap B X
inoremap C J
inoremap D E
inoremap E >
inoremap F U
inoremap G I
inoremap H D
inoremap I C
inoremap J H
inoremap K T
inoremap L N
inoremap M M
inoremap N B
inoremap O R
inoremap P L
inoremap Q "
inoremap R P
inoremap S O
inoremap T Y
inoremap U G
inoremap V K
inoremap W <
inoremap X Q
inoremap Y F
inoremap Z :
inoremap < W
inoremap > V
inoremap ? Z
inoremap : S
inoremap [ /
inoremap ] =
inoremap { ?
inoremap } +
inoremap - [
inoremap _ {
inoremap = ]
inoremap + }
