" These macros swap the left and right mouse buttons (for left handed)
" Don't forget to do ":set mouse=a" or the mouse won't work at all
noremap	 <LeftMouse>	<RightMouse>
noremap	 <2-LeftMouse>	<2-RightMouse>
noremap	 <3-LeftMouse>	<3-RightMouse>
noremap	 <4-LeftMouse>	<4-RightMouse>
noremap	 <LeftDrag>	<RightDrag>
noremap	 <LeftRelease>	<RightRelease>
noremap	 <RightMouse>	<LeftMouse>
noremap	 <2-RightMouse>	<2-LeftMouse>
noremap	 <3-RightMouse>	<3-LeftMouse>
noremap	 <4-RightMouse>	<4-LeftMouse>
noremap	 <RightDrag>	<LeftDrag>
noremap	 <RightRelease>	<LeftRelease>
noremap	 g<LeftMouse>	<C-RightMouse>
noremap	 g<RightMouse>	<C-LeftMouse>
noremap! <LeftMouse>	<RightMouse>
noremap! <LeftDrag>	<RightDrag>
noremap! <LeftRelease>	<RightRelease>
noremap! <RightMouse>	<LeftMouse>
noremap! <RightDrag>	<LeftDrag>
noremap! <RightRelease>	<LeftRelease>
