" Name:         Retro Box
" Description:  Retro groove color scheme similar to gruvbox originally designed by mor<PERSON><PERSON> <<EMAIL>>
" Author:       <PERSON> <<EMAIL>>, ported from gruvbox8 of Lifepillar <<EMAIL>>
" Maintainer:   <PERSON> <<EMAIL>>, ported from gruvbox8 of Lifepillar <<EMAIL>>
" Website:      https://www.github.com/vim/colorschemes
" License:      Vim License (see `:help license`)
" Last Change:  2025 Jan 07

" Generated by Colortemplate v2.2.3

hi clear
let g:colors_name = 'retrobox'

let s:t_Co = has('gui_running') ? -1 : (&t_Co ?? 0)

hi! link CursorColumn CursorLine
hi! link StatusLineTerm StatusLine
hi! link StatusLineTermNC StatusLineNC
hi! link VisualNOS Visual
hi! link Tag Special
hi! link lCursor Cursor
hi! link MessageWindow PMenu
hi! link PopupNotification Todo
hi! link PopupSelected PmenuSel
hi! link CurSearch IncSearch
hi! link Terminal Normal

if &background ==# 'dark'
  if (has('termguicolors') && &termguicolors) || has('gui_running')
    let g:terminal_ansi_colors = ['#1c1c1c', '#cc241d', '#98971a', '#d79921', '#458588', '#b16286', '#689d6a', '#a89984', '#928374', '#fb4934', '#b8bb26', '#fabd2f', '#83a598', '#d3869b', '#8ec07c', '#ebdbb2']
  endif
  hi Normal guifg=#ebdbb2 guibg=#1c1c1c gui=NONE cterm=NONE
  hi CursorLineNr guifg=#fabd2f guibg=#1c1c1c gui=bold cterm=bold
  hi FoldColumn guifg=#928374 guibg=#1c1c1c gui=NONE cterm=NONE
  hi SignColumn guifg=#928374 guibg=#1c1c1c gui=NONE cterm=NONE
  hi VertSplit guifg=#303030 guibg=#1c1c1c gui=NONE cterm=NONE
  hi ColorColumn guifg=NONE guibg=#000000 gui=NONE cterm=NONE
  hi Comment guifg=#928374 guibg=NONE gui=NONE cterm=NONE
  hi CursorLine guifg=NONE guibg=#303030 gui=NONE cterm=NONE
  hi Error guifg=#fb4934 guibg=#1c1c1c gui=bold,reverse cterm=bold,reverse
  hi ErrorMsg guifg=#1c1c1c guibg=#fb4934 gui=bold cterm=bold
  hi Folded guifg=#928374 guibg=#121212 gui=NONE cterm=NONE
  hi LineNr guifg=#7c6f64 guibg=NONE gui=NONE cterm=NONE
  hi MatchParen guifg=NONE guibg=#504945 gui=bold,underline cterm=bold,underline
  hi NonText guifg=#504945 guibg=NONE gui=NONE cterm=NONE
  hi Pmenu guifg=#ebdbb2 guibg=#3c3836 gui=NONE cterm=NONE
  hi PmenuSbar guifg=NONE guibg=#3c3836 gui=NONE cterm=NONE
  hi PmenuSel guifg=#3c3836 guibg=#83a598 gui=bold cterm=bold
  hi PmenuThumb guifg=NONE guibg=#7c6f64 gui=NONE cterm=NONE
  hi PmenuKind guifg=#fb4934 guibg=#3c3836 gui=NONE cterm=NONE
  hi PmenuKindSel guifg=#fb4934 guibg=#83a598 gui=NONE cterm=NONE
  hi PmenuExtra guifg=#a89984 guibg=#3c3836 gui=NONE cterm=NONE
  hi PmenuExtraSel guifg=#303030 guibg=#83a598 gui=NONE cterm=NONE
  hi PmenuMatch guifg=#b16286 guibg=#3c3836 gui=NONE cterm=NONE
  hi PmenuMatchSel guifg=#b16286 guibg=#83a598 gui=bold cterm=bold
  hi SpecialKey guifg=#928374 guibg=NONE gui=NONE cterm=NONE
  hi StatusLine guifg=#504945 guibg=#ebdbb2 gui=bold,reverse cterm=bold,reverse
  hi StatusLineNC guifg=#3c3836 guibg=#a89984 gui=reverse cterm=reverse
  hi TabLine guifg=#a89984 guibg=#3c3836 gui=NONE cterm=NONE
  hi TabLineFill guifg=#ebdbb2 guibg=#3c3836 gui=NONE cterm=NONE
  hi TabLineSel guifg=#fbf1c7 guibg=#1c1c1c gui=bold cterm=bold
  hi ToolbarButton guifg=#fbf1c7 guibg=#303030 gui=bold cterm=bold
  hi ToolbarLine guifg=NONE guibg=NONE gui=NONE ctermfg=NONE ctermbg=NONE cterm=NONE
  hi Visual guifg=#1c1c1c guibg=#83a598 gui=NONE cterm=NONE
  hi WildMenu guifg=#83a598 guibg=#504945 gui=bold cterm=bold
  hi EndOfBuffer guifg=#504945 guibg=NONE gui=NONE cterm=NONE
  hi Conceal guifg=#504945 guibg=NONE gui=NONE cterm=NONE
  hi Cursor guifg=#1c1c1c guibg=#fbf1c7 gui=NONE cterm=NONE
  hi DiffAdd guifg=#b8bb26 guibg=#1c1c1c gui=reverse cterm=reverse
  hi DiffChange guifg=#8ec07c guibg=#1c1c1c gui=reverse cterm=reverse
  hi DiffDelete guifg=#fb4934 guibg=#1c1c1c gui=reverse cterm=reverse
  hi DiffText guifg=#fabd2f guibg=#1c1c1c gui=reverse cterm=reverse
  hi Directory guifg=#b8bb26 guibg=NONE gui=bold cterm=bold
  hi IncSearch guifg=#fe8019 guibg=#1c1c1c gui=reverse cterm=reverse
  hi ModeMsg guifg=#fabd2f guibg=NONE gui=bold cterm=bold
  hi MoreMsg guifg=#fabd2f guibg=NONE gui=bold cterm=bold
  hi Question guifg=#fe8019 guibg=NONE gui=bold cterm=bold
  hi Search guifg=#98971a guibg=#1c1c1c gui=reverse cterm=reverse
  hi QuickFixLine guifg=#8ec07c guibg=#1c1c1c gui=reverse cterm=reverse
  hi SpellBad guifg=#fb4934 guibg=NONE guisp=#fb4934 gui=undercurl cterm=underline
  hi SpellCap guifg=#83a598 guibg=NONE guisp=#83a598 gui=undercurl cterm=underline
  hi SpellLocal guifg=#8ec07c guibg=NONE guisp=#8ec07c gui=undercurl cterm=underline
  hi SpellRare guifg=#d3869b guibg=NONE guisp=#d3869b gui=undercurl cterm=underline
  hi Title guifg=#b8bb26 guibg=NONE gui=bold cterm=bold
  hi WarningMsg guifg=#fb4934 guibg=NONE gui=bold cterm=bold
  hi Boolean guifg=#d3869b guibg=NONE gui=NONE cterm=NONE
  hi Character guifg=#d3869b guibg=NONE gui=NONE cterm=NONE
  hi Conditional guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi Constant guifg=#d3869b guibg=NONE gui=NONE cterm=NONE
  hi Define guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi Debug guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi Delimiter guifg=#fe8019 guibg=NONE gui=NONE cterm=NONE
  hi Error guifg=#fb4934 guibg=#1c1c1c gui=bold,reverse cterm=bold,reverse
  hi Exception guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi Float guifg=#d3869b guibg=NONE gui=NONE cterm=NONE
  hi Function guifg=#b8bb26 guibg=NONE gui=bold cterm=bold
  hi Identifier guifg=#83a598 guibg=NONE gui=NONE cterm=NONE
  hi Ignore guifg=fg guibg=NONE gui=NONE cterm=NONE
  hi Include guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi Keyword guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi Label guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi Macro guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi Number guifg=#d3869b guibg=NONE gui=NONE cterm=NONE
  hi Operator guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi PreCondit guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi PreProc guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi Repeat guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi SpecialChar guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi SpecialComment guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi Statement guifg=#fb4934 guibg=NONE gui=NONE cterm=NONE
  hi StorageClass guifg=#fe8019 guibg=NONE gui=NONE cterm=NONE
  hi Special guifg=#fe8019 guibg=NONE gui=NONE cterm=NONE
  hi String guifg=#b8bb26 guibg=NONE gui=NONE cterm=NONE
  hi Structure guifg=#8ec07c guibg=NONE gui=NONE cterm=NONE
  hi Todo guifg=fg guibg=#1c1c1c gui=bold cterm=bold
  hi Type guifg=#fabd2f guibg=NONE gui=NONE cterm=NONE
  hi Typedef guifg=#fabd2f guibg=NONE gui=NONE cterm=NONE
  hi Underlined guifg=#83a598 guibg=NONE gui=underline cterm=underline
  hi CursorIM guifg=#1c1c1c guibg=#fbf1c7 gui=NONE cterm=NONE
else
  " Light background
  if (has('termguicolors') && &termguicolors) || has('gui_running')
    let g:terminal_ansi_colors = ['#3c3836', '#cc241d', '#98971a', '#d79921', '#458588', '#b16286', '#689d6a', '#7c6f64', '#928374', '#9d0006', '#79740e', '#b57614', '#076678', '#8f3f71', '#427b58', '#fbf1c7']
  endif
  hi Normal guifg=#3c3836 guibg=#fbf1c7 gui=NONE cterm=NONE
  hi CursorLineNr guifg=#b57614 guibg=#fbf1c7 gui=bold cterm=bold
  hi FoldColumn guifg=#928374 guibg=#fbf1c7 gui=NONE cterm=NONE
  hi SignColumn guifg=#3c3836 guibg=#fbf1c7 gui=NONE cterm=NONE
  hi VertSplit guifg=#bdae93 guibg=#fbf1c7 gui=NONE cterm=NONE
  hi ColorColumn guifg=NONE guibg=#ebe1b7 gui=NONE cterm=NONE
  hi Comment guifg=#928374 guibg=NONE gui=NONE cterm=NONE
  hi CursorLine guifg=NONE guibg=#e5d4b1 gui=NONE cterm=NONE
  hi Error guifg=#9d0006 guibg=#fbf1c7 gui=bold,reverse cterm=bold,reverse
  hi ErrorMsg guifg=#fbf1c7 guibg=#9d0006 gui=bold cterm=bold
  hi Folded guifg=#928374 guibg=#ffffd7 gui=NONE cterm=NONE
  hi LineNr guifg=#a89984 guibg=NONE gui=NONE cterm=NONE
  hi MatchParen guifg=NONE guibg=#e5d4b1 gui=bold,underline cterm=bold,underline
  hi NonText guifg=#e5d4b1 guibg=NONE gui=NONE cterm=NONE
  hi Pmenu guifg=#3c3836 guibg=#e5d4b1 gui=NONE cterm=NONE
  hi PmenuSbar guifg=NONE guibg=#e5d4b1 gui=NONE cterm=NONE
  hi PmenuSel guifg=#e5d4b1 guibg=#076678 gui=bold cterm=bold
  hi PmenuThumb guifg=NONE guibg=#a89984 gui=NONE cterm=NONE
  hi PmenuKind guifg=#9d0006 guibg=#e5d4b1 gui=NONE cterm=NONE
  hi PmenuKindSel guifg=#9d0006 guibg=#076678 gui=NONE cterm=NONE
  hi PmenuExtra guifg=#7c6f64 guibg=#e5d4b1 gui=NONE cterm=NONE
  hi PmenuExtraSel guifg=#bdae93 guibg=#076678 gui=NONE cterm=NONE
  hi PmenuMatch guifg=#8f3f71 guibg=#e5d4b1 gui=NONE cterm=NONE
  hi PmenuMatchSel guifg=#d3869b guibg=#076678 gui=bold cterm=bold
  hi SpecialKey guifg=#928374 guibg=NONE gui=NONE cterm=NONE
  hi StatusLine guifg=#bdae93 guibg=#3c3836 gui=bold,reverse cterm=bold,reverse
  hi StatusLineNC guifg=#ebdbb2 guibg=#3c3836 gui=reverse cterm=reverse
  hi TabLine guifg=#665c54 guibg=#ebdbb2 gui=NONE cterm=NONE
  hi TabLineFill guifg=#ebdbb2 guibg=#ebdbb2 gui=NONE cterm=NONE
  hi TabLineSel guifg=#282828 guibg=#fbf1c7 gui=bold cterm=bold
  hi ToolbarButton guifg=#282828 guibg=#bdae93 gui=bold cterm=bold
  hi ToolbarLine guifg=NONE guibg=NONE gui=NONE ctermfg=NONE ctermbg=NONE cterm=NONE
  hi Visual guifg=#fbf1c7 guibg=#076678 gui=NONE cterm=NONE
  hi WildMenu guifg=#076678 guibg=#e5d4b1 gui=bold cterm=bold
  hi EndOfBuffer guifg=#e5d4b1 guibg=NONE gui=NONE cterm=NONE
  hi Conceal guifg=#a89984 guibg=NONE gui=NONE cterm=NONE
  hi Cursor guifg=#fbf1c7 guibg=#282828 gui=NONE cterm=NONE
  hi DiffAdd guifg=#79740e guibg=#fbf1c7 gui=reverse cterm=reverse
  hi DiffChange guifg=#427b58 guibg=#fbf1c7 gui=reverse cterm=reverse
  hi DiffDelete guifg=#9d0006 guibg=#fbf1c7 gui=reverse cterm=reverse
  hi DiffText guifg=#b57614 guibg=#fbf1c7 gui=reverse cterm=reverse
  hi Directory guifg=#79740e guibg=NONE gui=bold cterm=bold
  hi IncSearch guifg=#ff5f00 guibg=#fbf1c7 gui=reverse cterm=reverse
  hi ModeMsg guifg=#3c3836 guibg=NONE gui=bold cterm=bold
  hi MoreMsg guifg=#3c3836 guibg=NONE gui=bold cterm=bold
  hi Question guifg=#ff5f00 guibg=NONE gui=bold cterm=bold
  hi Search guifg=#98971a guibg=#fbf1c7 gui=reverse cterm=reverse
  hi QuickFixLine guifg=#427b58 guibg=#fbf1c7 gui=reverse cterm=reverse
  hi SpellBad guifg=#9d0006 guibg=NONE guisp=#9d0006 gui=undercurl cterm=underline
  hi SpellCap guifg=#076678 guibg=NONE guisp=#076678 gui=undercurl cterm=underline
  hi SpellLocal guifg=#427b58 guibg=NONE guisp=#427b58 gui=undercurl cterm=underline
  hi SpellRare guifg=#8f3f71 guibg=NONE guisp=#8f3f71 gui=undercurl cterm=underline
  hi Title guifg=#79740e guibg=NONE gui=bold cterm=bold
  hi WarningMsg guifg=#9d0006 guibg=NONE gui=bold cterm=bold
  hi Boolean guifg=#8f3f71 guibg=NONE gui=NONE cterm=NONE
  hi Character guifg=#8f3f71 guibg=NONE gui=NONE cterm=NONE
  hi Conditional guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi Constant guifg=#8f3f71 guibg=NONE gui=NONE cterm=NONE
  hi Define guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi Debug guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi Delimiter guifg=#ff5f00 guibg=NONE gui=NONE cterm=NONE
  hi Error guifg=#9d0006 guibg=#fbf1c7 gui=bold,reverse cterm=bold,reverse
  hi Exception guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi Float guifg=#8f3f71 guibg=NONE gui=NONE cterm=NONE
  hi Function guifg=#79740e guibg=NONE gui=bold cterm=bold
  hi Identifier guifg=#076678 guibg=NONE gui=NONE cterm=NONE
  hi Ignore guifg=fg guibg=NONE gui=NONE cterm=NONE
  hi Include guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi Keyword guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi Label guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi Macro guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi Number guifg=#8f3f71 guibg=NONE gui=NONE cterm=NONE
  hi Operator guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi PreCondit guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi PreProc guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi Repeat guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi SpecialChar guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi SpecialComment guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi Statement guifg=#9d0006 guibg=NONE gui=NONE cterm=NONE
  hi StorageClass guifg=#ff5f00 guibg=NONE gui=NONE cterm=NONE
  hi Special guifg=#ff5f00 guibg=NONE gui=NONE cterm=NONE
  hi String guifg=#79740e guibg=NONE gui=NONE cterm=NONE
  hi Structure guifg=#427b58 guibg=NONE gui=NONE cterm=NONE
  hi Todo guifg=fg guibg=#fbf1c7 gui=bold cterm=bold
  hi Type guifg=#b57614 guibg=NONE gui=NONE cterm=NONE
  hi Typedef guifg=#b57614 guibg=NONE gui=NONE cterm=NONE
  hi Underlined guifg=#076678 guibg=NONE gui=underline cterm=underline
  hi CursorIM guifg=#fbf1c7 guibg=#282828 gui=NONE cterm=NONE
endif

if s:t_Co >= 256
  if &background ==# 'dark'
    hi Normal ctermfg=187 ctermbg=234 cterm=NONE
    hi CursorLineNr ctermfg=214 ctermbg=234 cterm=bold
    hi FoldColumn ctermfg=102 ctermbg=234 cterm=NONE
    hi SignColumn ctermfg=102 ctermbg=234 cterm=NONE
    hi VertSplit ctermfg=236 ctermbg=234 cterm=NONE
    hi ColorColumn ctermfg=NONE ctermbg=16 cterm=NONE
    hi Comment ctermfg=102 ctermbg=NONE cterm=NONE
    hi CursorLine ctermfg=NONE ctermbg=236 cterm=NONE
    hi Error ctermfg=203 ctermbg=234 cterm=bold,reverse
    hi ErrorMsg ctermfg=234 ctermbg=203 cterm=bold
    hi Folded ctermfg=102 ctermbg=233 cterm=NONE
    hi LineNr ctermfg=243 ctermbg=NONE cterm=NONE
    hi MatchParen ctermfg=NONE ctermbg=239 cterm=bold,underline
    hi NonText ctermfg=239 ctermbg=NONE cterm=NONE
    hi Pmenu ctermfg=187 ctermbg=237 cterm=NONE
    hi PmenuSbar ctermfg=NONE ctermbg=237 cterm=NONE
    hi PmenuSel ctermfg=237 ctermbg=109 cterm=bold
    hi PmenuThumb ctermfg=NONE ctermbg=243 cterm=NONE
    hi PmenuKind ctermfg=203 ctermbg=237 cterm=NONE
    hi PmenuKindSel ctermfg=203 ctermbg=109 cterm=NONE
    hi PmenuExtra ctermfg=102 ctermbg=237 cterm=NONE
    hi PmenuExtraSel ctermfg=236 ctermbg=109 cterm=NONE
    hi PmenuMatch ctermfg=132 ctermbg=237 cterm=NONE
    hi PmenuMatchSel ctermfg=132 ctermbg=109 cterm=bold
    hi SpecialKey ctermfg=102 ctermbg=NONE cterm=NONE
    hi StatusLine ctermfg=239 ctermbg=187 cterm=bold,reverse
    hi StatusLineNC ctermfg=237 ctermbg=102 cterm=reverse
    hi TabLine ctermfg=102 ctermbg=237 cterm=NONE
    hi TabLineFill ctermfg=187 ctermbg=237 cterm=NONE
    hi TabLineSel ctermfg=230 ctermbg=234 cterm=bold
    hi ToolbarButton ctermfg=230 ctermbg=236 cterm=bold
    hi ToolbarLine ctermfg=NONE ctermbg=NONE cterm=NONE
    hi Visual ctermfg=234 ctermbg=109 cterm=NONE
    hi WildMenu ctermfg=109 ctermbg=239 cterm=bold
    hi EndOfBuffer ctermfg=239 ctermbg=NONE cterm=NONE
    hi Conceal ctermfg=239 ctermbg=NONE cterm=NONE
    hi Cursor ctermfg=234 ctermbg=230 cterm=NONE
    hi DiffAdd ctermfg=142 ctermbg=234 cterm=reverse
    hi DiffChange ctermfg=107 ctermbg=234 cterm=reverse
    hi DiffDelete ctermfg=203 ctermbg=234 cterm=reverse
    hi DiffText ctermfg=214 ctermbg=234 cterm=reverse
    hi Directory ctermfg=142 ctermbg=NONE cterm=bold
    hi IncSearch ctermfg=208 ctermbg=234 cterm=reverse
    hi ModeMsg ctermfg=214 ctermbg=NONE cterm=bold
    hi MoreMsg ctermfg=214 ctermbg=NONE cterm=bold
    hi Question ctermfg=208 ctermbg=NONE cterm=bold
    hi Search ctermfg=100 ctermbg=234 cterm=reverse
    hi QuickFixLine ctermfg=107 ctermbg=234 cterm=reverse
    hi SpellBad ctermfg=203 ctermbg=NONE cterm=underline
    hi SpellCap ctermfg=109 ctermbg=NONE cterm=underline
    hi SpellLocal ctermfg=107 ctermbg=NONE cterm=underline
    hi SpellRare ctermfg=175 ctermbg=NONE cterm=underline
    hi Title ctermfg=142 ctermbg=NONE cterm=bold
    hi WarningMsg ctermfg=203 ctermbg=NONE cterm=bold
    hi Boolean ctermfg=175 ctermbg=NONE cterm=NONE
    hi Character ctermfg=175 ctermbg=NONE cterm=NONE
    hi Conditional ctermfg=203 ctermbg=NONE cterm=NONE
    hi Constant ctermfg=175 ctermbg=NONE cterm=NONE
    hi Define ctermfg=107 ctermbg=NONE cterm=NONE
    hi Debug ctermfg=203 ctermbg=NONE cterm=NONE
    hi Delimiter ctermfg=208 ctermbg=NONE cterm=NONE
    hi Error ctermfg=203 ctermbg=234 cterm=bold,reverse
    hi Exception ctermfg=203 ctermbg=NONE cterm=NONE
    hi Float ctermfg=175 ctermbg=NONE cterm=NONE
    hi Function ctermfg=142 ctermbg=NONE cterm=bold
    hi Identifier ctermfg=109 ctermbg=NONE cterm=NONE
    hi Ignore ctermfg=fg ctermbg=NONE cterm=NONE
    hi Include ctermfg=107 ctermbg=NONE cterm=NONE
    hi Keyword ctermfg=203 ctermbg=NONE cterm=NONE
    hi Label ctermfg=203 ctermbg=NONE cterm=NONE
    hi Macro ctermfg=107 ctermbg=NONE cterm=NONE
    hi Number ctermfg=175 ctermbg=NONE cterm=NONE
    hi Operator ctermfg=107 ctermbg=NONE cterm=NONE
    hi PreCondit ctermfg=107 ctermbg=NONE cterm=NONE
    hi PreProc ctermfg=107 ctermbg=NONE cterm=NONE
    hi Repeat ctermfg=203 ctermbg=NONE cterm=NONE
    hi SpecialChar ctermfg=203 ctermbg=NONE cterm=NONE
    hi SpecialComment ctermfg=203 ctermbg=NONE cterm=NONE
    hi Statement ctermfg=203 ctermbg=NONE cterm=NONE
    hi StorageClass ctermfg=208 ctermbg=NONE cterm=NONE
    hi Special ctermfg=208 ctermbg=NONE cterm=NONE
    hi String ctermfg=142 ctermbg=NONE cterm=NONE
    hi Structure ctermfg=107 ctermbg=NONE cterm=NONE
    hi Todo ctermfg=fg ctermbg=234 cterm=bold
    hi Type ctermfg=214 ctermbg=NONE cterm=NONE
    hi Typedef ctermfg=214 ctermbg=NONE cterm=NONE
    hi Underlined ctermfg=109 ctermbg=NONE cterm=underline
    hi CursorIM ctermfg=234 ctermbg=230 cterm=NONE
  else
    " Light background
    hi Normal ctermfg=237 ctermbg=230 cterm=NONE
    hi CursorLineNr ctermfg=172 ctermbg=230 cterm=bold
    hi FoldColumn ctermfg=102 ctermbg=230 cterm=NONE
    hi SignColumn ctermfg=237 ctermbg=230 cterm=NONE
    hi VertSplit ctermfg=144 ctermbg=230 cterm=NONE
    hi ColorColumn ctermfg=NONE ctermbg=229 cterm=NONE
    hi Comment ctermfg=102 ctermbg=NONE cterm=NONE
    hi CursorLine ctermfg=NONE ctermbg=188 cterm=NONE
    hi Error ctermfg=124 ctermbg=230 cterm=bold,reverse
    hi ErrorMsg ctermfg=230 ctermbg=124 cterm=bold
    hi Folded ctermfg=102 ctermbg=231 cterm=NONE
    hi LineNr ctermfg=137 ctermbg=NONE cterm=NONE
    hi MatchParen ctermfg=NONE ctermbg=188 cterm=bold,underline
    hi NonText ctermfg=188 ctermbg=NONE cterm=NONE
    hi Pmenu ctermfg=237 ctermbg=188 cterm=NONE
    hi PmenuSbar ctermfg=NONE ctermbg=188 cterm=NONE
    hi PmenuSel ctermfg=188 ctermbg=23 cterm=bold
    hi PmenuThumb ctermfg=NONE ctermbg=137 cterm=NONE
    hi PmenuKind ctermfg=124 ctermbg=188 cterm=NONE
    hi PmenuKindSel ctermfg=124 ctermbg=23 cterm=NONE
    hi PmenuExtra ctermfg=243 ctermbg=188 cterm=NONE
    hi PmenuExtraSel ctermfg=144 ctermbg=23 cterm=NONE
    hi PmenuMatch ctermfg=126 ctermbg=188 cterm=NONE
    hi PmenuMatchSel ctermfg=175 ctermbg=23 cterm=bold
    hi SpecialKey ctermfg=102 ctermbg=NONE cterm=NONE
    hi StatusLine ctermfg=144 ctermbg=237 cterm=bold,reverse
    hi StatusLineNC ctermfg=187 ctermbg=237 cterm=reverse
    hi TabLine ctermfg=59 ctermbg=187 cterm=NONE
    hi TabLineFill ctermfg=187 ctermbg=187 cterm=NONE
    hi TabLineSel ctermfg=235 ctermbg=230 cterm=bold
    hi ToolbarButton ctermfg=235 ctermbg=144 cterm=bold
    hi ToolbarLine ctermfg=NONE ctermbg=NONE cterm=NONE
    hi Visual ctermfg=230 ctermbg=23 cterm=NONE
    hi WildMenu ctermfg=23 ctermbg=188 cterm=bold
    hi EndOfBuffer ctermfg=188 ctermbg=NONE cterm=NONE
    hi Conceal ctermfg=137 ctermbg=NONE cterm=NONE
    hi Cursor ctermfg=230 ctermbg=235 cterm=NONE
    hi DiffAdd ctermfg=64 ctermbg=230 cterm=reverse
    hi DiffChange ctermfg=29 ctermbg=230 cterm=reverse
    hi DiffDelete ctermfg=124 ctermbg=230 cterm=reverse
    hi DiffText ctermfg=172 ctermbg=230 cterm=reverse
    hi Directory ctermfg=64 ctermbg=NONE cterm=bold
    hi IncSearch ctermfg=202 ctermbg=230 cterm=reverse
    hi ModeMsg ctermfg=237 ctermbg=NONE cterm=bold
    hi MoreMsg ctermfg=237 ctermbg=NONE cterm=bold
    hi Question ctermfg=202 ctermbg=NONE cterm=bold
    hi Search ctermfg=100 ctermbg=230 cterm=reverse
    hi QuickFixLine ctermfg=29 ctermbg=230 cterm=reverse
    hi SpellBad ctermfg=124 ctermbg=NONE cterm=underline
    hi SpellCap ctermfg=23 ctermbg=NONE cterm=underline
    hi SpellLocal ctermfg=29 ctermbg=NONE cterm=underline
    hi SpellRare ctermfg=126 ctermbg=NONE cterm=underline
    hi Title ctermfg=64 ctermbg=NONE cterm=bold
    hi WarningMsg ctermfg=124 ctermbg=NONE cterm=bold
    hi Boolean ctermfg=126 ctermbg=NONE cterm=NONE
    hi Character ctermfg=126 ctermbg=NONE cterm=NONE
    hi Conditional ctermfg=124 ctermbg=NONE cterm=NONE
    hi Constant ctermfg=126 ctermbg=NONE cterm=NONE
    hi Define ctermfg=29 ctermbg=NONE cterm=NONE
    hi Debug ctermfg=124 ctermbg=NONE cterm=NONE
    hi Delimiter ctermfg=202 ctermbg=NONE cterm=NONE
    hi Error ctermfg=124 ctermbg=230 cterm=bold,reverse
    hi Exception ctermfg=124 ctermbg=NONE cterm=NONE
    hi Float ctermfg=126 ctermbg=NONE cterm=NONE
    hi Function ctermfg=64 ctermbg=NONE cterm=bold
    hi Identifier ctermfg=23 ctermbg=NONE cterm=NONE
    hi Ignore ctermfg=fg ctermbg=NONE cterm=NONE
    hi Include ctermfg=29 ctermbg=NONE cterm=NONE
    hi Keyword ctermfg=124 ctermbg=NONE cterm=NONE
    hi Label ctermfg=124 ctermbg=NONE cterm=NONE
    hi Macro ctermfg=29 ctermbg=NONE cterm=NONE
    hi Number ctermfg=126 ctermbg=NONE cterm=NONE
    hi Operator ctermfg=29 ctermbg=NONE cterm=NONE
    hi PreCondit ctermfg=29 ctermbg=NONE cterm=NONE
    hi PreProc ctermfg=29 ctermbg=NONE cterm=NONE
    hi Repeat ctermfg=124 ctermbg=NONE cterm=NONE
    hi SpecialChar ctermfg=124 ctermbg=NONE cterm=NONE
    hi SpecialComment ctermfg=124 ctermbg=NONE cterm=NONE
    hi Statement ctermfg=124 ctermbg=NONE cterm=NONE
    hi StorageClass ctermfg=202 ctermbg=NONE cterm=NONE
    hi Special ctermfg=202 ctermbg=NONE cterm=NONE
    hi String ctermfg=64 ctermbg=NONE cterm=NONE
    hi Structure ctermfg=29 ctermbg=NONE cterm=NONE
    hi Todo ctermfg=fg ctermbg=230 cterm=bold
    hi Type ctermfg=172 ctermbg=NONE cterm=NONE
    hi Typedef ctermfg=172 ctermbg=NONE cterm=NONE
    hi Underlined ctermfg=23 ctermbg=NONE cterm=underline
    hi CursorIM ctermfg=230 ctermbg=235 cterm=NONE
  endif
  unlet s:t_Co
  finish
endif

if s:t_Co >= 16
  if &background ==# 'dark'
    hi Normal ctermfg=White ctermbg=Black cterm=NONE
    hi ColorColumn ctermfg=Black ctermbg=DarkYellow cterm=NONE
    hi Comment ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi CursorLine ctermfg=NONE ctermbg=NONE cterm=underline
    hi CursorLineNr ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=Black cterm=reverse
    hi ErrorMsg ctermfg=Black ctermbg=Red cterm=NONE
    hi FoldColumn ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi Folded ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi LineNr ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi MatchParen ctermfg=NONE ctermbg=Black cterm=bold,underline
    hi NonText ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi Pmenu ctermfg=White ctermbg=DarkGray cterm=NONE
    hi PmenuSbar ctermfg=NONE ctermbg=DarkGray cterm=NONE
    hi PmenuSel ctermfg=Black ctermbg=Blue cterm=NONE
    hi PmenuThumb ctermfg=NONE ctermbg=Blue cterm=NONE
    hi PmenuKind ctermfg=DarkRed ctermbg=DarkGray cterm=NONE
    hi PmenuKindSel ctermfg=DarkRed ctermbg=Blue cterm=NONE
    hi PmenuExtra ctermfg=gray ctermbg=DarkGray cterm=NONE
    hi PmenuExtraSel ctermfg=Black ctermbg=Blue cterm=NONE
    hi PmenuMatch ctermfg=White ctermbg=DarkGray cterm=bold
    hi PmenuMatchSel ctermfg=Black ctermbg=Blue cterm=bold
    hi SignColumn ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi SpecialKey ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi StatusLine ctermfg=gray ctermbg=Black cterm=bold,reverse
    hi StatusLineNC ctermfg=gray ctermbg=Black cterm=reverse
    hi TabLine ctermfg=Black ctermbg=DarkGray cterm=NONE
    hi TabLineFill ctermfg=Black ctermbg=DarkGray cterm=NONE
    hi TabLineSel ctermfg=White ctermbg=Black cterm=bold
    hi ToolbarButton ctermfg=White ctermbg=DarkGray cterm=bold
    hi ToolbarLine ctermfg=NONE ctermbg=NONE cterm=NONE
    hi VertSplit ctermfg=Black ctermbg=gray cterm=NONE
    hi Visual ctermfg=Black ctermbg=Blue cterm=NONE
    hi WildMenu ctermfg=White ctermbg=Black cterm=bold
    hi EndOfBuffer ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi Conceal ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi Cursor ctermfg=Black ctermbg=White cterm=NONE
    hi DiffAdd ctermfg=Green ctermbg=Black cterm=reverse
    hi DiffChange ctermfg=Cyan ctermbg=Black cterm=reverse
    hi DiffDelete ctermfg=Red ctermbg=Black cterm=reverse
    hi DiffText ctermfg=Yellow ctermbg=Black cterm=reverse
    hi Directory ctermfg=Green ctermbg=NONE cterm=bold
    hi IncSearch ctermfg=Magenta ctermbg=Black cterm=reverse
    hi ModeMsg ctermfg=Yellow ctermbg=NONE cterm=bold
    hi MoreMsg ctermfg=Yellow ctermbg=NONE cterm=bold
    hi Question ctermfg=Magenta ctermbg=NONE cterm=bold
    hi Search ctermfg=DarkGreen ctermbg=Black cterm=reverse
    hi QuickFixLine ctermfg=Cyan ctermbg=Black cterm=reverse
    hi SpellBad ctermfg=Red ctermbg=NONE cterm=underline
    hi SpellCap ctermfg=Blue ctermbg=NONE cterm=underline
    hi SpellLocal ctermfg=Cyan ctermbg=NONE cterm=underline
    hi SpellRare ctermfg=Magenta ctermbg=NONE cterm=underline
    hi Title ctermfg=Green ctermbg=NONE cterm=bold
    hi WarningMsg ctermfg=Red ctermbg=NONE cterm=bold
    hi Boolean ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Character ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Conditional ctermfg=Red ctermbg=NONE cterm=NONE
    hi Constant ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Define ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Debug ctermfg=Red ctermbg=NONE cterm=NONE
    hi Delimiter ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=Black cterm=bold,reverse
    hi Exception ctermfg=Red ctermbg=NONE cterm=NONE
    hi Float ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Function ctermfg=Green ctermbg=NONE cterm=bold
    hi Identifier ctermfg=Blue ctermbg=NONE cterm=NONE
    hi Ignore ctermfg=fg ctermbg=NONE cterm=NONE
    hi Include ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Keyword ctermfg=Red ctermbg=NONE cterm=NONE
    hi Label ctermfg=Red ctermbg=NONE cterm=NONE
    hi Macro ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Number ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Operator ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreCondit ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreProc ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Repeat ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialChar ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialComment ctermfg=Red ctermbg=NONE cterm=NONE
    hi Statement ctermfg=Red ctermbg=NONE cterm=NONE
    hi StorageClass ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Special ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi String ctermfg=Green ctermbg=NONE cterm=NONE
    hi Structure ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Todo ctermfg=fg ctermbg=Black cterm=bold
    hi Type ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Typedef ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Underlined ctermfg=Blue ctermbg=NONE cterm=underline
    hi CursorIM ctermfg=Black ctermbg=White cterm=NONE
  else
    " Light background
    hi Normal ctermfg=Black ctermbg=White cterm=NONE
    hi ColorColumn ctermfg=White ctermbg=Grey cterm=NONE
    hi Comment ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi CursorLine ctermfg=NONE ctermbg=NONE cterm=underline
    hi CursorLineNr ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=White cterm=reverse
    hi ErrorMsg ctermfg=White ctermbg=Red cterm=NONE
    hi FoldColumn ctermfg=Grey ctermbg=NONE cterm=NONE
    hi Folded ctermfg=Grey ctermbg=NONE cterm=NONE
    hi LineNr ctermfg=Grey ctermbg=NONE cterm=NONE
    hi MatchParen ctermfg=NONE ctermbg=White cterm=bold,underline
    hi NonText ctermfg=Grey ctermbg=NONE cterm=NONE
    hi Pmenu ctermfg=Black ctermbg=Grey cterm=NONE
    hi PmenuSbar ctermfg=NONE ctermbg=Grey cterm=NONE
    hi PmenuSel ctermfg=White ctermbg=Blue cterm=NONE
    hi PmenuThumb ctermfg=NONE ctermbg=Blue cterm=NONE
    hi PmenuKind ctermfg=DarkRed ctermbg=Grey cterm=NONE
    hi PmenuKindSel ctermfg=DarkRed ctermbg=Blue cterm=NONE
    hi PmenuExtra ctermfg=DarkGray ctermbg=Grey cterm=NONE
    hi PmenuExtraSel ctermfg=White ctermbg=Blue cterm=NONE
    hi PmenuMatch ctermfg=Black ctermbg=Grey cterm=bold
    hi PmenuMatchSel ctermfg=White ctermbg=Blue cterm=bold
    hi SignColumn ctermfg=Grey ctermbg=NONE cterm=NONE
    hi SpecialKey ctermfg=Grey ctermbg=NONE cterm=NONE
    hi StatusLine ctermfg=DarkGray ctermbg=White cterm=bold,reverse
    hi StatusLineNC ctermfg=Grey ctermbg=DarkGray cterm=reverse
    hi TabLine ctermfg=DarkGray ctermbg=Grey cterm=NONE
    hi TabLineFill ctermfg=White ctermbg=Grey cterm=NONE
    hi TabLineSel ctermfg=DarkGray ctermbg=White cterm=bold
    hi ToolbarButton ctermfg=Black ctermbg=Grey cterm=bold
    hi ToolbarLine ctermfg=NONE ctermbg=NONE cterm=NONE
    hi VertSplit ctermfg=DarkGray ctermbg=Grey cterm=NONE
    hi Visual ctermfg=White ctermbg=Blue cterm=NONE
    hi WildMenu ctermfg=Black ctermbg=White cterm=bold
    hi EndOfBuffer ctermfg=Grey ctermbg=NONE cterm=NONE
    hi Conceal ctermfg=Grey ctermbg=NONE cterm=NONE
    hi Cursor ctermfg=White ctermbg=DarkGray cterm=NONE
    hi DiffAdd ctermfg=Green ctermbg=White cterm=reverse
    hi DiffChange ctermfg=Cyan ctermbg=White cterm=reverse
    hi DiffDelete ctermfg=Red ctermbg=White cterm=reverse
    hi DiffText ctermfg=Yellow ctermbg=White cterm=reverse
    hi Directory ctermfg=Green ctermbg=NONE cterm=bold
    hi IncSearch ctermfg=Magenta ctermbg=White cterm=reverse
    hi ModeMsg ctermfg=Black ctermbg=NONE cterm=bold
    hi MoreMsg ctermfg=Black ctermbg=NONE cterm=bold
    hi Question ctermfg=Magenta ctermbg=NONE cterm=bold
    hi Search ctermfg=DarkGreen ctermbg=White cterm=reverse
    hi QuickFixLine ctermfg=Cyan ctermbg=White cterm=reverse
    hi SpellBad ctermfg=Red ctermbg=NONE cterm=underline
    hi SpellCap ctermfg=Blue ctermbg=NONE cterm=underline
    hi SpellLocal ctermfg=Cyan ctermbg=NONE cterm=underline
    hi SpellRare ctermfg=Magenta ctermbg=NONE cterm=underline
    hi Title ctermfg=Green ctermbg=NONE cterm=bold
    hi WarningMsg ctermfg=Red ctermbg=NONE cterm=bold
    hi Boolean ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Character ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Conditional ctermfg=Red ctermbg=NONE cterm=NONE
    hi Constant ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Define ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Debug ctermfg=Red ctermbg=NONE cterm=NONE
    hi Delimiter ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=White cterm=bold,reverse
    hi Exception ctermfg=Red ctermbg=NONE cterm=NONE
    hi Float ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Function ctermfg=Green ctermbg=NONE cterm=bold
    hi Identifier ctermfg=Blue ctermbg=NONE cterm=NONE
    hi Ignore ctermfg=fg ctermbg=NONE cterm=NONE
    hi Include ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Keyword ctermfg=Red ctermbg=NONE cterm=NONE
    hi Label ctermfg=Red ctermbg=NONE cterm=NONE
    hi Macro ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Number ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Operator ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreCondit ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreProc ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Repeat ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialChar ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialComment ctermfg=Red ctermbg=NONE cterm=NONE
    hi Statement ctermfg=Red ctermbg=NONE cterm=NONE
    hi StorageClass ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Special ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi String ctermfg=Green ctermbg=NONE cterm=NONE
    hi Structure ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Todo ctermfg=fg ctermbg=White cterm=bold
    hi Type ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Typedef ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Underlined ctermfg=Blue ctermbg=NONE cterm=underline
    hi CursorIM ctermfg=White ctermbg=DarkGray cterm=NONE
  endif
  unlet s:t_Co
  finish
endif

if s:t_Co >= 8
  if &background ==# 'dark'
    hi Normal ctermfg=gray ctermbg=Black cterm=NONE
    hi ColorColumn ctermfg=Black ctermbg=gray cterm=NONE
    hi Comment ctermfg=gray ctermbg=NONE cterm=bold
    hi CursorLine ctermfg=NONE ctermbg=NONE cterm=underline
    hi CursorLineNr ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=Black cterm=reverse
    hi ErrorMsg ctermfg=Black ctermbg=Red cterm=NONE
    hi FoldColumn ctermfg=gray ctermbg=NONE cterm=NONE
    hi Folded ctermfg=gray ctermbg=NONE cterm=NONE
    hi LineNr ctermfg=gray ctermbg=NONE cterm=NONE
    hi MatchParen ctermfg=gray ctermbg=NONE cterm=bold,underline
    hi NonText ctermfg=gray ctermbg=NONE cterm=NONE
    hi Pmenu ctermfg=DarkGray ctermbg=White cterm=NONE
    hi PmenuSbar ctermfg=NONE ctermbg=DarkGray cterm=NONE
    hi PmenuSel ctermfg=Black ctermbg=Blue cterm=NONE
    hi PmenuThumb ctermfg=NONE ctermbg=Blue cterm=NONE
    hi PmenuKind ctermfg=Red ctermbg=White cterm=NONE
    hi PmenuKindSel ctermfg=Red ctermbg=Blue cterm=NONE
    hi PmenuExtra ctermfg=DarkGray ctermbg=White cterm=NONE
    hi PmenuExtraSel ctermfg=DarkGray ctermbg=Blue cterm=NONE
    hi PmenuMatch ctermfg=DarkGray ctermbg=White cterm=bold
    hi PmenuMatchSel ctermfg=Black ctermbg=Blue cterm=bold
    hi SignColumn ctermfg=gray ctermbg=NONE cterm=NONE
    hi SpecialKey ctermfg=gray ctermbg=NONE cterm=NONE
    hi StatusLine ctermfg=gray ctermbg=Black cterm=bold,reverse
    hi StatusLineNC ctermfg=gray ctermbg=Black cterm=reverse
    hi TabLine ctermfg=Black ctermbg=gray cterm=NONE
    hi TabLineFill ctermfg=Black ctermbg=gray cterm=NONE
    hi TabLineSel ctermfg=gray ctermbg=Black cterm=NONE
    hi ToolbarButton ctermfg=Black ctermbg=gray cterm=bold
    hi ToolbarLine ctermfg=NONE ctermbg=NONE cterm=NONE
    hi VertSplit ctermfg=Black ctermbg=gray cterm=NONE
    hi Visual ctermfg=Black ctermbg=Blue cterm=NONE
    hi WildMenu ctermfg=Blue ctermbg=DarkGray cterm=bold
    hi EndOfBuffer ctermfg=NONE ctermbg=NONE cterm=NONE
    hi Conceal ctermfg=DarkGray ctermbg=NONE cterm=NONE
    hi Cursor ctermfg=Black ctermbg=White cterm=NONE
    hi DiffAdd ctermfg=Green ctermbg=Black cterm=reverse
    hi DiffChange ctermfg=Cyan ctermbg=Black cterm=reverse
    hi DiffDelete ctermfg=Red ctermbg=Black cterm=reverse
    hi DiffText ctermfg=Yellow ctermbg=Black cterm=reverse
    hi Directory ctermfg=Green ctermbg=NONE cterm=bold
    hi IncSearch ctermfg=Magenta ctermbg=Black cterm=reverse
    hi ModeMsg ctermfg=Yellow ctermbg=NONE cterm=bold
    hi MoreMsg ctermfg=Yellow ctermbg=NONE cterm=bold
    hi Question ctermfg=Magenta ctermbg=NONE cterm=bold
    hi Search ctermfg=DarkGreen ctermbg=Black cterm=reverse
    hi QuickFixLine ctermfg=Cyan ctermbg=Black cterm=reverse
    hi SpellBad ctermfg=Red ctermbg=NONE cterm=underline
    hi SpellCap ctermfg=Blue ctermbg=NONE cterm=underline
    hi SpellLocal ctermfg=Cyan ctermbg=NONE cterm=underline
    hi SpellRare ctermfg=Magenta ctermbg=NONE cterm=underline
    hi Title ctermfg=Green ctermbg=NONE cterm=bold
    hi WarningMsg ctermfg=Red ctermbg=NONE cterm=bold
    hi Boolean ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Character ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Conditional ctermfg=Red ctermbg=NONE cterm=NONE
    hi Constant ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Define ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Debug ctermfg=Red ctermbg=NONE cterm=NONE
    hi Delimiter ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=Black cterm=bold,reverse
    hi Exception ctermfg=Red ctermbg=NONE cterm=NONE
    hi Float ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Function ctermfg=Green ctermbg=NONE cterm=bold
    hi Identifier ctermfg=Blue ctermbg=NONE cterm=NONE
    hi Ignore ctermfg=fg ctermbg=NONE cterm=NONE
    hi Include ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Keyword ctermfg=Red ctermbg=NONE cterm=NONE
    hi Label ctermfg=Red ctermbg=NONE cterm=NONE
    hi Macro ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Number ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Operator ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreCondit ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreProc ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Repeat ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialChar ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialComment ctermfg=Red ctermbg=NONE cterm=NONE
    hi Statement ctermfg=Red ctermbg=NONE cterm=NONE
    hi StorageClass ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Special ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi String ctermfg=Green ctermbg=NONE cterm=NONE
    hi Structure ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Todo ctermfg=fg ctermbg=Black cterm=bold
    hi Type ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Typedef ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Underlined ctermfg=Blue ctermbg=NONE cterm=underline
    hi CursorIM ctermfg=Black ctermbg=White cterm=NONE
  else
    " Light background
    hi Normal ctermfg=Black ctermbg=Grey cterm=NONE
    hi ColorColumn ctermfg=White ctermbg=Black cterm=NONE
    hi Comment ctermfg=Black ctermbg=NONE cterm=bold
    hi CursorLine ctermfg=NONE ctermbg=NONE cterm=underline
    hi CursorLineNr ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=White cterm=reverse
    hi ErrorMsg ctermfg=White ctermbg=Red cterm=NONE
    hi FoldColumn ctermfg=Black ctermbg=NONE cterm=NONE
    hi Folded ctermfg=Black ctermbg=NONE cterm=NONE
    hi LineNr ctermfg=Black ctermbg=NONE cterm=NONE
    hi MatchParen ctermfg=Black ctermbg=NONE cterm=bold,underline
    hi NonText ctermfg=Black ctermbg=NONE cterm=NONE
    hi Pmenu ctermfg=Grey ctermbg=Black cterm=NONE
    hi PmenuSbar ctermfg=NONE ctermbg=Grey cterm=NONE
    hi PmenuSel ctermfg=White ctermbg=Blue cterm=NONE
    hi PmenuThumb ctermfg=NONE ctermbg=Blue cterm=NONE
    hi PmenuKind ctermfg=Red ctermbg=Black cterm=NONE
    hi PmenuKindSel ctermfg=Red ctermbg=Blue cterm=NONE
    hi PmenuExtra ctermfg=Grey ctermbg=Black cterm=NONE
    hi PmenuExtraSel ctermfg=Grey ctermbg=Blue cterm=NONE
    hi PmenuMatch ctermfg=Grey ctermbg=Black cterm=bold
    hi PmenuMatchSel ctermfg=White ctermbg=Blue cterm=bold
    hi SignColumn ctermfg=Black ctermbg=NONE cterm=NONE
    hi SpecialKey ctermfg=Black ctermbg=NONE cterm=NONE
    hi StatusLine ctermfg=Black ctermbg=White cterm=bold,reverse
    hi StatusLineNC ctermfg=Black ctermbg=White cterm=reverse
    hi TabLine ctermfg=White ctermbg=Black cterm=NONE
    hi TabLineFill ctermfg=White ctermbg=Black cterm=NONE
    hi TabLineSel ctermfg=Black ctermbg=White cterm=NONE
    hi ToolbarButton ctermfg=White ctermbg=Black cterm=bold
    hi ToolbarLine ctermfg=NONE ctermbg=NONE cterm=NONE
    hi VertSplit ctermfg=White ctermbg=Black cterm=NONE
    hi Visual ctermfg=White ctermbg=Blue cterm=NONE
    hi WildMenu ctermfg=Blue ctermbg=Grey cterm=bold
    hi EndOfBuffer ctermfg=NONE ctermbg=NONE cterm=NONE
    hi Conceal ctermfg=Grey ctermbg=NONE cterm=NONE
    hi Cursor ctermfg=White ctermbg=DarkGray cterm=NONE
    hi DiffAdd ctermfg=Green ctermbg=White cterm=reverse
    hi DiffChange ctermfg=Cyan ctermbg=White cterm=reverse
    hi DiffDelete ctermfg=Red ctermbg=White cterm=reverse
    hi DiffText ctermfg=Yellow ctermbg=White cterm=reverse
    hi Directory ctermfg=Green ctermbg=NONE cterm=bold
    hi IncSearch ctermfg=Magenta ctermbg=White cterm=reverse
    hi ModeMsg ctermfg=Black ctermbg=NONE cterm=bold
    hi MoreMsg ctermfg=Black ctermbg=NONE cterm=bold
    hi Question ctermfg=Magenta ctermbg=NONE cterm=bold
    hi Search ctermfg=DarkGreen ctermbg=White cterm=reverse
    hi QuickFixLine ctermfg=Cyan ctermbg=White cterm=reverse
    hi SpellBad ctermfg=Red ctermbg=NONE cterm=underline
    hi SpellCap ctermfg=Blue ctermbg=NONE cterm=underline
    hi SpellLocal ctermfg=Cyan ctermbg=NONE cterm=underline
    hi SpellRare ctermfg=Magenta ctermbg=NONE cterm=underline
    hi Title ctermfg=Green ctermbg=NONE cterm=bold
    hi WarningMsg ctermfg=Red ctermbg=NONE cterm=bold
    hi Boolean ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Character ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Conditional ctermfg=Red ctermbg=NONE cterm=NONE
    hi Constant ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Define ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Debug ctermfg=Red ctermbg=NONE cterm=NONE
    hi Delimiter ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Error ctermfg=Red ctermbg=White cterm=bold,reverse
    hi Exception ctermfg=Red ctermbg=NONE cterm=NONE
    hi Float ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Function ctermfg=Green ctermbg=NONE cterm=bold
    hi Identifier ctermfg=Blue ctermbg=NONE cterm=NONE
    hi Ignore ctermfg=fg ctermbg=NONE cterm=NONE
    hi Include ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Keyword ctermfg=Red ctermbg=NONE cterm=NONE
    hi Label ctermfg=Red ctermbg=NONE cterm=NONE
    hi Macro ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Number ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Operator ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreCondit ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi PreProc ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Repeat ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialChar ctermfg=Red ctermbg=NONE cterm=NONE
    hi SpecialComment ctermfg=Red ctermbg=NONE cterm=NONE
    hi Statement ctermfg=Red ctermbg=NONE cterm=NONE
    hi StorageClass ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi Special ctermfg=Magenta ctermbg=NONE cterm=NONE
    hi String ctermfg=Green ctermbg=NONE cterm=NONE
    hi Structure ctermfg=Cyan ctermbg=NONE cterm=NONE
    hi Todo ctermfg=fg ctermbg=White cterm=bold
    hi Type ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Typedef ctermfg=Yellow ctermbg=NONE cterm=NONE
    hi Underlined ctermfg=Blue ctermbg=NONE cterm=underline
    hi CursorIM ctermfg=White ctermbg=DarkGray cterm=NONE
  endif
  unlet s:t_Co
  finish
endif

if s:t_Co >= 0
  hi Normal term=NONE
  hi ColorColumn term=reverse
  hi Conceal term=NONE
  hi Cursor term=reverse
  hi CursorColumn term=NONE
  hi CursorLine term=underline
  hi CursorLineNr term=bold
  hi DiffAdd term=reverse
  hi DiffChange term=NONE
  hi DiffDelete term=reverse
  hi DiffText term=reverse
  hi Directory term=NONE
  hi EndOfBuffer term=NONE
  hi ErrorMsg term=bold,reverse
  hi FoldColumn term=NONE
  hi Folded term=NONE
  hi IncSearch term=bold,reverse,underline
  hi LineNr term=NONE
  hi MatchParen term=bold,underline
  hi ModeMsg term=bold
  hi MoreMsg term=NONE
  hi NonText term=NONE
  hi Pmenu term=reverse
  hi PmenuSbar term=reverse
  hi PmenuSel term=bold
  hi PmenuThumb term=NONE
  hi Question term=standout
  hi Search term=reverse
  hi SignColumn term=reverse
  hi SpecialKey term=bold
  hi SpellBad term=underline
  hi SpellCap term=underline
  hi SpellLocal term=underline
  hi SpellRare term=underline
  hi StatusLine term=bold,reverse
  hi StatusLineNC term=bold,underline
  hi TabLine term=bold,underline
  hi TabLineFill term=NONE
  hi Terminal term=NONE
  hi TabLineSel term=bold,reverse
  hi Title term=NONE
  hi VertSplit term=NONE
  hi Visual term=reverse
  hi VisualNOS term=NONE
  hi WarningMsg term=standout
  hi WildMenu term=bold
  hi CursorIM term=NONE
  hi ToolbarLine term=reverse
  hi ToolbarButton term=bold,reverse
  hi CurSearch term=reverse
  hi CursorLineFold term=underline
  hi CursorLineSign term=underline
  hi Comment term=bold
  hi Constant term=NONE
  hi Error term=bold,reverse
  hi Identifier term=NONE
  hi Ignore term=NONE
  hi PreProc term=NONE
  hi Special term=NONE
  hi Statement term=NONE
  hi Todo term=bold,reverse
  hi Type term=NONE
  hi Underlined term=underline
  unlet s:t_Co
  finish
endif

" Color: neutralred              #cc241d        160            DarkRed
" Color: neutralgreen            #98971a        100            DarkGreen
" Color: neutralyellow           #d79921        172            DarkYellow
" Color: neutralblue             #458588        66             DarkBlue
" Color: neutralpurple           #b16286        132            DarkMagenta
" Color: neutralaqua             #689d6a        71             DarkCyan
" Color: neutralorange           #d65d0e        166            LightRed
" Background: dark
" Color: bg0                     #1c1c1c        234            Black
" Color: bg1                     #3c3836        237            DarkGray
" Color: bg2                     #504945        239            DarkGray
" Color: bg3                     #303030        236
" Color: bg4                     #7c6f64        243
" Color: bg5                     #000000        16             DarkGray
" Color: bg6                     #121212        233            DarkGray
" Color: fg0                     #fbf1c7        230            White
" Color: fg1                     #ebdbb2        187            White
" Color: fg2                     #d5c4a1        187
" Color: fg3                     #bdae93        144
" Color: fg4                     #a89984        102            gray
" Color: grey                    #928374        102            DarkGray
" Color: red                     #fb4934        203            Red
" Color: green                   #b8bb26        142            Green
" Color: yellow                  #fabd2f        214            Yellow
" Color: blue                    #83a598        109            Blue
" Color: purple                  #d3869b        175            Magenta
" Color: aqua                    #8ec07c        107            Cyan
" Color: orange                  #fe8019        208            Magenta
" Term colors: bg0  neutralred neutralgreen neutralyellow neutralblue neutralpurple neutralaqua fg4
" Term colors: grey red        green        yellow        blue        purple        aqua        fg1
" Background: light
" Color: bg0                     #fbf1c7        230            White
" Color: bg1                     #ebdbb2        187            Grey
" Color: bg2                     #e5d4b1        188            Grey
" Color: bg3                     #bdae93        144
" Color: bg4                     #a89984        137            Grey
" Color: bg5                     #ebe1b7        229            Grey
" Color: bg6                     #ffffd7        231            Grey
" Color: fg0                     #282828        235            DarkGray
" Color: fg1                     #3c3836        237            Black
" Color: fg2                     #503836        237
" Color: fg3                     #665c54        59
" Color: fg4                     #7c6f64        243            Black
" Color: grey                    #928374        102            DarkGray
" Color: red                     #9d0006        124            Red
" Color: green                   #79740e        64             Green
" Color: yellow                  #b57614        172            Yellow
" Color: blue                    #076678        23             Blue
" Color: purple                  #8f3f71        126            Magenta
" Color: lightpurple             #d3869b        175            Magenta
" Color: aqua                    #427b58        29             Cyan
" Color: orange                  #ff5f00        202            Magenta
" Term colors: fg1  neutralred neutralgreen neutralyellow neutralblue neutralpurple neutralaqua fg4
" Term colors: grey red        green        yellow        blue        purple        aqua        bg0
" Background: any
" vim: et ts=8 sw=2 sts=2
