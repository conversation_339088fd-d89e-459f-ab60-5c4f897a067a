%!PS-Adobe-3.0 Resource-ProcSet
%%Title: VIM-Prolog
%%Version: 1.4 1
%%EndComments
% Editing of this file is NOT RECOMMENDED.  You run a very good risk of causing
% all PostScript printing from VIM failing if you do.  PostScript is not called
% a write-only language for nothing!
/packedarray where not{userdict begin/setpacking/pop load def/currentpacking
false def end}{pop}ifelse/CP currentpacking def true setpacking
/bd{bind def}bind def/ld{load def}bd/ed{exch def}bd/d/def ld
/db{dict begin}bd/cde{currentdict end}bd
/T true d/F false d
/SO null d/sv{/SO save d}bd/re{SO restore}bd
/L2 systemdict/languagelevel 2 copy known{get exec}{pop pop 1}ifelse 2 ge d
/m/moveto ld/s/show ld /ms{m s}bd /g/setgray ld/r/setrgbcolor ld/sp{showpage}bd
/gs/gsave ld/gr/grestore ld/cp/currentpoint ld
/ul{gs UW setlinewidth cp UO add 2 copy newpath m 3 1 roll add exch lineto
stroke gr}bd
/bg{gs r cp BO add 4 -2 roll rectfill gr}bd
/sl{90 rotate 0 exch translate}bd
L2{
/sspd{mark exch{setpagedevice}stopped cleartomark}bd
/nc{1 db/NumCopies ed cde sspd}bd
/sps{3 db/Orientation ed[3 1 roll]/PageSize ed/ImagingBBox null d cde sspd}bd
/dt{2 db/Tumble ed/Duplex ed cde sspd}bd
/c{1 db/Collate ed cde sspd}bd
}{
/nc{/#copies ed}bd
/sps{statusdict/setpage get exec}bd
/dt{statusdict/settumble 2 copy known{get exec}{pop pop pop}ifelse
statusdict/setduplexmode 2 copy known{get exec}{pop pop pop}ifelse}bd
/c{pop}bd
}ifelse
/ffs{findfont exch scalefont d}bd/sf{setfont}bd
/ref{1 db findfont dup maxlength dict/NFD ed{exch dup/FID ne{exch NFD 3 1 roll
put}{pop pop}ifelse}forall/Encoding findresource dup length 256 eq{NFD/Encoding
3 -1 roll put}{pop}ifelse NFD dup/FontType get 3 ne{/CharStrings}{/CharProcs}
ifelse 2 copy known{2 copy get dup maxlength dict copy[/questiondown/space]{2
copy known{2 copy get 2 index/.notdef 3 -1 roll put pop exit}if pop}forall put
}{pop pop}ifelse dup NFD/FontName 3 -1 roll put NFD definefont pop end}bd
CP setpacking
(\004)cvn{}bd
% vim:ff=unix:
%%EOF
