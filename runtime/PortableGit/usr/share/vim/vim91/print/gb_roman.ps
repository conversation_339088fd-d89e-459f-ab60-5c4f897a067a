%!PS-Adobe-3.0 Resource-Encoding
%%Title: VIM-gb_roman
%%Version: 1.0 0
%%EndComments
% Different to ASCII at code points 36 and 126
/VIM-gb_roman[
32{/.notdef}repeat
/space /exclam /quotedbl /numbersign /yuan /percent /ampersand /quotesingle
/parenleft /parenright /asterisk /plus /comma /minus /period /slash
/zero /one /two /three /four /five /six /seven
/eight /nine /colon /semicolon /less /equal /greater /question
/at /A /B /C /D /E /F /G
/H /I /J /K /L /M /N /O
/P /Q /R /S /T /U /V /W
/X /Y /Z /bracketleft /backslash /bracketright /asciicircum /underscore
/grave /a /b /c /d /e /f /g
/h /i /j /k /l /m /n /o
/p /q /r /s /t /u /v /w
/x /y /z /braceleft /bar /braceright /overline /.notdef
128{/.notdef}repeat]
/Encoding defineresource pop
% vim:ff=unix:
%%EOF
