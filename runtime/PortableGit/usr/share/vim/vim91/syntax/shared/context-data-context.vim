vim9script

# Vim syntax file
# Language: ConTeXt
# Automatically generated by mtx-interface (2023-12-26 16:40)

syn keyword contextConstants zerocount minusone minustwo plusone plustwo contained
syn keyword contextConstants plusthree plusfour plusfive plussix plusseven contained
syn keyword contextConstants pluseight plusnine plusten pluseleven plustwelve contained
syn keyword contextConstants plussixteen plusfifty plushundred plusonehundred plustwohundred contained
syn keyword contextConstants plusfivehundred plusthousand plustenthousand plustwentythousand medcard contained
syn keyword contextConstants maxcard maxcardminusone maxiterator zeropoint onepoint contained
syn keyword contextConstants halfapoint onebasepoint maxcount maxdimen scaledpoint contained
syn keyword contextConstants thousandpoint points halfpoint zeroskip centeringskip contained
syn keyword contextConstants stretchingskip shrinkingskip centeringfillskip stretchingfillskip shrinkingfillskip contained
syn keyword contextConstants zeromuskip onemuskip pluscxxvii pluscxxviii pluscclv contained
syn keyword contextConstants pluscclvi normalpagebox binaryshiftedten binaryshiftedtwenty binaryshiftedthirty contained
syn keyword contextConstants thickermuskip directionlefttoright directionrighttoleft endoflinetoken outputnewlinechar contained
syn keyword contextConstants emptytoks empty undefined prerollrun voidbox contained
syn keyword contextConstants emptybox emptyvbox emptyhbox bigskipamount medskipamount contained
syn keyword contextConstants smallskipamount fmtname fmtversion texengine texenginename contained
syn keyword contextConstants texengineversion texenginefunctionality luatexengine pdftexengine xetexengine contained
syn keyword contextConstants unknownengine contextformat contextversion contextlmtxmode contextmark contained
syn keyword contextConstants mksuffix activecatcode bgroup egroup endline contained
syn keyword contextConstants conditionaltrue conditionalfalse attributeunsetvalue statuswrite uprotationangle contained
syn keyword contextConstants rightrotationangle downrotationangle leftrotationangle inicatcodes ctxcatcodes contained
syn keyword contextConstants texcatcodes notcatcodes txtcatcodes vrbcatcodes prtcatcodes contained
syn keyword contextConstants nilcatcodes luacatcodes tpacatcodes tpbcatcodes xmlcatcodes contained
syn keyword contextConstants ctdcatcodes rlncatcodes escapecatcode begingroupcatcode endgroupcatcode contained
syn keyword contextConstants mathshiftcatcode alignmentcatcode endoflinecatcode parametercatcode superscriptcatcode contained
syn keyword contextConstants subscriptcatcode ignorecatcode spacecatcode lettercatcode othercatcode contained
syn keyword contextConstants activecatcode commentcatcode invalidcatcode tabasciicode newlineasciicode contained
syn keyword contextConstants formfeedasciicode endoflineasciicode endoffileasciicode commaasciicode spaceasciicode contained
syn keyword contextConstants periodasciicode hashasciicode dollarasciicode commentasciicode ampersandasciicode contained
syn keyword contextConstants colonasciicode semicolonasciicode backslashasciicode circumflexasciicode underscoreasciicode contained
syn keyword contextConstants leftbraceasciicode barasciicode rightbraceasciicode tildeasciicode delasciicode contained
syn keyword contextConstants leftparentasciicode rightparentasciicode lessthanasciicode morethanasciicode doublecommentsignal contained
syn keyword contextConstants atsignasciicode exclamationmarkasciicode questionmarkasciicode doublequoteasciicode singlequoteasciicode contained
syn keyword contextConstants forwardslashasciicode primeasciicode hyphenasciicode percentasciicode leftbracketasciicode contained
syn keyword contextConstants rightbracketasciicode zeroasciicode nineasciicode alowercaseasciicode zlowercaseasciicode contained
syn keyword contextConstants hsizefrozenparcode skipfrozenparcode hangfrozenparcode indentfrozenparcode parfillfrozenparcode contained
syn keyword contextConstants adjustfrozenparcode protrudefrozenparcode tolerancefrozenparcode stretchfrozenparcode loosenessfrozenparcode contained
syn keyword contextConstants lastlinefrozenparcode linepenaltyfrozenparcode clubpenaltyfrozenparcode widowpenaltyfrozenparcode displaypenaltyfrozenparcode contained
syn keyword contextConstants brokenpenaltyfrozenparcode demeritsfrozenparcode shapefrozenparcode linefrozenparcode hyphenationfrozenparcode contained
syn keyword contextConstants shapingpenaltyfrozenparcode orphanpenaltyfrozenparcode allfrozenparcode emergencyfrozenparcode parpassesfrozenparcode contained
syn keyword contextConstants singlelinepenaltyfrozenparcode activemathcharcode activetabtoken activeformfeedtoken activeendoflinetoken contained
syn keyword contextConstants batchmodecode nonstopmodecode scrollmodecode errorstopmodecode bottomlevelgroupcode contained
syn keyword contextConstants simplegroupcode hboxgroupcode adjustedhboxgroupcode vboxgroupcode vtopgroupcode contained
syn keyword contextConstants aligngroupcode noaligngroupcode outputgroupcode mathgroupcode discretionarygroupcode contained
syn keyword contextConstants insertgroupcode vadjustgroupcode vcentergroupcode mathabovegroupcode mathchoicegroupcode contained
syn keyword contextConstants alsosimplegroupcode semisimplegroupcode mathshiftgroupcode mathleftgroupcode localboxgroupcode contained
syn keyword contextConstants splitoffgroupcode splitkeepgroupcode preamblegroupcode alignsetgroupcode finrowgroupcode contained
syn keyword contextConstants discretionarygroupcode markautomigrationcode insertautomigrationcode adjustautomigrationcode preautomigrationcode contained
syn keyword contextConstants postautomigrationcode charnodecode hlistnodecode vlistnodecode rulenodecode contained
syn keyword contextConstants insertnodecode marknodecode adjustnodecode ligaturenodecode discretionarynodecode contained
syn keyword contextConstants whatsitnodecode mathnodecode gluenodecode kernnodecode penaltynodecode contained
syn keyword contextConstants unsetnodecode mathsnodecode overrulemathcontrolcode underrulemathcontrolcode radicalrulemathcontrolcode contained
syn keyword contextConstants fractionrulemathcontrolcode accentskewhalfmathcontrolcode accentskewapplymathcontrolcode applyordinarykernpairmathcontrolcode applyverticalitalickernmathcontrolcode contained
syn keyword contextConstants applyordinaryitalickernmathcontrolcode applycharitalickernmathcontrolcode reboxcharitalickernmathcontrolcode applyboxeditalickernmathcontrolcode staircasekernmathcontrolcode contained
syn keyword contextConstants applytextitalickernmathcontrolcode applyscriptitalickernmathcontrolcode checkspaceitalickernmathcontrolcode checktextitalickernmathcontrolcode analyzescriptnucleuscharmathcontrolcode contained
syn keyword contextConstants analyzescriptnucleuslistmathcontrolcode analyzescriptnucleusboxmathcontrolcode accenttopskewwithoffsetmathcontrolcode ignorekerndimensionsmathcontrolcode ignoreflataccentsmathcontrolcode contained
syn keyword contextConstants extendaccentsmathcontrolcode extenddelimitersmathcontrolcode noligaturingglyphoptioncode nokerningglyphoptioncode noexpansionglyphoptioncode contained
syn keyword contextConstants noprotrusionglyphoptioncode noleftkerningglyphoptioncode noleftligaturingglyphoptioncode norightkerningglyphoptioncode norightligaturingglyphoptioncode contained
syn keyword contextConstants noitaliccorrectionglyphoptioncode islargeoperatorglyphoptioncode hasitalicshapeglyphoptioncode normalparcontextcode vmodeparcontextcode contained
syn keyword contextConstants vboxparcontextcode vtopparcontextcode vcenterparcontextcode vadjustparcontextcode insertparcontextcode contained
syn keyword contextConstants outputparcontextcode alignparcontextcode noalignparcontextcode spanparcontextcode resetparcontextcode contained
syn keyword contextConstants leftoriginlistanchorcode leftheightlistanchorcode leftdepthlistanchorcode rightoriginlistanchorcode rightheightlistanchorcode contained
syn keyword contextConstants rightdepthlistanchorcode centeroriginlistanchorcode centerheightlistanchorcode centerdepthlistanchorcode halfwaytotallistanchorcode contained
syn keyword contextConstants halfwayheightlistanchorcode halfwaydepthlistanchorcode halfwayleftlistanchorcode halfwayrightlistanchorcode negatexlistsigncode contained
syn keyword contextConstants negateylistsigncode negatelistsigncode fontslantperpoint fontinterwordspace fontinterwordstretch contained
syn keyword contextConstants fontinterwordshrink fontexheight fontemwidth fontextraspace slantperpoint contained
syn keyword contextConstants mathexheight mathemwidth interwordspace interwordstretch interwordshrink contained
syn keyword contextConstants exheight emwidth extraspace mathaxisheight muquad contained
syn keyword contextConstants startmode stopmode startnotmode stopnotmode startmodeset contained
syn keyword contextConstants stopmodeset doifmode doifelsemode doifmodeelse doifnotmode contained
syn keyword contextConstants startmodeset stopmodeset startallmodes stopallmodes startnotallmodes contained
syn keyword contextConstants stopnotallmodes doifallmodes doifelseallmodes doifallmodeselse doifnotallmodes contained
syn keyword contextConstants startenvironment stopenvironment environment startcomponent stopcomponent contained
syn keyword contextConstants component startlocalcomponent stoplocalcomponent startproduct stopproduct contained
syn keyword contextConstants product startproject stopproject project starttext contained
syn keyword contextConstants stoptext startnotext stopnotext startdocument stopdocument contained
syn keyword contextConstants documentvariable unexpandeddocumentvariable setupdocument presetdocument doifelsedocumentvariable contained
syn keyword contextConstants doifdocumentvariableelse doifdocumentvariable doifnotdocumentvariable startmodule stopmodule contained
syn keyword contextConstants usemodule usetexmodule useluamodule setupmodule currentmoduleparameter contained
syn keyword contextConstants moduleparameter everystarttext everystoptext everyforgetall luaenvironment contained
syn keyword contextConstants startTEXpage stopTEXpage enablemode disablemode preventmode contained
syn keyword contextConstants definemode globalenablemode globaldisablemode globalpreventmode pushmode contained
syn keyword contextConstants popmode typescriptone typescripttwo typescriptthree mathsizesuffix contained
syn keyword contextConstants mathordinarycode mathordcode mathoperatorcode mathopcode mathbinarycode contained
syn keyword contextConstants mathbincode mathrelationcode mathrelcode mathopencode mathclosecode contained
syn keyword contextConstants mathpunctuationcode mathpunctcode mathovercode mathundercode mathinnercode contained
syn keyword contextConstants mathradicalcode mathfractioncode mathmiddlecode mathaccentcode mathfencedcode contained
syn keyword contextConstants mathghostcode mathvariablecode mathactivecode mathvcentercode mathconstructcode contained
syn keyword contextConstants mathwrappedcode mathbegincode mathendcode mathexplicitcode mathdivisioncode contained
syn keyword contextConstants mathfactorialcode mathdimensioncode mathexperimentalcode mathtextpunctuationcode mathimaginarycode contained
syn keyword contextConstants mathdifferentialcode mathexponentialcode mathellipsiscode mathfunctioncode mathdigitcode contained
syn keyword contextConstants mathalphacode mathboxcode mathchoicecode mathnothingcode mathlimopcode contained
syn keyword contextConstants mathnolopcode mathunsetcode mathunspacedcode mathallcode mathfakecode contained
syn keyword contextConstants mathunarycode mathmaybeordinarycode mathmayberelationcode mathmaybebinarycode mathnumbergroupcode contained
syn keyword contextConstants mathchemicalbondcode constantnumber constantnumberargument constantdimen constantdimenargument contained
syn keyword contextConstants constantemptyargument luastringsep !!bs !!es lefttorightmark contained
syn keyword contextConstants righttoleftmark lrm rlm bidilre bidirle contained
syn keyword contextConstants bidipop bidilro bidirlo breakablethinspace nobreakspace contained
syn keyword contextConstants nonbreakablespace narrownobreakspace zerowidthnobreakspace ideographicspace ideographichalffillspace contained
syn keyword contextConstants twoperemspace threeperemspace fourperemspace fiveperemspace sixperemspace contained
syn keyword contextConstants figurespace punctuationspace hairspace enquad emquad contained
syn keyword contextConstants zerowidthspace zerowidthnonjoiner zerowidthjoiner zwnj zwj contained
syn keyword contextConstants optionalspace asciispacechar softhyphen autoinsertedspace Ux contained
syn keyword contextConstants eUx startlmtxmode stoplmtxmode startmkivmode stopmkivmode contained
syn keyword contextConstants wildcardsymbol normalhyphenationcode automatichyphenationcode explicithyphenationcode syllablehyphenationcode contained
syn keyword contextConstants uppercasehyphenationcode collapsehyphenationcode compoundhyphenationcode strictstarthyphenationcode strictendhyphenationcode contained
syn keyword contextConstants automaticpenaltyhyphenationcode explicitpenaltyhyphenationcode permitgluehyphenationcode permitallhyphenationcode permitmathreplacehyphenationcode contained
syn keyword contextConstants forcecheckhyphenationcode lazyligatureshyphenationcode forcehandlerhyphenationcode feedbackcompoundhyphenationcode ignoreboundshyphenationcode contained
syn keyword contextConstants partialhyphenationcode completehyphenationcode normalizelinenormalizecode parindentskipnormalizecode swaphangindentnormalizecode contained
syn keyword contextConstants swapparsshapenormalizecode breakafterdirnormalizecode removemarginkernsnormalizecode clipwidthnormalizecode flattendiscretionariesnormalizecode contained
syn keyword contextConstants discardzerotabskipsnormalizecode flattenhleadersnormalizecode normalizeparnormalizeparcode flattenvleadersnormalizeparcode limitprevgrafnormalizeparcode contained
syn keyword contextConstants nopreslackclassoptioncode nopostslackclassoptioncode lefttopkernclassoptioncode righttopkernclassoptioncode leftbottomkernclassoptioncode contained
syn keyword contextConstants rightbottomkernclassoptioncode lookaheadforendclassoptioncode noitaliccorrectionclassoptioncode defaultmathclassoptions checkligatureclassoptioncode contained
syn keyword contextConstants checkitaliccorrectionclassoptioncode checkkernpairclassoptioncode flattenclassoptioncode omitpenaltyclassoptioncode unpackclassoptioncode contained
syn keyword contextConstants raiseprimeclassoptioncode carryoverlefttopkernclassoptioncode carryoverleftbottomkernclassoptioncode carryoverrighttopkernclassoptioncode carryoverrightbottomkernclassoptioncode contained
syn keyword contextConstants preferdelimiterdimensionsclassoptioncode autoinjectclassoptioncode removeitaliccorrectionclassoptioncode operatoritaliccorrectionclassoptioncode shortinlineclassoptioncode contained
syn keyword contextConstants pushnestingclassoptioncode popnestingclassoptioncode obeynestingclassoptioncode noligaturingglyphoptioncode nokerningglyphoptioncode contained
syn keyword contextConstants noleftligatureglyphoptioncode noleftkernglyphoptioncode norightligatureglyphoptioncode norightkernglyphoptioncode noexpansionglyphoptioncode contained
syn keyword contextConstants noprotrusionglyphoptioncode noitaliccorrectionglyphoptioncode nokerningcode noligaturingcode indecentparpassclasses contained
syn keyword contextConstants looseparpassclasses tightparpassclasses verylooseparpassclass looseparpassclass semilooseparpassclass contained
syn keyword contextConstants decentparpassclass almostdecentparpassclasses semitightparpassclass tightparpassclass frozenflagcode contained
syn keyword contextConstants tolerantflagcode protectedflagcode primitiveflagcode permanentflagcode noalignedflagcode contained
syn keyword contextConstants immutableflagcode mutableflagcode globalflagcode overloadedflagcode immediateflagcode contained
syn keyword contextConstants conditionalflagcode valueflagcode instanceflagcode ordmathflattencode binmathflattencode contained
syn keyword contextConstants relmathflattencode punctmathflattencode innermathflattencode normalworddiscoptioncode preworddiscoptioncode contained
syn keyword contextConstants postworddiscoptioncode preferbreakdiscoptioncode prefernobreakdiscoptioncode continueifinputfile continuewhenlmtxmode contained
syn keyword contextConstants continuewhenmkivmode uunit contained
syn keyword contextHelpers startsetups stopsetups startxmlsetups stopxmlsetups startluasetups contained
syn keyword contextHelpers stopluasetups starttexsetups stoptexsetups startrawsetups stoprawsetups contained
syn keyword contextHelpers startlocalsetups stoplocalsetups starttexdefinition stoptexdefinition starttexcode contained
syn keyword contextHelpers stoptexcode startcontextcode stopcontextcode startcontextdefinitioncode stopcontextdefinitioncode contained
syn keyword contextHelpers texdefinition doifelsesetups doifsetupselse doifsetups doifnotsetups contained
syn keyword contextHelpers setup setups texsetup xmlsetup luasetup contained
syn keyword contextHelpers directsetup fastsetup copysetups resetsetups doifelsecommandhandler contained
syn keyword contextHelpers doifcommandhandlerelse doifnotcommandhandler doifcommandhandler newmode setmode contained
syn keyword contextHelpers resetmode newsystemmode setsystemmode resetsystemmode pushsystemmode contained
syn keyword contextHelpers popsystemmode globalsetmode globalresetmode globalsetsystemmode globalresetsystemmode contained
syn keyword contextHelpers booleanmodevalue newcount newdimen newskip newmuskip contained
syn keyword contextHelpers newbox newtoks newread newwrite newmarks contained
syn keyword contextHelpers newinsert newattribute newif newfloat newlanguage contained
syn keyword contextHelpers newfamily newfam newhelp newuserunit newinteger contained
syn keyword contextHelpers newdimension newgluespec newmugluespec newposit aliasinteger contained
syn keyword contextHelpers aliasdimension aliasposit then begcsname autorule contained
syn keyword contextHelpers tobits tohexa strippedcsname checkedstrippedcsname nofarguments contained
syn keyword contextHelpers firstargumentfalse firstargumenttrue secondargumentfalse secondargumenttrue thirdargumentfalse contained
syn keyword contextHelpers thirdargumenttrue fourthargumentfalse fourthargumenttrue fifthargumentfalse fifthargumenttrue contained
syn keyword contextHelpers sixthargumentfalse sixthargumenttrue seventhargumentfalse seventhargumenttrue doglobal contained
syn keyword contextHelpers dodoglobal redoglobal resetglobal donothing untraceddonothing contained
syn keyword contextHelpers dontcomplain moreboxtracing lessboxtracing noboxtracing forgetall contained
syn keyword contextHelpers donetrue donefalse foundtrue foundfalse inlineordisplaymath contained
syn keyword contextHelpers indisplaymath forcedisplaymath startforceddisplaymath stopforceddisplaymath startpickupmath contained
syn keyword contextHelpers stoppickupmath reqno forceinlinemath mathortext thebox contained
syn keyword contextHelpers htdp unvoidbox hfilll vfilll mathbox contained
syn keyword contextHelpers mathlimop mathnolop mathnothing mathalpha currentcatcodetable contained
syn keyword contextHelpers defaultcatcodetable catcodetablename newcatcodetable startcatcodetable stopcatcodetable contained
syn keyword contextHelpers startextendcatcodetable stopextendcatcodetable pushcatcodetable popcatcodetable restorecatcodes contained
syn keyword contextHelpers setcatcodetable letcatcodecommand defcatcodecommand uedcatcodecommand hglue contained
syn keyword contextHelpers vglue hfillneg vfillneg hfilllneg vfilllneg contained
syn keyword contextHelpers hsplit ruledhss ruledhfil ruledhfill ruledhfilll contained
syn keyword contextHelpers ruledhfilneg ruledhfillneg normalhfillneg normalhfilllneg ruledvss contained
syn keyword contextHelpers ruledvfil ruledvfill ruledvfilll ruledvfilneg ruledvfillneg contained
syn keyword contextHelpers normalvfillneg normalvfilllneg ruledhbox ruledvbox ruledvtop contained
syn keyword contextHelpers ruleddbox ruledvcenter ruledmbox ruledhpack ruledvpack contained
syn keyword contextHelpers ruledtpack ruleddpack ruledvsplit ruledtsplit ruleddsplit contained
syn keyword contextHelpers ruledhskip ruledvskip ruledkern ruledmskip ruledmkern contained
syn keyword contextHelpers ruledhglue ruledvglue normalhglue normalvglue ruledpenalty contained
syn keyword contextHelpers filledhboxb filledhboxr filledhboxg filledhboxc filledhboxm contained
syn keyword contextHelpers filledhboxy filledhboxk scratchstring scratchstringone scratchstringtwo contained
syn keyword contextHelpers tempstring scratchcounter globalscratchcounter privatescratchcounter scratchfloat contained
syn keyword contextHelpers globalscratchfloat privatescratchfloat scratchdimen globalscratchdimen privatescratchdimen contained
syn keyword contextHelpers scratchskip globalscratchskip privatescratchskip scratchmuskip globalscratchmuskip contained
syn keyword contextHelpers privatescratchmuskip scratchtoks globalscratchtoks privatescratchtoks scratchbox contained
syn keyword contextHelpers globalscratchbox privatescratchbox scratchmacro scratchmacroone scratchmacrotwo contained
syn keyword contextHelpers scratchconditiontrue scratchconditionfalse ifscratchcondition scratchconditiononetrue scratchconditiononefalse contained
syn keyword contextHelpers ifscratchconditionone scratchconditiontwotrue scratchconditiontwofalse ifscratchconditiontwo globalscratchcounterone contained
syn keyword contextHelpers globalscratchcountertwo globalscratchcounterthree groupedcommand groupedcommandcs triggergroupedcommand contained
syn keyword contextHelpers triggergroupedcommandcs simplegroupedcommand simplegroupedcommandcs pickupgroupedcommand pickupgroupedcommandcs contained
syn keyword contextHelpers mathgroupedcommandcs usedbaselineskip usedlineskip usedlineskiplimit availablehsize contained
syn keyword contextHelpers localhsize setlocalhsize distributedhsize hsizefraction next contained
syn keyword contextHelpers nexttoken nextbox dowithnextbox dowithnextboxcs dowithnextboxcontent contained
syn keyword contextHelpers dowithnextboxcontentcs flushnextbox boxisempty boxtostring contentostring contained
syn keyword contextHelpers prerolltostring givenwidth givenheight givendepth scangivendimensions contained
syn keyword contextHelpers scratchwidth scratchheight scratchdepth scratchoffset scratchdistance contained
syn keyword contextHelpers scratchtotal scratchitalic scratchhsize scratchvsize scratchxoffset contained
syn keyword contextHelpers scratchyoffset scratchhoffset scratchvoffset scratchxposition scratchyposition contained
syn keyword contextHelpers scratchtopoffset scratchbottomoffset scratchleftoffset scratchrightoffset scratchcounterone contained
syn keyword contextHelpers scratchcountertwo scratchcounterthree scratchcounterfour scratchcounterfive scratchcountersix contained
syn keyword contextHelpers scratchfloatone scratchfloattwo scratchfloatthree scratchfloatfour scratchfloatfive contained
syn keyword contextHelpers scratchfloatsix scratchdimenone scratchdimentwo scratchdimenthree scratchdimenfour contained
syn keyword contextHelpers scratchdimenfive scratchdimensix scratchskipone scratchskiptwo scratchskipthree contained
syn keyword contextHelpers scratchskipfour scratchskipfive scratchskipsix scratchmuskipone scratchmuskiptwo contained
syn keyword contextHelpers scratchmuskipthree scratchmuskipfour scratchmuskipfive scratchmuskipsix scratchtoksone contained
syn keyword contextHelpers scratchtokstwo scratchtoksthree scratchtoksfour scratchtoksfive scratchtokssix contained
syn keyword contextHelpers scratchboxone scratchboxtwo scratchboxthree scratchboxfour scratchboxfive contained
syn keyword contextHelpers scratchboxsix scratchnx scratchny scratchmx scratchmy contained
syn keyword contextHelpers scratchunicode scratchmin scratchmax scratchread scratchwrite contained
syn keyword contextHelpers pfsin pfcos pftan pfasin pfacos contained
syn keyword contextHelpers pfatan pfsinh pfcosh pftanh pfasinh contained
syn keyword contextHelpers pfacosh pfatanh pfsqrt pflog pfexp contained
syn keyword contextHelpers pfceil pffloor pfround pfabs pfrad contained
syn keyword contextHelpers pfdeg pfatantwo pfpow pfmod pfrem contained
syn keyword contextHelpers scratchleftskip scratchrightskip scratchtopskip scratchbottomskip doif contained
syn keyword contextHelpers doifnot doifelse firstinset doifinset doifnotinset contained
syn keyword contextHelpers doifelseinset doifinsetelse doifelsenextchar doifnextcharelse doifelsenextcharcs contained
syn keyword contextHelpers doifnextcharcselse doifelsenextoptional doifnextoptionalelse doifelsenextoptionalcs doifnextoptionalcselse contained
syn keyword contextHelpers doifelsefastoptionalcheck doiffastoptionalcheckelse doifelsefastoptionalcheckcs doiffastoptionalcheckcselse doifelsenextbgroup contained
syn keyword contextHelpers doifnextbgroupelse doifelsenextbgroupcs doifnextbgroupcselse doifelsenextparenthesis doifnextparenthesiselse contained
syn keyword contextHelpers doifelseundefined doifundefinedelse doifelsedefined doifdefinedelse doifundefined contained
syn keyword contextHelpers doifdefined doifelsevalue doifvalue doifnotvalue doifnothing contained
syn keyword contextHelpers doifsomething doifelsenothing doifnothingelse doifelsesomething doifsomethingelse contained
syn keyword contextHelpers doifvaluenothing doifvaluesomething doifelsevaluenothing doifvaluenothingelse doifelsedimension contained
syn keyword contextHelpers doifdimensionelse doifelsenumber doifnumberelse doifnumber doifnotnumber contained
syn keyword contextHelpers doifelsecommon doifcommonelse doifcommon doifnotcommon doifinstring contained
syn keyword contextHelpers doifnotinstring doifelseinstring doifinstringelse doifelseassignment doifassignmentelse contained
syn keyword contextHelpers docheckassignment doifelseassignmentcs doifassignmentelsecs validassignment novalidassignment contained
syn keyword contextHelpers doiftext doifelsetext doiftextelse doifnottext validtext contained
syn keyword contextHelpers quitcondition truecondition falsecondition tracingall tracingnone contained
syn keyword contextHelpers loggingall tracingcatcodes showluatokens aliasmacro removetoks contained
syn keyword contextHelpers appendtoks prependtoks appendtotoks prependtotoks to contained
syn keyword contextHelpers endgraf endpar reseteverypar finishpar empty contained
syn keyword contextHelpers null space quad enspace emspace contained
syn keyword contextHelpers charspace nbsp crlf obeyspaces obeylines contained
syn keyword contextHelpers obeytabs obeypages obeyedspace obeyedline obeyedtab contained
syn keyword contextHelpers obeyedpage normalspace naturalspace controlspace normalspaces contained
syn keyword contextHelpers ignoretabs ignorelines ignorepages ignoreeofs setcontrolspaces contained
syn keyword contextHelpers executeifdefined singleexpandafter doubleexpandafter tripleexpandafter dontleavehmode contained
syn keyword contextHelpers removelastspace removeunwantedspaces keepunwantedspaces removepunctuation ignoreparskip contained
syn keyword contextHelpers forcestrutdepth onlynonbreakablespace wait writestatus define contained
syn keyword contextHelpers defineexpandable redefine setmeasure setemeasure setgmeasure contained
syn keyword contextHelpers setxmeasure definemeasure freezemeasure measure measured contained
syn keyword contextHelpers directmeasure setquantity setequantity setgquantity setxquantity contained
syn keyword contextHelpers definequantity freezequantity quantity quantitied directquantity contained
syn keyword contextHelpers installcorenamespace getvalue getuvalue setvalue setevalue contained
syn keyword contextHelpers setgvalue setxvalue letvalue letgvalue resetvalue contained
syn keyword contextHelpers undefinevalue ignorevalue setuvalue setuevalue setugvalue contained
syn keyword contextHelpers setuxvalue globallet udef ugdef uedef contained
syn keyword contextHelpers uxdef checked unique getparameters geteparameters contained
syn keyword contextHelpers getgparameters getxparameters forgetparameters copyparameters getdummyparameters contained
syn keyword contextHelpers dummyparameter directdummyparameter setdummyparameter letdummyparameter setexpandeddummyparameter contained
syn keyword contextHelpers resetdummyparameter usedummystyleandcolor usedummystyleparameter usedummycolorparameter processcommalist contained
syn keyword contextHelpers processcommacommand quitcommalist quitprevcommalist processaction processallactions contained
syn keyword contextHelpers processfirstactioninset processallactionsinset unexpanded expanded startexpanded contained
syn keyword contextHelpers stopexpanded protect unprotect firstofoneargument firstoftwoarguments contained
syn keyword contextHelpers secondoftwoarguments firstofthreearguments secondofthreearguments thirdofthreearguments firstoffourarguments contained
syn keyword contextHelpers secondoffourarguments thirdoffourarguments fourthoffourarguments firstoffivearguments secondoffivearguments contained
syn keyword contextHelpers thirdoffivearguments fourthoffivearguments fifthoffivearguments firstofsixarguments secondofsixarguments contained
syn keyword contextHelpers thirdofsixarguments fourthofsixarguments fifthofsixarguments sixthofsixarguments firstofoneunexpanded contained
syn keyword contextHelpers firstoftwounexpanded secondoftwounexpanded firstofthreeunexpanded secondofthreeunexpanded thirdofthreeunexpanded contained
syn keyword contextHelpers gobbleoneargument gobbletwoarguments gobblethreearguments gobblefourarguments gobblefivearguments contained
syn keyword contextHelpers gobblesixarguments gobblesevenarguments gobbleeightarguments gobbleninearguments gobbletenarguments contained
syn keyword contextHelpers gobbleoneoptional gobbletwooptionals gobblethreeoptionals gobblefouroptionals gobblefiveoptionals contained
syn keyword contextHelpers dorecurse doloop exitloop dostepwiserecurse recurselevel contained
syn keyword contextHelpers recursedepth dofastloopcs fastloopindex fastloopfinal dowith contained
syn keyword contextHelpers doloopovermatch doloopovermatched doloopoverlist newconstant setnewconstant contained
syn keyword contextHelpers setconstant setconstantvalue newconditional settrue setfalse contained
syn keyword contextHelpers settruevalue setfalsevalue setconditional newmacro setnewmacro contained
syn keyword contextHelpers newfraction newsignal newboundary dosingleempty dodoubleempty contained
syn keyword contextHelpers dotripleempty doquadrupleempty doquintupleempty dosixtupleempty doseventupleempty contained
syn keyword contextHelpers dosingleargument dodoubleargument dotripleargument doquadrupleargument doquintupleargument contained
syn keyword contextHelpers dosixtupleargument doseventupleargument dosinglegroupempty dodoublegroupempty dotriplegroupempty contained
syn keyword contextHelpers doquadruplegroupempty doquintuplegroupempty permitspacesbetweengroups dontpermitspacesbetweengroups nopdfcompression contained
syn keyword contextHelpers maximumpdfcompression normalpdfcompression onlypdfobjectcompression nopdfobjectcompression modulonumber contained
syn keyword contextHelpers dividenumber getfirstcharacter doifelsefirstchar doiffirstcharelse mathclassvalue contained
syn keyword contextHelpers startnointerference stopnointerference twodigits threedigits leftorright contained
syn keyword contextHelpers offinterlineskip oninterlineskip nointerlineskip strut halfstrut contained
syn keyword contextHelpers quarterstrut depthstrut halflinestrut noheightstrut setstrut contained
syn keyword contextHelpers strutbox strutht strutdp strutwd struthtdp contained
syn keyword contextHelpers strutgap begstrut endstrut lineheight leftboundary contained
syn keyword contextHelpers rightboundary signalcharacter ascender descender capheight contained
syn keyword contextHelpers aligncontentleft aligncontentmiddle aligncontentright shiftbox vpackbox contained
syn keyword contextHelpers hpackbox vpackedbox hpackedbox normalreqno startimath contained
syn keyword contextHelpers stopimath normalstartimath normalstopimath startdmath stopdmath contained
syn keyword contextHelpers normalstartdmath normalstopdmath normalsuperscript normalsubscript normalnosuperscript contained
syn keyword contextHelpers normalnosubscript normalprimescript superscript subscript nosuperscript contained
syn keyword contextHelpers nosubscript primescript superprescript subprescript nosuperprescript contained
syn keyword contextHelpers nosubsprecript uncramped cramped mathstyletrigger triggermathstyle contained
syn keyword contextHelpers triggeredmathstyle mathstylefont mathsmallstylefont mathstyleface mathsmallstyleface contained
syn keyword contextHelpers mathstylecommand mathpalette mathstylehbox mathstylevbox mathstylevcenter contained
syn keyword contextHelpers mathstylevcenteredhbox mathstylevcenteredvbox mathtext setmathsmalltextbox setmathtextbox contained
syn keyword contextHelpers pushmathstyle popmathstyle triggerdisplaystyle triggertextstyle triggerscriptstyle contained
syn keyword contextHelpers triggerscriptscriptstyle triggeruncrampedstyle triggercrampedstyle triggersmallstyle triggeruncrampedsmallstyle contained
syn keyword contextHelpers triggercrampedsmallstyle triggerbigstyle triggeruncrampedbigstyle triggercrampedbigstyle luaexpr contained
syn keyword contextHelpers expelsedoif expdoif expdoifnot expdoifelsecommon expdoifcommonelse contained
syn keyword contextHelpers expdoifelseinset expdoifinsetelse glyphscaled ctxdirectlua ctxlatelua contained
syn keyword contextHelpers ctxsprint ctxwrite ctxcommand ctxdirectcommand ctxlatecommand contained
syn keyword contextHelpers ctxreport ctxlua luacode lateluacode directluacode contained
syn keyword contextHelpers registerctxluafile ctxloadluafile luaversion luamajorversion luaminorversion contained
syn keyword contextHelpers ctxluacode luaconditional luaexpanded ctxluamatch ctxluamatchfile contained
syn keyword contextHelpers startluaparameterset stopluaparameterset luaparameterset definenamedlua obeylualines contained
syn keyword contextHelpers obeyluatokens startluacode stopluacode startlua stoplua contained
syn keyword contextHelpers startctxfunction stopctxfunction ctxfunction startctxfunctiondefinition stopctxfunctiondefinition contained
syn keyword contextHelpers installctxfunction installprotectedctxfunction installprotectedctxscanner installctxscanner resetctxscanner contained
syn keyword contextHelpers cldprocessfile cldloadfile cldloadviafile cldcontext cldcommand contained
syn keyword contextHelpers carryoverpar freezeparagraphproperties defrostparagraphproperties setparagraphfreezing forgetparagraphfreezing contained
syn keyword contextHelpers updateparagraphproperties updateparagraphpenalties updateparagraphdemerits updateparagraphshapes updateparagraphlines contained
syn keyword contextHelpers updateparagraphpasses lastlinewidth assumelongusagecs righttolefthbox lefttorighthbox contained
syn keyword contextHelpers righttoleftvbox lefttorightvbox righttoleftvtop lefttorightvtop rtlhbox contained
syn keyword contextHelpers ltrhbox rtlvbox ltrvbox rtlvtop ltrvtop contained
syn keyword contextHelpers autodirhbox autodirvbox autodirvtop leftorrighthbox leftorrightvbox contained
syn keyword contextHelpers leftorrightvtop lefttoright righttoleft checkedlefttoright checkedrighttoleft contained
syn keyword contextHelpers synchronizelayoutdirection synchronizedisplaydirection synchronizeinlinedirection dirlre dirrle contained
syn keyword contextHelpers dirlro dirrlo rtltext ltrtext lesshyphens contained
syn keyword contextHelpers morehyphens nohyphens dohyphens dohyphencollapsing nohyphencollapsing contained
syn keyword contextHelpers compounddiscretionary Ucheckedstartdisplaymath Ucheckedstopdisplaymath break nobreak contained
syn keyword contextHelpers allowbreak goodbreak nospace nospacing dospacing contained
syn keyword contextHelpers naturalhbox naturalvbox naturalvtop naturalhpack naturalvpack contained
syn keyword contextHelpers naturaltpack reversehbox reversevbox reversevtop reversehpack contained
syn keyword contextHelpers reversevpack reversetpack hcontainer vcontainer tcontainer contained
syn keyword contextHelpers frule compoundhyphenpenalty start stop unsupportedcs contained
syn keyword contextHelpers openout closeout write openin closein contained
syn keyword contextHelpers read readline readlinedirect readfromterminal boxlines contained
syn keyword contextHelpers boxline setboxline copyboxline boxlinewd boxlineht contained
syn keyword contextHelpers boxlinedp boxlinenw boxlinenh boxlinend boxlinels contained
syn keyword contextHelpers boxliners boxlinelh boxlinerh boxlinelp boxlinerp contained
syn keyword contextHelpers boxlinein boxrangewd boxrangeht boxrangedp bitwiseset contained
syn keyword contextHelpers bitwiseand bitwiseor bitwisexor bitwisenot bitwisenil contained
syn keyword contextHelpers ifbitwiseand bitwise bitwiseshift bitwiseflip textdir contained
syn keyword contextHelpers linedir pardir boxdir prelistbox postlistbox contained
syn keyword contextHelpers prelistcopy postlistcopy setprelistbox setpostlistbox noligaturing contained
syn keyword contextHelpers nokerning noexpansion noprotrusion noleftkerning noleftligaturing contained
syn keyword contextHelpers norightkerning norightligaturing noitaliccorrection futureletnexttoken defbackslashbreak contained
syn keyword contextHelpers letbackslashbreak pushoverloadmode popoverloadmode pushrunstate poprunstate contained
syn keyword contextHelpers suggestedalias showboxhere discoptioncodestring flagcodestring frozenparcodestring contained
syn keyword contextHelpers glyphoptioncodestring groupcodestring hyphenationcodestring mathcontrolcodestring mathflattencodestring contained
syn keyword contextHelpers normalizecodestring parcontextcodestring newlocalcount newlocaldimen newlocalskip contained
syn keyword contextHelpers newlocalmuskip newlocaltoks newlocalbox newlocalwrite newlocalread contained
syn keyword contextHelpers setnewlocalcount setnewlocaldimen setnewlocalskip setnewlocalmuskip setnewlocaltoks contained
syn keyword contextHelpers setnewlocalbox ifexpression localcontrolledrepeating expandedrepeating unexpandedrepeating contained
syn keyword contextHelpers lastchkinteger ifchkinteger mathordinary mathoperator mathbinary contained
syn keyword contextHelpers mathrelation mathpunctuation mathfraction mathradical mathmiddle contained
syn keyword contextHelpers mathaccent mathfenced mathghost mathvariable mathactive contained
syn keyword contextHelpers mathvcenter mathimaginary mathdifferential mathexponential mathdigit contained
syn keyword contextHelpers mathdivision mathfactorial mathwrapped mathconstruct mathdimension contained
syn keyword contextHelpers mathunary mathchemicalbond filebasename filenameonly filedirname contained
syn keyword contextHelpers filesuffix setmathoption resetmathoption contained
