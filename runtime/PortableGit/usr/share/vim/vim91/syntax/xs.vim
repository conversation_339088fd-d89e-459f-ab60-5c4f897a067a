" Vim syntax file
" Language:      XS (Perl extension interface language)
" Author:        Autogenerated from perl headers, on an original basis of <PERSON> <<EMAIL>>
" Maintainer:    vim-perl <<EMAIL>>
" Previous:      <PERSON> <<EMAIL>>
" Homepage:      https://github.com/vim-perl/vim-perl
" Bugs/requests: https://github.com/vim-perl/vim-perl/issues
" License:       Vim License (see :help license)
" Last Change:   2018 Mar 28

if exists("b:current_syntax")
  finish
endif

runtime! syntax/c.vim

" Configuration:
" let xs_superseded = 0 " mark C functions superseded by Perl replacements (ex. memcpy vs Copy)
" let xs_not_core   = 0 " mark private core functions

if get(g:, 'xs_superseded', 0)
syn keyword xsSuperseded atof atol calloc clearerr exit fclose feof ferror
syn keyword xsSuperseded fflush fgetc fgetpos fgets fopen fprintf fputc fputs
syn keyword xsSuperseded fread free freopen fseek fsetpos fwrite getc getenv
syn keyword xsSuperseded isalnum isalpha iscntrl isdigit isgraph islower
syn keyword xsSuperseded isprint ispunct isspace isupper isxdigit malloc
syn keyword xsSuperseded memcpy memmove memset printf putc rand realloc
syn keyword xsSuperseded rewind setenv sprintf srand stderr stdin stdout
syn keyword xsSuperseded strcat strcmp strcpy strdup strlen strncat strncmp
syn keyword xsSuperseded strncpy strstr strtod strtol strtoul system tolower
syn keyword xsSuperseded toupper ungetc
endif
if get(g:, 'xs_not_core', 0)
syn keyword xsPrivate F0convert Perl__add_range_to_invlist
syn keyword xsPrivate Perl__core_swash_init Perl__get_encoding
syn keyword xsPrivate Perl__get_swash_invlist Perl__invlist_contents
syn keyword xsPrivate Perl__invlist_dump
syn keyword xsPrivate Perl__invlist_intersection_maybe_complement_2nd
syn keyword xsPrivate Perl__invlist_invert Perl__invlist_populate_swatch
syn keyword xsPrivate Perl__invlist_search
syn keyword xsPrivate Perl__invlist_union_maybe_complement_2nd
syn keyword xsPrivate Perl__load_PL_utf8_foldclosures Perl__new_invlist
syn keyword xsPrivate Perl__setup_canned_invlist Perl__swash_inversion_hash
syn keyword xsPrivate Perl__swash_to_invlist Perl__to_fold_latin1
syn keyword xsPrivate Perl__warn_problematic_locale Perl_av_reify
syn keyword xsPrivate Perl_current_re_engine Perl_cv_ckproto_len_flags
syn keyword xsPrivate Perl_emulate_cop_io Perl_find_rundefsvoffset
syn keyword xsPrivate Perl_get_re_arg Perl_grok_atoUV Perl_isALNUM_lazy
syn keyword xsPrivate Perl_isIDFIRST_lazy Perl_is_uni_alnum
syn keyword xsPrivate Perl_is_uni_alnum_lc Perl_is_uni_alnumc
syn keyword xsPrivate Perl_is_uni_alnumc_lc Perl_is_uni_alpha
syn keyword xsPrivate Perl_is_uni_alpha_lc Perl_is_uni_ascii
syn keyword xsPrivate Perl_is_uni_ascii_lc Perl_is_uni_blank
syn keyword xsPrivate Perl_is_uni_blank_lc Perl_is_uni_cntrl
syn keyword xsPrivate Perl_is_uni_cntrl_lc Perl_is_uni_digit
syn keyword xsPrivate Perl_is_uni_digit_lc Perl_is_uni_graph
syn keyword xsPrivate Perl_is_uni_graph_lc Perl_is_uni_idfirst
syn keyword xsPrivate Perl_is_uni_idfirst_lc Perl_is_uni_lower
syn keyword xsPrivate Perl_is_uni_lower_lc Perl_is_uni_print
syn keyword xsPrivate Perl_is_uni_print_lc Perl_is_uni_punct
syn keyword xsPrivate Perl_is_uni_punct_lc Perl_is_uni_space
syn keyword xsPrivate Perl_is_uni_space_lc Perl_is_uni_upper
syn keyword xsPrivate Perl_is_uni_upper_lc Perl_is_uni_xdigit
syn keyword xsPrivate Perl_is_uni_xdigit_lc Perl_is_utf8_alnum
syn keyword xsPrivate Perl_is_utf8_alnumc Perl_is_utf8_alpha
syn keyword xsPrivate Perl_is_utf8_ascii Perl_is_utf8_blank Perl_is_utf8_char
syn keyword xsPrivate Perl_is_utf8_cntrl Perl_is_utf8_digit
syn keyword xsPrivate Perl_is_utf8_graph Perl_is_utf8_idcont
syn keyword xsPrivate Perl_is_utf8_idfirst Perl_is_utf8_lower
syn keyword xsPrivate Perl_is_utf8_mark Perl_is_utf8_perl_space
syn keyword xsPrivate Perl_is_utf8_perl_word Perl_is_utf8_posix_digit
syn keyword xsPrivate Perl_is_utf8_print Perl_is_utf8_punct
syn keyword xsPrivate Perl_is_utf8_space Perl_is_utf8_upper
syn keyword xsPrivate Perl_is_utf8_xdigit Perl_is_utf8_xidcont
syn keyword xsPrivate Perl_is_utf8_xidfirst Perl_mg_find_mglob Perl_mg_length
syn keyword xsPrivate Perl_multideref_stringify Perl_new_warnings_bitfield
syn keyword xsPrivate Perl_op_clear Perl_ptr_table_clear Perl_qerror
syn keyword xsPrivate Perl_reg_named_buff Perl_reg_named_buff_iter
syn keyword xsPrivate Perl_reg_numbered_buff_fetch
syn keyword xsPrivate Perl_reg_numbered_buff_length
syn keyword xsPrivate Perl_reg_numbered_buff_store Perl_reg_qr_package
syn keyword xsPrivate Perl_reg_temp_copy Perl_regprop Perl_report_uninit
syn keyword xsPrivate Perl_sv_magicext_mglob Perl_sv_setsv_cow
syn keyword xsPrivate Perl_to_uni_lower_lc Perl_to_uni_title_lc
syn keyword xsPrivate Perl_to_uni_upper_lc Perl_try_amagic_bin
syn keyword xsPrivate Perl_try_amagic_un Perl_utf8_to_uvchr
syn keyword xsPrivate Perl_utf8_to_uvuni Perl_utf8_to_uvuni_buf
syn keyword xsPrivate Perl_valid_utf8_to_uvuni Perl_validate_proto
syn keyword xsPrivate Perl_vivify_defelem Perl_yylex S_F0convert
syn keyword xsPrivate S__append_range_to_invlist S__make_exactf_invlist
syn keyword xsPrivate S_add_above_Latin1_folds S_add_data S_add_multi_match
syn keyword xsPrivate S_add_utf16_textfilter S_adjust_size_and_find_bucket
syn keyword xsPrivate S_advance_one_SB S_advance_one_WB S_amagic_cmp
syn keyword xsPrivate S_amagic_cmp_locale S_amagic_i_ncmp S_amagic_ncmp
syn keyword xsPrivate S_anonymise_cv_maybe S_ao S_apply_attrs
syn keyword xsPrivate S_apply_attrs_my S_assert_uft8_cache_coherent
syn keyword xsPrivate S_assignment_type S_backup_one_SB S_backup_one_WB
syn keyword xsPrivate S_bad_type_gv S_bad_type_pv
syn keyword xsPrivate S_check_locale_boundary_crossing S_check_type_and_open
syn keyword xsPrivate S_check_uni S_checkcomma S_ckwarn_common
syn keyword xsPrivate S_clear_placeholders S_clear_special_blocks
syn keyword xsPrivate S_cntrl_to_mnemonic S_construct_ahocorasick_from_trie
syn keyword xsPrivate S_cop_free S_could_it_be_a_POSIX_class S_cr_textfilter
syn keyword xsPrivate S_curse S_cv_dump S_deb_curcv S_deb_stack_n S_debprof
syn keyword xsPrivate S_debug_start_match S_del_sv
syn keyword xsPrivate S_deprecate_commaless_var_list S_destroy_matcher
syn keyword xsPrivate S_div128 S_do_chomp S_do_delete_local S_do_oddball
syn keyword xsPrivate S_do_smartmatch S_do_trans_complex
syn keyword xsPrivate S_do_trans_complex_utf8 S_do_trans_count
syn keyword xsPrivate S_do_trans_count_utf8 S_do_trans_simple
syn keyword xsPrivate S_do_trans_simple_utf8 S_docatch S_doeval S_dofindlabel
syn keyword xsPrivate S_doform S_dooneliner S_doopen_pm S_doparseform
syn keyword xsPrivate S_dopoptoeval S_dopoptogiven S_dopoptolabel
syn keyword xsPrivate S_dopoptoloop S_dopoptosub_at S_dopoptowhen
syn keyword xsPrivate S_dump_exec_pos S_dump_trie S_dump_trie_interim_list
syn keyword xsPrivate S_dump_trie_interim_table S_dumpuntil S_dup_attrlist
syn keyword xsPrivate S_exec_failed S_expect_number S_filter_gets
syn keyword xsPrivate S_finalize_op S_find_and_forget_pmops
syn keyword xsPrivate S_find_array_subscript S_find_beginning S_find_byclass
syn keyword xsPrivate S_find_default_stash S_find_hash_subscript
syn keyword xsPrivate S_find_in_my_stash S_find_uninit_var S_first_symbol
syn keyword xsPrivate S_fixup_errno_string S_fold_constants S_forbid_setid
syn keyword xsPrivate S_force_ident S_force_ident_maybe_lex S_force_list
syn keyword xsPrivate S_force_next S_force_strict_version S_force_version
syn keyword xsPrivate S_force_word S_forget_pmop S_form_short_octal_warning
syn keyword xsPrivate S_gen_constant_list S_get_ANYOF_cp_list_for_ssc
syn keyword xsPrivate S_get_aux_mg S_get_num S_glob_2number
syn keyword xsPrivate S_glob_assign_glob S_grok_bslash_N S_grok_bslash_c
syn keyword xsPrivate S_grok_bslash_o S_group_end S_gv_init_svtype
syn keyword xsPrivate S_gv_is_in_main S_gv_magicalize S_gv_magicalize_isa
syn keyword xsPrivate S_handle_regex_sets S_hfreeentries S_hsplit
syn keyword xsPrivate S_hv_auxinit S_hv_auxinit_internal S_hv_delete_common
syn keyword xsPrivate S_hv_free_ent_ret S_hv_magic_check S_hv_notallowed
syn keyword xsPrivate S_incline S_incpush S_incpush_if_exists
syn keyword xsPrivate S_incpush_use_sep S_ingroup S_init_ids S_init_interp
syn keyword xsPrivate S_init_main_stash S_init_perllib
syn keyword xsPrivate S_init_postdump_symbols S_init_predump_symbols
syn keyword xsPrivate S_inplace_aassign S_intuit_method S_intuit_more
syn keyword xsPrivate S_invlist_extend S_invlist_iternext
syn keyword xsPrivate S_invoke_exception_hook S_isFOO_lc S_isFOO_utf8_lc
syn keyword xsPrivate S_isGCB S_isSB S_isWB S_is_an_int
syn keyword xsPrivate S_is_handle_constructor S_is_ssc_worth_it S_isa_lookup
syn keyword xsPrivate S_join_exact S_leave_common S_listkids
syn keyword xsPrivate S_looks_like_bool S_magic_methcall1 S_make_matcher
syn keyword xsPrivate S_make_trie S_matcher_matches_sv S_maybe_multimagic_gv
syn keyword xsPrivate S_mayberelocate S_measure_struct S_mem_log_common
syn keyword xsPrivate S_mess_alloc S_minus_v S_missingterm S_modkids
syn keyword xsPrivate S_more_sv S_move_proto_attr S_mro_clean_isarev
syn keyword xsPrivate S_mro_gather_and_rename S_mro_get_linear_isa_dfs
syn keyword xsPrivate S_mul128 S_mulexp10 S_my_bytes_to_utf8 S_my_exit_jump
syn keyword xsPrivate S_my_kid S_need_utf8 S_newGIVWHENOP S_new_constant
syn keyword xsPrivate S_new_he S_new_logop S_next_symbol S_nextchar
syn keyword xsPrivate S_no_bareword_allowed S_no_fh_allowed S_no_op
syn keyword xsPrivate S_not_a_number S_not_incrementable S_nuke_stacks
syn keyword xsPrivate S_num_overflow S_open_script S_openn_cleanup
syn keyword xsPrivate S_openn_setup S_pack_rec S_pad_alloc_name
syn keyword xsPrivate S_pad_check_dup S_pad_findlex S_pad_reset S_parse_body
syn keyword xsPrivate S_parse_gv_stash_name S_parse_ident
syn keyword xsPrivate S_parse_lparen_question_flags S_pending_ident S_pidgone
syn keyword xsPrivate S_pm_description S_pmtrans
syn keyword xsPrivate S_populate_ANYOF_from_invlist S_printbuf
syn keyword xsPrivate S_process_special_blocks S_ptr_table_find
syn keyword xsPrivate S_put_charclass_bitmap_innards S_put_code_point
syn keyword xsPrivate S_put_range S_qsortsvu S_re_croak2 S_ref_array_or_hash
syn keyword xsPrivate S_refcounted_he_value S_refkids S_refto S_reg
syn keyword xsPrivate S_reg2Lanode S_reg_check_named_buff_matched S_reg_node
syn keyword xsPrivate S_reg_recode S_reg_scan_name S_reganode S_regatom
syn keyword xsPrivate S_regbranch S_regclass S_regcppop S_regcppush
syn keyword xsPrivate S_regdump_extflags S_regdump_intflags
syn keyword xsPrivate S_regex_set_precedence S_reghop3 S_reghop4
syn keyword xsPrivate S_reghopmaybe3 S_reginclass S_reginsert S_regmatch
syn keyword xsPrivate S_regnode_guts S_regpatws S_regpiece S_regrepeat
syn keyword xsPrivate S_regtail S_regtail_study S_regtry S_require_tie_mod
syn keyword xsPrivate S_restore_magic S_run_body S_run_user_filter
syn keyword xsPrivate S_rxres_free S_rxres_restore S_save_hek_flags
syn keyword xsPrivate S_save_lines S_save_magic_flags S_save_pushptri32ptr
syn keyword xsPrivate S_save_scalar_at S_scalar_mod_type S_scalarboolean
syn keyword xsPrivate S_scalarkids S_scalarseq S_scan_commit S_scan_const
syn keyword xsPrivate S_scan_formline S_scan_heredoc S_scan_ident
syn keyword xsPrivate S_scan_inputsymbol S_scan_pat S_scan_str S_scan_subst
syn keyword xsPrivate S_scan_trans S_scan_word S_search_const S_sequence_num
syn keyword xsPrivate S_set_ANYOF_arg S_share_hek_flags S_simplify_sort
syn keyword xsPrivate S_skipspace_flags S_sortcv S_sortcv_stacked
syn keyword xsPrivate S_sortcv_xsub S_space_join_names_mortal S_ssc_and
syn keyword xsPrivate S_ssc_anything S_ssc_finalize S_ssc_init
syn keyword xsPrivate S_ssc_is_anything S_ssc_is_cp_posixl_init S_ssc_or
syn keyword xsPrivate S_stdize_locale S_strip_return S_study_chunk
syn keyword xsPrivate S_sublex_done S_sublex_push S_sublex_start
syn keyword xsPrivate S_sv_2iuv_common S_sv_2iuv_non_preserve S_sv_add_arena
syn keyword xsPrivate S_sv_buf_to_rw S_sv_display S_sv_dup_common
syn keyword xsPrivate S_sv_dup_inc_multiple S_sv_exp_grow S_sv_i_ncmp
syn keyword xsPrivate S_sv_ncmp S_sv_pos_b2u_midway S_sv_pos_u2b_cached
syn keyword xsPrivate S_sv_pos_u2b_forwards S_sv_pos_u2b_midway
syn keyword xsPrivate S_sv_release_COW S_swallow_bom S_swash_scan_list_line
syn keyword xsPrivate S_swatch_get S_to_byte_substr S_to_lower_latin1
syn keyword xsPrivate S_to_utf8_substr S_tokenize_use S_tokeq S_tokereport
syn keyword xsPrivate S_too_few_arguments_pv S_too_many_arguments_pv
syn keyword xsPrivate S_uiv_2buf S_unpack_rec S_unreferenced_to_tmp_stack
syn keyword xsPrivate S_unshare_hek_or_pvn S_unwind_handler_stack
syn keyword xsPrivate S_update_debugger_info S_usage S_utf16_textfilter
syn keyword xsPrivate S_utf8_mg_len_cache_update S_utf8_mg_pos_cache_update
syn keyword xsPrivate S_validate_suid S_visit S_with_queued_errors
syn keyword xsPrivate S_xs_version_bootcheck S_yywarn _add_range_to_invlist
syn keyword xsPrivate _append_range_to_invlist _core_swash_init _get_encoding
syn keyword xsPrivate _get_swash_invlist _invlist_array_init
syn keyword xsPrivate _invlist_contains_cp _invlist_contents _invlist_dump
syn keyword xsPrivate _invlist_intersection
syn keyword xsPrivate _invlist_intersection_maybe_complement_2nd
syn keyword xsPrivate _invlist_invert _invlist_len _invlist_populate_swatch
syn keyword xsPrivate _invlist_search _invlist_subtract _invlist_union
syn keyword xsPrivate _invlist_union_maybe_complement_2nd
syn keyword xsPrivate _load_PL_utf8_foldclosures _make_exactf_invlist
syn keyword xsPrivate _new_invlist _setup_canned_invlist
syn keyword xsPrivate _swash_inversion_hash _swash_to_invlist _to_fold_latin1
syn keyword xsPrivate _warn_problematic_locale add_above_Latin1_folds
syn keyword xsPrivate add_cp_to_invlist add_data add_multi_match
syn keyword xsPrivate add_utf16_textfilter adjust_size_and_find_bucket
syn keyword xsPrivate advance_one_SB advance_one_WB
syn keyword xsPrivate alloc_maybe_populate_EXACT amagic_cmp amagic_cmp_locale
syn keyword xsPrivate amagic_i_ncmp amagic_ncmp anonymise_cv_maybe ao
syn keyword xsPrivate apply_attrs apply_attrs_my assert_uft8_cache_coherent
syn keyword xsPrivate assignment_type av_reify backup_one_SB backup_one_WB
syn keyword xsPrivate bad_type_gv bad_type_pv check_locale_boundary_crossing
syn keyword xsPrivate check_type_and_open check_uni checkcomma ckwarn_common
syn keyword xsPrivate clear_placeholders clear_special_blocks
syn keyword xsPrivate cntrl_to_mnemonic compute_EXACTish
syn keyword xsPrivate construct_ahocorasick_from_trie cop_free
syn keyword xsPrivate could_it_be_a_POSIX_class cr_textfilter
syn keyword xsPrivate current_re_engine curse cv_ckproto_len_flags cv_dump
syn keyword xsPrivate deb_curcv deb_stack_n debprof debug_start_match del_sv
syn keyword xsPrivate deprecate_commaless_var_list destroy_matcher div128
syn keyword xsPrivate do_aexec do_chomp do_delete_local do_exec do_oddball
syn keyword xsPrivate do_smartmatch do_trans_complex do_trans_complex_utf8
syn keyword xsPrivate do_trans_count do_trans_count_utf8 do_trans_simple
syn keyword xsPrivate do_trans_simple_utf8 docatch doeval dofindlabel doform
syn keyword xsPrivate dooneliner doopen_pm doparseform dopoptoeval
syn keyword xsPrivate dopoptogiven dopoptolabel dopoptoloop dopoptosub_at
syn keyword xsPrivate dopoptowhen dump_exec_pos dump_trie
syn keyword xsPrivate dump_trie_interim_list dump_trie_interim_table
syn keyword xsPrivate dumpuntil dup_attrlist exec_failed expect_number
syn keyword xsPrivate filter_gets finalize_op find_and_forget_pmops
syn keyword xsPrivate find_array_subscript find_beginning find_byclass
syn keyword xsPrivate find_default_stash find_hash_subscript find_in_my_stash
syn keyword xsPrivate find_rundefsvoffset find_uninit_var first_symbol
syn keyword xsPrivate fixup_errno_string fold_constants forbid_setid
syn keyword xsPrivate force_ident force_ident_maybe_lex force_list force_next
syn keyword xsPrivate force_strict_version force_version force_word
syn keyword xsPrivate forget_pmop form_short_octal_warning free_c_backtrace
syn keyword xsPrivate gen_constant_list get_ANYOF_cp_list_for_ssc get_aux_mg
syn keyword xsPrivate get_invlist_iter_addr get_invlist_offset_addr
syn keyword xsPrivate get_invlist_previous_index_addr get_num glob_2number
syn keyword xsPrivate glob_assign_glob grok_atoUV grok_bslash_N grok_bslash_c
syn keyword xsPrivate grok_bslash_o grok_bslash_x group_end gv_init_svtype
syn keyword xsPrivate gv_is_in_main gv_magicalize gv_magicalize_isa
syn keyword xsPrivate handle_regex_sets hfreeentries hsplit hv_auxinit
syn keyword xsPrivate hv_auxinit_internal hv_delete_common hv_free_ent_ret
syn keyword xsPrivate hv_magic_check hv_notallowed incline incpush
syn keyword xsPrivate incpush_if_exists incpush_use_sep ingroup init_ids
syn keyword xsPrivate init_interp init_main_stash init_perllib
syn keyword xsPrivate init_postdump_symbols init_predump_symbols
syn keyword xsPrivate inplace_aassign intuit_method intuit_more invlist_array
syn keyword xsPrivate invlist_clone invlist_extend invlist_highest
syn keyword xsPrivate invlist_is_iterating invlist_iterfinish
syn keyword xsPrivate invlist_iterinit invlist_iternext invlist_max
syn keyword xsPrivate invlist_previous_index invlist_set_len
syn keyword xsPrivate invlist_set_previous_index invlist_trim
syn keyword xsPrivate invoke_exception_hook isALNUM_lazy isFOO_lc
syn keyword xsPrivate isFOO_utf8_lc isGCB isIDFIRST_lazy isSB isWB is_an_int
syn keyword xsPrivate is_handle_constructor is_ssc_worth_it is_uni_alnum
syn keyword xsPrivate is_uni_alnum_lc is_uni_alnumc is_uni_alnumc_lc
syn keyword xsPrivate is_uni_alpha is_uni_alpha_lc is_uni_ascii
syn keyword xsPrivate is_uni_ascii_lc is_uni_blank is_uni_blank_lc
syn keyword xsPrivate is_uni_cntrl is_uni_cntrl_lc is_uni_digit
syn keyword xsPrivate is_uni_digit_lc is_uni_graph is_uni_graph_lc
syn keyword xsPrivate is_uni_idfirst is_uni_idfirst_lc is_uni_lower
syn keyword xsPrivate is_uni_lower_lc is_uni_print is_uni_print_lc
syn keyword xsPrivate is_uni_punct is_uni_punct_lc is_uni_space
syn keyword xsPrivate is_uni_space_lc is_uni_upper is_uni_upper_lc
syn keyword xsPrivate is_uni_xdigit is_uni_xdigit_lc is_utf8_alnum
syn keyword xsPrivate is_utf8_alnumc is_utf8_alpha is_utf8_ascii
syn keyword xsPrivate is_utf8_blank is_utf8_char is_utf8_cntrl is_utf8_digit
syn keyword xsPrivate is_utf8_graph is_utf8_idcont is_utf8_idfirst
syn keyword xsPrivate is_utf8_lower is_utf8_mark is_utf8_perl_space
syn keyword xsPrivate is_utf8_perl_word is_utf8_posix_digit is_utf8_print
syn keyword xsPrivate is_utf8_punct is_utf8_space is_utf8_upper
syn keyword xsPrivate is_utf8_xdigit is_utf8_xidcont is_utf8_xidfirst
syn keyword xsPrivate isa_lookup join_exact leave_common listkids
syn keyword xsPrivate looks_like_bool magic_methcall1 make_matcher make_trie
syn keyword xsPrivate matcher_matches_sv maybe_multimagic_gv mayberelocate
syn keyword xsPrivate measure_struct mem_log_common mess_alloc mg_find_mglob
syn keyword xsPrivate mg_length minus_v missingterm modkids more_sv
syn keyword xsPrivate move_proto_attr mro_clean_isarev mro_gather_and_rename
syn keyword xsPrivate mro_get_linear_isa_dfs mul128 mulexp10
syn keyword xsPrivate multideref_stringify my_bytes_to_utf8 my_exit_jump
syn keyword xsPrivate my_kid need_utf8 newGIVWHENOP new_he new_logop
syn keyword xsPrivate next_symbol nextchar no_bareword_allowed no_fh_allowed
syn keyword xsPrivate no_op not_a_number not_incrementable nuke_stacks
syn keyword xsPrivate num_overflow op_clear open_script openn_cleanup
syn keyword xsPrivate openn_setup pack_rec pad_alloc_name pad_check_dup
syn keyword xsPrivate pad_findlex pad_reset parse_body parse_gv_stash_name
syn keyword xsPrivate parse_ident parse_lparen_question_flags pending_ident
syn keyword xsPrivate pidgone pm_description pmtrans
syn keyword xsPrivate populate_ANYOF_from_invlist printbuf
syn keyword xsPrivate process_special_blocks ptr_table_clear ptr_table_find
syn keyword xsPrivate put_charclass_bitmap_innards put_code_point put_range
syn keyword xsPrivate qerror qsortsvu re_croak2 ref_array_or_hash
syn keyword xsPrivate refcounted_he_value refkids refto reg reg2Lanode
syn keyword xsPrivate reg_check_named_buff_matched reg_named_buff
syn keyword xsPrivate reg_named_buff_iter reg_node reg_numbered_buff_fetch
syn keyword xsPrivate reg_numbered_buff_length reg_numbered_buff_store
syn keyword xsPrivate reg_qr_package reg_recode reg_scan_name reg_skipcomment
syn keyword xsPrivate reg_temp_copy reganode regatom regbranch regclass
syn keyword xsPrivate regcppop regcppush regcurly regdump_extflags
syn keyword xsPrivate regdump_intflags regex_set_precedence reghop3 reghop4
syn keyword xsPrivate reghopmaybe3 reginclass reginsert regmatch regnode_guts
syn keyword xsPrivate regpatws regpiece regpposixcc regprop regrepeat regtail
syn keyword xsPrivate regtail_study regtry report_uninit require_tie_mod
syn keyword xsPrivate restore_magic run_body run_user_filter rxres_free
syn keyword xsPrivate rxres_restore save_hek_flags save_lines
syn keyword xsPrivate save_magic_flags save_pushptri32ptr save_scalar_at
syn keyword xsPrivate scalar_mod_type scalarboolean scalarkids scalarseq
syn keyword xsPrivate scan_commit scan_const scan_formline scan_heredoc
syn keyword xsPrivate scan_ident scan_inputsymbol scan_pat scan_str
syn keyword xsPrivate scan_subst scan_trans scan_word search_const
syn keyword xsPrivate sequence_num set_ANYOF_arg share_hek_flags
syn keyword xsPrivate simplify_sort skipspace_flags sortcv sortcv_stacked
syn keyword xsPrivate sortcv_xsub space_join_names_mortal ssc_add_range
syn keyword xsPrivate ssc_and ssc_anything ssc_clear_locale ssc_cp_and
syn keyword xsPrivate ssc_finalize ssc_init ssc_intersection ssc_is_anything
syn keyword xsPrivate ssc_is_cp_posixl_init ssc_or ssc_union stdize_locale
syn keyword xsPrivate strip_return study_chunk sublex_done sublex_push
syn keyword xsPrivate sublex_start sv_2iuv_common sv_2iuv_non_preserve
syn keyword xsPrivate sv_add_arena sv_buf_to_rw sv_copypv sv_display
syn keyword xsPrivate sv_dup_common sv_dup_inc_multiple sv_exp_grow sv_i_ncmp
syn keyword xsPrivate sv_magicext_mglob sv_ncmp sv_only_taint_gmagic
syn keyword xsPrivate sv_or_pv_pos_u2b sv_pos_b2u_midway sv_pos_u2b_cached
syn keyword xsPrivate sv_pos_u2b_forwards sv_pos_u2b_midway sv_release_COW
syn keyword xsPrivate sv_setsv_cow swallow_bom swash_scan_list_line
syn keyword xsPrivate swatch_get to_byte_substr to_lower_latin1
syn keyword xsPrivate to_uni_lower_lc to_uni_title_lc to_uni_upper_lc
syn keyword xsPrivate to_utf8_substr tokenize_use tokeq tokereport
syn keyword xsPrivate too_few_arguments_pv too_many_arguments_pv uiv_2buf
syn keyword xsPrivate unpack_rec unreferenced_to_tmp_stack unshare_hek_or_pvn
syn keyword xsPrivate unwind_handler_stack update_debugger_info usage
syn keyword xsPrivate utf16_textfilter utf8_mg_len_cache_update
syn keyword xsPrivate utf8_mg_pos_cache_update utf8_to_uvchr utf8_to_uvuni
syn keyword xsPrivate utf8_to_uvuni_buf valid_utf8_to_uvuni validate_proto
syn keyword xsPrivate visit vivify_defelem with_queued_errors yylex yywarn
endif
syn keyword xsType AMT AMTS ANY AV BHK BINOP BLOCK CHECKPOINT CLONE_PARAMS
syn keyword xsType COP COPHH CV DB_Hash_t DB_Prefix_t DEBUG_t Direntry_t
syn keyword xsType Fpos_t Free_t GCB_enum GP GV Gid_t Groups_t HE HEK HV I16
syn keyword xsType I32 I64 I8 IO IV Int64 JMPENV LISTOP LOGOP LOOP MAGIC
syn keyword xsType METHOP MGS MGVTBL Malloc_t Mmap_t Mode_t NV Netdb_hlen_t
syn keyword xsType Netdb_host_t Netdb_name_t Netdb_net_t OP OPCODE OPSLAB
syn keyword xsType OPSLOT Off_t Optype PAD PADLIST PADNAME PADNAMELIST
syn keyword xsType PADOFFSET PADOP PERL_CONTEXT PERL_DRAND48_T PERL_SI PMOP
syn keyword xsType PTR_TBL_ENT_t PTR_TBL_t PVOP PerlHandShakeInterpreter
syn keyword xsType PerlIO PerlIO_funcs PerlIO_list_s PerlIO_list_t PerlIOl
syn keyword xsType PerlInterpreter Pid_t Quad_t REGEXP RExC_state_t
syn keyword xsType Rand_seed_t SB_enum SSize_t STRLEN STRUCT_SV SUBLEXINFO SV
syn keyword xsType SVOP Select_fd_set_t Shmat_t Signal_t Sigsave_t Size_t
syn keyword xsType Sock_size_t Stat_t TM64 Time64_T Time_t U16 U32 U64 U8
syn keyword xsType UNOP UNOP_AUX UV Uid_t Uquad_t WB_enum XINVLIST XOP XPV
syn keyword xsType XPVAV XPVBM XPVCV XPVFM XPVGV XPVHV XPVIO XPVIV XPVLV
syn keyword xsType XPVMG XPVNV XPVUV Year _PerlIO _PerlIO_funcs
syn keyword xsType _char_class_number _pMY_CXT _pTHX _reg_ac_data
syn keyword xsType _reg_trie_data _reg_trie_state _reg_trie_trans
syn keyword xsType _reg_trie_trans_list_elem _sublex_info _xhvnameu _xivu
syn keyword xsType _xmgu _xnvu am_table am_table_short block_eval
syn keyword xsType block_format block_givwhen block_hooks block_loop
syn keyword xsType block_sub bound_type clone_params custom_op cv_flags_t
syn keyword xsType expectation gccbug_semun line_t magic mem_log_type methop
syn keyword xsType mgvtbl mro_alg mro_meta my_cxt_t opcode opslab opslot p5rx
syn keyword xsType pMY_CXT pMY_CXT_ pTHX pTHX_ padlist padname
syn keyword xsType padname_with_str padnamelist padtidy_type perl_cond
syn keyword xsType perl_debug_pad perl_drand48_t perl_key
syn keyword xsType perl_memory_debug_header perl_mstats perl_mstats_t
syn keyword xsType perl_mutex perl_os_thread perl_phase perl_vars
syn keyword xsType pthread_addr_t ptr_tbl ptr_tbl_ent refcounted_he
syn keyword xsType reg_ac_data reg_code_block reg_data reg_substr_data
syn keyword xsType reg_substr_datum reg_trie_data reg_trie_state
syn keyword xsType reg_trie_trans reg_trie_trans_le regex_charset regnode
syn keyword xsType regnode_1 regnode_2 regnode_2L regnode_charclass
syn keyword xsType regnode_charclass_class regnode_charclass_posixl
syn keyword xsType regnode_ssc regnode_string semun shared_he svtype ufuncs
syn keyword xsType unop_aux xop_flags_enum xpv xpvav xpvcv xpvfm xpvgv xpvhv
syn keyword xsType xpvhv_aux xpvinvlist xpvio xpviv xpvlv xpvmg xpvnv xpvuv
syn keyword xsType yytokentype
syn keyword xsString IVdf NVef NVff NVgf SVf SVf256 SVf32 SVf_ UVof UVuf UVxf
syn keyword xsConstant CXt_BLOCK CXt_EVAL CXt_FORMAT CXt_GIVEN CXt_LOOP_FOR
syn keyword xsConstant CXt_LOOP_LAZYIV CXt_LOOP_LAZYSV CXt_LOOP_PLAIN
syn keyword xsConstant CXt_NULL CXt_SUB CXt_SUBST CXt_WHEN GCB_BOUND GCB_CR
syn keyword xsConstant GCB_Control GCB_EDGE GCB_Extend GCB_L GCB_LF GCB_LV
syn keyword xsConstant GCB_LVT GCB_Other GCB_Prepend GCB_Regional_Indicator
syn keyword xsConstant GCB_SpacingMark GCB_T GCB_V G_ARRAY G_DISCARD G_EVAL
syn keyword xsConstant G_FAKINGEVAL G_KEEPERR G_METHOD G_METHOD_NAMED
syn keyword xsConstant G_NOARGS G_NODEBUG G_RE_REPARSING G_SCALAR
syn keyword xsConstant G_UNDEF_FILL G_VOID G_WANT G_WARN_ALL_MASK
syn keyword xsConstant G_WARN_ALL_OFF G_WARN_ALL_ON G_WARN_OFF G_WARN_ON
syn keyword xsConstant G_WARN_ONCE G_WRITING_TO_STDERR OA_AVREF OA_BASEOP
syn keyword xsConstant OA_BASEOP_OR_UNOP OA_BINOP OA_CLASS_MASK OA_COP
syn keyword xsConstant OA_CVREF OA_DANGEROUS OA_DEFGV OA_FILEREF
syn keyword xsConstant OA_FILESTATOP OA_FOLDCONST OA_HVREF OA_LIST OA_LISTOP
syn keyword xsConstant OA_LOGOP OA_LOOP OA_LOOPEXOP OA_MARK OA_METHOP
syn keyword xsConstant OA_OPTIONAL OA_OTHERINT OA_PADOP OA_PMOP
syn keyword xsConstant OA_PVOP_OR_SVOP OA_RETSCALAR OA_SCALAR OA_SCALARREF
syn keyword xsConstant OA_SVOP OA_TARGET OA_TARGLEX OA_UNOP OA_UNOP_AUX
syn keyword xsConstant OP_AASSIGN OP_ABS OP_ACCEPT OP_ADD OP_AEACH OP_AELEM
syn keyword xsConstant OP_AELEMFAST OP_AELEMFAST_LEX OP_AKEYS OP_ALARM OP_AND
syn keyword xsConstant OP_ANDASSIGN OP_ANONCODE OP_ANONCONST OP_ANONHASH
syn keyword xsConstant OP_ANONLIST OP_ASLICE OP_ATAN2 OP_AV2ARYLEN OP_AVALUES
syn keyword xsConstant OP_BACKTICK OP_BIND OP_BINMODE OP_BIT_AND OP_BIT_OR
syn keyword xsConstant OP_BIT_XOR OP_BLESS OP_BREAK OP_CALLER OP_CHDIR
syn keyword xsConstant OP_CHMOD OP_CHOMP OP_CHOP OP_CHOWN OP_CHR OP_CHROOT
syn keyword xsConstant OP_CLONECV OP_CLOSE OP_CLOSEDIR OP_COMPLEMENT
syn keyword xsConstant OP_CONCAT OP_COND_EXPR OP_CONNECT OP_CONST OP_CONTINUE
syn keyword xsConstant OP_COREARGS OP_COS OP_CRYPT OP_CUSTOM OP_DBMCLOSE
syn keyword xsConstant OP_DBMOPEN OP_DBSTATE OP_DEFINED OP_DELETE OP_DIE
syn keyword xsConstant OP_DIVIDE OP_DOFILE OP_DOR OP_DORASSIGN OP_DUMP
syn keyword xsConstant OP_EACH OP_EGRENT OP_EHOSTENT OP_ENETENT OP_ENTER
syn keyword xsConstant OP_ENTEREVAL OP_ENTERGIVEN OP_ENTERITER OP_ENTERLOOP
syn keyword xsConstant OP_ENTERSUB OP_ENTERTRY OP_ENTERWHEN OP_ENTERWRITE
syn keyword xsConstant OP_EOF OP_EPROTOENT OP_EPWENT OP_EQ OP_ESERVENT
syn keyword xsConstant OP_EXEC OP_EXISTS OP_EXIT OP_EXP OP_FC OP_FCNTL
syn keyword xsConstant OP_FILENO OP_FLIP OP_FLOCK OP_FLOP OP_FORK OP_FORMLINE
syn keyword xsConstant OP_FTATIME OP_FTBINARY OP_FTBLK OP_FTCHR OP_FTCTIME
syn keyword xsConstant OP_FTDIR OP_FTEEXEC OP_FTEOWNED OP_FTEREAD OP_FTEWRITE
syn keyword xsConstant OP_FTFILE OP_FTIS OP_FTLINK OP_FTMTIME OP_FTPIPE
syn keyword xsConstant OP_FTREXEC OP_FTROWNED OP_FTRREAD OP_FTRWRITE
syn keyword xsConstant OP_FTSGID OP_FTSIZE OP_FTSOCK OP_FTSUID OP_FTSVTX
syn keyword xsConstant OP_FTTEXT OP_FTTTY OP_FTZERO OP_GE OP_GELEM OP_GETC
syn keyword xsConstant OP_GETLOGIN OP_GETPEERNAME OP_GETPGRP OP_GETPPID
syn keyword xsConstant OP_GETPRIORITY OP_GETSOCKNAME OP_GGRENT OP_GGRGID
syn keyword xsConstant OP_GGRNAM OP_GHBYADDR OP_GHBYNAME OP_GHOSTENT OP_GLOB
syn keyword xsConstant OP_GMTIME OP_GNBYADDR OP_GNBYNAME OP_GNETENT OP_GOTO
syn keyword xsConstant OP_GPBYNAME OP_GPBYNUMBER OP_GPROTOENT OP_GPWENT
syn keyword xsConstant OP_GPWNAM OP_GPWUID OP_GREPSTART OP_GREPWHILE
syn keyword xsConstant OP_GSBYNAME OP_GSBYPORT OP_GSERVENT OP_GSOCKOPT OP_GT
syn keyword xsConstant OP_GV OP_GVSV OP_HELEM OP_HEX OP_HINTSEVAL OP_HSLICE
syn keyword xsConstant OP_INDEX OP_INT OP_INTROCV OP_IOCTL OP_ITER OP_I_ADD
syn keyword xsConstant OP_I_DIVIDE OP_I_EQ OP_I_GE OP_I_GT OP_I_LE OP_I_LT
syn keyword xsConstant OP_I_MODULO OP_I_MULTIPLY OP_I_NCMP OP_I_NE
syn keyword xsConstant OP_I_NEGATE OP_I_POSTDEC OP_I_POSTINC OP_I_PREDEC
syn keyword xsConstant OP_I_PREINC OP_I_SUBTRACT OP_JOIN OP_KEYS OP_KILL
syn keyword xsConstant OP_KVASLICE OP_KVHSLICE OP_LAST OP_LC OP_LCFIRST OP_LE
syn keyword xsConstant OP_LEAVE OP_LEAVEEVAL OP_LEAVEGIVEN OP_LEAVELOOP
syn keyword xsConstant OP_LEAVESUB OP_LEAVESUBLV OP_LEAVETRY OP_LEAVEWHEN
syn keyword xsConstant OP_LEAVEWRITE OP_LEFT_SHIFT OP_LENGTH OP_LINESEQ
syn keyword xsConstant OP_LINK OP_LIST OP_LISTEN OP_LOCALTIME OP_LOCK OP_LOG
syn keyword xsConstant OP_LSLICE OP_LSTAT OP_LT OP_LVAVREF OP_LVREF
syn keyword xsConstant OP_LVREFSLICE OP_MAPSTART OP_MAPWHILE OP_MATCH
syn keyword xsConstant OP_METHOD OP_METHOD_NAMED OP_METHOD_REDIR
syn keyword xsConstant OP_METHOD_REDIR_SUPER OP_METHOD_SUPER OP_MKDIR
syn keyword xsConstant OP_MODULO OP_MSGCTL OP_MSGGET OP_MSGRCV OP_MSGSND
syn keyword xsConstant OP_MULTIDEREF OP_MULTIPLY OP_NBIT_AND OP_NBIT_OR
syn keyword xsConstant OP_NBIT_XOR OP_NCMP OP_NCOMPLEMENT OP_NE OP_NEGATE
syn keyword xsConstant OP_NEXT OP_NEXTSTATE OP_NOT OP_NULL OP_OCT OP_ONCE
syn keyword xsConstant OP_OPEN OP_OPEN_DIR OP_OR OP_ORASSIGN OP_ORD OP_PACK
syn keyword xsConstant OP_PADANY OP_PADAV OP_PADCV OP_PADHV OP_PADRANGE
syn keyword xsConstant OP_PADSV OP_PIPE_OP OP_POP OP_POS OP_POSTDEC
syn keyword xsConstant OP_POSTINC OP_POW OP_PREDEC OP_PREINC OP_PRINT
syn keyword xsConstant OP_PROTOTYPE OP_PRTF OP_PUSH OP_PUSHMARK OP_PUSHRE
syn keyword xsConstant OP_QR OP_QUOTEMETA OP_RAND OP_RANGE OP_RCATLINE
syn keyword xsConstant OP_REACH OP_READ OP_READDIR OP_READLINE OP_READLINK
syn keyword xsConstant OP_RECV OP_REDO OP_REF OP_REFASSIGN OP_REFGEN
syn keyword xsConstant OP_REGCMAYBE OP_REGCOMP OP_REGCRESET OP_RENAME
syn keyword xsConstant OP_REPEAT OP_REQUIRE OP_RESET OP_RETURN OP_REVERSE
syn keyword xsConstant OP_REWINDDIR OP_RIGHT_SHIFT OP_RINDEX OP_RKEYS
syn keyword xsConstant OP_RMDIR OP_RUNCV OP_RV2AV OP_RV2CV OP_RV2GV OP_RV2HV
syn keyword xsConstant OP_RV2SV OP_RVALUES OP_SASSIGN OP_SAY OP_SBIT_AND
syn keyword xsConstant OP_SBIT_OR OP_SBIT_XOR OP_SCALAR OP_SCHOMP OP_SCHOP
syn keyword xsConstant OP_SCMP OP_SCOMPLEMENT OP_SCOPE OP_SEEK OP_SEEKDIR
syn keyword xsConstant OP_SELECT OP_SEMCTL OP_SEMGET OP_SEMOP OP_SEND OP_SEQ
syn keyword xsConstant OP_SETPGRP OP_SETPRIORITY OP_SGE OP_SGRENT OP_SGT
syn keyword xsConstant OP_SHIFT OP_SHMCTL OP_SHMGET OP_SHMREAD OP_SHMWRITE
syn keyword xsConstant OP_SHOSTENT OP_SHUTDOWN OP_SIN OP_SLE OP_SLEEP OP_SLT
syn keyword xsConstant OP_SMARTMATCH OP_SNE OP_SNETENT OP_SOCKET OP_SOCKPAIR
syn keyword xsConstant OP_SORT OP_SPLICE OP_SPLIT OP_SPRINTF OP_SPROTOENT
syn keyword xsConstant OP_SPWENT OP_SQRT OP_SRAND OP_SREFGEN OP_SSELECT
syn keyword xsConstant OP_SSERVENT OP_SSOCKOPT OP_STAT OP_STRINGIFY OP_STUB
syn keyword xsConstant OP_STUDY OP_SUBST OP_SUBSTCONT OP_SUBSTR OP_SUBTRACT
syn keyword xsConstant OP_SYMLINK OP_SYSCALL OP_SYSOPEN OP_SYSREAD OP_SYSSEEK
syn keyword xsConstant OP_SYSTEM OP_SYSWRITE OP_TELL OP_TELLDIR OP_TIE
syn keyword xsConstant OP_TIED OP_TIME OP_TMS OP_TRANS OP_TRANSR OP_TRUNCATE
syn keyword xsConstant OP_UC OP_UCFIRST OP_UMASK OP_UNDEF OP_UNLINK OP_UNPACK
syn keyword xsConstant OP_UNSHIFT OP_UNSTACK OP_UNTIE OP_UTIME OP_VALUES
syn keyword xsConstant OP_VEC OP_WAIT OP_WAITPID OP_WANTARRAY OP_WARN OP_XOR
syn keyword xsConstant OP_max OPf_FOLDED OPf_KIDS OPf_KNOW OPf_LIST OPf_MOD
syn keyword xsConstant OPf_PARENS OPf_REF OPf_SPECIAL OPf_STACKED OPf_WANT
syn keyword xsConstant OPf_WANT_LIST OPf_WANT_SCALAR OPf_WANT_VOID
syn keyword xsConstant OPpALLOW_FAKE OPpARG1_MASK OPpARG2_MASK OPpARG3_MASK
syn keyword xsConstant OPpARG4_MASK OPpASSIGN_BACKWARDS OPpASSIGN_COMMON
syn keyword xsConstant OPpASSIGN_CV_TO_GV OPpCONST_BARE OPpCONST_ENTERED
syn keyword xsConstant OPpCONST_NOVER OPpCONST_SHORTCIRCUIT OPpCONST_STRICT
syn keyword xsConstant OPpCOREARGS_DEREF1 OPpCOREARGS_DEREF2
syn keyword xsConstant OPpCOREARGS_PUSHMARK OPpCOREARGS_SCALARMOD OPpDEREF
syn keyword xsConstant OPpDEREF_AV OPpDEREF_HV OPpDEREF_SV OPpDONT_INIT_GV
syn keyword xsConstant OPpEARLY_CV OPpENTERSUB_AMPER OPpENTERSUB_DB
syn keyword xsConstant OPpENTERSUB_HASTARG OPpENTERSUB_INARGS
syn keyword xsConstant OPpENTERSUB_LVAL_MASK OPpENTERSUB_NOPAREN
syn keyword xsConstant OPpEVAL_BYTES OPpEVAL_COPHH OPpEVAL_HAS_HH
syn keyword xsConstant OPpEVAL_RE_REPARSING OPpEVAL_UNICODE OPpEXISTS_SUB
syn keyword xsConstant OPpFLIP_LINENUM OPpFT_ACCESS OPpFT_AFTER_t
syn keyword xsConstant OPpFT_STACKED OPpFT_STACKING OPpGREP_LEX
syn keyword xsConstant OPpHINT_STRICT_REFS OPpHUSH_VMSISH OPpITER_DEF
syn keyword xsConstant OPpITER_REVERSED OPpLIST_GUESSED OPpLVALUE
syn keyword xsConstant OPpLVAL_DEFER OPpLVAL_INTRO OPpLVREF_AV OPpLVREF_CV
syn keyword xsConstant OPpLVREF_ELEM OPpLVREF_HV OPpLVREF_ITER OPpLVREF_SV
syn keyword xsConstant OPpLVREF_TYPE OPpMAYBE_LVSUB OPpMAYBE_TRUEBOOL
syn keyword xsConstant OPpMAY_RETURN_CONSTANT OPpMULTIDEREF_DELETE
syn keyword xsConstant OPpMULTIDEREF_EXISTS OPpOFFBYONE OPpOPEN_IN_CRLF
syn keyword xsConstant OPpOPEN_IN_RAW OPpOPEN_OUT_CRLF OPpOPEN_OUT_RAW
syn keyword xsConstant OPpOUR_INTRO OPpPADRANGE_COUNTMASK
syn keyword xsConstant OPpPADRANGE_COUNTSHIFT OPpPAD_STATE OPpPV_IS_UTF8
syn keyword xsConstant OPpREFCOUNTED OPpREPEAT_DOLIST OPpREVERSE_INPLACE
syn keyword xsConstant OPpRUNTIME OPpSLICE OPpSLICEWARNING OPpSORT_DESCEND
syn keyword xsConstant OPpSORT_INPLACE OPpSORT_INTEGER OPpSORT_NUMERIC
syn keyword xsConstant OPpSORT_QSORT OPpSORT_REVERSE OPpSORT_STABLE
syn keyword xsConstant OPpSPLIT_IMPLIM OPpSUBSTR_REPL_FIRST OPpTARGET_MY
syn keyword xsConstant OPpTRANS_ALL OPpTRANS_COMPLEMENT OPpTRANS_DELETE
syn keyword xsConstant OPpTRANS_FROM_UTF OPpTRANS_GROWS OPpTRANS_IDENTICAL
syn keyword xsConstant OPpTRANS_SQUASH OPpTRANS_TO_UTF OPpTRUEBOOL
syn keyword xsConstant PERL_MAGIC_READONLY_ACCEPTABLE
syn keyword xsConstant PERL_MAGIC_TYPE_IS_VALUE_MAGIC
syn keyword xsConstant PERL_MAGIC_TYPE_READONLY_ACCEPTABLE
syn keyword xsConstant PERL_MAGIC_UTF8_CACHESIZE PERL_MAGIC_VALUE_MAGIC
syn keyword xsConstant PERL_MAGIC_VTABLE_MASK PERL_MAGIC_arylen
syn keyword xsConstant PERL_MAGIC_arylen_p PERL_MAGIC_backref PERL_MAGIC_bm
syn keyword xsConstant PERL_MAGIC_checkcall PERL_MAGIC_collxfrm
syn keyword xsConstant PERL_MAGIC_dbfile PERL_MAGIC_dbline
syn keyword xsConstant PERL_MAGIC_debugvar PERL_MAGIC_defelem PERL_MAGIC_env
syn keyword xsConstant PERL_MAGIC_envelem PERL_MAGIC_ext PERL_MAGIC_fm
syn keyword xsConstant PERL_MAGIC_hints PERL_MAGIC_hintselem PERL_MAGIC_isa
syn keyword xsConstant PERL_MAGIC_isaelem PERL_MAGIC_lvref PERL_MAGIC_nkeys
syn keyword xsConstant PERL_MAGIC_overload_table PERL_MAGIC_pos PERL_MAGIC_qr
syn keyword xsConstant PERL_MAGIC_regdata PERL_MAGIC_regdatum
syn keyword xsConstant PERL_MAGIC_regex_global PERL_MAGIC_rhash
syn keyword xsConstant PERL_MAGIC_shared PERL_MAGIC_shared_scalar
syn keyword xsConstant PERL_MAGIC_sig PERL_MAGIC_sigelem PERL_MAGIC_substr
syn keyword xsConstant PERL_MAGIC_sv PERL_MAGIC_symtab PERL_MAGIC_taint
syn keyword xsConstant PERL_MAGIC_tied PERL_MAGIC_tiedelem
syn keyword xsConstant PERL_MAGIC_tiedscalar PERL_MAGIC_utf8 PERL_MAGIC_uvar
syn keyword xsConstant PERL_MAGIC_uvar_elem PERL_MAGIC_vec PERL_MAGIC_vstring
syn keyword xsConstant REGEX_ASCII_MORE_RESTRICTED_CHARSET
syn keyword xsConstant REGEX_ASCII_RESTRICTED_CHARSET REGEX_DEPENDS_CHARSET
syn keyword xsConstant REGEX_LOCALE_CHARSET REGEX_UNICODE_CHARSET SB_ATerm
syn keyword xsConstant SB_BOUND SB_CR SB_Close SB_EDGE SB_Extend SB_Format
syn keyword xsConstant SB_LF SB_Lower SB_Numeric SB_OLetter SB_Other
syn keyword xsConstant SB_SContinue SB_STerm SB_Sep SB_Sp SB_Upper SVfARG
syn keyword xsConstant SVf_AMAGIC SVf_BREAK SVf_FAKE SVf_IOK SVf_IVisUV
syn keyword xsConstant SVf_IsCOW SVf_NOK SVf_OK SVf_OOK SVf_POK SVf_PROTECT
syn keyword xsConstant SVf_READONLY SVf_ROK SVf_THINKFIRST SVf_UTF8 SVp_IOK
syn keyword xsConstant SVp_NOK SVp_POK SVp_SCREAM SVpad_OUR SVpad_STATE
syn keyword xsConstant SVpad_TYPED SVpav_REAL SVpav_REIFY SVpbm_TAIL
syn keyword xsConstant SVpbm_VALID SVpgv_GP SVphv_CLONEABLE SVphv_HASKFLAGS
syn keyword xsConstant SVphv_LAZYDEL SVphv_SHAREKEYS SVprv_PCS_IMPORTED
syn keyword xsConstant SVprv_WEAKREF SVs_GMG SVs_OBJECT SVs_PADMY
syn keyword xsConstant SVs_PADSTALE SVs_PADTMP SVs_RMG SVs_SMG SVs_TEMP
syn keyword xsConstant SVt_INVLIST SVt_IV SVt_LAST SVt_NULL SVt_NV SVt_PV
syn keyword xsConstant SVt_PVAV SVt_PVBM SVt_PVCV SVt_PVFM SVt_PVGV SVt_PVHV
syn keyword xsConstant SVt_PVIO SVt_PVIV SVt_PVLV SVt_PVMG SVt_PVNV
syn keyword xsConstant SVt_REGEXP SVt_RV TRADITIONAL_BOUND WB_ALetter
syn keyword xsConstant WB_BOUND WB_CR WB_Double_Quote WB_EDGE WB_Extend
syn keyword xsConstant WB_ExtendNumLet WB_Format WB_Hebrew_Letter WB_Katakana
syn keyword xsConstant WB_LF WB_MidLetter WB_MidNum WB_MidNumLet WB_Newline
syn keyword xsConstant WB_Numeric WB_Other WB_Regional_Indicator
syn keyword xsConstant WB_Single_Quote WB_UNKNOWN XATTRBLOCK XATTRTERM XBLOCK
syn keyword xsConstant XBLOCKTERM XOPERATOR XOPe_xop_class XOPe_xop_desc
syn keyword xsConstant XOPe_xop_name XOPe_xop_peep XOPe_xop_ptr XPOSTDEREF
syn keyword xsConstant XREF XSTATE XTERM XTERMBLOCK XTERMORDORDOR
syn keyword xsConstant _CC_ENUM_ALPHA _CC_ENUM_ALPHANUMERIC _CC_ENUM_ASCII
syn keyword xsConstant _CC_ENUM_BLANK _CC_ENUM_CASED _CC_ENUM_CNTRL
syn keyword xsConstant _CC_ENUM_DIGIT _CC_ENUM_GRAPH _CC_ENUM_LOWER
syn keyword xsConstant _CC_ENUM_PRINT _CC_ENUM_PUNCT _CC_ENUM_SPACE
syn keyword xsConstant _CC_ENUM_UPPER _CC_ENUM_VERTSPACE _CC_ENUM_WORDCHAR
syn keyword xsConstant _CC_ENUM_XDIGIT padtidy_FORMAT padtidy_SUB
syn keyword xsConstant padtidy_SUBCLONE
syn keyword xsException XCPT_CATCH XCPT_RETHROW XCPT_TRY_END XCPT_TRY_START
syn keyword xsException dXCPT
syn keyword xsKeyword ALIAS: BOOT: CASE: CLEANUP: CODE: C_ARGS: DISABLE
syn keyword xsKeyword ENABLE FALLBACK: IN INCLUDE: INIT: INPUT: INTERFACE:
syn keyword xsKeyword INTERFACE_MACRO: IN_OUT IN_OUTLIST MODULE NO_INIT:
syn keyword xsKeyword NO_OUTPUT: OUT OUTLIST OUTPUT: OVERLOAD: PACKAGE
syn keyword xsKeyword POSTCALL: PPCODE: PREFIX PREINIT: PROTOTYPE:
syn keyword xsKeyword PROTOTYPES: REQUIRE: SCOPE: VERSIONCHECK: length
syn keyword xsFunction GetVars Gv_AMupdate PerlIO_clearerr PerlIO_close
syn keyword xsFunction PerlIO_eof PerlIO_error PerlIO_fileno PerlIO_fill
syn keyword xsFunction PerlIO_flush PerlIO_get_base PerlIO_get_bufsiz
syn keyword xsFunction PerlIO_get_cnt PerlIO_get_ptr PerlIO_read PerlIO_seek
syn keyword xsFunction PerlIO_set_cnt PerlIO_set_ptrcnt PerlIO_setlinebuf
syn keyword xsFunction PerlIO_stderr PerlIO_stdin PerlIO_stdout PerlIO_tell
syn keyword xsFunction PerlIO_unread PerlIO_write Perl_GetVars
syn keyword xsFunction Perl_Gv_AMupdate Perl_PerlIO_clearerr
syn keyword xsFunction Perl_PerlIO_close Perl_PerlIO_context_layers
syn keyword xsFunction Perl_PerlIO_eof Perl_PerlIO_error Perl_PerlIO_fileno
syn keyword xsFunction Perl_PerlIO_fill Perl_PerlIO_flush
syn keyword xsFunction Perl_PerlIO_get_base Perl_PerlIO_get_bufsiz
syn keyword xsFunction Perl_PerlIO_get_cnt Perl_PerlIO_get_ptr
syn keyword xsFunction Perl_PerlIO_read Perl_PerlIO_seek Perl_PerlIO_set_cnt
syn keyword xsFunction Perl_PerlIO_set_ptrcnt Perl_PerlIO_setlinebuf
syn keyword xsFunction Perl_PerlIO_stderr Perl_PerlIO_stdin
syn keyword xsFunction Perl_PerlIO_stdout Perl_PerlIO_tell Perl_PerlIO_unread
syn keyword xsFunction Perl_PerlIO_write Perl__get_regclass_nonbitmap_data
syn keyword xsFunction Perl__is_cur_LC_category_utf8
syn keyword xsFunction Perl__is_in_locale_category Perl__is_uni_FOO
syn keyword xsFunction Perl__is_uni_perl_idcont Perl__is_uni_perl_idstart
syn keyword xsFunction Perl__is_utf8_FOO Perl__is_utf8_idcont
syn keyword xsFunction Perl__is_utf8_idstart Perl__is_utf8_mark
syn keyword xsFunction Perl__is_utf8_perl_idcont Perl__is_utf8_perl_idstart
syn keyword xsFunction Perl__is_utf8_xidcont Perl__is_utf8_xidstart
syn keyword xsFunction Perl__new_invlist_C_array Perl__to_uni_fold_flags
syn keyword xsFunction Perl__to_utf8_fold_flags Perl__to_utf8_lower_flags
syn keyword xsFunction Perl__to_utf8_title_flags Perl__to_utf8_upper_flags
syn keyword xsFunction Perl_alloccopstash Perl_amagic_call
syn keyword xsFunction Perl_amagic_deref_call Perl_any_dup
syn keyword xsFunction Perl_apply_attrs_string Perl_atfork_lock
syn keyword xsFunction Perl_atfork_unlock Perl_av_arylen_p Perl_av_clear
syn keyword xsFunction Perl_av_create_and_push Perl_av_create_and_unshift_one
syn keyword xsFunction Perl_av_delete Perl_av_exists Perl_av_extend
syn keyword xsFunction Perl_av_fetch Perl_av_fill Perl_av_iter_p Perl_av_len
syn keyword xsFunction Perl_av_make Perl_av_pop Perl_av_push Perl_av_shift
syn keyword xsFunction Perl_av_store Perl_av_undef Perl_av_unshift
syn keyword xsFunction Perl_block_end Perl_block_gimme Perl_block_start
syn keyword xsFunction Perl_blockhook_register Perl_bytes_cmp_utf8
syn keyword xsFunction Perl_bytes_from_utf8 Perl_bytes_to_utf8 Perl_call_argv
syn keyword xsFunction Perl_call_atexit Perl_call_list Perl_call_method
syn keyword xsFunction Perl_call_pv Perl_call_sv Perl_caller_cx Perl_calloc
syn keyword xsFunction Perl_cast_i32 Perl_cast_iv Perl_cast_ulong
syn keyword xsFunction Perl_cast_uv Perl_ck_entersub_args_list
syn keyword xsFunction Perl_ck_entersub_args_proto
syn keyword xsFunction Perl_ck_entersub_args_proto_or_list Perl_ck_warner
syn keyword xsFunction Perl_ck_warner_d Perl_ckwarn Perl_ckwarn_d
syn keyword xsFunction Perl_clone_params_del Perl_clone_params_new
syn keyword xsFunction Perl_cop_fetch_label Perl_cop_store_label Perl_croak
syn keyword xsFunction Perl_croak_no_modify Perl_croak_nocontext
syn keyword xsFunction Perl_croak_sv Perl_croak_xs_usage Perl_csighandler
syn keyword xsFunction Perl_custom_op_desc Perl_custom_op_name
syn keyword xsFunction Perl_custom_op_register Perl_cv_clone Perl_cv_const_sv
syn keyword xsFunction Perl_cv_get_call_checker Perl_cv_name
syn keyword xsFunction Perl_cv_set_call_checker
syn keyword xsFunction Perl_cv_set_call_checker_flags Perl_cv_undef
syn keyword xsFunction Perl_cx_dump Perl_cx_dup Perl_cxinc Perl_deb
syn keyword xsFunction Perl_deb_nocontext Perl_debop Perl_debprofdump
syn keyword xsFunction Perl_debstack Perl_debstackptrs Perl_delimcpy
syn keyword xsFunction Perl_despatch_signals Perl_die Perl_die_nocontext
syn keyword xsFunction Perl_die_sv Perl_dirp_dup Perl_do_aspawn
syn keyword xsFunction Perl_do_binmode Perl_do_close Perl_do_gv_dump
syn keyword xsFunction Perl_do_gvgv_dump Perl_do_hv_dump Perl_do_join
syn keyword xsFunction Perl_do_magic_dump Perl_do_op_dump Perl_do_open9
syn keyword xsFunction Perl_do_openn Perl_do_pmop_dump Perl_do_spawn
syn keyword xsFunction Perl_do_spawn_nowait Perl_do_sprintf Perl_do_sv_dump
syn keyword xsFunction Perl_doing_taint Perl_doref Perl_dounwind
syn keyword xsFunction Perl_dowantarray Perl_dump_all Perl_dump_c_backtrace
syn keyword xsFunction Perl_dump_eval Perl_dump_form Perl_dump_indent
syn keyword xsFunction Perl_dump_mstats Perl_dump_packsubs Perl_dump_sub
syn keyword xsFunction Perl_dump_vindent Perl_eval_pv Perl_eval_sv
syn keyword xsFunction Perl_fbm_compile Perl_fbm_instr Perl_filter_add
syn keyword xsFunction Perl_filter_del Perl_filter_read Perl_find_runcv
syn keyword xsFunction Perl_find_rundefsv Perl_foldEQ Perl_foldEQ_latin1
syn keyword xsFunction Perl_foldEQ_locale Perl_foldEQ_utf8_flags Perl_form
syn keyword xsFunction Perl_form_nocontext Perl_fp_dup Perl_fprintf_nocontext
syn keyword xsFunction Perl_free_global_struct Perl_free_tmps Perl_get_av
syn keyword xsFunction Perl_get_c_backtrace_dump Perl_get_context Perl_get_cv
syn keyword xsFunction Perl_get_cvn_flags Perl_get_hv Perl_get_mstats
syn keyword xsFunction Perl_get_op_descs Perl_get_op_names Perl_get_ppaddr
syn keyword xsFunction Perl_get_sv Perl_get_vtbl Perl_getcwd_sv Perl_gp_dup
syn keyword xsFunction Perl_gp_free Perl_gp_ref Perl_grok_bin Perl_grok_hex
syn keyword xsFunction Perl_grok_infnan Perl_grok_number
syn keyword xsFunction Perl_grok_number_flags Perl_grok_numeric_radix
syn keyword xsFunction Perl_grok_oct Perl_gv_add_by_type Perl_gv_autoload_pv
syn keyword xsFunction Perl_gv_autoload_pvn Perl_gv_autoload_sv Perl_gv_check
syn keyword xsFunction Perl_gv_const_sv Perl_gv_dump Perl_gv_efullname
syn keyword xsFunction Perl_gv_efullname4 Perl_gv_fetchfile
syn keyword xsFunction Perl_gv_fetchfile_flags Perl_gv_fetchmeth_pv
syn keyword xsFunction Perl_gv_fetchmeth_pv_autoload Perl_gv_fetchmeth_pvn
syn keyword xsFunction Perl_gv_fetchmeth_pvn_autoload Perl_gv_fetchmeth_sv
syn keyword xsFunction Perl_gv_fetchmeth_sv_autoload
syn keyword xsFunction Perl_gv_fetchmethod_autoload
syn keyword xsFunction Perl_gv_fetchmethod_pv_flags
syn keyword xsFunction Perl_gv_fetchmethod_pvn_flags
syn keyword xsFunction Perl_gv_fetchmethod_sv_flags Perl_gv_fetchpv
syn keyword xsFunction Perl_gv_fetchpvn_flags Perl_gv_fetchsv
syn keyword xsFunction Perl_gv_fullname Perl_gv_fullname4 Perl_gv_handler
syn keyword xsFunction Perl_gv_init_pv Perl_gv_init_pvn Perl_gv_init_sv
syn keyword xsFunction Perl_gv_name_set Perl_gv_stashpv Perl_gv_stashpvn
syn keyword xsFunction Perl_gv_stashsv Perl_he_dup Perl_hek_dup
syn keyword xsFunction Perl_hv_assert Perl_hv_clear
syn keyword xsFunction Perl_hv_clear_placeholders Perl_hv_common
syn keyword xsFunction Perl_hv_common_key_len Perl_hv_copy_hints_hv
syn keyword xsFunction Perl_hv_delayfree_ent Perl_hv_eiter_p
syn keyword xsFunction Perl_hv_eiter_set Perl_hv_fill Perl_hv_free_ent
syn keyword xsFunction Perl_hv_iterinit Perl_hv_iterkey Perl_hv_iterkeysv
syn keyword xsFunction Perl_hv_iternext_flags Perl_hv_iternextsv
syn keyword xsFunction Perl_hv_iterval Perl_hv_ksplit Perl_hv_name_set
syn keyword xsFunction Perl_hv_placeholders_get Perl_hv_placeholders_set
syn keyword xsFunction Perl_hv_rand_set Perl_hv_riter_p Perl_hv_riter_set
syn keyword xsFunction Perl_hv_scalar Perl_init_global_struct
syn keyword xsFunction Perl_init_i18nl10n Perl_init_i18nl14n Perl_init_stacks
syn keyword xsFunction Perl_init_tm Perl_instr Perl_intro_my
syn keyword xsFunction Perl_is_invariant_string Perl_is_lvalue_sub
syn keyword xsFunction Perl_is_utf8_string Perl_is_utf8_string_loclen
syn keyword xsFunction Perl_isinfnan Perl_leave_scope Perl_lex_bufutf8
syn keyword xsFunction Perl_lex_discard_to Perl_lex_grow_linestr
syn keyword xsFunction Perl_lex_next_chunk Perl_lex_peek_unichar
syn keyword xsFunction Perl_lex_read_space Perl_lex_read_to
syn keyword xsFunction Perl_lex_read_unichar Perl_lex_start Perl_lex_stuff_pv
syn keyword xsFunction Perl_lex_stuff_pvn Perl_lex_stuff_sv Perl_lex_unstuff
syn keyword xsFunction Perl_load_module Perl_load_module_nocontext
syn keyword xsFunction Perl_looks_like_number Perl_magic_dump Perl_malloc
syn keyword xsFunction Perl_markstack_grow Perl_mess Perl_mess_nocontext
syn keyword xsFunction Perl_mess_sv Perl_mfree Perl_mg_clear Perl_mg_copy
syn keyword xsFunction Perl_mg_dup Perl_mg_find Perl_mg_findext Perl_mg_free
syn keyword xsFunction Perl_mg_free_type Perl_mg_get Perl_mg_magical
syn keyword xsFunction Perl_mg_set Perl_mg_size Perl_mini_mktime
syn keyword xsFunction Perl_moreswitches Perl_mro_get_from_name
syn keyword xsFunction Perl_mro_get_linear_isa Perl_mro_get_private_data
syn keyword xsFunction Perl_mro_method_changed_in Perl_mro_register
syn keyword xsFunction Perl_mro_set_mro Perl_mro_set_private_data
syn keyword xsFunction Perl_my_atof Perl_my_atof2 Perl_my_bcopy Perl_my_bzero
syn keyword xsFunction Perl_my_chsize Perl_my_cxt_index Perl_my_cxt_init
syn keyword xsFunction Perl_my_dirfd Perl_my_exit Perl_my_failure_exit
syn keyword xsFunction Perl_my_fflush_all Perl_my_fork Perl_my_memcmp
syn keyword xsFunction Perl_my_memset Perl_my_pclose Perl_my_popen
syn keyword xsFunction Perl_my_popen_list Perl_my_setenv Perl_my_setlocale
syn keyword xsFunction Perl_my_snprintf Perl_my_socketpair Perl_my_sprintf
syn keyword xsFunction Perl_my_strerror Perl_my_strftime Perl_my_strlcat
syn keyword xsFunction Perl_my_strlcpy Perl_my_vsnprintf Perl_newANONATTRSUB
syn keyword xsFunction Perl_newANONHASH Perl_newANONLIST Perl_newANONSUB
syn keyword xsFunction Perl_newASSIGNOP Perl_newAVREF Perl_newBINOP
syn keyword xsFunction Perl_newCONDOP Perl_newCONSTSUB Perl_newCONSTSUB_flags
syn keyword xsFunction Perl_newCVREF Perl_newDEFSVOP Perl_newFORM
syn keyword xsFunction Perl_newFOROP Perl_newGIVENOP Perl_newGVOP
syn keyword xsFunction Perl_newGVREF Perl_newGVgen_flags Perl_newHVREF
syn keyword xsFunction Perl_newHVhv Perl_newLISTOP Perl_newLOGOP
syn keyword xsFunction Perl_newLOOPEX Perl_newLOOPOP Perl_newMETHOP
syn keyword xsFunction Perl_newMETHOP_named Perl_newMYSUB Perl_newNULLLIST
syn keyword xsFunction Perl_newOP Perl_newPADNAMELIST Perl_newPADNAMEouter
syn keyword xsFunction Perl_newPADNAMEpvn Perl_newPADOP Perl_newPMOP
syn keyword xsFunction Perl_newPROG Perl_newPVOP Perl_newRANGE Perl_newRV
syn keyword xsFunction Perl_newRV_noinc Perl_newSLICEOP Perl_newSTATEOP
syn keyword xsFunction Perl_newSV Perl_newSVOP Perl_newSVREF Perl_newSV_type
syn keyword xsFunction Perl_newSVhek Perl_newSViv Perl_newSVnv Perl_newSVpv
syn keyword xsFunction Perl_newSVpv_share Perl_newSVpvf
syn keyword xsFunction Perl_newSVpvf_nocontext Perl_newSVpvn
syn keyword xsFunction Perl_newSVpvn_flags Perl_newSVpvn_share Perl_newSVrv
syn keyword xsFunction Perl_newSVsv Perl_newSVuv Perl_newUNOP
syn keyword xsFunction Perl_newUNOP_AUX Perl_newWHENOP Perl_newWHILEOP
syn keyword xsFunction Perl_newXS Perl_newXS_flags Perl_new_collate
syn keyword xsFunction Perl_new_ctype Perl_new_numeric Perl_new_stackinfo
syn keyword xsFunction Perl_new_version Perl_ninstr Perl_nothreadhook
syn keyword xsFunction Perl_op_append_elem Perl_op_append_list
syn keyword xsFunction Perl_op_contextualize Perl_op_convert_list
syn keyword xsFunction Perl_op_dump Perl_op_free Perl_op_linklist
syn keyword xsFunction Perl_op_null Perl_op_parent Perl_op_prepend_elem
syn keyword xsFunction Perl_op_refcnt_lock Perl_op_refcnt_unlock
syn keyword xsFunction Perl_op_scope Perl_op_sibling_splice Perl_pack_cat
syn keyword xsFunction Perl_packlist Perl_pad_add_anon Perl_pad_add_name_pv
syn keyword xsFunction Perl_pad_add_name_pvn Perl_pad_add_name_sv
syn keyword xsFunction Perl_pad_alloc Perl_pad_compname_type
syn keyword xsFunction Perl_pad_findmy_pv Perl_pad_findmy_pvn
syn keyword xsFunction Perl_pad_findmy_sv Perl_pad_new Perl_pad_setsv
syn keyword xsFunction Perl_pad_sv Perl_pad_tidy Perl_padnamelist_fetch
syn keyword xsFunction Perl_padnamelist_store Perl_parse_arithexpr
syn keyword xsFunction Perl_parse_barestmt Perl_parse_block
syn keyword xsFunction Perl_parse_fullexpr Perl_parse_fullstmt
syn keyword xsFunction Perl_parse_label Perl_parse_listexpr
syn keyword xsFunction Perl_parse_stmtseq Perl_parse_termexpr Perl_parser_dup
syn keyword xsFunction Perl_pmop_dump Perl_pop_scope Perl_pregcomp
syn keyword xsFunction Perl_pregexec Perl_pregfree Perl_pregfree2
syn keyword xsFunction Perl_prescan_version Perl_printf_nocontext
syn keyword xsFunction Perl_ptr_table_fetch Perl_ptr_table_free
syn keyword xsFunction Perl_ptr_table_new Perl_ptr_table_split
syn keyword xsFunction Perl_ptr_table_store Perl_push_scope Perl_pv_display
syn keyword xsFunction Perl_pv_escape Perl_pv_pretty Perl_pv_uni_display
syn keyword xsFunction Perl_quadmath_format_needed
syn keyword xsFunction Perl_quadmath_format_single Perl_re_compile
syn keyword xsFunction Perl_re_dup_guts Perl_re_intuit_start
syn keyword xsFunction Perl_re_intuit_string Perl_realloc Perl_reentrant_free
syn keyword xsFunction Perl_reentrant_init Perl_reentrant_retry
syn keyword xsFunction Perl_reentrant_size Perl_reg_named_buff_all
syn keyword xsFunction Perl_reg_named_buff_exists Perl_reg_named_buff_fetch
syn keyword xsFunction Perl_reg_named_buff_firstkey
syn keyword xsFunction Perl_reg_named_buff_nextkey Perl_reg_named_buff_scalar
syn keyword xsFunction Perl_regclass_swash Perl_regdump Perl_regdupe_internal
syn keyword xsFunction Perl_regexec_flags Perl_regfree_internal
syn keyword xsFunction Perl_reginitcolors Perl_regnext Perl_repeatcpy
syn keyword xsFunction Perl_require_pv Perl_rninstr Perl_rsignal
syn keyword xsFunction Perl_rsignal_state Perl_runops_debug
syn keyword xsFunction Perl_runops_standard Perl_rv2cv_op_cv Perl_rvpv_dup
syn keyword xsFunction Perl_safesyscalloc Perl_safesysfree Perl_safesysmalloc
syn keyword xsFunction Perl_safesysrealloc Perl_save_I16 Perl_save_I32
syn keyword xsFunction Perl_save_I8 Perl_save_adelete Perl_save_aelem_flags
syn keyword xsFunction Perl_save_alloc Perl_save_aptr Perl_save_ary
syn keyword xsFunction Perl_save_bool Perl_save_clearsv Perl_save_delete
syn keyword xsFunction Perl_save_destructor Perl_save_destructor_x
syn keyword xsFunction Perl_save_generic_pvref Perl_save_generic_svref
syn keyword xsFunction Perl_save_gp Perl_save_hash Perl_save_hdelete
syn keyword xsFunction Perl_save_helem_flags Perl_save_hints Perl_save_hptr
syn keyword xsFunction Perl_save_int Perl_save_item Perl_save_iv
syn keyword xsFunction Perl_save_list Perl_save_long Perl_save_nogv
syn keyword xsFunction Perl_save_padsv_and_mortalize Perl_save_pptr
syn keyword xsFunction Perl_save_pushi32ptr Perl_save_pushptr
syn keyword xsFunction Perl_save_pushptrptr Perl_save_re_context
syn keyword xsFunction Perl_save_scalar Perl_save_set_svflags
syn keyword xsFunction Perl_save_shared_pvref Perl_save_sptr Perl_save_svref
syn keyword xsFunction Perl_save_vptr Perl_savepv Perl_savepvn
syn keyword xsFunction Perl_savesharedpv Perl_savesharedpvn
syn keyword xsFunction Perl_savesharedsvpv Perl_savestack_grow
syn keyword xsFunction Perl_savestack_grow_cnt Perl_savesvpv Perl_scan_bin
syn keyword xsFunction Perl_scan_hex Perl_scan_num Perl_scan_oct
syn keyword xsFunction Perl_scan_version Perl_scan_vstring Perl_seed
syn keyword xsFunction Perl_set_context Perl_set_numeric_local
syn keyword xsFunction Perl_set_numeric_radix Perl_set_numeric_standard
syn keyword xsFunction Perl_setdefout Perl_share_hek Perl_si_dup Perl_sortsv
syn keyword xsFunction Perl_sortsv_flags Perl_ss_dup Perl_stack_grow
syn keyword xsFunction Perl_start_subparse Perl_str_to_version
syn keyword xsFunction Perl_sv_2bool_flags Perl_sv_2cv Perl_sv_2io
syn keyword xsFunction Perl_sv_2iv_flags Perl_sv_2mortal Perl_sv_2nv_flags
syn keyword xsFunction Perl_sv_2pv_flags Perl_sv_2pvbyte Perl_sv_2pvutf8
syn keyword xsFunction Perl_sv_2uv_flags Perl_sv_backoff Perl_sv_bless
syn keyword xsFunction Perl_sv_cat_decode Perl_sv_catpv Perl_sv_catpv_flags
syn keyword xsFunction Perl_sv_catpv_mg Perl_sv_catpvf Perl_sv_catpvf_mg
syn keyword xsFunction Perl_sv_catpvf_mg_nocontext Perl_sv_catpvf_nocontext
syn keyword xsFunction Perl_sv_catpvn_flags Perl_sv_catsv_flags Perl_sv_chop
syn keyword xsFunction Perl_sv_clear Perl_sv_cmp Perl_sv_cmp_flags
syn keyword xsFunction Perl_sv_cmp_locale Perl_sv_cmp_locale_flags
syn keyword xsFunction Perl_sv_collxfrm_flags Perl_sv_copypv_flags
syn keyword xsFunction Perl_sv_dec Perl_sv_dec_nomg Perl_sv_derived_from
syn keyword xsFunction Perl_sv_derived_from_pv Perl_sv_derived_from_pvn
syn keyword xsFunction Perl_sv_derived_from_sv Perl_sv_destroyable
syn keyword xsFunction Perl_sv_does Perl_sv_does_pv Perl_sv_does_pvn
syn keyword xsFunction Perl_sv_does_sv Perl_sv_dump Perl_sv_dup
syn keyword xsFunction Perl_sv_dup_inc Perl_sv_eq_flags
syn keyword xsFunction Perl_sv_force_normal_flags Perl_sv_free
syn keyword xsFunction Perl_sv_get_backrefs Perl_sv_gets Perl_sv_grow
syn keyword xsFunction Perl_sv_inc Perl_sv_inc_nomg Perl_sv_insert_flags
syn keyword xsFunction Perl_sv_isa Perl_sv_isobject Perl_sv_iv Perl_sv_len
syn keyword xsFunction Perl_sv_len_utf8 Perl_sv_magic Perl_sv_magicext
syn keyword xsFunction Perl_sv_newmortal Perl_sv_newref Perl_sv_nosharing
syn keyword xsFunction Perl_sv_nounlocking Perl_sv_nv Perl_sv_peek
syn keyword xsFunction Perl_sv_pos_b2u Perl_sv_pos_b2u_flags Perl_sv_pos_u2b
syn keyword xsFunction Perl_sv_pos_u2b_flags Perl_sv_pvbyten
syn keyword xsFunction Perl_sv_pvbyten_force Perl_sv_pvn
syn keyword xsFunction Perl_sv_pvn_force_flags Perl_sv_pvn_nomg
syn keyword xsFunction Perl_sv_pvutf8n Perl_sv_pvutf8n_force
syn keyword xsFunction Perl_sv_recode_to_utf8 Perl_sv_reftype Perl_sv_replace
syn keyword xsFunction Perl_sv_report_used Perl_sv_reset Perl_sv_rvweaken
syn keyword xsFunction Perl_sv_setiv Perl_sv_setiv_mg Perl_sv_setnv
syn keyword xsFunction Perl_sv_setnv_mg Perl_sv_setpv Perl_sv_setpv_mg
syn keyword xsFunction Perl_sv_setpvf Perl_sv_setpvf_mg
syn keyword xsFunction Perl_sv_setpvf_mg_nocontext Perl_sv_setpvf_nocontext
syn keyword xsFunction Perl_sv_setpviv Perl_sv_setpviv_mg Perl_sv_setpvn
syn keyword xsFunction Perl_sv_setpvn_mg Perl_sv_setref_iv Perl_sv_setref_nv
syn keyword xsFunction Perl_sv_setref_pv Perl_sv_setref_pvn Perl_sv_setref_uv
syn keyword xsFunction Perl_sv_setsv_flags Perl_sv_setsv_mg Perl_sv_setuv
syn keyword xsFunction Perl_sv_setuv_mg Perl_sv_tainted Perl_sv_true
syn keyword xsFunction Perl_sv_uni_display Perl_sv_unmagic Perl_sv_unmagicext
syn keyword xsFunction Perl_sv_unref_flags Perl_sv_untaint Perl_sv_upgrade
syn keyword xsFunction Perl_sv_usepvn_flags Perl_sv_utf8_decode
syn keyword xsFunction Perl_sv_utf8_downgrade Perl_sv_utf8_encode
syn keyword xsFunction Perl_sv_utf8_upgrade_flags_grow Perl_sv_uv
syn keyword xsFunction Perl_sv_vcatpvf Perl_sv_vcatpvf_mg Perl_sv_vcatpvfn
syn keyword xsFunction Perl_sv_vcatpvfn_flags Perl_sv_vsetpvf
syn keyword xsFunction Perl_sv_vsetpvf_mg Perl_sv_vsetpvfn Perl_swash_fetch
syn keyword xsFunction Perl_swash_init Perl_sync_locale Perl_sys_init
syn keyword xsFunction Perl_sys_init3 Perl_sys_intern_clear
syn keyword xsFunction Perl_sys_intern_dup Perl_sys_intern_init Perl_sys_term
syn keyword xsFunction Perl_taint_env Perl_taint_proper Perl_to_uni_lower
syn keyword xsFunction Perl_to_uni_title Perl_to_uni_upper Perl_to_utf8_case
syn keyword xsFunction Perl_unlnk Perl_unpack_str Perl_unpackstring
syn keyword xsFunction Perl_unsharepvn Perl_upg_version Perl_utf16_to_utf8
syn keyword xsFunction Perl_utf16_to_utf8_reversed Perl_utf8_distance
syn keyword xsFunction Perl_utf8_hop Perl_utf8_length Perl_utf8_to_bytes
syn keyword xsFunction Perl_utf8n_to_uvchr Perl_utf8n_to_uvuni
syn keyword xsFunction Perl_uvoffuni_to_utf8_flags Perl_uvuni_to_utf8
syn keyword xsFunction Perl_uvuni_to_utf8_flags Perl_valid_utf8_to_uvchr
syn keyword xsFunction Perl_vcmp Perl_vcroak Perl_vdeb Perl_vform
syn keyword xsFunction Perl_vload_module Perl_vmess Perl_vnewSVpvf
syn keyword xsFunction Perl_vnormal Perl_vnumify Perl_vstringify Perl_vverify
syn keyword xsFunction Perl_vwarn Perl_vwarner Perl_warn Perl_warn_nocontext
syn keyword xsFunction Perl_warn_sv Perl_warner Perl_warner_nocontext
syn keyword xsFunction Perl_whichsig_pv Perl_whichsig_pvn Perl_whichsig_sv
syn keyword xsFunction Perl_wrap_op_checker _get_regclass_nonbitmap_data
syn keyword xsFunction _is_cur_LC_category_utf8 _is_in_locale_category
syn keyword xsFunction _is_uni_FOO _is_uni_perl_idcont _is_uni_perl_idstart
syn keyword xsFunction _is_utf8_FOO _is_utf8_char_slow _is_utf8_idcont
syn keyword xsFunction _is_utf8_idstart _is_utf8_mark _is_utf8_perl_idcont
syn keyword xsFunction _is_utf8_perl_idstart _is_utf8_xidcont
syn keyword xsFunction _is_utf8_xidstart _new_invlist_C_array
syn keyword xsFunction _to_uni_fold_flags _to_utf8_fold_flags
syn keyword xsFunction _to_utf8_lower_flags _to_utf8_title_flags
syn keyword xsFunction _to_utf8_upper_flags alloccopstash amagic_call
syn keyword xsFunction amagic_deref_call any_dup append_utf8_from_native_byte
syn keyword xsFunction apply_attrs_string atfork_lock atfork_unlock av_clear
syn keyword xsFunction av_delete av_exists av_extend av_fetch av_fill av_len
syn keyword xsFunction av_make av_pop av_push av_shift av_store av_top_index
syn keyword xsFunction av_undef av_unshift block_end block_gimme block_start
syn keyword xsFunction bytes_cmp_utf8 bytes_from_utf8 bytes_to_utf8 call_argv
syn keyword xsFunction call_atexit call_list call_method call_pv call_sv
syn keyword xsFunction caller_cx cast_i32 cast_iv cast_ulong cast_uv
syn keyword xsFunction ck_entersub_args_list ck_entersub_args_proto
syn keyword xsFunction ck_entersub_args_proto_or_list ck_warner ck_warner_d
syn keyword xsFunction croak croak_memory_wrap croak_no_modify
syn keyword xsFunction croak_nocontext croak_sv croak_xs_usage csighandler
syn keyword xsFunction custom_op_desc custom_op_name cv_clone cv_const_sv
syn keyword xsFunction cv_get_call_checker cv_name cv_set_call_checker
syn keyword xsFunction cv_set_call_checker_flags cv_undef cx_dump cx_dup
syn keyword xsFunction cxinc deb deb_nocontext debop debprofdump debstack
syn keyword xsFunction debstackptrs delimcpy despatch_signals die
syn keyword xsFunction die_nocontext die_sv dirp_dup do_aspawn do_binmode
syn keyword xsFunction do_close do_gv_dump do_gvgv_dump do_hv_dump do_join
syn keyword xsFunction do_magic_dump do_op_dump do_open9 do_openn
syn keyword xsFunction do_pmop_dump do_spawn do_spawn_nowait do_sprintf
syn keyword xsFunction do_sv_dump doing_taint doref dounwind dowantarray
syn keyword xsFunction dump_all dump_c_backtrace dump_eval dump_form
syn keyword xsFunction dump_indent dump_mstats dump_packsubs dump_sub
syn keyword xsFunction dump_vindent eval_pv eval_sv fbm_compile fbm_instr
syn keyword xsFunction filter_add filter_del filter_read find_runcv
syn keyword xsFunction find_rundefsv foldEQ foldEQ_latin1 foldEQ_locale
syn keyword xsFunction foldEQ_utf8_flags form form_nocontext fp_dup
syn keyword xsFunction fprintf_nocontext free_global_struct free_tmps get_av
syn keyword xsFunction get_c_backtrace_dump get_context get_cv get_cvn_flags
syn keyword xsFunction get_hv get_mstats get_op_descs get_op_names get_ppaddr
syn keyword xsFunction get_sv get_vtbl getcwd_sv gp_dup gp_free gp_ref
syn keyword xsFunction grok_bin grok_hex grok_infnan grok_number
syn keyword xsFunction grok_number_flags grok_numeric_radix grok_oct
syn keyword xsFunction gv_add_by_type gv_autoload_pv gv_autoload_pvn
syn keyword xsFunction gv_autoload_sv gv_check gv_const_sv gv_dump
syn keyword xsFunction gv_efullname gv_efullname4 gv_fetchfile
syn keyword xsFunction gv_fetchfile_flags gv_fetchmeth_pv
syn keyword xsFunction gv_fetchmeth_pv_autoload gv_fetchmeth_pvn
syn keyword xsFunction gv_fetchmeth_pvn_autoload gv_fetchmeth_sv
syn keyword xsFunction gv_fetchmeth_sv_autoload gv_fetchmethod_autoload
syn keyword xsFunction gv_fetchmethod_pv_flags gv_fetchmethod_pvn_flags
syn keyword xsFunction gv_fetchmethod_sv_flags gv_fetchpv gv_fetchpvn_flags
syn keyword xsFunction gv_fetchsv gv_fullname gv_fullname4 gv_handler
syn keyword xsFunction gv_init_pv gv_init_pvn gv_init_sv gv_name_set
syn keyword xsFunction gv_stashpv gv_stashpvn gv_stashsv he_dup hek_dup
syn keyword xsFunction hv_clear hv_clear_placeholders hv_common
syn keyword xsFunction hv_common_key_len hv_copy_hints_hv hv_delayfree_ent
syn keyword xsFunction hv_free_ent hv_iterinit hv_iterkey hv_iterkeysv
syn keyword xsFunction hv_iternext_flags hv_iternextsv hv_iterval hv_ksplit
syn keyword xsFunction hv_name_set hv_rand_set hv_scalar init_global_struct
syn keyword xsFunction init_i18nl10n init_i18nl14n init_stacks init_tm instr
syn keyword xsFunction intro_my is_invariant_string is_lvalue_sub
syn keyword xsFunction is_safe_syscall is_utf8_string is_utf8_string_loclen
syn keyword xsFunction isinfnan leave_scope lex_bufutf8 lex_discard_to
syn keyword xsFunction lex_grow_linestr lex_next_chunk lex_peek_unichar
syn keyword xsFunction lex_read_space lex_read_to lex_read_unichar lex_start
syn keyword xsFunction lex_stuff_pv lex_stuff_pvn lex_stuff_sv lex_unstuff
syn keyword xsFunction load_module load_module_nocontext looks_like_number
syn keyword xsFunction magic_dump markstack_grow mess mess_nocontext mess_sv
syn keyword xsFunction mg_clear mg_copy mg_dup mg_find mg_findext mg_free
syn keyword xsFunction mg_free_type mg_get mg_magical mg_set mg_size
syn keyword xsFunction mini_mktime moreswitches mro_get_linear_isa
syn keyword xsFunction mro_method_changed_in my_atof my_atof2 my_bcopy
syn keyword xsFunction my_bzero my_chsize my_dirfd my_exit my_failure_exit
syn keyword xsFunction my_fflush_all my_fork my_memcmp my_memset my_pclose
syn keyword xsFunction my_popen my_popen_list my_setenv my_setlocale
syn keyword xsFunction my_socketpair my_strerror my_strftime newANONATTRSUB
syn keyword xsFunction newANONHASH newANONLIST newANONSUB newASSIGNOP
syn keyword xsFunction newAVREF newBINOP newCONDOP newCONSTSUB
syn keyword xsFunction newCONSTSUB_flags newCVREF newDEFSVOP newFORM newFOROP
syn keyword xsFunction newGIVENOP newGVOP newGVREF newGVgen_flags newHVREF
syn keyword xsFunction newHVhv newLISTOP newLOGOP newLOOPEX newLOOPOP
syn keyword xsFunction newMETHOP newMETHOP_named newMYSUB newNULLLIST newOP
syn keyword xsFunction newPADNAMELIST newPADNAMEouter newPADNAMEpvn newPADOP
syn keyword xsFunction newPMOP newPROG newPVOP newRANGE newRV newRV_noinc
syn keyword xsFunction newSLICEOP newSTATEOP newSV newSVOP newSVREF
syn keyword xsFunction newSV_type newSVhek newSViv newSVnv newSVpv
syn keyword xsFunction newSVpv_share newSVpvf newSVpvf_nocontext newSVpvn
syn keyword xsFunction newSVpvn_flags newSVpvn_share newSVrv newSVsv newSVuv
syn keyword xsFunction newUNOP newUNOP_AUX newWHENOP newWHILEOP newXS
syn keyword xsFunction newXS_flags new_collate new_ctype new_numeric
syn keyword xsFunction new_stackinfo new_version ninstr nothreadhook
syn keyword xsFunction op_append_elem op_append_list op_contextualize
syn keyword xsFunction op_convert_list op_dump op_free op_linklist op_null
syn keyword xsFunction op_parent op_prepend_elem op_refcnt_lock
syn keyword xsFunction op_refcnt_unlock op_scope op_sibling_splice pack_cat
syn keyword xsFunction packlist pad_add_anon pad_add_name_pv pad_add_name_pvn
syn keyword xsFunction pad_add_name_sv pad_alloc pad_compname_type
syn keyword xsFunction pad_findmy_pv pad_findmy_pvn pad_findmy_sv pad_new
syn keyword xsFunction pad_setsv pad_sv pad_tidy padnamelist_fetch
syn keyword xsFunction padnamelist_store parse_arithexpr parse_barestmt
syn keyword xsFunction parse_block parse_fullexpr parse_fullstmt parse_label
syn keyword xsFunction parse_listexpr parse_stmtseq parse_termexpr parser_dup
syn keyword xsFunction pmop_dump pop_scope pregcomp pregexec pregfree
syn keyword xsFunction pregfree2 prescan_version printf_nocontext
syn keyword xsFunction ptr_table_fetch ptr_table_free ptr_table_new
syn keyword xsFunction ptr_table_split ptr_table_store push_scope pv_display
syn keyword xsFunction pv_escape pv_pretty pv_uni_display
syn keyword xsFunction quadmath_format_needed quadmath_format_single
syn keyword xsFunction re_compile re_dup_guts re_intuit_start
syn keyword xsFunction re_intuit_string reentrant_free reentrant_init
syn keyword xsFunction reentrant_retry reentrant_size reg_named_buff_all
syn keyword xsFunction reg_named_buff_exists reg_named_buff_fetch
syn keyword xsFunction reg_named_buff_firstkey reg_named_buff_nextkey
syn keyword xsFunction reg_named_buff_scalar regclass_swash regdump
syn keyword xsFunction regdupe_internal regexec_flags regfree_internal
syn keyword xsFunction reginitcolors regnext repeatcpy require_pv rninstr
syn keyword xsFunction rsignal rsignal_state runops_debug runops_standard
syn keyword xsFunction rv2cv_op_cv rvpv_dup safesyscalloc safesysfree
syn keyword xsFunction safesysmalloc safesysrealloc save_I16 save_I32 save_I8
syn keyword xsFunction save_adelete save_aelem_flags save_alloc save_aptr
syn keyword xsFunction save_ary save_bool save_clearsv save_delete
syn keyword xsFunction save_destructor save_destructor_x save_generic_pvref
syn keyword xsFunction save_generic_svref save_gp save_hash save_hdelete
syn keyword xsFunction save_helem_flags save_hints save_hptr save_int
syn keyword xsFunction save_item save_iv save_list save_long save_nogv
syn keyword xsFunction save_padsv_and_mortalize save_pptr save_pushi32ptr
syn keyword xsFunction save_pushptr save_pushptrptr save_re_context
syn keyword xsFunction save_scalar save_set_svflags save_shared_pvref
syn keyword xsFunction save_sptr save_svref save_vptr savepv savepvn
syn keyword xsFunction savesharedpv savesharedpvn savesharedsvpv
syn keyword xsFunction savestack_grow savestack_grow_cnt savesvpv scan_bin
syn keyword xsFunction scan_hex scan_num scan_oct scan_version scan_vstring
syn keyword xsFunction seed set_context set_numeric_local set_numeric_radix
syn keyword xsFunction set_numeric_standard setdefout share_hek si_dup sortsv
syn keyword xsFunction sortsv_flags ss_dup stack_grow start_subparse
syn keyword xsFunction str_to_version sv_2bool_flags sv_2cv sv_2io
syn keyword xsFunction sv_2iv_flags sv_2mortal sv_2nv_flags sv_2pv_flags
syn keyword xsFunction sv_2pvbyte sv_2pvutf8 sv_2uv_flags sv_backoff sv_bless
syn keyword xsFunction sv_cat_decode sv_catpv sv_catpv_flags sv_catpv_mg
syn keyword xsFunction sv_catpvf sv_catpvf_mg sv_catpvf_mg_nocontext
syn keyword xsFunction sv_catpvf_nocontext sv_catpvn_flags sv_catsv_flags
syn keyword xsFunction sv_chop sv_clear sv_cmp_flags sv_cmp_locale_flags
syn keyword xsFunction sv_collxfrm_flags sv_copypv_flags sv_dec sv_dec_nomg
syn keyword xsFunction sv_derived_from sv_derived_from_pv sv_derived_from_pvn
syn keyword xsFunction sv_derived_from_sv sv_destroyable sv_does sv_does_pv
syn keyword xsFunction sv_does_pvn sv_does_sv sv_dump sv_dup sv_dup_inc
syn keyword xsFunction sv_eq_flags sv_force_normal_flags sv_free
syn keyword xsFunction sv_get_backrefs sv_gets sv_grow sv_inc sv_inc_nomg
syn keyword xsFunction sv_insert_flags sv_isa sv_isobject sv_iv sv_len
syn keyword xsFunction sv_len_utf8 sv_magic sv_magicext sv_newmortal
syn keyword xsFunction sv_newref sv_nosharing sv_nounlocking sv_nv sv_peek
syn keyword xsFunction sv_pos_b2u sv_pos_b2u_flags sv_pos_u2b
syn keyword xsFunction sv_pos_u2b_flags sv_pvbyten sv_pvbyten_force sv_pvn
syn keyword xsFunction sv_pvn_force_flags sv_pvn_nomg sv_pvutf8n
syn keyword xsFunction sv_pvutf8n_force sv_recode_to_utf8 sv_reftype
syn keyword xsFunction sv_replace sv_report_used sv_reset sv_rvweaken
syn keyword xsFunction sv_setiv sv_setiv_mg sv_setnv sv_setnv_mg sv_setpv
syn keyword xsFunction sv_setpv_mg sv_setpvf sv_setpvf_mg
syn keyword xsFunction sv_setpvf_mg_nocontext sv_setpvf_nocontext sv_setpviv
syn keyword xsFunction sv_setpviv_mg sv_setpvn sv_setpvn_mg sv_setref_iv
syn keyword xsFunction sv_setref_nv sv_setref_pv sv_setref_pvn sv_setref_uv
syn keyword xsFunction sv_setsv_flags sv_setsv_mg sv_setuv sv_setuv_mg
syn keyword xsFunction sv_tainted sv_true sv_uni_display sv_unmagic
syn keyword xsFunction sv_unmagicext sv_unref_flags sv_untaint sv_upgrade
syn keyword xsFunction sv_usepvn_flags sv_utf8_decode sv_utf8_downgrade
syn keyword xsFunction sv_utf8_encode sv_utf8_upgrade_flags_grow sv_uv
syn keyword xsFunction sv_vcatpvf sv_vcatpvf_mg sv_vcatpvfn sv_vcatpvfn_flags
syn keyword xsFunction sv_vsetpvf sv_vsetpvf_mg sv_vsetpvfn swash_fetch
syn keyword xsFunction swash_init sync_locale sys_intern_clear sys_intern_dup
syn keyword xsFunction sys_intern_init taint_env taint_proper to_uni_lower
syn keyword xsFunction to_uni_title to_uni_upper to_utf8_case unlnk
syn keyword xsFunction unpack_str unpackstring unsharepvn upg_version
syn keyword xsFunction utf16_to_utf8 utf16_to_utf8_reversed utf8_distance
syn keyword xsFunction utf8_hop utf8_length utf8_to_bytes utf8n_to_uvchr
syn keyword xsFunction utf8n_to_uvuni uvoffuni_to_utf8_flags uvuni_to_utf8
syn keyword xsFunction uvuni_to_utf8_flags valid_utf8_to_uvchr vcmp vcroak
syn keyword xsFunction vdeb vform vload_module vmess vnewSVpvf vnormal
syn keyword xsFunction vnumify vstringify vverify vwarn vwarner warn
syn keyword xsFunction warn_nocontext warn_sv warner warner_nocontext
syn keyword xsFunction whichsig_pv whichsig_pvn whichsig_sv wrap_op_checker
syn keyword xsVariable MARK MY_CXT ORIGMARK PL_I PL_No PL_Vars PL_VarsPtr
syn keyword xsVariable PL_Yes PL_a2e PL_bincompat_options PL_bitcount
syn keyword xsVariable PL_block_type PL_bufend PL_bufptr PL_charclass
syn keyword xsVariable PL_check PL_copline PL_core_reg_engine PL_cshname
syn keyword xsVariable PL_e2a PL_e2utf PL_error_count PL_expect PL_fold
syn keyword xsVariable PL_fold_latin1 PL_fold_locale PL_force_link_funcs
syn keyword xsVariable PL_freq PL_global_struct_size PL_hexdigit PL_in_my
syn keyword xsVariable PL_in_my_stash PL_interp_size PL_interp_size_5_18_0
syn keyword xsVariable PL_last_lop PL_last_lop_op PL_last_uni PL_latin1_lc
syn keyword xsVariable PL_lex_allbrackets PL_lex_brackets PL_lex_brackstack
syn keyword xsVariable PL_lex_casemods PL_lex_casestack PL_lex_defer
syn keyword xsVariable PL_lex_dojoin PL_lex_fakeeof PL_lex_formbrack
syn keyword xsVariable PL_lex_inpat PL_lex_inwhat PL_lex_op PL_lex_repl
syn keyword xsVariable PL_lex_starts PL_lex_state PL_lex_stuff PL_linestart
syn keyword xsVariable PL_linestr PL_magic_data PL_magic_vtable_names
syn keyword xsVariable PL_memory_wrap PL_mod_latin1_uc PL_multi_close
syn keyword xsVariable PL_multi_end PL_multi_open PL_multi_start PL_nexttoke
syn keyword xsVariable PL_nexttype PL_nextval PL_no_aelem PL_no_dir_func
syn keyword xsVariable PL_no_func PL_no_helem_sv PL_no_localize_ref PL_no_mem
syn keyword xsVariable PL_no_modify PL_no_myglob PL_no_security
syn keyword xsVariable PL_no_sock_func PL_no_symref PL_no_symref_sv
syn keyword xsVariable PL_no_usym PL_no_wrongref PL_oldbufptr PL_oldoldbufptr
syn keyword xsVariable PL_op_desc PL_op_name PL_op_private_bitdef_ix
syn keyword xsVariable PL_op_private_bitdefs PL_op_private_bitfields
syn keyword xsVariable PL_op_private_labels PL_op_private_valid PL_opargs
syn keyword xsVariable PL_phase_names PL_ppaddr PL_preambled
syn keyword xsVariable PL_reg_extflags_name PL_reg_intflags_name PL_reg_name
syn keyword xsVariable PL_regkind PL_revision PL_rsfp PL_rsfp_filters
syn keyword xsVariable PL_runops_dbg PL_runops_std PL_sh_path PL_sig_name
syn keyword xsVariable PL_sig_num PL_simple PL_simple_bitmask PL_sublex_info
syn keyword xsVariable PL_subversion PL_tokenbuf PL_utf2e PL_utf8skip
syn keyword xsVariable PL_uudmap PL_uuemap PL_valid_types_IVX
syn keyword xsVariable PL_valid_types_IV_set PL_valid_types_NVX
syn keyword xsVariable PL_valid_types_NV_set PL_valid_types_PVX
syn keyword xsVariable PL_valid_types_RV PL_varies PL_varies_bitmask
syn keyword xsVariable PL_version PL_warn_nl PL_warn_nosemi PL_warn_reserved
syn keyword xsVariable PL_warn_uninit PL_warn_uninit_sv RETVAL SP TARG
syn keyword xsVariable _aMY_CXT _aTHX aMY_CXT aMY_CXT_ aTHX aTHX_ items
syn keyword xsMacro ABORT ACCEPT ADDOP AHOCORASICK AHOCORASICKC
syn keyword xsMacro ALLOC_THREAD_KEY AMG_CALLun AMG_CALLunary AMGf_assign
syn keyword xsMacro AMGf_noleft AMGf_noright AMGf_numarg AMGf_numeric
syn keyword xsMacro AMGf_set AMGf_unary AMGf_want_list AMGfallNEVER AMGfallNO
syn keyword xsMacro AMGfallYES AMT_AMAGIC AMT_AMAGIC_off AMT_AMAGIC_on
syn keyword xsMacro AMTf_AMAGIC ANDAND ANDOP ANGSTROM_SIGN ANONSUB ANYOF
syn keyword xsMacro ANYOFL ANYOF_ALNUM ANYOF_ALNUML ANYOF_ALPHA
syn keyword xsMacro ANYOF_ALPHANUMERIC ANYOF_ASCII ANYOF_BIT ANYOF_BITMAP
syn keyword xsMacro ANYOF_BITMAP_BYTE ANYOF_BITMAP_CLEAR
syn keyword xsMacro ANYOF_BITMAP_CLEARALL ANYOF_BITMAP_SET
syn keyword xsMacro ANYOF_BITMAP_SETALL ANYOF_BITMAP_SIZE ANYOF_BITMAP_TEST
syn keyword xsMacro ANYOF_BITMAP_ZERO ANYOF_BLANK ANYOF_CASED
syn keyword xsMacro ANYOF_CLASS_CLEAR ANYOF_CLASS_OR ANYOF_CLASS_SET
syn keyword xsMacro ANYOF_CLASS_SETALL ANYOF_CLASS_TEST
syn keyword xsMacro ANYOF_CLASS_TEST_ANY_SET ANYOF_CLASS_ZERO ANYOF_CNTRL
syn keyword xsMacro ANYOF_COMMON_FLAGS ANYOF_DIGIT ANYOF_FLAGS
syn keyword xsMacro ANYOF_FLAGS_ALL ANYOF_FOLD_SHARP_S ANYOF_GRAPH
syn keyword xsMacro ANYOF_HAS_NONBITMAP_NON_UTF8_MATCHES
syn keyword xsMacro ANYOF_HAS_UTF8_NONBITMAP_MATCHES ANYOF_HORIZWS
syn keyword xsMacro ANYOF_INVERT ANYOF_LOCALE_FLAGS ANYOF_LOC_FOLD
syn keyword xsMacro ANYOF_LOWER ANYOF_MATCHES_ALL_ABOVE_BITMAP
syn keyword xsMacro ANYOF_MATCHES_ALL_NON_UTF8_NON_ASCII ANYOF_MATCHES_POSIXL
syn keyword xsMacro ANYOF_MAX ANYOF_NALNUM ANYOF_NALNUML ANYOF_NALPHA
syn keyword xsMacro ANYOF_NALPHANUMERIC ANYOF_NASCII ANYOF_NBLANK
syn keyword xsMacro ANYOF_NCASED ANYOF_NCNTRL ANYOF_NDIGIT ANYOF_NGRAPH
syn keyword xsMacro ANYOF_NHORIZWS ANYOF_NLOWER ANYOF_NPRINT ANYOF_NPUNCT
syn keyword xsMacro ANYOF_NSPACE ANYOF_NSPACEL ANYOF_NUPPER ANYOF_NVERTWS
syn keyword xsMacro ANYOF_NWORDCHAR ANYOF_NXDIGIT ANYOF_ONLY_HAS_BITMAP
syn keyword xsMacro ANYOF_POSIXL_AND ANYOF_POSIXL_CLEAR ANYOF_POSIXL_MAX
syn keyword xsMacro ANYOF_POSIXL_OR ANYOF_POSIXL_SET ANYOF_POSIXL_SETALL
syn keyword xsMacro ANYOF_POSIXL_SKIP ANYOF_POSIXL_SSC_TEST_ALL_SET
syn keyword xsMacro ANYOF_POSIXL_SSC_TEST_ANY_SET ANYOF_POSIXL_TEST
syn keyword xsMacro ANYOF_POSIXL_TEST_ALL_SET ANYOF_POSIXL_TEST_ANY_SET
syn keyword xsMacro ANYOF_POSIXL_ZERO ANYOF_PRINT ANYOF_PUNCT ANYOF_SKIP
syn keyword xsMacro ANYOF_SPACE ANYOF_SPACEL ANYOF_UNIPROP ANYOF_UPPER
syn keyword xsMacro ANYOF_VERTWS ANYOF_WARN_SUPER ANYOF_WORDCHAR ANYOF_XDIGIT
syn keyword xsMacro ARCHLIB ARCHLIB_EXP ARCHNAME ARG ARG1 ARG1_LOC ARG1_SET
syn keyword xsMacro ARG2 ARG2L ARG2L_LOC ARG2L_SET ARG2_LOC ARG2_SET ARGTARG
syn keyword xsMacro ARG_LOC ARG_SET ARG_VALUE ARG__SET ARROW
syn keyword xsMacro ASCII_MORE_RESTRICT_PAT_MODS ASCII_RESTRICT_PAT_MOD
syn keyword xsMacro ASCII_RESTRICT_PAT_MODS ASCII_TO_NATIVE ASCTIME_R_PROTO
syn keyword xsMacro ASSERT_CURPAD_ACTIVE ASSERT_CURPAD_LEGAL ASSIGNOP ASSUME
syn keyword xsMacro Atof Atol Atoul AvALLOC AvARRAY AvARYLEN AvFILL AvFILLp
syn keyword xsMacro AvMAX AvREAL AvREALISH AvREAL_off AvREAL_on AvREAL_only
syn keyword xsMacro AvREIFY AvREIFY_off AvREIFY_on AvREIFY_only BADVERSION
syn keyword xsMacro BASEOP BHKf_bhk_eval BHKf_bhk_post_end BHKf_bhk_pre_end
syn keyword xsMacro BHKf_bhk_start BIN BIN_EXP BITANDOP BITMAP_BYTE
syn keyword xsMacro BITMAP_TEST BITOROP BIT_BUCKET BIT_DIGITS BOL
syn keyword xsMacro BOM_UTF8_FIRST_BYTE BOM_UTF8_TAIL BOUND BOUNDA BOUNDL
syn keyword xsMacro BOUNDU BRANCH BRANCHJ BRANCH_next BRANCH_next_fail
syn keyword xsMacro BSD_GETPGRP BSD_SETPGRP BSDish BUFSIZ BYTEORDER
syn keyword xsMacro BhkDISABLE BhkENABLE BhkENTRY BhkENTRY_set BhkFLAGS Bit
syn keyword xsMacro BmFLAGS BmPREVIOUS BmRARE BmUSEFUL CALLREGCOMP
syn keyword xsMacro CALLREGCOMP_ENG CALLREGDUPE CALLREGDUPE_PVT CALLREGEXEC
syn keyword xsMacro CALLREGFREE CALLREGFREE_PVT CALLREG_INTUIT_START
syn keyword xsMacro CALLREG_INTUIT_STRING CALLREG_NAMED_BUFF_ALL
syn keyword xsMacro CALLREG_NAMED_BUFF_CLEAR CALLREG_NAMED_BUFF_COUNT
syn keyword xsMacro CALLREG_NAMED_BUFF_DELETE CALLREG_NAMED_BUFF_EXISTS
syn keyword xsMacro CALLREG_NAMED_BUFF_FETCH CALLREG_NAMED_BUFF_FIRSTKEY
syn keyword xsMacro CALLREG_NAMED_BUFF_NEXTKEY CALLREG_NAMED_BUFF_SCALAR
syn keyword xsMacro CALLREG_NAMED_BUFF_STORE CALLREG_NUMBUF_FETCH
syn keyword xsMacro CALLREG_NUMBUF_LENGTH CALLREG_NUMBUF_STORE
syn keyword xsMacro CALLREG_PACKAGE CALLRUNOPS CALL_BLOCK_HOOKS
syn keyword xsMacro CALL_CHECKER_REQUIRE_GV CALL_FPTR CANY CAN_COW_FLAGS
syn keyword xsMacro CAN_COW_MASK CAN_PROTOTYPE CAN_VAPROTO
syn keyword xsMacro CASE_STD_PMMOD_FLAGS_PARSE_SET CASTFLAGS CASTNEGFLOAT
syn keyword xsMacro CAT2 CATCH_GET CATCH_SET CHANGE_MULTICALL_FLAGS CHARBITS
syn keyword xsMacro CHARSET_PAT_MODS CHECK_MALLOC_TAINT
syn keyword xsMacro CHECK_MALLOC_TOO_LATE_FOR CHECK_MALLOC_TOO_LATE_FOR_
syn keyword xsMacro CLEAR_ARGARRAY CLEAR_ERRSV CLONEf_CLONE_HOST
syn keyword xsMacro CLONEf_COPY_STACKS CLONEf_JOIN_IN CLONEf_KEEP_PTR_TABLE
syn keyword xsMacro CLOSE CLUMP CLUMP_2IV CLUMP_2UV COLONATTR
syn keyword xsMacro COMBINING_GRAVE_ACCENT_UTF8 COMMIT COMMIT_next
syn keyword xsMacro COMMIT_next_fail COND_BROADCAST COND_DESTROY COND_INIT
syn keyword xsMacro COND_SIGNAL COND_WAIT CONTINUE CONTINUE_PAT_MOD
syn keyword xsMacro COPHH_KEY_UTF8 COP_SEQMAX_INC COP_SEQ_RANGE_HIGH
syn keyword xsMacro COP_SEQ_RANGE_LOW CPERLarg CPERLarg_ CPERLscope CPPLAST
syn keyword xsMacro CPPMINUS CPPRUN CPPSTDIN CRYPT_R_PROTO CR_NATIVE CSH
syn keyword xsMacro CTERMID_R_PROTO CTIME_R_PROTO CTYPE256 CURLY CURLYM
syn keyword xsMacro CURLYM_A CURLYM_A_fail CURLYM_B CURLYM_B_fail CURLYN
syn keyword xsMacro CURLYX CURLYX_end CURLYX_end_fail CURLY_B_max
syn keyword xsMacro CURLY_B_max_fail CURLY_B_min CURLY_B_min_fail
syn keyword xsMacro CURLY_B_min_known CURLY_B_min_known_fail
syn keyword xsMacro CURRENT_FEATURE_BUNDLE CURRENT_HINTS CUTGROUP
syn keyword xsMacro CUTGROUP_next CUTGROUP_next_fail CV_NAME_NOTQUAL
syn keyword xsMacro CV_UNDEF_KEEP_NAME CVf_ANON CVf_ANONCONST CVf_AUTOLOAD
syn keyword xsMacro CVf_BUILTIN_ATTRS CVf_CLONE CVf_CLONED CVf_CONST
syn keyword xsMacro CVf_CVGV_RC CVf_DYNFILE CVf_HASEVAL CVf_ISXSUB
syn keyword xsMacro CVf_LEXICAL CVf_LVALUE CVf_METHOD CVf_NAMED CVf_NODEBUG
syn keyword xsMacro CVf_SLABBED CVf_UNIQUE CVf_WEAKOUTSIDE CXINC CXTYPEMASK
syn keyword xsMacro CX_CURPAD_SAVE CX_CURPAD_SV CXp_FOR_DEF CXp_FOR_LVREF
syn keyword xsMacro CXp_HASARGS CXp_MULTICALL CXp_ONCE CXp_REAL CXp_SUB_RE
syn keyword xsMacro CXp_SUB_RE_FAKE CXp_TRYBLOCK C_ARRAY_END C_ARRAY_LENGTH
syn keyword xsMacro C_FAC_POSIX CopFILE CopFILEAV CopFILEAVx CopFILEGV
syn keyword xsMacro CopFILEGV_set CopFILESV CopFILE_free CopFILE_set
syn keyword xsMacro CopFILE_setn CopHINTHASH_get CopHINTHASH_set CopHINTS_get
syn keyword xsMacro CopHINTS_set CopLABEL CopLABEL_alloc CopLABEL_len
syn keyword xsMacro CopLABEL_len_flags CopLINE CopLINE_dec CopLINE_inc
syn keyword xsMacro CopLINE_set CopSTASH CopSTASHPV CopSTASHPV_set
syn keyword xsMacro CopSTASH_eq CopSTASH_ne CopSTASH_set Copy CopyD CowREFCNT
syn keyword xsMacro Ctl CvANON CvANONCONST CvANONCONST_off CvANONCONST_on
syn keyword xsMacro CvANON_off CvANON_on CvAUTOLOAD CvAUTOLOAD_off
syn keyword xsMacro CvAUTOLOAD_on CvCLONE CvCLONED CvCLONED_off CvCLONED_on
syn keyword xsMacro CvCLONE_off CvCLONE_on CvCONST CvCONST_off CvCONST_on
syn keyword xsMacro CvCVGV_RC CvCVGV_RC_off CvCVGV_RC_on CvDEPTH
syn keyword xsMacro CvDEPTHunsafe CvDYNFILE CvDYNFILE_off CvDYNFILE_on CvEVAL
syn keyword xsMacro CvEVAL_off CvEVAL_on CvFILE CvFILEGV CvFILE_set_from_cop
syn keyword xsMacro CvFLAGS CvGV CvGV_set CvHASEVAL CvHASEVAL_off
syn keyword xsMacro CvHASEVAL_on CvHASGV CvHSCXT CvISXSUB CvISXSUB_off
syn keyword xsMacro CvISXSUB_on CvLEXICAL CvLEXICAL_off CvLEXICAL_on CvLVALUE
syn keyword xsMacro CvLVALUE_off CvLVALUE_on CvMETHOD CvMETHOD_off
syn keyword xsMacro CvMETHOD_on CvNAMED CvNAMED_off CvNAMED_on CvNAME_HEK_set
syn keyword xsMacro CvNODEBUG CvNODEBUG_off CvNODEBUG_on CvOUTSIDE
syn keyword xsMacro CvOUTSIDE_SEQ CvPADLIST CvPADLIST_set CvPROTO CvPROTOLEN
syn keyword xsMacro CvROOT CvSLABBED CvSLABBED_off CvSLABBED_on CvSPECIAL
syn keyword xsMacro CvSPECIAL_off CvSPECIAL_on CvSTART CvSTASH CvSTASH_set
syn keyword xsMacro CvUNIQUE CvUNIQUE_off CvUNIQUE_on CvWEAKOUTSIDE
syn keyword xsMacro CvWEAKOUTSIDE_off CvWEAKOUTSIDE_on CvXSUB CvXSUBANY
syn keyword xsMacro CxFOREACH CxFOREACHDEF CxHASARGS CxITERVAR
syn keyword xsMacro CxITERVAR_PADSV CxLABEL CxLABEL_len CxLABEL_len_flags
syn keyword xsMacro CxLVAL CxMULTICALL CxOLD_IN_EVAL CxOLD_OP_TYPE CxONCE
syn keyword xsMacro CxPADLOOP CxPOPSUB_DONE CxREALEVAL CxTRYBLOCK CxTYPE
syn keyword xsMacro CxTYPE_is_LOOP DBL_DIG DBL_MAX DBL_MIN DBM_ckFilter
syn keyword xsMacro DBM_setFilter DBVARMG_COUNT DBVARMG_SIGNAL DBVARMG_SINGLE
syn keyword xsMacro DBVARMG_TRACE DB_VERSION_MAJOR_CFG DB_VERSION_MINOR_CFG
syn keyword xsMacro DB_VERSION_PATCH_CFG DEBUG_A DEBUG_A_FLAG DEBUG_A_TEST
syn keyword xsMacro DEBUG_A_TEST_ DEBUG_B DEBUG_BUFFERS_r DEBUG_B_FLAG
syn keyword xsMacro DEBUG_B_TEST DEBUG_B_TEST_ DEBUG_C DEBUG_COMPILE_r
syn keyword xsMacro DEBUG_CX DEBUG_C_FLAG DEBUG_C_TEST DEBUG_C_TEST_ DEBUG_D
syn keyword xsMacro DEBUG_DB_RECURSE_FLAG DEBUG_DUMP_r DEBUG_D_FLAG
syn keyword xsMacro DEBUG_D_TEST DEBUG_D_TEST_ DEBUG_EXECUTE_r DEBUG_EXTRA_r
syn keyword xsMacro DEBUG_FLAGS_r DEBUG_GPOS_r DEBUG_H DEBUG_H_FLAG
syn keyword xsMacro DEBUG_H_TEST DEBUG_H_TEST_ DEBUG_INTUIT_r DEBUG_J_FLAG
syn keyword xsMacro DEBUG_J_TEST DEBUG_J_TEST_ DEBUG_L DEBUG_L_FLAG
syn keyword xsMacro DEBUG_L_TEST DEBUG_L_TEST_ DEBUG_M DEBUG_MASK
syn keyword xsMacro DEBUG_MATCH_r DEBUG_M_FLAG DEBUG_M_TEST DEBUG_M_TEST_
syn keyword xsMacro DEBUG_OFFSETS_r DEBUG_OPTIMISE_MORE_r DEBUG_OPTIMISE_r
syn keyword xsMacro DEBUG_P DEBUG_PARSE_r DEBUG_P_FLAG DEBUG_P_TEST
syn keyword xsMacro DEBUG_P_TEST_ DEBUG_Pv DEBUG_Pv_TEST DEBUG_Pv_TEST_
syn keyword xsMacro DEBUG_R DEBUG_R_FLAG DEBUG_R_TEST DEBUG_R_TEST_ DEBUG_S
syn keyword xsMacro DEBUG_SCOPE DEBUG_STACK_r DEBUG_STATE_r DEBUG_S_FLAG
syn keyword xsMacro DEBUG_S_TEST DEBUG_S_TEST_ DEBUG_T DEBUG_TEST_r
syn keyword xsMacro DEBUG_TOP_FLAG DEBUG_TRIE_COMPILE_MORE_r
syn keyword xsMacro DEBUG_TRIE_COMPILE_r DEBUG_TRIE_EXECUTE_MORE_r
syn keyword xsMacro DEBUG_TRIE_EXECUTE_r DEBUG_TRIE_r DEBUG_T_FLAG
syn keyword xsMacro DEBUG_T_TEST DEBUG_T_TEST_ DEBUG_U DEBUG_U_FLAG
syn keyword xsMacro DEBUG_U_TEST DEBUG_U_TEST_ DEBUG_Uv DEBUG_Uv_TEST
syn keyword xsMacro DEBUG_Uv_TEST_ DEBUG_X DEBUG_X_FLAG DEBUG_X_TEST
syn keyword xsMacro DEBUG_X_TEST_ DEBUG_Xv DEBUG_Xv_TEST DEBUG_Xv_TEST_
syn keyword xsMacro DEBUG__ DEBUG_c DEBUG_c_FLAG DEBUG_c_TEST DEBUG_c_TEST_
syn keyword xsMacro DEBUG_f DEBUG_f_FLAG DEBUG_f_TEST DEBUG_f_TEST_ DEBUG_l
syn keyword xsMacro DEBUG_l_FLAG DEBUG_l_TEST DEBUG_l_TEST_ DEBUG_m
syn keyword xsMacro DEBUG_m_FLAG DEBUG_m_TEST DEBUG_m_TEST_ DEBUG_o
syn keyword xsMacro DEBUG_o_FLAG DEBUG_o_TEST DEBUG_o_TEST_ DEBUG_p
syn keyword xsMacro DEBUG_p_FLAG DEBUG_p_TEST DEBUG_p_TEST_ DEBUG_q
syn keyword xsMacro DEBUG_q_FLAG DEBUG_q_TEST DEBUG_q_TEST_ DEBUG_r
syn keyword xsMacro DEBUG_r_FLAG DEBUG_r_TEST DEBUG_r_TEST_ DEBUG_s
syn keyword xsMacro DEBUG_s_FLAG DEBUG_s_TEST DEBUG_s_TEST_ DEBUG_t_FLAG
syn keyword xsMacro DEBUG_t_TEST DEBUG_t_TEST_ DEBUG_u DEBUG_u_FLAG
syn keyword xsMacro DEBUG_u_TEST DEBUG_u_TEST_ DEBUG_v DEBUG_v_FLAG
syn keyword xsMacro DEBUG_v_TEST DEBUG_v_TEST_ DEBUG_x DEBUG_x_FLAG
syn keyword xsMacro DEBUG_x_TEST DEBUG_x_TEST_
syn keyword xsMacro DECLARATION_FOR_LC_NUMERIC_MANIPULATION
syn keyword xsMacro DECLARATION_FOR_STORE_LC_NUMERIC_SET_TO_NEEDED
syn keyword xsMacro DECLARE_STORE_LC_NUMERIC_SET_TO_NEEDED DEFAULT
syn keyword xsMacro DEFAULT_PAT_MOD DEFINEP DEFSV DEFSV_set DEL_NATIVE
syn keyword xsMacro DEPENDS_PAT_MOD DEPENDS_PAT_MODS DETACH DIE DM_ARRAY_ISA
syn keyword xsMacro DM_DELAY DM_EGID DM_EUID DM_GID DM_RGID DM_RUID DM_UID DO
syn keyword xsMacro DOINIT DOLSHARP DONT_DECLARE_STD DORDOR DOROP DOSISH
syn keyword xsMacro DOTDOT DOUBLEKIND DOUBLESIZE DOUBLE_BIG_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_128_BIT_BIG_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_128_BIT_LITTLE_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_32_BIT_BIG_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_32_BIT_LITTLE_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_64_BIT_BIG_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_64_BIT_LITTLE_ENDIAN
syn keyword xsMacro DOUBLE_IS_IEEE_754_64_BIT_MIXED_ENDIAN_BE_LE
syn keyword xsMacro DOUBLE_IS_IEEE_754_64_BIT_MIXED_ENDIAN_LE_BE
syn keyword xsMacro DOUBLE_IS_UNKNOWN_FORMAT DOUBLE_LITTLE_ENDIAN
syn keyword xsMacro DOUBLE_MIX_ENDIAN DO_UTF8 DPTR2FPTR DRAND48_R_PROTO
syn keyword xsMacro DUP_WARNINGS Drand01 ELSE ELSIF EMBEDMYMALLOC END
syn keyword xsMacro ENDGRENT_R_PROTO ENDHOSTENT_R_PROTO ENDLIKE
syn keyword xsMacro ENDNETENT_R_PROTO ENDPROTOENT_R_PROTO ENDPWENT_R_PROTO
syn keyword xsMacro ENDSERVENT_R_PROTO END_EXTERN_C ENTER ENTER_with_name
syn keyword xsMacro ENTRY_PROBE EOF EOL EOS EQOP ERRSV ESC_NATIVE EVAL
syn keyword xsMacro EVAL_AB EVAL_AB_fail EVAL_INEVAL EVAL_INREQUIRE
syn keyword xsMacro EVAL_KEEPERR EVAL_NULL EVAL_RE_REPARSING EVAL_WARNONLY
syn keyword xsMacro EXACT EXACTF EXACTFA EXACTFA_NO_TRIE EXACTFL EXACTFLU8
syn keyword xsMacro EXACTFU EXACTFU_SS EXACTL EXEC_ARGV_CAST EXEC_PAT_MOD
syn keyword xsMacro EXEC_PAT_MODS EXPECT EXT EXTCONST EXTEND EXTEND_MORTAL
syn keyword xsMacro EXTERN_C EXTPERLIO EXTRA_SIZE EXTRA_STEP_2ARGS EXT_MGVTBL
syn keyword xsMacro EXT_PAT_MODS FAKE_BIT_BUCKET FAKE_DEFAULT_SIGNAL_HANDLERS
syn keyword xsMacro FAKE_PERSISTENT_SIGNAL_HANDLERS FALSE FBMcf_TAIL
syn keyword xsMacro FBMcf_TAIL_DOLLAR FBMcf_TAIL_DOLLARM FBMcf_TAIL_Z
syn keyword xsMacro FBMcf_TAIL_z FBMrf_MULTILINE FCNTL_CAN_LOCK FD_CLR
syn keyword xsMacro FD_ISSET FD_SET FD_ZERO FEATURE_ARYBASE_IS_ENABLED
syn keyword xsMacro FEATURE_BITWISE_IS_ENABLED FEATURE_BUNDLE_510
syn keyword xsMacro FEATURE_BUNDLE_511 FEATURE_BUNDLE_515
syn keyword xsMacro FEATURE_BUNDLE_CUSTOM FEATURE_BUNDLE_DEFAULT
syn keyword xsMacro FEATURE_EVALBYTES_IS_ENABLED FEATURE_FC_IS_ENABLED
syn keyword xsMacro FEATURE_IS_ENABLED FEATURE_LEXSUBS_IS_ENABLED
syn keyword xsMacro FEATURE_POSTDEREF_IS_ENABLED
syn keyword xsMacro FEATURE_POSTDEREF_QQ_IS_ENABLED
syn keyword xsMacro FEATURE_REFALIASING_IS_ENABLED FEATURE_SAY_IS_ENABLED
syn keyword xsMacro FEATURE_SIGNATURES_IS_ENABLED FEATURE_STATE_IS_ENABLED
syn keyword xsMacro FEATURE_SWITCH_IS_ENABLED FEATURE_UNICODE_IS_ENABLED
syn keyword xsMacro FEATURE_UNIEVAL_IS_ENABLED FEATURE___SUB___IS_ENABLED
syn keyword xsMacro FFLUSH_NULL FF_0DECIMAL FF_BLANK FF_CHECKCHOP FF_CHECKNL
syn keyword xsMacro FF_CHOP FF_DECIMAL FF_END FF_FETCH FF_HALFSPACE FF_ITEM
syn keyword xsMacro FF_LINEGLOB FF_LINEMARK FF_LINESNGL FF_LITERAL FF_MORE
syn keyword xsMacro FF_NEWLINE FF_SKIP FF_SPACE FILE FILE_base FILE_bufsiz
syn keyword xsMacro FILE_cnt FILE_ptr FILL_ADVANCE_NODE
syn keyword xsMacro FILL_ADVANCE_NODE_2L_ARG FILL_ADVANCE_NODE_ARG
syn keyword xsMacro FILTER_DATA FILTER_ISREADER FILTER_READ
syn keyword xsMacro FIND_RUNCV_level_eq FIND_RUNCV_padid_eq
syn keyword xsMacro FIRST_SURROGATE_UTF8_FIRST_BYTE FITS_IN_8_BITS FLAGS
syn keyword xsMacro FLEXFILENAMES FOLDEQ_LOCALE FOLDEQ_S1_ALREADY_FOLDED
syn keyword xsMacro FOLDEQ_S1_FOLDS_SANE FOLDEQ_S2_ALREADY_FOLDED
syn keyword xsMacro FOLDEQ_S2_FOLDS_SANE FOLDEQ_UTF8_NOMIX_ASCII
syn keyword xsMacro FOLD_FLAGS_FULL FOLD_FLAGS_LOCALE FOLD_FLAGS_NOMIX_ASCII
syn keyword xsMacro FOR FORMAT FORMLBRACK FORMRBRACK FPTR2DPTR FP_PINF
syn keyword xsMacro FP_QNAN FREETMPS FREE_THREAD_KEY FSEEKSIZE FUNC FUNC0
syn keyword xsMacro FUNC0OP FUNC0SUB FUNC1 FUNCMETH FUNCTION__ F_atan2_amg
syn keyword xsMacro F_cos_amg F_exp_amg F_log_amg F_pow_amg F_sin_amg
syn keyword xsMacro F_sqrt_amg Fflush FmLINES FreeOp Fstat GCB_ENUM_COUNT
syn keyword xsMacro GCC_DIAG_IGNORE GCC_DIAG_PRAGMA GCC_DIAG_RESTORE
syn keyword xsMacro GDBMNDBM_H_USES_PROTOTYPES GETATARGET GETGRENT_R_PROTO
syn keyword xsMacro GETGRGID_R_PROTO GETGRNAM_R_PROTO GETHOSTBYADDR_R_PROTO
syn keyword xsMacro GETHOSTBYNAME_R_PROTO GETHOSTENT_R_PROTO GETLOGIN_R_PROTO
syn keyword xsMacro GETNETBYADDR_R_PROTO GETNETBYNAME_R_PROTO
syn keyword xsMacro GETNETENT_R_PROTO GETPROTOBYNAME_R_PROTO
syn keyword xsMacro GETPROTOBYNUMBER_R_PROTO GETPROTOENT_R_PROTO
syn keyword xsMacro GETPWENT_R_PROTO GETPWNAM_R_PROTO GETPWUID_R_PROTO
syn keyword xsMacro GETSERVBYNAME_R_PROTO GETSERVBYPORT_R_PROTO
syn keyword xsMacro GETSERVENT_R_PROTO GETSPNAM_R_PROTO GETTARGET
syn keyword xsMacro GETTARGETSTACKED GET_RE_DEBUG_FLAGS
syn keyword xsMacro GET_RE_DEBUG_FLAGS_DECL GIMME GIMME_V GIVEN
syn keyword xsMacro GLOBAL_PAT_MOD GMTIME_MAX GMTIME_MIN GMTIME_R
syn keyword xsMacro GMTIME_R_PROTO GOSTART GOSUB GPOS GPf_ALIASED_SV
syn keyword xsMacro GRAMBARESTMT GRAMBLOCK GRAMEXPR GRAMFULLSTMT GRAMPROG
syn keyword xsMacro GRAMSTMTSEQ GREEK_CAPITAL_LETTER_IOTA_UTF8
syn keyword xsMacro GREEK_CAPITAL_LETTER_MU GREEK_SMALL_LETTER_MU
syn keyword xsMacro GREEK_SMALL_LETTER_MU_UTF8 GROK_NUMERIC_RADIX GROUPP
syn keyword xsMacro GRPASSWD GV_ADD GV_ADDMG GV_ADDMULTI GV_ADDWARN
syn keyword xsMacro GV_AUTOLOAD GV_AUTOLOAD_ISMETHOD GV_CACHE_ONLY GV_CROAK
syn keyword xsMacro GV_NOADD_MASK GV_NOADD_NOINIT GV_NOEXPAND GV_NOINIT
syn keyword xsMacro GV_NOTQUAL GV_NO_SVGMAGIC GV_SUPER GVf_ASSUMECV
syn keyword xsMacro GVf_IMPORTED GVf_IMPORTED_AV GVf_IMPORTED_CV
syn keyword xsMacro GVf_IMPORTED_HV GVf_IMPORTED_SV GVf_INTRO GVf_MULTI
syn keyword xsMacro Gconvert Gid_t_f Gid_t_sign Gid_t_size GvALIASED_SV
syn keyword xsMacro GvALIASED_SV_off GvALIASED_SV_on GvASSIGN_GENERATION
syn keyword xsMacro GvASSIGN_GENERATION_set GvASSUMECV GvASSUMECV_off
syn keyword xsMacro GvASSUMECV_on GvAV GvAVn GvCV GvCVGEN GvCV_set GvCVu
syn keyword xsMacro GvEGV GvEGVx GvENAME GvENAMELEN GvENAMEUTF8 GvENAME_HEK
syn keyword xsMacro GvESTASH GvFILE GvFILEGV GvFILE_HEK GvFILEx GvFLAGS
syn keyword xsMacro GvFORM GvGP GvGPFLAGS GvGP_set GvHV GvHVn GvIMPORTED
syn keyword xsMacro GvIMPORTED_AV GvIMPORTED_AV_off GvIMPORTED_AV_on
syn keyword xsMacro GvIMPORTED_CV GvIMPORTED_CV_off GvIMPORTED_CV_on
syn keyword xsMacro GvIMPORTED_HV GvIMPORTED_HV_off GvIMPORTED_HV_on
syn keyword xsMacro GvIMPORTED_SV GvIMPORTED_SV_off GvIMPORTED_SV_on
syn keyword xsMacro GvIMPORTED_off GvIMPORTED_on GvINTRO GvINTRO_off
syn keyword xsMacro GvINTRO_on GvIN_PAD GvIN_PAD_off GvIN_PAD_on GvIO GvIOn
syn keyword xsMacro GvIOp GvLINE GvMULTI GvMULTI_off GvMULTI_on GvNAME
syn keyword xsMacro GvNAMELEN GvNAMELEN_get GvNAMEUTF8 GvNAME_HEK GvNAME_get
syn keyword xsMacro GvREFCNT GvSTASH GvSV GvSVn GvXPVGV Gv_AMG HANDY_H
syn keyword xsMacro HASATTRIBUTE_DEPRECATED HASATTRIBUTE_FORMAT
syn keyword xsMacro HASATTRIBUTE_MALLOC HASATTRIBUTE_NONNULL
syn keyword xsMacro HASATTRIBUTE_NORETURN HASATTRIBUTE_PURE
syn keyword xsMacro HASATTRIBUTE_UNUSED HASATTRIBUTE_WARN_UNUSED_RESULT
syn keyword xsMacro HASCONST HASHBRACK HASVOLATILE HAS_ACCESS HAS_ACOSH
syn keyword xsMacro HAS_ALARM HAS_ASINH HAS_ATANH HAS_ATOLL HAS_BACKTRACE
syn keyword xsMacro HAS_BCMP HAS_BCOPY HAS_BOOL HAS_BUILTIN_CHOOSE_EXPR
syn keyword xsMacro HAS_BUILTIN_EXPECT HAS_BZERO HAS_C99
syn keyword xsMacro HAS_C99_VARIADIC_MACROS HAS_CBRT HAS_CHOWN HAS_CHROOT
syn keyword xsMacro HAS_CLEARENV HAS_COPYSIGN HAS_COPYSIGNL HAS_CRYPT
syn keyword xsMacro HAS_CTERMID HAS_CUSERID HAS_DBL_DIG HAS_DBMINIT_PROTO
syn keyword xsMacro HAS_DIFFTIME HAS_DIRFD HAS_DLADDR HAS_DLERROR
syn keyword xsMacro HAS_DRAND48_PROTO HAS_DUP2 HAS_EACCESS HAS_ENDGRENT
syn keyword xsMacro HAS_ENDHOSTENT HAS_ENDNETENT HAS_ENDPROTOENT HAS_ENDPWENT
syn keyword xsMacro HAS_ENDSERVENT HAS_ERF HAS_ERFC HAS_EXP2 HAS_EXPM1
syn keyword xsMacro HAS_FCHDIR HAS_FCHMOD HAS_FCHOWN HAS_FCNTL HAS_FDIM
syn keyword xsMacro HAS_FD_SET HAS_FEGETROUND HAS_FGETPOS HAS_FINITE
syn keyword xsMacro HAS_FINITEL HAS_FLOCK HAS_FLOCK_PROTO HAS_FMA HAS_FMAX
syn keyword xsMacro HAS_FMIN HAS_FORK HAS_FPATHCONF HAS_FPCLASSIFY HAS_FREXPL
syn keyword xsMacro HAS_FSEEKO HAS_FSETPOS HAS_FSTATFS HAS_FSTATVFS HAS_FSYNC
syn keyword xsMacro HAS_FTELLO HAS_FUTIMES HAS_GETADDRINFO HAS_GETCWD
syn keyword xsMacro HAS_GETGRENT HAS_GETGROUPS HAS_GETHOSTBYADDR
syn keyword xsMacro HAS_GETHOSTBYNAME HAS_GETHOSTENT HAS_GETHOSTNAME
syn keyword xsMacro HAS_GETHOST_PROTOS HAS_GETITIMER HAS_GETLOGIN
syn keyword xsMacro HAS_GETMNTENT HAS_GETNAMEINFO HAS_GETNETBYADDR
syn keyword xsMacro HAS_GETNETBYNAME HAS_GETNETENT HAS_GETNET_PROTOS
syn keyword xsMacro HAS_GETPAGESIZE HAS_GETPGID HAS_GETPGRP HAS_GETPPID
syn keyword xsMacro HAS_GETPRIORITY HAS_GETPROTOBYNAME HAS_GETPROTOBYNUMBER
syn keyword xsMacro HAS_GETPROTOENT HAS_GETPROTO_PROTOS HAS_GETPWENT
syn keyword xsMacro HAS_GETSERVBYNAME HAS_GETSERVBYPORT HAS_GETSERVENT
syn keyword xsMacro HAS_GETSERV_PROTOS HAS_GETSPNAM HAS_GETTIMEOFDAY
syn keyword xsMacro HAS_GNULIBC HAS_GROUP HAS_HASMNTOPT HAS_HTONL HAS_HTONS
syn keyword xsMacro HAS_HYPOT HAS_ILOGB HAS_ILOGBL HAS_INETNTOP HAS_INETPTON
syn keyword xsMacro HAS_INET_ATON HAS_INT64_T HAS_IOCTL HAS_IPV6_MREQ
syn keyword xsMacro HAS_IP_MREQ HAS_IP_MREQ_SOURCE HAS_ISASCII HAS_ISBLANK
syn keyword xsMacro HAS_ISFINITE HAS_ISINF HAS_ISINFL HAS_ISNAN HAS_ISNANL
syn keyword xsMacro HAS_ISNORMAL HAS_J0 HAS_J0L HAS_KILL HAS_KILLPG
syn keyword xsMacro HAS_LCHOWN HAS_LC_MONETARY_2008 HAS_LDBL_DIG HAS_LDEXPL
syn keyword xsMacro HAS_LGAMMA HAS_LGAMMA_R HAS_LINK HAS_LLRINT HAS_LLRINTL
syn keyword xsMacro HAS_LLROUND HAS_LLROUNDL HAS_LOCALECONV HAS_LOCKF
syn keyword xsMacro HAS_LOG1P HAS_LOG2 HAS_LOGB HAS_LONG_DOUBLE HAS_LONG_LONG
syn keyword xsMacro HAS_LRINT HAS_LRINTL HAS_LROUND HAS_LROUNDL
syn keyword xsMacro HAS_LSEEK_PROTO HAS_LSTAT HAS_MADVISE HAS_MBLEN
syn keyword xsMacro HAS_MBSTOWCS HAS_MBTOWC HAS_MEMCHR HAS_MEMCMP HAS_MEMCPY
syn keyword xsMacro HAS_MEMMOVE HAS_MEMSET HAS_MKDIR HAS_MKDTEMP HAS_MKFIFO
syn keyword xsMacro HAS_MKSTEMP HAS_MKSTEMPS HAS_MKTIME HAS_MMAP HAS_MODFL
syn keyword xsMacro HAS_MODFL_PROTO HAS_MPROTECT HAS_MSG HAS_MSG_CTRUNC
syn keyword xsMacro HAS_MSG_DONTROUTE HAS_MSG_OOB HAS_MSG_PEEK HAS_MSG_PROXY
syn keyword xsMacro HAS_MSYNC HAS_MUNMAP HAS_NAN HAS_NEARBYINT HAS_NEXTAFTER
syn keyword xsMacro HAS_NEXTTOWARD HAS_NICE HAS_NL_LANGINFO HAS_NTOHL
syn keyword xsMacro HAS_NTOHS HAS_OPEN3 HAS_PASSWD HAS_PATHCONF HAS_PAUSE
syn keyword xsMacro HAS_PIPE HAS_POLL HAS_PRCTL HAS_PRCTL_SET_NAME
syn keyword xsMacro HAS_PROCSELFEXE HAS_PTHREAD_ATFORK
syn keyword xsMacro HAS_PTHREAD_ATTR_SETSCOPE
syn keyword xsMacro HAS_PTHREAD_UNCHECKED_GETSPECIFIC_NP HAS_PTHREAD_YIELD
syn keyword xsMacro HAS_PTRDIFF_T HAS_READDIR HAS_READLINK HAS_READV
syn keyword xsMacro HAS_RECVMSG HAS_REGCOMP HAS_REMAINDER HAS_REMQUO
syn keyword xsMacro HAS_RENAME HAS_REWINDDIR HAS_RINT HAS_RMDIR HAS_ROUND
syn keyword xsMacro HAS_SANE_MEMCMP HAS_SBRK_PROTO HAS_SCALBN HAS_SCALBNL
syn keyword xsMacro HAS_SCHED_YIELD HAS_SCM_RIGHTS HAS_SEEKDIR HAS_SELECT
syn keyword xsMacro HAS_SEM HAS_SENDMSG HAS_SETEGID HAS_SETEUID HAS_SETGRENT
syn keyword xsMacro HAS_SETGROUPS HAS_SETHOSTENT HAS_SETITIMER HAS_SETLINEBUF
syn keyword xsMacro HAS_SETLOCALE HAS_SETNETENT HAS_SETPGID HAS_SETPGRP
syn keyword xsMacro HAS_SETPRIORITY HAS_SETPROTOENT HAS_SETPWENT HAS_SETREGID
syn keyword xsMacro HAS_SETRESGID HAS_SETRESUID HAS_SETREUID HAS_SETSERVENT
syn keyword xsMacro HAS_SETSID HAS_SETVBUF HAS_SHM HAS_SHMAT_PROTOTYPE
syn keyword xsMacro HAS_SIGACTION HAS_SIGNBIT HAS_SIGPROCMASK HAS_SIGSETJMP
syn keyword xsMacro HAS_SIN6_SCOPE_ID HAS_SKIP_LOCALE_INIT HAS_SNPRINTF
syn keyword xsMacro HAS_SOCKADDR_IN6 HAS_SOCKATMARK HAS_SOCKATMARK_PROTO
syn keyword xsMacro HAS_SOCKET HAS_SQRTL HAS_STAT HAS_STATIC_INLINE
syn keyword xsMacro HAS_STRCHR HAS_STRCOLL HAS_STRFTIME HAS_STRTOD HAS_STRTOL
syn keyword xsMacro HAS_STRTOLD HAS_STRTOLL HAS_STRTOQ HAS_STRTOUL
syn keyword xsMacro HAS_STRTOULL HAS_STRTOUQ HAS_STRUCT_CMSGHDR
syn keyword xsMacro HAS_STRUCT_MSGHDR HAS_STRUCT_STATFS
syn keyword xsMacro HAS_STRUCT_STATFS_F_FLAGS HAS_STRXFRM HAS_SYMLINK
syn keyword xsMacro HAS_SYSCALL HAS_SYSCALL_PROTO HAS_SYSCONF HAS_SYSTEM
syn keyword xsMacro HAS_SYS_ERRLIST HAS_TCGETPGRP HAS_TCSETPGRP HAS_TELLDIR
syn keyword xsMacro HAS_TELLDIR_PROTO HAS_TGAMMA HAS_TIME HAS_TIMEGM
syn keyword xsMacro HAS_TIMES HAS_TM_TM_GMTOFF HAS_TM_TM_ZONE HAS_TRUNC
syn keyword xsMacro HAS_TRUNCATE HAS_TRUNCL HAS_TZNAME HAS_UALARM HAS_UMASK
syn keyword xsMacro HAS_UNAME HAS_UNSETENV HAS_USLEEP HAS_USLEEP_PROTO
syn keyword xsMacro HAS_USTAT HAS_UTIME HAS_VPRINTF HAS_VSNPRINTF HAS_WAIT
syn keyword xsMacro HAS_WAIT4 HAS_WAITPID HAS_WCSCMP HAS_WCSTOMBS HAS_WCSXFRM
syn keyword xsMacro HAS_WCTOMB HAS_WRITEV HEK_BASESIZE HEK_FLAGS HEK_HASH
syn keyword xsMacro HEK_KEY HEK_LEN HEK_UTF8 HEK_UTF8_off HEK_UTF8_on
syn keyword xsMacro HEK_WASUTF8 HEK_WASUTF8_off HEK_WASUTF8_on HEKf HEKf256
syn keyword xsMacro HEKfARG HE_SVSLOT HEf_SVKEY HINTS_REFCNT_INIT
syn keyword xsMacro HINTS_REFCNT_LOCK HINTS_REFCNT_TERM HINTS_REFCNT_UNLOCK
syn keyword xsMacro HINT_BLOCK_SCOPE HINT_BYTES HINT_EXPLICIT_STRICT_REFS
syn keyword xsMacro HINT_EXPLICIT_STRICT_SUBS HINT_EXPLICIT_STRICT_VARS
syn keyword xsMacro HINT_FEATURE_MASK HINT_FEATURE_SHIFT HINT_FILETEST_ACCESS
syn keyword xsMacro HINT_INTEGER HINT_LEXICAL_IO_IN HINT_LEXICAL_IO_OUT
syn keyword xsMacro HINT_LOCALE HINT_LOCALE_PARTIAL HINT_LOCALIZE_HH
syn keyword xsMacro HINT_NEW_BINARY HINT_NEW_FLOAT HINT_NEW_INTEGER
syn keyword xsMacro HINT_NEW_RE HINT_NEW_STRING HINT_NO_AMAGIC HINT_RE_EVAL
syn keyword xsMacro HINT_RE_FLAGS HINT_RE_TAINT HINT_SORT_MERGESORT
syn keyword xsMacro HINT_SORT_QUICKSORT HINT_SORT_SORT_BITS HINT_SORT_STABLE
syn keyword xsMacro HINT_STRICT_REFS HINT_STRICT_SUBS HINT_STRICT_VARS
syn keyword xsMacro HINT_UNI_8_BIT HINT_UTF8 HS_APIVERLEN_MAX HS_CXT
syn keyword xsMacro HS_GETAPIVERLEN HS_GETINTERPSIZE HS_GETXSVERLEN HS_KEY
syn keyword xsMacro HS_KEYp HS_XSVERLEN_MAX HSf_IMP_CXT HSf_NOCHK HSf_POPMARK
syn keyword xsMacro HSf_SETXSUBFN HSm_APIVERLEN HSm_INTRPSIZE HSm_KEY_MATCH
syn keyword xsMacro HSm_XSVERLEN HV_DELETE HV_DISABLE_UVAR_XKEY
syn keyword xsMacro HV_FETCH_EMPTY_HE HV_FETCH_ISEXISTS HV_FETCH_ISSTORE
syn keyword xsMacro HV_FETCH_JUST_SV HV_FETCH_LVALUE
syn keyword xsMacro HV_ITERNEXT_WANTPLACEHOLDERS HV_NAME_SETALL
syn keyword xsMacro HVhek_ENABLEHVKFLAGS HVhek_FREEKEY HVhek_KEYCANONICAL
syn keyword xsMacro HVhek_MASK HVhek_PLACEHOLD HVhek_UNSHARED HVhek_UTF8
syn keyword xsMacro HVhek_WASUTF8 HVrhek_IV HVrhek_PV HVrhek_PV_UTF8
syn keyword xsMacro HVrhek_UV HVrhek_delete HVrhek_typemask HVrhek_undef
syn keyword xsMacro HYPHEN_UTF8 H_EBCDIC_TABLES H_PERL H_REGCHARCLASS
syn keyword xsMacro H_UNICODE_CONSTANTS H_UTF8 HeHASH HeKEY HeKEY_hek
syn keyword xsMacro HeKEY_sv HeKFLAGS HeKLEN HeKLEN_UTF8 HeKUTF8 HeKWASUTF8
syn keyword xsMacro HeNEXT HePV HeSVKEY HeSVKEY_force HeSVKEY_set HeUTF8
syn keyword xsMacro HeVAL HvAMAGIC HvAMAGIC_off HvAMAGIC_on HvARRAY HvAUX
syn keyword xsMacro HvAUXf_NO_DEREF HvAUXf_SCAN_STASH HvEITER HvEITER_get
syn keyword xsMacro HvEITER_set HvENAME HvENAMELEN HvENAMELEN_get HvENAMEUTF8
syn keyword xsMacro HvENAME_HEK HvENAME_HEK_NN HvENAME_get HvFILL HvHASKFLAGS
syn keyword xsMacro HvHASKFLAGS_off HvHASKFLAGS_on HvKEYS HvLASTRAND_get
syn keyword xsMacro HvLAZYDEL HvLAZYDEL_off HvLAZYDEL_on HvMAX HvMROMETA
syn keyword xsMacro HvNAME HvNAMELEN HvNAMELEN_get HvNAMEUTF8 HvNAME_HEK
syn keyword xsMacro HvNAME_HEK_NN HvNAME_get HvPLACEHOLDERS
syn keyword xsMacro HvPLACEHOLDERS_get HvPLACEHOLDERS_set HvRAND_get HvRITER
syn keyword xsMacro HvRITER_get HvRITER_set HvSHAREKEYS HvSHAREKEYS_off
syn keyword xsMacro HvSHAREKEYS_on HvTOTALKEYS HvUSEDKEYS I16SIZE I16TYPE
syn keyword xsMacro I16_MAX I16_MIN I32SIZE I32TYPE I32_MAX I32_MAX_P1
syn keyword xsMacro I32_MIN I64SIZE I64TYPE I8SIZE I8TYPE I8_TO_NATIVE
syn keyword xsMacro I8_TO_NATIVE_UTF8 IF IFMATCH IFMATCH_A IFMATCH_A_fail
syn keyword xsMacro IFTHEN IGNORE_PAT_MOD ILLEGAL_UTF8_BYTE INIT INIT_THREADS
syn keyword xsMacro INIT_TRACK_MEMPOOL INSUBP INT2PTR INT32_MIN INT64_C
syn keyword xsMacro INT64_MIN INTSIZE INT_64_T INT_PAT_MODS IN_BYTES
syn keyword xsMacro IN_ENCODING IN_LC IN_LC_ALL_COMPILETIME IN_LC_ALL_RUNTIME
syn keyword xsMacro IN_LC_COMPILETIME IN_LC_PARTIAL_COMPILETIME
syn keyword xsMacro IN_LC_PARTIAL_RUNTIME IN_LC_RUNTIME IN_LOCALE
syn keyword xsMacro IN_LOCALE_COMPILETIME IN_LOCALE_RUNTIME
syn keyword xsMacro IN_PERL_COMPILETIME IN_PERL_RUNTIME IN_SOME_LOCALE_FORM
syn keyword xsMacro IN_SOME_LOCALE_FORM_COMPILETIME
syn keyword xsMacro IN_SOME_LOCALE_FORM_RUNTIME IN_UNI_8_BIT
syn keyword xsMacro IN_UTF8_CTYPE_LOCALE IOCPARM_LEN IOf_ARGV IOf_DIDTOP
syn keyword xsMacro IOf_FAKE_DIRP IOf_FLUSH IOf_NOLINE IOf_START IOf_UNTAINT
syn keyword xsMacro ISA_VERSION_OBJ IS_ANYOF_TRIE
syn keyword xsMacro IS_NUMBER_GREATER_THAN_UV_MAX IS_NUMBER_INFINITY
syn keyword xsMacro IS_NUMBER_IN_UV IS_NUMBER_NAN IS_NUMBER_NEG
syn keyword xsMacro IS_NUMBER_NOT_INT IS_NUMBER_TRAILING IS_NUMERIC_RADIX
syn keyword xsMacro IS_PADCONST IS_PADGV IS_SAFE_PATHNAME IS_SAFE_SYSCALL
syn keyword xsMacro IS_TRIE_AC IS_UTF8_CHAR IS_UTF8_CHAR_FAST IVSIZE IVTYPE
syn keyword xsMacro IV_DIG IV_MAX IV_MAX_P1 IV_MIN I_32 I_ARPA_INET I_ASSERT
syn keyword xsMacro I_BFD I_CRYPT I_DBM I_DIRENT I_DLFCN I_EXECINFO I_FENV
syn keyword xsMacro I_FLOAT I_GDBM I_GDBMNDBM I_GRP I_INTTYPES I_LANGINFO
syn keyword xsMacro I_LIMITS I_LOCALE I_MATH I_MNTENT I_NETDB I_NETINET_IN
syn keyword xsMacro I_NETINET_TCP I_POLL I_PTHREAD I_PWD I_QUADMATH I_SHADOW
syn keyword xsMacro I_STDARG I_STDBOOL I_STDDEF I_STDINT I_STDLIB I_STRING
syn keyword xsMacro I_SYSLOG I_SYSUIO I_SYSUTSNAME I_SYS_DIR I_SYS_FILE
syn keyword xsMacro I_SYS_IOCTL I_SYS_MOUNT I_SYS_PARAM I_SYS_POLL
syn keyword xsMacro I_SYS_RESOURCE I_SYS_SELECT I_SYS_STAT I_SYS_STATFS
syn keyword xsMacro I_SYS_STATVFS I_SYS_TIME I_SYS_TIMES I_SYS_TYPES I_SYS_UN
syn keyword xsMacro I_SYS_VFS I_SYS_WAIT I_TERMIOS I_TIME I_UNISTD I_USTAT
syn keyword xsMacro I_UTIME I_V I_VALUES IoANY IoBOTTOM_GV IoBOTTOM_NAME
syn keyword xsMacro IoDIRP IoFLAGS IoFMT_GV IoFMT_NAME IoIFP IoLINES
syn keyword xsMacro IoLINES_LEFT IoOFP IoPAGE IoPAGE_LEN IoTOP_GV IoTOP_NAME
syn keyword xsMacro IoTYPE IoTYPE_APPEND IoTYPE_CLOSED IoTYPE_IMPLICIT
syn keyword xsMacro IoTYPE_NUMERIC IoTYPE_PIPE IoTYPE_RDONLY IoTYPE_RDWR
syn keyword xsMacro IoTYPE_SOCKET IoTYPE_STD IoTYPE_WRONLY IsSet
syn keyword xsMacro JMPENV_BOOTSTRAP JMPENV_JUMP JMPENV_POP JMPENV_PUSH JOIN
syn keyword xsMacro KEEPCOPY_PAT_MOD KEEPCOPY_PAT_MODS KEEPS KEEPS_next
syn keyword xsMacro KEEPS_next_fail KELVIN_SIGN KEYWORD_PLUGIN_DECLINE
syn keyword xsMacro KEYWORD_PLUGIN_EXPR KEYWORD_PLUGIN_STMT KEY_AUTOLOAD
syn keyword xsMacro KEY_BEGIN KEY_CHECK KEY_DESTROY KEY_END KEY_INIT KEY_NULL
syn keyword xsMacro KEY_UNITCHECK KEY___DATA__ KEY___END__ KEY___FILE__
syn keyword xsMacro KEY___LINE__ KEY___PACKAGE__ KEY___SUB__ KEY_abs
syn keyword xsMacro KEY_accept KEY_alarm KEY_and KEY_atan2 KEY_bind
syn keyword xsMacro KEY_binmode KEY_bless KEY_break KEY_caller KEY_chdir
syn keyword xsMacro KEY_chmod KEY_chomp KEY_chop KEY_chown KEY_chr KEY_chroot
syn keyword xsMacro KEY_close KEY_closedir KEY_cmp KEY_connect KEY_continue
syn keyword xsMacro KEY_cos KEY_crypt KEY_dbmclose KEY_dbmopen KEY_default
syn keyword xsMacro KEY_defined KEY_delete KEY_die KEY_do KEY_dump KEY_each
syn keyword xsMacro KEY_else KEY_elsif KEY_endgrent KEY_endhostent
syn keyword xsMacro KEY_endnetent KEY_endprotoent KEY_endpwent KEY_endservent
syn keyword xsMacro KEY_eof KEY_eq KEY_eval KEY_evalbytes KEY_exec KEY_exists
syn keyword xsMacro KEY_exit KEY_exp KEY_fc KEY_fcntl KEY_fileno KEY_flock
syn keyword xsMacro KEY_for KEY_foreach KEY_fork KEY_format KEY_formline
syn keyword xsMacro KEY_ge KEY_getc KEY_getgrent KEY_getgrgid KEY_getgrnam
syn keyword xsMacro KEY_gethostbyaddr KEY_gethostbyname KEY_gethostent
syn keyword xsMacro KEY_getlogin KEY_getnetbyaddr KEY_getnetbyname
syn keyword xsMacro KEY_getnetent KEY_getpeername KEY_getpgrp KEY_getppid
syn keyword xsMacro KEY_getpriority KEY_getprotobyname KEY_getprotobynumber
syn keyword xsMacro KEY_getprotoent KEY_getpwent KEY_getpwnam KEY_getpwuid
syn keyword xsMacro KEY_getservbyname KEY_getservbyport KEY_getservent
syn keyword xsMacro KEY_getsockname KEY_getsockopt KEY_given KEY_glob
syn keyword xsMacro KEY_gmtime KEY_goto KEY_grep KEY_gt KEY_hex KEY_if
syn keyword xsMacro KEY_index KEY_int KEY_ioctl KEY_join KEY_keys KEY_kill
syn keyword xsMacro KEY_last KEY_lc KEY_lcfirst KEY_le KEY_length KEY_link
syn keyword xsMacro KEY_listen KEY_local KEY_localtime KEY_lock KEY_log
syn keyword xsMacro KEY_lstat KEY_lt KEY_m KEY_map KEY_mkdir KEY_msgctl
syn keyword xsMacro KEY_msgget KEY_msgrcv KEY_msgsnd KEY_my KEY_ne KEY_next
syn keyword xsMacro KEY_no KEY_not KEY_oct KEY_open KEY_opendir KEY_or
syn keyword xsMacro KEY_ord KEY_our KEY_pack KEY_package KEY_pipe KEY_pop
syn keyword xsMacro KEY_pos KEY_print KEY_printf KEY_prototype KEY_push KEY_q
syn keyword xsMacro KEY_qq KEY_qr KEY_quotemeta KEY_qw KEY_qx KEY_rand
syn keyword xsMacro KEY_read KEY_readdir KEY_readline KEY_readlink
syn keyword xsMacro KEY_readpipe KEY_recv KEY_redo KEY_ref KEY_rename
syn keyword xsMacro KEY_require KEY_reset KEY_return KEY_reverse
syn keyword xsMacro KEY_rewinddir KEY_rindex KEY_rmdir KEY_s KEY_say
syn keyword xsMacro KEY_scalar KEY_seek KEY_seekdir KEY_select KEY_semctl
syn keyword xsMacro KEY_semget KEY_semop KEY_send KEY_setgrent KEY_sethostent
syn keyword xsMacro KEY_setnetent KEY_setpgrp KEY_setpriority KEY_setprotoent
syn keyword xsMacro KEY_setpwent KEY_setservent KEY_setsockopt KEY_shift
syn keyword xsMacro KEY_shmctl KEY_shmget KEY_shmread KEY_shmwrite
syn keyword xsMacro KEY_shutdown KEY_sin KEY_sleep KEY_socket KEY_socketpair
syn keyword xsMacro KEY_sort KEY_splice KEY_split KEY_sprintf KEY_sqrt
syn keyword xsMacro KEY_srand KEY_stat KEY_state KEY_study KEY_sub KEY_substr
syn keyword xsMacro KEY_symlink KEY_syscall KEY_sysopen KEY_sysread
syn keyword xsMacro KEY_sysseek KEY_system KEY_syswrite KEY_tell KEY_telldir
syn keyword xsMacro KEY_tie KEY_tied KEY_time KEY_times KEY_tr KEY_truncate
syn keyword xsMacro KEY_uc KEY_ucfirst KEY_umask KEY_undef KEY_unless
syn keyword xsMacro KEY_unlink KEY_unpack KEY_unshift KEY_untie KEY_until
syn keyword xsMacro KEY_use KEY_utime KEY_values KEY_vec KEY_wait KEY_waitpid
syn keyword xsMacro KEY_wantarray KEY_warn KEY_when KEY_while KEY_write KEY_x
syn keyword xsMacro KEY_xor KEY_y LABEL LATIN1_TO_NATIVE
syn keyword xsMacro LATIN_CAPITAL_LETTER_A_WITH_RING_ABOVE
syn keyword xsMacro LATIN_CAPITAL_LETTER_A_WITH_RING_ABOVE_NATIVE
syn keyword xsMacro LATIN_CAPITAL_LETTER_SHARP_S
syn keyword xsMacro LATIN_CAPITAL_LETTER_SHARP_S_UTF8
syn keyword xsMacro LATIN_CAPITAL_LETTER_Y_WITH_DIAERESIS
syn keyword xsMacro LATIN_SMALL_LETTER_A_WITH_RING_ABOVE
syn keyword xsMacro LATIN_SMALL_LETTER_A_WITH_RING_ABOVE_NATIVE
syn keyword xsMacro LATIN_SMALL_LETTER_LONG_S LATIN_SMALL_LETTER_LONG_S_UTF8
syn keyword xsMacro LATIN_SMALL_LETTER_SHARP_S
syn keyword xsMacro LATIN_SMALL_LETTER_SHARP_S_NATIVE
syn keyword xsMacro LATIN_SMALL_LETTER_Y_WITH_DIAERESIS
syn keyword xsMacro LATIN_SMALL_LETTER_Y_WITH_DIAERESIS_NATIVE
syn keyword xsMacro LATIN_SMALL_LIGATURE_LONG_S_T
syn keyword xsMacro LATIN_SMALL_LIGATURE_LONG_S_T_UTF8
syn keyword xsMacro LATIN_SMALL_LIGATURE_ST LATIN_SMALL_LIGATURE_ST_UTF8
syn keyword xsMacro LDBL_DIG LEAVE LEAVESUB LEAVE_SCOPE LEAVE_with_name
syn keyword xsMacro LEX_DONT_CLOSE_RSFP LEX_EVALBYTES LEX_IGNORE_UTF8_HINTS
syn keyword xsMacro LEX_KEEP_PREVIOUS LEX_NOTPARSING LEX_START_COPIED
syn keyword xsMacro LEX_START_FLAGS LEX_START_SAME_FILTER LEX_STUFF_UTF8
syn keyword xsMacro LF_NATIVE LIBERAL LIBM_LIB_VERSION LIB_INVARG LIKELY
syn keyword xsMacro LINKLIST LNBREAK LOADED_FILE_PROBE LOADING_FILE_PROBE
syn keyword xsMacro LOCAL LOCALE_PAT_MOD LOCALE_PAT_MODS LOCALTIME_MAX
syn keyword xsMacro LOCALTIME_MIN LOCALTIME_R LOCALTIME_R_PROTO
syn keyword xsMacro LOCAL_PATCH_COUNT LOCK_DOLLARZERO_MUTEX
syn keyword xsMacro LOCK_LC_NUMERIC_STANDARD LOCK_NUMERIC_STANDARD LOC_SED
syn keyword xsMacro LOGICAL LONGDOUBLE_BIG_ENDIAN LONGDOUBLE_DOUBLEDOUBLE
syn keyword xsMacro LONGDOUBLE_LITTLE_ENDIAN LONGDOUBLE_X86_80_BIT LONGJMP
syn keyword xsMacro LONGLONGSIZE LONGSIZE LONG_DOUBLEKIND LONG_DOUBLESIZE
syn keyword xsMacro LONG_DOUBLE_EQUALS_DOUBLE LONG_DOUBLE_IS_DOUBLE
syn keyword xsMacro LONG_DOUBLE_IS_DOUBLEDOUBLE_128_BIT_BIG_ENDIAN
syn keyword xsMacro LONG_DOUBLE_IS_DOUBLEDOUBLE_128_BIT_LITTLE_ENDIAN
syn keyword xsMacro LONG_DOUBLE_IS_IEEE_754_128_BIT_BIG_ENDIAN
syn keyword xsMacro LONG_DOUBLE_IS_IEEE_754_128_BIT_LITTLE_ENDIAN
syn keyword xsMacro LONG_DOUBLE_IS_UNKNOWN_FORMAT
syn keyword xsMacro LONG_DOUBLE_IS_X86_80_BIT_BIG_ENDIAN
syn keyword xsMacro LONG_DOUBLE_IS_X86_80_BIT_LITTLE_ENDIAN LOOPEX
syn keyword xsMacro LOOP_PAT_MODS LSEEKSIZE LSTOP LSTOPSUB LVRET L_R_TZSET
syn keyword xsMacro LvFLAGS LvSTARGOFF LvTARG LvTARGLEN LvTARGOFF LvTYPE
syn keyword xsMacro MALLOC_CHECK_TAINT MALLOC_CHECK_TAINT2 MALLOC_CTL_H
syn keyword xsMacro MALLOC_INIT MALLOC_OVERHEAD MALLOC_TERM
syn keyword xsMacro MALLOC_TOO_LATE_FOR MARKPOINT MARKPOINT_next
syn keyword xsMacro MARKPOINT_next_fail MASK MATCHOP MAXARG MAXO MAXPATHLEN
syn keyword xsMacro MAXSYSFD MAX_CHARSET_NAME_LENGTH MAX_FEATURE_LEN
syn keyword xsMacro MAX_PORTABLE_UTF8_TWO_BYTE
syn keyword xsMacro MAX_PRINT_A_FOR_USE_ONLY_BY_REGCOMP_DOT_C
syn keyword xsMacro MAX_RECURSE_EVAL_NOCHANGE_DEPTH MAX_UTF8_TWO_BYTE
syn keyword xsMacro MAYBE_DEREF_GV MAYBE_DEREF_GV_flags MAYBE_DEREF_GV_nomg
syn keyword xsMacro MBOL MB_CUR_MAX MDEREF_ACTION_MASK MDEREF_AV_gvav_aelem
syn keyword xsMacro MDEREF_AV_gvsv_vivify_rv2av_aelem MDEREF_AV_padav_aelem
syn keyword xsMacro MDEREF_AV_padsv_vivify_rv2av_aelem
syn keyword xsMacro MDEREF_AV_pop_rv2av_aelem MDEREF_AV_vivify_rv2av_aelem
syn keyword xsMacro MDEREF_FLAG_last MDEREF_HV_gvhv_helem
syn keyword xsMacro MDEREF_HV_gvsv_vivify_rv2hv_helem MDEREF_HV_padhv_helem
syn keyword xsMacro MDEREF_HV_padsv_vivify_rv2hv_helem
syn keyword xsMacro MDEREF_HV_pop_rv2hv_helem MDEREF_HV_vivify_rv2hv_helem
syn keyword xsMacro MDEREF_INDEX_MASK MDEREF_INDEX_const MDEREF_INDEX_gvsv
syn keyword xsMacro MDEREF_INDEX_none MDEREF_INDEX_padsv MDEREF_MASK
syn keyword xsMacro MDEREF_SHIFT MDEREF_reload MEMBER_TO_FPTR MEM_ALIGNBYTES
syn keyword xsMacro MEM_LOG_ALLOC MEM_LOG_FREE MEM_LOG_REALLOC MEM_SIZE
syn keyword xsMacro MEM_SIZE_MAX MEM_WRAP_CHECK MEM_WRAP_CHECK_
syn keyword xsMacro MEM_WRAP_CHECK_1 MEM_WRAP_CHECK_2 MEOL METHOD MEXTEND
syn keyword xsMacro MGf_BYTES MGf_COPY MGf_DUP MGf_GSKIP MGf_LOCAL
syn keyword xsMacro MGf_MINMATCH MGf_PERSIST MGf_REFCOUNTED MGf_REQUIRE_GV
syn keyword xsMacro MGf_TAINTEDDIR MICRO_SIGN MICRO_SIGN_NATIVE MINMOD
syn keyword xsMacro MJD_OFFSET_DEBUG MRO_GET_PRIVATE_DATA MSPAGAIN MULOP
syn keyword xsMacro MULTICALL MULTILINE_PAT_MOD MULTIPLICITY MURMUR_C1
syn keyword xsMacro MURMUR_C2 MURMUR_C3 MURMUR_C4 MURMUR_C5 MURMUR_DOBLOCK
syn keyword xsMacro MURMUR_DOBYTES MUTABLE_AV MUTABLE_CV MUTABLE_GV
syn keyword xsMacro MUTABLE_HV MUTABLE_IO MUTABLE_PTR MUTABLE_SV
syn keyword xsMacro MUTEX_DESTROY MUTEX_INIT MUTEX_INIT_NEEDS_MUTEX_ZEROED
syn keyword xsMacro MUTEX_LOCK MUTEX_UNLOCK MY MY_CXT_CLONE MY_CXT_INDEX
syn keyword xsMacro MY_CXT_INIT MY_CXT_INIT_ARG MY_CXT_INIT_INTERP M_PAT_MODS
syn keyword xsMacro MgBYTEPOS MgBYTEPOS_set MgPV MgPV_const MgPV_nolen_const
syn keyword xsMacro MgTAINTEDDIR MgTAINTEDDIR_off MgTAINTEDDIR_on Mkdir Move
syn keyword xsMacro MoveD NAN_COMPARE_BROKEN NATIVE8_TO_UNI
syn keyword xsMacro NATIVE_BYTE_IS_INVARIANT NATIVE_SKIP NATIVE_TO_ASCII
syn keyword xsMacro NATIVE_TO_I8 NATIVE_TO_LATIN1 NATIVE_TO_UNI NATIVE_TO_UTF
syn keyword xsMacro NATIVE_UTF8_TO_I8 NBOUND NBOUNDA NBOUNDL NBOUNDU
syn keyword xsMacro NBSP_NATIVE NBSP_UTF8 NDBM_H_USES_PROTOTYPES NDEBUG
syn keyword xsMacro NEED_PTHREAD_INIT NEED_VA_COPY NEGATIVE_INDICES_VAR
syn keyword xsMacro NETDB_R_OBSOLETE NEWSV NEW_VERSION NEXTOPER
syn keyword xsMacro NEXT_LINE_CHAR NEXT_OFF NGROUPP NOAMP NOCAPTURE_PAT_MOD
syn keyword xsMacro NOCAPTURE_PAT_MODS NODE_ALIGN NODE_ALIGN_FILL NODE_STEP_B
syn keyword xsMacro NODE_STEP_REGNODE NODE_SZ_STR NOLINE NONDESTRUCT_PAT_MOD
syn keyword xsMacro NONDESTRUCT_PAT_MODS
syn keyword xsMacro NON_OTHER_COUNT_FOR_USE_ONLY_BY_REGCOMP_DOT_C NOOP
syn keyword xsMacro NORETURN_FUNCTION_END NORMAL NOTHING NOTOP NOT_IN_PAD
syn keyword xsMacro NOT_REACHED NO_ENV_ARRAY_IN_MAIN NO_LOCALE
syn keyword xsMacro NO_LOCALECONV_MON_THOUSANDS_SEP NO_TAINT_SUPPORT NPOSIXA
syn keyword xsMacro NPOSIXD NPOSIXL NPOSIXU NREF NREFF NREFFA NREFFL NREFFU
syn keyword xsMacro NSIG NUM2PTR NUM_ANYOF_CODE_POINTS NVSIZE NVTYPE
syn keyword xsMacro NV_BIG_ENDIAN NV_DIG NV_EPSILON NV_INF NV_LITTLE_ENDIAN
syn keyword xsMacro NV_MANT_DIG NV_MAX NV_MAX_10_EXP NV_MAX_EXP NV_MIN
syn keyword xsMacro NV_MIN_10_EXP NV_MIN_EXP NV_MIX_ENDIAN NV_NAN
syn keyword xsMacro NV_OVERFLOWS_INTEGERS_AT NV_PRESERVES_UV_BITS
syn keyword xsMacro NV_WITHIN_IV NV_WITHIN_UV New NewOp NewOpSz Newc Newx
syn keyword xsMacro Newxc Newxz Newz NofAMmeth Null Nullav Nullch Nullcv
syn keyword xsMacro Nullfp Nullgv Nullhe Nullhek Nullhv Nullop Nullsv OASHIFT
syn keyword xsMacro OCSHIFT OCTAL_VALUE OFFUNISKIP ONCE_PAT_MOD ONCE_PAT_MODS
syn keyword xsMacro OPEN OPERAND OPFAIL OPSLOT_HEADER OPSLOT_HEADER_P
syn keyword xsMacro OPTIMIZED OP_BINARY OP_CHECK_MUTEX_INIT
syn keyword xsMacro OP_CHECK_MUTEX_LOCK OP_CHECK_MUTEX_TERM
syn keyword xsMacro OP_CHECK_MUTEX_UNLOCK OP_CLASS OP_DESC OP_ENTRY_PROBE
syn keyword xsMacro OP_FREED OP_GIMME OP_GIMME_REVERSE OP_IS_DIRHOP
syn keyword xsMacro OP_IS_FILETEST OP_IS_FILETEST_ACCESS OP_IS_INFIX_BIT
syn keyword xsMacro OP_IS_NUMCOMPARE OP_IS_SOCKET OP_LVALUE_NO_CROAK OP_NAME
syn keyword xsMacro OP_REFCNT_INIT OP_REFCNT_LOCK OP_REFCNT_TERM
syn keyword xsMacro OP_REFCNT_UNLOCK OP_SIBLING OP_TYPE_IS OP_TYPE_ISNT
syn keyword xsMacro OP_TYPE_ISNT_AND_WASNT OP_TYPE_ISNT_AND_WASNT_NN
syn keyword xsMacro OP_TYPE_ISNT_NN OP_TYPE_IS_NN OP_TYPE_IS_OR_WAS
syn keyword xsMacro OP_TYPE_IS_OR_WAS_NN OROP OROR OSNAME OSVERS O_CREAT
syn keyword xsMacro O_RDONLY O_RDWR O_TEXT O_WRONLY Off Off_t_size
syn keyword xsMacro OpHAS_SIBLING OpLASTSIB_set OpMAYBESIB_set OpMORESIB_set
syn keyword xsMacro OpREFCNT_dec OpREFCNT_inc OpREFCNT_set OpSIBLING OpSLAB
syn keyword xsMacro OpSLOT OpslabREFCNT_dec OpslabREFCNT_dec_padok OutCopFILE
syn keyword xsMacro PADNAME_FROM_PV PADNAMEt_LVALUE PADNAMEt_OUR
syn keyword xsMacro PADNAMEt_OUTER PADNAMEt_STATE PADNAMEt_TYPED PAD_BASE_SV
syn keyword xsMacro PAD_CLONE_VARS PAD_COMPNAME PAD_COMPNAME_FLAGS
syn keyword xsMacro PAD_COMPNAME_FLAGS_isOUR PAD_COMPNAME_GEN
syn keyword xsMacro PAD_COMPNAME_GEN_set PAD_COMPNAME_OURSTASH
syn keyword xsMacro PAD_COMPNAME_PV PAD_COMPNAME_SV PAD_COMPNAME_TYPE
syn keyword xsMacro PAD_FAKELEX_ANON PAD_FAKELEX_MULTI PAD_RESTORE_LOCAL
syn keyword xsMacro PAD_SAVE_LOCAL PAD_SAVE_SETNULLPAD PAD_SETSV PAD_SET_CUR
syn keyword xsMacro PAD_SET_CUR_NOSAVE PAD_SV PAD_SVl PARENT_FAKELEX_FLAGS
syn keyword xsMacro PARENT_PAD_INDEX PARSE_OPTIONAL PASS1 PASS2 PATCHLEVEL
syn keyword xsMacro PERLDB_ALL PERLDB_GOTO PERLDB_INTER PERLDB_LINE
syn keyword xsMacro PERLDB_NAMEANON PERLDB_NAMEEVAL PERLDB_NOOPT
syn keyword xsMacro PERLDB_SAVESRC PERLDB_SAVESRC_INVALID
syn keyword xsMacro PERLDB_SAVESRC_NOSUBS PERLDB_SINGLE PERLDB_SUB
syn keyword xsMacro PERLDB_SUBLINE PERLDB_SUB_NN PERLDBf_GOTO PERLDBf_INTER
syn keyword xsMacro PERLDBf_LINE PERLDBf_NAMEANON PERLDBf_NAMEEVAL
syn keyword xsMacro PERLDBf_NONAME PERLDBf_NOOPT PERLDBf_SAVESRC
syn keyword xsMacro PERLDBf_SAVESRC_INVALID PERLDBf_SAVESRC_NOSUBS
syn keyword xsMacro PERLDBf_SINGLE PERLDBf_SUB PERLDBf_SUBLINE
syn keyword xsMacro PERLIOBUF_DEFAULT_BUFSIZ PERLIO_DUP_CLONE PERLIO_DUP_FD
syn keyword xsMacro PERLIO_FUNCS_CAST PERLIO_FUNCS_CONST PERLIO_FUNCS_DECL
syn keyword xsMacro PERLIO_F_APPEND PERLIO_F_CANREAD PERLIO_F_CANWRITE
syn keyword xsMacro PERLIO_F_CLEARED PERLIO_F_CRLF PERLIO_F_EOF
syn keyword xsMacro PERLIO_F_ERROR PERLIO_F_FASTGETS PERLIO_F_LINEBUF
syn keyword xsMacro PERLIO_F_NOTREG PERLIO_F_OPEN PERLIO_F_RDBUF
syn keyword xsMacro PERLIO_F_TEMP PERLIO_F_TRUNCATE PERLIO_F_TTY
syn keyword xsMacro PERLIO_F_UNBUF PERLIO_F_UTF8 PERLIO_F_WRBUF PERLIO_INIT
syn keyword xsMacro PERLIO_IS_STDIO PERLIO_K_BUFFERED PERLIO_K_CANCRLF
syn keyword xsMacro PERLIO_K_DESTRUCT PERLIO_K_DUMMY PERLIO_K_FASTGETS
syn keyword xsMacro PERLIO_K_MULTIARG PERLIO_K_RAW PERLIO_K_UTF8
syn keyword xsMacro PERLIO_LAYERS PERLIO_NOT_STDIO PERLIO_STDTEXT PERLIO_TERM
syn keyword xsMacro PERLIO_USING_CRLF PERLSI_DESTROY PERLSI_DIEHOOK
syn keyword xsMacro PERLSI_MAGIC PERLSI_MAIN PERLSI_OVERLOAD PERLSI_REQUIRE
syn keyword xsMacro PERLSI_SIGNAL PERLSI_SORT PERLSI_UNDEF PERLSI_UNKNOWN
syn keyword xsMacro PERLSI_WARNHOOK PERL_ABS PERL_ALLOC_CHECK PERL_ANY_COW
syn keyword xsMacro PERL_API_REVISION PERL_API_SUBVERSION PERL_API_VERSION
syn keyword xsMacro PERL_API_VERSION_STRING PERL_ARENA_ROOTS_SIZE
syn keyword xsMacro PERL_ARENA_SIZE PERL_ARGS_ASSERT_ADD_ABOVE_LATIN1_FOLDS
syn keyword xsMacro PERL_ARGS_ASSERT_ADD_DATA
syn keyword xsMacro PERL_ARGS_ASSERT_ADD_MULTI_MATCH
syn keyword xsMacro PERL_ARGS_ASSERT_ADD_UTF16_TEXTFILTER
syn keyword xsMacro PERL_ARGS_ASSERT_ADJUST_SIZE_AND_FIND_BUCKET
syn keyword xsMacro PERL_ARGS_ASSERT_ADVANCE_ONE_SB
syn keyword xsMacro PERL_ARGS_ASSERT_ADVANCE_ONE_WB
syn keyword xsMacro PERL_ARGS_ASSERT_ALLOCCOPSTASH PERL_ARGS_ASSERT_ALLOCMY
syn keyword xsMacro PERL_ARGS_ASSERT_ALLOC_MAYBE_POPULATE_EXACT
syn keyword xsMacro PERL_ARGS_ASSERT_AMAGIC_CALL PERL_ARGS_ASSERT_AMAGIC_CMP
syn keyword xsMacro PERL_ARGS_ASSERT_AMAGIC_CMP_LOCALE
syn keyword xsMacro PERL_ARGS_ASSERT_AMAGIC_DEREF_CALL
syn keyword xsMacro PERL_ARGS_ASSERT_AMAGIC_I_NCMP
syn keyword xsMacro PERL_ARGS_ASSERT_AMAGIC_NCMP
syn keyword xsMacro PERL_ARGS_ASSERT_ANONYMISE_CV_MAYBE
syn keyword xsMacro PERL_ARGS_ASSERT_ANY_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_APPEND_UTF8_FROM_NATIVE_BYTE
syn keyword xsMacro PERL_ARGS_ASSERT_APPLY PERL_ARGS_ASSERT_APPLY_ATTRS
syn keyword xsMacro PERL_ARGS_ASSERT_APPLY_ATTRS_MY
syn keyword xsMacro PERL_ARGS_ASSERT_APPLY_ATTRS_STRING
syn keyword xsMacro PERL_ARGS_ASSERT_ASSERT_UFT8_CACHE_COHERENT
syn keyword xsMacro PERL_ARGS_ASSERT_AV_ARYLEN_P PERL_ARGS_ASSERT_AV_CLEAR
syn keyword xsMacro PERL_ARGS_ASSERT_AV_CREATE_AND_PUSH
syn keyword xsMacro PERL_ARGS_ASSERT_AV_CREATE_AND_UNSHIFT_ONE
syn keyword xsMacro PERL_ARGS_ASSERT_AV_DELETE PERL_ARGS_ASSERT_AV_EXISTS
syn keyword xsMacro PERL_ARGS_ASSERT_AV_EXTEND
syn keyword xsMacro PERL_ARGS_ASSERT_AV_EXTEND_GUTS PERL_ARGS_ASSERT_AV_FETCH
syn keyword xsMacro PERL_ARGS_ASSERT_AV_FILL PERL_ARGS_ASSERT_AV_ITER_P
syn keyword xsMacro PERL_ARGS_ASSERT_AV_LEN PERL_ARGS_ASSERT_AV_MAKE
syn keyword xsMacro PERL_ARGS_ASSERT_AV_POP PERL_ARGS_ASSERT_AV_PUSH
syn keyword xsMacro PERL_ARGS_ASSERT_AV_REIFY PERL_ARGS_ASSERT_AV_SHIFT
syn keyword xsMacro PERL_ARGS_ASSERT_AV_STORE PERL_ARGS_ASSERT_AV_TOP_INDEX
syn keyword xsMacro PERL_ARGS_ASSERT_AV_UNDEF PERL_ARGS_ASSERT_AV_UNSHIFT
syn keyword xsMacro PERL_ARGS_ASSERT_BACKUP_ONE_SB
syn keyword xsMacro PERL_ARGS_ASSERT_BACKUP_ONE_WB
syn keyword xsMacro PERL_ARGS_ASSERT_BAD_TYPE_GV PERL_ARGS_ASSERT_BAD_TYPE_PV
syn keyword xsMacro PERL_ARGS_ASSERT_BIND_MATCH
syn keyword xsMacro PERL_ARGS_ASSERT_BLOCKHOOK_REGISTER
syn keyword xsMacro PERL_ARGS_ASSERT_BYTES_CMP_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_BYTES_FROM_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_BYTES_TO_UTF8 PERL_ARGS_ASSERT_CALL_ARGV
syn keyword xsMacro PERL_ARGS_ASSERT_CALL_LIST PERL_ARGS_ASSERT_CALL_METHOD
syn keyword xsMacro PERL_ARGS_ASSERT_CALL_PV PERL_ARGS_ASSERT_CALL_SV
syn keyword xsMacro PERL_ARGS_ASSERT_CANDO PERL_ARGS_ASSERT_CHECKCOMMA
syn keyword xsMacro PERL_ARGS_ASSERT_CHECK_LOCALE_BOUNDARY_CROSSING
syn keyword xsMacro PERL_ARGS_ASSERT_CHECK_TYPE_AND_OPEN
syn keyword xsMacro PERL_ARGS_ASSERT_CHECK_UTF8_PRINT
syn keyword xsMacro PERL_ARGS_ASSERT_CK_ANONCODE PERL_ARGS_ASSERT_CK_BACKTICK
syn keyword xsMacro PERL_ARGS_ASSERT_CK_BITOP PERL_ARGS_ASSERT_CK_CMP
syn keyword xsMacro PERL_ARGS_ASSERT_CK_CONCAT PERL_ARGS_ASSERT_CK_DEFINED
syn keyword xsMacro PERL_ARGS_ASSERT_CK_DELETE PERL_ARGS_ASSERT_CK_EACH
syn keyword xsMacro PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_CORE
syn keyword xsMacro PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_LIST
syn keyword xsMacro PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_PROTO
syn keyword xsMacro PERL_ARGS_ASSERT_CK_ENTERSUB_ARGS_PROTO_OR_LIST
syn keyword xsMacro PERL_ARGS_ASSERT_CK_EOF PERL_ARGS_ASSERT_CK_EVAL
syn keyword xsMacro PERL_ARGS_ASSERT_CK_EXEC PERL_ARGS_ASSERT_CK_EXISTS
syn keyword xsMacro PERL_ARGS_ASSERT_CK_FTST PERL_ARGS_ASSERT_CK_FUN
syn keyword xsMacro PERL_ARGS_ASSERT_CK_GLOB PERL_ARGS_ASSERT_CK_GREP
syn keyword xsMacro PERL_ARGS_ASSERT_CK_INDEX PERL_ARGS_ASSERT_CK_JOIN
syn keyword xsMacro PERL_ARGS_ASSERT_CK_LENGTH PERL_ARGS_ASSERT_CK_LFUN
syn keyword xsMacro PERL_ARGS_ASSERT_CK_LISTIOB PERL_ARGS_ASSERT_CK_MATCH
syn keyword xsMacro PERL_ARGS_ASSERT_CK_METHOD PERL_ARGS_ASSERT_CK_NULL
syn keyword xsMacro PERL_ARGS_ASSERT_CK_OPEN PERL_ARGS_ASSERT_CK_PROTOTYPE
syn keyword xsMacro PERL_ARGS_ASSERT_CK_READLINE
syn keyword xsMacro PERL_ARGS_ASSERT_CK_REFASSIGN PERL_ARGS_ASSERT_CK_REPEAT
syn keyword xsMacro PERL_ARGS_ASSERT_CK_REQUIRE PERL_ARGS_ASSERT_CK_RETURN
syn keyword xsMacro PERL_ARGS_ASSERT_CK_RFUN PERL_ARGS_ASSERT_CK_RVCONST
syn keyword xsMacro PERL_ARGS_ASSERT_CK_SASSIGN PERL_ARGS_ASSERT_CK_SELECT
syn keyword xsMacro PERL_ARGS_ASSERT_CK_SHIFT PERL_ARGS_ASSERT_CK_SMARTMATCH
syn keyword xsMacro PERL_ARGS_ASSERT_CK_SORT PERL_ARGS_ASSERT_CK_SPAIR
syn keyword xsMacro PERL_ARGS_ASSERT_CK_SPLIT PERL_ARGS_ASSERT_CK_STRINGIFY
syn keyword xsMacro PERL_ARGS_ASSERT_CK_SUBR PERL_ARGS_ASSERT_CK_SUBSTR
syn keyword xsMacro PERL_ARGS_ASSERT_CK_SVCONST PERL_ARGS_ASSERT_CK_TELL
syn keyword xsMacro PERL_ARGS_ASSERT_CK_TRUNC PERL_ARGS_ASSERT_CK_WARNER
syn keyword xsMacro PERL_ARGS_ASSERT_CK_WARNER_D
syn keyword xsMacro PERL_ARGS_ASSERT_CLEAR_PLACEHOLDERS
syn keyword xsMacro PERL_ARGS_ASSERT_CLEAR_SPECIAL_BLOCKS
syn keyword xsMacro PERL_ARGS_ASSERT_CLONE_PARAMS_DEL
syn keyword xsMacro PERL_ARGS_ASSERT_CLONE_PARAMS_NEW
syn keyword xsMacro PERL_ARGS_ASSERT_CLOSEST_COP
syn keyword xsMacro PERL_ARGS_ASSERT_COMPUTE_EXACTISH
syn keyword xsMacro PERL_ARGS_ASSERT_CONSTRUCT_AHOCORASICK_FROM_TRIE
syn keyword xsMacro PERL_ARGS_ASSERT_COP_FETCH_LABEL
syn keyword xsMacro PERL_ARGS_ASSERT_COP_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_COP_STORE_LABEL
syn keyword xsMacro PERL_ARGS_ASSERT_CORESUB_OP
syn keyword xsMacro PERL_ARGS_ASSERT_CORE_PROTOTYPE
syn keyword xsMacro PERL_ARGS_ASSERT_COULD_IT_BE_A_POSIX_CLASS
syn keyword xsMacro PERL_ARGS_ASSERT_CROAK_SV PERL_ARGS_ASSERT_CROAK_XS_USAGE
syn keyword xsMacro PERL_ARGS_ASSERT_CURSE PERL_ARGS_ASSERT_CUSTOM_OP_DESC
syn keyword xsMacro PERL_ARGS_ASSERT_CUSTOM_OP_GET_FIELD
syn keyword xsMacro PERL_ARGS_ASSERT_CUSTOM_OP_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_CUSTOM_OP_REGISTER
syn keyword xsMacro PERL_ARGS_ASSERT_CVGV_FROM_HEK PERL_ARGS_ASSERT_CVGV_SET
syn keyword xsMacro PERL_ARGS_ASSERT_CVSTASH_SET
syn keyword xsMacro PERL_ARGS_ASSERT_CV_CKPROTO_LEN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_CV_CLONE PERL_ARGS_ASSERT_CV_CLONE_INTO
syn keyword xsMacro PERL_ARGS_ASSERT_CV_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_CV_GET_CALL_CHECKER
syn keyword xsMacro PERL_ARGS_ASSERT_CV_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_CV_SET_CALL_CHECKER
syn keyword xsMacro PERL_ARGS_ASSERT_CV_SET_CALL_CHECKER_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_CV_UNDEF PERL_ARGS_ASSERT_CV_UNDEF_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_CX_DUMP PERL_ARGS_ASSERT_CX_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_DEB PERL_ARGS_ASSERT_DEBOP
syn keyword xsMacro PERL_ARGS_ASSERT_DEBPROF
syn keyword xsMacro PERL_ARGS_ASSERT_DEBUG_START_MATCH
syn keyword xsMacro PERL_ARGS_ASSERT_DEB_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_DEB_STACK_N
syn keyword xsMacro PERL_ARGS_ASSERT_DEFELEM_TARGET PERL_ARGS_ASSERT_DELIMCPY
syn keyword xsMacro PERL_ARGS_ASSERT_DEL_SV PERL_ARGS_ASSERT_DESTROY_MATCHER
syn keyword xsMacro PERL_ARGS_ASSERT_DIE_SV PERL_ARGS_ASSERT_DIE_UNWIND
syn keyword xsMacro PERL_ARGS_ASSERT_DIRP_DUP PERL_ARGS_ASSERT_DIV128
syn keyword xsMacro PERL_ARGS_ASSERT_DOFILE PERL_ARGS_ASSERT_DOFINDLABEL
syn keyword xsMacro PERL_ARGS_ASSERT_DOFORM PERL_ARGS_ASSERT_DOONELINER
syn keyword xsMacro PERL_ARGS_ASSERT_DOOPEN_PM PERL_ARGS_ASSERT_DOPARSEFORM
syn keyword xsMacro PERL_ARGS_ASSERT_DOPOPTOLABEL
syn keyword xsMacro PERL_ARGS_ASSERT_DOPOPTOSUB_AT PERL_ARGS_ASSERT_DOREF
syn keyword xsMacro PERL_ARGS_ASSERT_DO_AEXEC PERL_ARGS_ASSERT_DO_AEXEC5
syn keyword xsMacro PERL_ARGS_ASSERT_DO_ASPAWN PERL_ARGS_ASSERT_DO_BINMODE
syn keyword xsMacro PERL_ARGS_ASSERT_DO_CHOMP PERL_ARGS_ASSERT_DO_DUMP_PAD
syn keyword xsMacro PERL_ARGS_ASSERT_DO_EOF PERL_ARGS_ASSERT_DO_EXEC
syn keyword xsMacro PERL_ARGS_ASSERT_DO_EXEC3 PERL_ARGS_ASSERT_DO_GVGV_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_DO_GV_DUMP PERL_ARGS_ASSERT_DO_HV_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_DO_IPCCTL PERL_ARGS_ASSERT_DO_IPCGET
syn keyword xsMacro PERL_ARGS_ASSERT_DO_JOIN PERL_ARGS_ASSERT_DO_MAGIC_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_DO_MSGRCV PERL_ARGS_ASSERT_DO_MSGSND
syn keyword xsMacro PERL_ARGS_ASSERT_DO_NCMP PERL_ARGS_ASSERT_DO_ODDBALL
syn keyword xsMacro PERL_ARGS_ASSERT_DO_OPEN PERL_ARGS_ASSERT_DO_OPEN6
syn keyword xsMacro PERL_ARGS_ASSERT_DO_OPEN9 PERL_ARGS_ASSERT_DO_OPENN
syn keyword xsMacro PERL_ARGS_ASSERT_DO_OPEN_RAW PERL_ARGS_ASSERT_DO_OP_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_DO_PMOP_DUMP PERL_ARGS_ASSERT_DO_PRINT
syn keyword xsMacro PERL_ARGS_ASSERT_DO_SEMOP PERL_ARGS_ASSERT_DO_SHMIO
syn keyword xsMacro PERL_ARGS_ASSERT_DO_SPAWN
syn keyword xsMacro PERL_ARGS_ASSERT_DO_SPAWN_NOWAIT
syn keyword xsMacro PERL_ARGS_ASSERT_DO_SPRINTF PERL_ARGS_ASSERT_DO_SV_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_DO_SYSSEEK PERL_ARGS_ASSERT_DO_TELL
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS_COMPLEX
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS_COMPLEX_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS_COUNT
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS_COUNT_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS_SIMPLE
syn keyword xsMacro PERL_ARGS_ASSERT_DO_TRANS_SIMPLE_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_DO_VECGET PERL_ARGS_ASSERT_DO_VECSET
syn keyword xsMacro PERL_ARGS_ASSERT_DO_VOP PERL_ARGS_ASSERT_DRAND48_INIT_R
syn keyword xsMacro PERL_ARGS_ASSERT_DRAND48_R PERL_ARGS_ASSERT_DUMPUNTIL
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_C_BACKTRACE
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_EXEC_POS PERL_ARGS_ASSERT_DUMP_FORM
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_INDENT PERL_ARGS_ASSERT_DUMP_MSTATS
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_PACKSUBS
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_PACKSUBS_PERL
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_SUB PERL_ARGS_ASSERT_DUMP_SUB_PERL
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_SV_CHILD PERL_ARGS_ASSERT_DUMP_TRIE
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_TRIE_INTERIM_LIST
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_TRIE_INTERIM_TABLE
syn keyword xsMacro PERL_ARGS_ASSERT_DUMP_VINDENT
syn keyword xsMacro PERL_ARGS_ASSERT_DUP_ATTRLIST
syn keyword xsMacro PERL_ARGS_ASSERT_EMULATE_COP_IO PERL_ARGS_ASSERT_EVAL_PV
syn keyword xsMacro PERL_ARGS_ASSERT_EVAL_SV PERL_ARGS_ASSERT_EXEC_FAILED
syn keyword xsMacro PERL_ARGS_ASSERT_EXPECT_NUMBER PERL_ARGS_ASSERT_F0CONVERT
syn keyword xsMacro PERL_ARGS_ASSERT_FBM_COMPILE PERL_ARGS_ASSERT_FBM_INSTR
syn keyword xsMacro PERL_ARGS_ASSERT_FEATURE_IS_ENABLED
syn keyword xsMacro PERL_ARGS_ASSERT_FILTER_DEL PERL_ARGS_ASSERT_FILTER_GETS
syn keyword xsMacro PERL_ARGS_ASSERT_FILTER_READ PERL_ARGS_ASSERT_FINALIZE_OP
syn keyword xsMacro PERL_ARGS_ASSERT_FINALIZE_OPTREE
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_AND_FORGET_PMOPS
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_ARRAY_SUBSCRIPT
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_BEGINNING
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_BYCLASS
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_DEFAULT_STASH
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_HASH_SUBSCRIPT
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_IN_MY_STASH
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_RUNDEFSV2
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_SCRIPT
syn keyword xsMacro PERL_ARGS_ASSERT_FIND_UNINIT_VAR
syn keyword xsMacro PERL_ARGS_ASSERT_FIRST_SYMBOL
syn keyword xsMacro PERL_ARGS_ASSERT_FIXUP_ERRNO_STRING
syn keyword xsMacro PERL_ARGS_ASSERT_FOLDEQ PERL_ARGS_ASSERT_FOLDEQ_LATIN1
syn keyword xsMacro PERL_ARGS_ASSERT_FOLDEQ_LOCALE
syn keyword xsMacro PERL_ARGS_ASSERT_FOLDEQ_UTF8_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_FOLD_CONSTANTS
syn keyword xsMacro PERL_ARGS_ASSERT_FORCE_IDENT
syn keyword xsMacro PERL_ARGS_ASSERT_FORCE_STRICT_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_FORCE_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_FORCE_WORD PERL_ARGS_ASSERT_FORGET_PMOP
syn keyword xsMacro PERL_ARGS_ASSERT_FORM PERL_ARGS_ASSERT_FORM_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_FORM_SHORT_OCTAL_WARNING
syn keyword xsMacro PERL_ARGS_ASSERT_FPRINTF_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_FP_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_FREE_GLOBAL_STRUCT
syn keyword xsMacro PERL_ARGS_ASSERT_GETCWD_SV PERL_ARGS_ASSERT_GETENV_LEN
syn keyword xsMacro PERL_ARGS_ASSERT_GET_AND_CHECK_BACKSLASH_N_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_GET_ANYOF_CP_LIST_FOR_SSC
syn keyword xsMacro PERL_ARGS_ASSERT_GET_AUX_MG PERL_ARGS_ASSERT_GET_AV
syn keyword xsMacro PERL_ARGS_ASSERT_GET_CV PERL_ARGS_ASSERT_GET_CVN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GET_DB_SUB
syn keyword xsMacro PERL_ARGS_ASSERT_GET_DEBUG_OPTS
syn keyword xsMacro PERL_ARGS_ASSERT_GET_HASH_SEED PERL_ARGS_ASSERT_GET_HV
syn keyword xsMacro PERL_ARGS_ASSERT_GET_INVLIST_ITER_ADDR
syn keyword xsMacro PERL_ARGS_ASSERT_GET_INVLIST_OFFSET_ADDR
syn keyword xsMacro PERL_ARGS_ASSERT_GET_INVLIST_PREVIOUS_INDEX_ADDR
syn keyword xsMacro PERL_ARGS_ASSERT_GET_MSTATS PERL_ARGS_ASSERT_GET_NUM
syn keyword xsMacro PERL_ARGS_ASSERT_GET_SV PERL_ARGS_ASSERT_GLOB_2NUMBER
syn keyword xsMacro PERL_ARGS_ASSERT_GLOB_ASSIGN_GLOB PERL_ARGS_ASSERT_GP_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_ATOUV PERL_ARGS_ASSERT_GROK_BIN
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_BSLASH_N
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_BSLASH_O
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_BSLASH_X PERL_ARGS_ASSERT_GROK_HEX
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_INFNAN PERL_ARGS_ASSERT_GROK_NUMBER
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_NUMBER_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_NUMERIC_RADIX
syn keyword xsMacro PERL_ARGS_ASSERT_GROK_OCT PERL_ARGS_ASSERT_GROUP_END
syn keyword xsMacro PERL_ARGS_ASSERT_GV_AMUPDATE
syn keyword xsMacro PERL_ARGS_ASSERT_GV_AUTOLOAD_PV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_AUTOLOAD_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_GV_AUTOLOAD_SV PERL_ARGS_ASSERT_GV_CHECK
syn keyword xsMacro PERL_ARGS_ASSERT_GV_CONST_SV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_EFULLNAME
syn keyword xsMacro PERL_ARGS_ASSERT_GV_EFULLNAME3
syn keyword xsMacro PERL_ARGS_ASSERT_GV_EFULLNAME4
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHFILE
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHFILE_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETHOD
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETHOD_AUTOLOAD
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETHOD_PVN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETHOD_PV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETHOD_SV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETH_PV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETH_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETH_PVN_AUTOLOAD
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETH_PV_AUTOLOAD
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETH_SV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHMETH_SV_AUTOLOAD
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHPV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHPVN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FETCHSV PERL_ARGS_ASSERT_GV_FULLNAME
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FULLNAME3
syn keyword xsMacro PERL_ARGS_ASSERT_GV_FULLNAME4 PERL_ARGS_ASSERT_GV_INIT_PV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_INIT_PVN PERL_ARGS_ASSERT_GV_INIT_SV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_INIT_SVTYPE
syn keyword xsMacro PERL_ARGS_ASSERT_GV_IS_IN_MAIN
syn keyword xsMacro PERL_ARGS_ASSERT_GV_MAGICALIZE
syn keyword xsMacro PERL_ARGS_ASSERT_GV_MAGICALIZE_ISA
syn keyword xsMacro PERL_ARGS_ASSERT_GV_NAME_SET PERL_ARGS_ASSERT_GV_OVERRIDE
syn keyword xsMacro PERL_ARGS_ASSERT_GV_SETREF PERL_ARGS_ASSERT_GV_STASHPV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_STASHPVN
syn keyword xsMacro PERL_ARGS_ASSERT_GV_STASHPVN_INTERNAL
syn keyword xsMacro PERL_ARGS_ASSERT_GV_STASHSV
syn keyword xsMacro PERL_ARGS_ASSERT_GV_TRY_DOWNGRADE
syn keyword xsMacro PERL_ARGS_ASSERT_HANDLE_REGEX_SETS
syn keyword xsMacro PERL_ARGS_ASSERT_HEK_DUP PERL_ARGS_ASSERT_HE_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_HFREEENTRIES
syn keyword xsMacro PERL_ARGS_ASSERT_HFREE_NEXT_ENTRY PERL_ARGS_ASSERT_HSPLIT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ASSERT PERL_ARGS_ASSERT_HV_AUXINIT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_AUXINIT_INTERNAL
syn keyword xsMacro PERL_ARGS_ASSERT_HV_BACKREFERENCES_P
syn keyword xsMacro PERL_ARGS_ASSERT_HV_CLEAR_PLACEHOLDERS
syn keyword xsMacro PERL_ARGS_ASSERT_HV_COMMON_KEY_LEN
syn keyword xsMacro PERL_ARGS_ASSERT_HV_DELAYFREE_ENT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_DELETE PERL_ARGS_ASSERT_HV_DELETE_ENT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_EITER_P PERL_ARGS_ASSERT_HV_EITER_SET
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ENAME_ADD
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ENAME_DELETE
syn keyword xsMacro PERL_ARGS_ASSERT_HV_EXISTS PERL_ARGS_ASSERT_HV_EXISTS_ENT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_FETCH PERL_ARGS_ASSERT_HV_FETCH_ENT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_FILL PERL_ARGS_ASSERT_HV_FREE_ENT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_FREE_ENT_RET
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ITERINIT PERL_ARGS_ASSERT_HV_ITERKEY
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ITERKEYSV
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ITERNEXT
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ITERNEXTSV
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ITERNEXT_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_HV_ITERVAL
syn keyword xsMacro PERL_ARGS_ASSERT_HV_KILL_BACKREFS
syn keyword xsMacro PERL_ARGS_ASSERT_HV_KSPLIT PERL_ARGS_ASSERT_HV_MAGIC
syn keyword xsMacro PERL_ARGS_ASSERT_HV_MAGIC_CHECK
syn keyword xsMacro PERL_ARGS_ASSERT_HV_NAME_SET
syn keyword xsMacro PERL_ARGS_ASSERT_HV_NOTALLOWED
syn keyword xsMacro PERL_ARGS_ASSERT_HV_PLACEHOLDERS_GET
syn keyword xsMacro PERL_ARGS_ASSERT_HV_PLACEHOLDERS_P
syn keyword xsMacro PERL_ARGS_ASSERT_HV_PLACEHOLDERS_SET
syn keyword xsMacro PERL_ARGS_ASSERT_HV_RAND_SET PERL_ARGS_ASSERT_HV_RITER_P
syn keyword xsMacro PERL_ARGS_ASSERT_HV_RITER_SET PERL_ARGS_ASSERT_HV_SCALAR
syn keyword xsMacro PERL_ARGS_ASSERT_INCLINE PERL_ARGS_ASSERT_INCPUSH
syn keyword xsMacro PERL_ARGS_ASSERT_INCPUSH_IF_EXISTS
syn keyword xsMacro PERL_ARGS_ASSERT_INCPUSH_USE_SEP
syn keyword xsMacro PERL_ARGS_ASSERT_INIT_ARGV_SYMBOLS
syn keyword xsMacro PERL_ARGS_ASSERT_INIT_POSTDUMP_SYMBOLS
syn keyword xsMacro PERL_ARGS_ASSERT_INIT_TM PERL_ARGS_ASSERT_INPLACE_AASSIGN
syn keyword xsMacro PERL_ARGS_ASSERT_INSTR PERL_ARGS_ASSERT_INTUIT_METHOD
syn keyword xsMacro PERL_ARGS_ASSERT_INTUIT_MORE
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_ARRAY
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_CLONE
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_EXTEND
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_HIGHEST
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_IS_ITERATING
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_ITERFINISH
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_ITERINIT
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_ITERNEXT
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_MAX
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_PREVIOUS_INDEX
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_SET_LEN
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_SET_PREVIOUS_INDEX
syn keyword xsMacro PERL_ARGS_ASSERT_INVLIST_TRIM PERL_ARGS_ASSERT_IO_CLOSE
syn keyword xsMacro PERL_ARGS_ASSERT_ISALNUM_LAZY PERL_ARGS_ASSERT_ISA_LOOKUP
syn keyword xsMacro PERL_ARGS_ASSERT_ISFOO_UTF8_LC
syn keyword xsMacro PERL_ARGS_ASSERT_ISIDFIRST_LAZY
syn keyword xsMacro PERL_ARGS_ASSERT_ISINFNANSV PERL_ARGS_ASSERT_ISSB
syn keyword xsMacro PERL_ARGS_ASSERT_ISWB PERL_ARGS_ASSERT_IS_AN_INT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_HANDLE_CONSTRUCTOR
syn keyword xsMacro PERL_ARGS_ASSERT_IS_INVARIANT_STRING
syn keyword xsMacro PERL_ARGS_ASSERT_IS_SAFE_SYSCALL
syn keyword xsMacro PERL_ARGS_ASSERT_IS_SSC_WORTH_IT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_ALNUM
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_ALNUMC
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_ALPHA
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_ASCII
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_BLANK
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_CHAR
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_CHAR_BUF
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_CNTRL
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_COMMON
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_DIGIT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_GRAPH
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_IDCONT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_IDFIRST
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_LOWER
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_MARK
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_PERL_SPACE
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_PERL_WORD
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_POSIX_DIGIT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_PRINT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_PUNCT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_SPACE
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_STRING
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_STRING_LOC
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_STRING_LOCLEN
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_UPPER
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_XDIGIT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_XIDCONT
syn keyword xsMacro PERL_ARGS_ASSERT_IS_UTF8_XIDFIRST PERL_ARGS_ASSERT_JMAYBE
syn keyword xsMacro PERL_ARGS_ASSERT_JOIN_EXACT PERL_ARGS_ASSERT_KEYWORD
syn keyword xsMacro PERL_ARGS_ASSERT_KEYWORD_PLUGIN_STANDARD
syn keyword xsMacro PERL_ARGS_ASSERT_LEAVE_COMMON
syn keyword xsMacro PERL_ARGS_ASSERT_LEX_DISCARD_TO
syn keyword xsMacro PERL_ARGS_ASSERT_LEX_READ_TO
syn keyword xsMacro PERL_ARGS_ASSERT_LEX_STUFF_PV
syn keyword xsMacro PERL_ARGS_ASSERT_LEX_STUFF_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_LEX_STUFF_SV
syn keyword xsMacro PERL_ARGS_ASSERT_LEX_UNSTUFF PERL_ARGS_ASSERT_LOAD_MODULE
syn keyword xsMacro PERL_ARGS_ASSERT_LOAD_MODULE_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_LOCALIZE
syn keyword xsMacro PERL_ARGS_ASSERT_LOOKS_LIKE_BOOL
syn keyword xsMacro PERL_ARGS_ASSERT_LOOKS_LIKE_NUMBER PERL_ARGS_ASSERT_LOP
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARARYLEN_P
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARENV
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARHINT
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARHINTS
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARISA
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEARSIG
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_CLEAR_ALL_ENV
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_COPYCALLCHECKER
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_EXISTSPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_FREEARYLEN_P
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_FREEOVRLD
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GET
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETARYLEN
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETDEBUGVAR
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETDEFELEM
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETNKEYS
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETPOS
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETSIG
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETSUBSTR
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETTAINT
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETUVAR
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_GETVEC
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_KILLBACKREFS
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_METHCALL
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_METHCALL1
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_METHPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_NEXTPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_REGDATA_CNT
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_REGDATUM_GET
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_REGDATUM_SET
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SCALARPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SET
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETARYLEN
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETCOLLXFRM
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETDBLINE
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETDEBUGVAR
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETDEFELEM
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETENV
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETHINT
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETISA
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETLVREF
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETMGLOB
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETNKEYS
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETPOS
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETREGEXP
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETSIG
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETSUBSTR
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETTAINT
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETUTF8
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETUVAR
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SETVEC
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SET_ALL_ENV
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_SIZEPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAGIC_WIPEPACK
syn keyword xsMacro PERL_ARGS_ASSERT_MAKE_MATCHER PERL_ARGS_ASSERT_MAKE_TRIE
syn keyword xsMacro PERL_ARGS_ASSERT_MALLOCED_SIZE
syn keyword xsMacro PERL_ARGS_ASSERT_MATCHER_MATCHES_SV
syn keyword xsMacro PERL_ARGS_ASSERT_MAYBERELOCATE
syn keyword xsMacro PERL_ARGS_ASSERT_MAYBE_MULTIMAGIC_GV
syn keyword xsMacro PERL_ARGS_ASSERT_MEASURE_STRUCT
syn keyword xsMacro PERL_ARGS_ASSERT_MEM_COLLXFRM
syn keyword xsMacro PERL_ARGS_ASSERT_MEM_LOG_COMMON PERL_ARGS_ASSERT_MESS
syn keyword xsMacro PERL_ARGS_ASSERT_MESS_NOCONTEXT PERL_ARGS_ASSERT_MESS_SV
syn keyword xsMacro PERL_ARGS_ASSERT_MG_CLEAR PERL_ARGS_ASSERT_MG_COPY
syn keyword xsMacro PERL_ARGS_ASSERT_MG_DUP PERL_ARGS_ASSERT_MG_FIND_MGLOB
syn keyword xsMacro PERL_ARGS_ASSERT_MG_FREE PERL_ARGS_ASSERT_MG_FREE_TYPE
syn keyword xsMacro PERL_ARGS_ASSERT_MG_GET PERL_ARGS_ASSERT_MG_LENGTH
syn keyword xsMacro PERL_ARGS_ASSERT_MG_LOCALIZE PERL_ARGS_ASSERT_MG_MAGICAL
syn keyword xsMacro PERL_ARGS_ASSERT_MG_SET PERL_ARGS_ASSERT_MG_SIZE
syn keyword xsMacro PERL_ARGS_ASSERT_MINI_MKTIME
syn keyword xsMacro PERL_ARGS_ASSERT_MORESWITCHES
syn keyword xsMacro PERL_ARGS_ASSERT_MOVE_PROTO_ATTR
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_CLEAN_ISAREV
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_GATHER_AND_RENAME
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_GET_FROM_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_GET_LINEAR_ISA
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_GET_LINEAR_ISA_DFS
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_GET_PRIVATE_DATA
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_ISA_CHANGED_IN
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_META_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_META_INIT
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_METHOD_CHANGED_IN
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_PACKAGE_MOVED
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_REGISTER
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_SET_MRO
syn keyword xsMacro PERL_ARGS_ASSERT_MRO_SET_PRIVATE_DATA
syn keyword xsMacro PERL_ARGS_ASSERT_MUL128
syn keyword xsMacro PERL_ARGS_ASSERT_MULTIDEREF_STRINGIFY
syn keyword xsMacro PERL_ARGS_ASSERT_MY_ATOF PERL_ARGS_ASSERT_MY_ATOF2
syn keyword xsMacro PERL_ARGS_ASSERT_MY_ATTRS PERL_ARGS_ASSERT_MY_BCOPY
syn keyword xsMacro PERL_ARGS_ASSERT_MY_BYTES_TO_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_MY_BZERO PERL_ARGS_ASSERT_MY_CXT_INDEX
syn keyword xsMacro PERL_ARGS_ASSERT_MY_CXT_INIT PERL_ARGS_ASSERT_MY_KID
syn keyword xsMacro PERL_ARGS_ASSERT_MY_MEMCMP PERL_ARGS_ASSERT_MY_MEMSET
syn keyword xsMacro PERL_ARGS_ASSERT_MY_POPEN PERL_ARGS_ASSERT_MY_POPEN_LIST
syn keyword xsMacro PERL_ARGS_ASSERT_MY_SNPRINTF PERL_ARGS_ASSERT_MY_SPRINTF
syn keyword xsMacro PERL_ARGS_ASSERT_MY_STRFTIME
syn keyword xsMacro PERL_ARGS_ASSERT_MY_VSNPRINTF PERL_ARGS_ASSERT_NEED_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_NEWAVREF PERL_ARGS_ASSERT_NEWCONDOP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWFOROP PERL_ARGS_ASSERT_NEWGIVENOP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWGIVWHENOP PERL_ARGS_ASSERT_NEWGP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWGVGEN_FLAGS PERL_ARGS_ASSERT_NEWGVOP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWHVREF PERL_ARGS_ASSERT_NEWLOGOP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWLOOPEX PERL_ARGS_ASSERT_NEWMETHOP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWMETHOP_NAMED
syn keyword xsMacro PERL_ARGS_ASSERT_NEWMYSUB
syn keyword xsMacro PERL_ARGS_ASSERT_NEWPADNAMEOUTER
syn keyword xsMacro PERL_ARGS_ASSERT_NEWPADNAMEPVN PERL_ARGS_ASSERT_NEWPADOP
syn keyword xsMacro PERL_ARGS_ASSERT_NEWPROG PERL_ARGS_ASSERT_NEWRANGE
syn keyword xsMacro PERL_ARGS_ASSERT_NEWRV PERL_ARGS_ASSERT_NEWRV_NOINC
syn keyword xsMacro PERL_ARGS_ASSERT_NEWSTUB PERL_ARGS_ASSERT_NEWSVAVDEFELEM
syn keyword xsMacro PERL_ARGS_ASSERT_NEWSVOP PERL_ARGS_ASSERT_NEWSVPVF
syn keyword xsMacro PERL_ARGS_ASSERT_NEWSVPVF_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_NEWSVREF PERL_ARGS_ASSERT_NEWSVRV
syn keyword xsMacro PERL_ARGS_ASSERT_NEWWHENOP PERL_ARGS_ASSERT_NEWXS
syn keyword xsMacro PERL_ARGS_ASSERT_NEWXS_DEFFILE
syn keyword xsMacro PERL_ARGS_ASSERT_NEWXS_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_NEWXS_LEN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_NEW_CONSTANT PERL_ARGS_ASSERT_NEW_CTYPE
syn keyword xsMacro PERL_ARGS_ASSERT_NEW_LOGOP PERL_ARGS_ASSERT_NEW_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_NEW_WARNINGS_BITFIELD
syn keyword xsMacro PERL_ARGS_ASSERT_NEXTARGV PERL_ARGS_ASSERT_NEXTCHAR
syn keyword xsMacro PERL_ARGS_ASSERT_NEXT_SYMBOL PERL_ARGS_ASSERT_NINSTR
syn keyword xsMacro PERL_ARGS_ASSERT_NOPERL_DIE PERL_ARGS_ASSERT_NOT_A_NUMBER
syn keyword xsMacro PERL_ARGS_ASSERT_NOT_INCREMENTABLE
syn keyword xsMacro PERL_ARGS_ASSERT_NO_BAREWORD_ALLOWED
syn keyword xsMacro PERL_ARGS_ASSERT_NO_FH_ALLOWED PERL_ARGS_ASSERT_NO_OP
syn keyword xsMacro PERL_ARGS_ASSERT_OOPSAV PERL_ARGS_ASSERT_OOPSHV
syn keyword xsMacro PERL_ARGS_ASSERT_OPENN_CLEANUP
syn keyword xsMacro PERL_ARGS_ASSERT_OPENN_SETUP PERL_ARGS_ASSERT_OPEN_SCRIPT
syn keyword xsMacro PERL_ARGS_ASSERT_OPMETHOD_STASH
syn keyword xsMacro PERL_ARGS_ASSERT_OPSLAB_FORCE_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_OPSLAB_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_OPSLAB_FREE_NOPAD
syn keyword xsMacro PERL_ARGS_ASSERT_OP_CLEAR
syn keyword xsMacro PERL_ARGS_ASSERT_OP_CONTEXTUALIZE
syn keyword xsMacro PERL_ARGS_ASSERT_OP_DUMP PERL_ARGS_ASSERT_OP_INTEGERIZE
syn keyword xsMacro PERL_ARGS_ASSERT_OP_LINKLIST PERL_ARGS_ASSERT_OP_NULL
syn keyword xsMacro PERL_ARGS_ASSERT_OP_PARENT PERL_ARGS_ASSERT_OP_REFCNT_DEC
syn keyword xsMacro PERL_ARGS_ASSERT_OP_RELOCATE_SV
syn keyword xsMacro PERL_ARGS_ASSERT_OP_STD_INIT PERL_ARGS_ASSERT_PACKAGE
syn keyword xsMacro PERL_ARGS_ASSERT_PACKAGE_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_PACKLIST PERL_ARGS_ASSERT_PACK_CAT
syn keyword xsMacro PERL_ARGS_ASSERT_PACK_REC PERL_ARGS_ASSERT_PADLIST_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_PADLIST_STORE
syn keyword xsMacro PERL_ARGS_ASSERT_PADNAMELIST_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_PADNAMELIST_FETCH
syn keyword xsMacro PERL_ARGS_ASSERT_PADNAMELIST_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_PADNAMELIST_STORE
syn keyword xsMacro PERL_ARGS_ASSERT_PADNAME_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_PADNAME_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_ADD_ANON
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_ADD_NAME_PV
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_ADD_NAME_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_ADD_NAME_SV
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_ADD_WEAKREF
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_ALLOC_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_CHECK_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_FINDLEX
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_FINDMY_PV
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_FINDMY_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_FINDMY_SV
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_FIXUP_INNER_ANONS
syn keyword xsMacro PERL_ARGS_ASSERT_PAD_PUSH PERL_ARGS_ASSERT_PAD_SETSV
syn keyword xsMacro PERL_ARGS_ASSERT_PARSER_DUP PERL_ARGS_ASSERT_PARSER_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_PARSER_FREE_NEXTTOKE_OPS
syn keyword xsMacro PERL_ARGS_ASSERT_PARSE_GV_STASH_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_PARSE_IDENT
syn keyword xsMacro PERL_ARGS_ASSERT_PARSE_LPAREN_QUESTION_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_PARSE_UNICODE_OPTS
syn keyword xsMacro PERL_ARGS_ASSERT_PATH_IS_SEARCHABLE
syn keyword xsMacro PERL_ARGS_ASSERT_PERLIO_READ
syn keyword xsMacro PERL_ARGS_ASSERT_PERLIO_UNREAD
syn keyword xsMacro PERL_ARGS_ASSERT_PERLIO_WRITE
syn keyword xsMacro PERL_ARGS_ASSERT_PERL_ALLOC_USING
syn keyword xsMacro PERL_ARGS_ASSERT_PERL_CLONE
syn keyword xsMacro PERL_ARGS_ASSERT_PERL_CLONE_USING
syn keyword xsMacro PERL_ARGS_ASSERT_PERL_CONSTRUCT
syn keyword xsMacro PERL_ARGS_ASSERT_PERL_DESTRUCT PERL_ARGS_ASSERT_PERL_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_PERL_PARSE PERL_ARGS_ASSERT_PERL_RUN
syn keyword xsMacro PERL_ARGS_ASSERT_PMRUNTIME PERL_ARGS_ASSERT_PMTRANS
syn keyword xsMacro PERL_ARGS_ASSERT_PM_DESCRIPTION
syn keyword xsMacro PERL_ARGS_ASSERT_POPULATE_ANYOF_FROM_INVLIST
syn keyword xsMacro PERL_ARGS_ASSERT_POPULATE_ISA PERL_ARGS_ASSERT_PREGCOMP
syn keyword xsMacro PERL_ARGS_ASSERT_PREGEXEC PERL_ARGS_ASSERT_PREGFREE2
syn keyword xsMacro PERL_ARGS_ASSERT_PRESCAN_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_PRINTBUF
syn keyword xsMacro PERL_ARGS_ASSERT_PRINTF_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_PROCESS_SPECIAL_BLOCKS
syn keyword xsMacro PERL_ARGS_ASSERT_PTR_TABLE_FETCH
syn keyword xsMacro PERL_ARGS_ASSERT_PTR_TABLE_FIND
syn keyword xsMacro PERL_ARGS_ASSERT_PTR_TABLE_SPLIT
syn keyword xsMacro PERL_ARGS_ASSERT_PTR_TABLE_STORE
syn keyword xsMacro PERL_ARGS_ASSERT_PUT_CHARCLASS_BITMAP_INNARDS
syn keyword xsMacro PERL_ARGS_ASSERT_PUT_CODE_POINT
syn keyword xsMacro PERL_ARGS_ASSERT_PUT_RANGE PERL_ARGS_ASSERT_PV_DISPLAY
syn keyword xsMacro PERL_ARGS_ASSERT_PV_ESCAPE PERL_ARGS_ASSERT_PV_PRETTY
syn keyword xsMacro PERL_ARGS_ASSERT_PV_UNI_DISPLAY PERL_ARGS_ASSERT_QERROR
syn keyword xsMacro PERL_ARGS_ASSERT_QSORTSVU
syn keyword xsMacro PERL_ARGS_ASSERT_QUADMATH_FORMAT_NEEDED
syn keyword xsMacro PERL_ARGS_ASSERT_QUADMATH_FORMAT_SINGLE
syn keyword xsMacro PERL_ARGS_ASSERT_REENTRANT_RETRY
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_FETCH_PV
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_FETCH_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_FETCH_SV
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_NEW_PV
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_NEW_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_NEW_SV
syn keyword xsMacro PERL_ARGS_ASSERT_REFCOUNTED_HE_VALUE
syn keyword xsMacro PERL_ARGS_ASSERT_REFTO PERL_ARGS_ASSERT_REG
syn keyword xsMacro PERL_ARGS_ASSERT_REG2LANODE PERL_ARGS_ASSERT_REGANODE
syn keyword xsMacro PERL_ARGS_ASSERT_REGATOM PERL_ARGS_ASSERT_REGBRANCH
syn keyword xsMacro PERL_ARGS_ASSERT_REGCLASS PERL_ARGS_ASSERT_REGCLASS_SWASH
syn keyword xsMacro PERL_ARGS_ASSERT_REGCPPOP PERL_ARGS_ASSERT_REGCPPUSH
syn keyword xsMacro PERL_ARGS_ASSERT_REGCURLY PERL_ARGS_ASSERT_REGDUMP
syn keyword xsMacro PERL_ARGS_ASSERT_REGDUPE_INTERNAL
syn keyword xsMacro PERL_ARGS_ASSERT_REGEXEC_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_REGFREE_INTERNAL
syn keyword xsMacro PERL_ARGS_ASSERT_REGHOP3 PERL_ARGS_ASSERT_REGHOP4
syn keyword xsMacro PERL_ARGS_ASSERT_REGHOPMAYBE3 PERL_ARGS_ASSERT_REGINCLASS
syn keyword xsMacro PERL_ARGS_ASSERT_REGINSERT PERL_ARGS_ASSERT_REGMATCH
syn keyword xsMacro PERL_ARGS_ASSERT_REGNODE_GUTS PERL_ARGS_ASSERT_REGPATWS
syn keyword xsMacro PERL_ARGS_ASSERT_REGPIECE PERL_ARGS_ASSERT_REGPPOSIXCC
syn keyword xsMacro PERL_ARGS_ASSERT_REGPROP PERL_ARGS_ASSERT_REGREPEAT
syn keyword xsMacro PERL_ARGS_ASSERT_REGTAIL PERL_ARGS_ASSERT_REGTAIL_STUDY
syn keyword xsMacro PERL_ARGS_ASSERT_REGTRY
syn keyword xsMacro PERL_ARGS_ASSERT_REG_CHECK_NAMED_BUFF_MATCHED
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_ALL
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_EXISTS
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_FETCH
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_FIRSTKEY
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_ITER
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_NEXTKEY
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NAMED_BUFF_SCALAR
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NODE
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_FETCH
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_LENGTH
syn keyword xsMacro PERL_ARGS_ASSERT_REG_NUMBERED_BUFF_STORE
syn keyword xsMacro PERL_ARGS_ASSERT_REG_QR_PACKAGE
syn keyword xsMacro PERL_ARGS_ASSERT_REG_RECODE
syn keyword xsMacro PERL_ARGS_ASSERT_REG_SCAN_NAME
syn keyword xsMacro PERL_ARGS_ASSERT_REG_SKIPCOMMENT
syn keyword xsMacro PERL_ARGS_ASSERT_REG_TEMP_COPY PERL_ARGS_ASSERT_REPEATCPY
syn keyword xsMacro PERL_ARGS_ASSERT_REPORT_REDEFINED_CV
syn keyword xsMacro PERL_ARGS_ASSERT_REQUIRE_PV
syn keyword xsMacro PERL_ARGS_ASSERT_REQUIRE_TIE_MOD
syn keyword xsMacro PERL_ARGS_ASSERT_RE_COMPILE PERL_ARGS_ASSERT_RE_CROAK2
syn keyword xsMacro PERL_ARGS_ASSERT_RE_DUP_GUTS
syn keyword xsMacro PERL_ARGS_ASSERT_RE_INTUIT_START
syn keyword xsMacro PERL_ARGS_ASSERT_RE_INTUIT_STRING
syn keyword xsMacro PERL_ARGS_ASSERT_RE_OP_COMPILE PERL_ARGS_ASSERT_RNINSTR
syn keyword xsMacro PERL_ARGS_ASSERT_RSIGNAL_SAVE
syn keyword xsMacro PERL_ARGS_ASSERT_RUN_USER_FILTER
syn keyword xsMacro PERL_ARGS_ASSERT_RV2CV_OP_CV PERL_ARGS_ASSERT_RVPV_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_RXRES_FREE
syn keyword xsMacro PERL_ARGS_ASSERT_RXRES_RESTORE
syn keyword xsMacro PERL_ARGS_ASSERT_RXRES_SAVE PERL_ARGS_ASSERT_SAME_DIRENT
syn keyword xsMacro PERL_ARGS_ASSERT_SAVESHAREDSVPV PERL_ARGS_ASSERT_SAVESVPV
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_ADELETE
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_AELEM_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_ALIASED_SV
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_APTR PERL_ARGS_ASSERT_SAVE_ARY
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_BOOL PERL_ARGS_ASSERT_SAVE_CLEARSV
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_DELETE
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_DESTRUCTOR
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_GENERIC_PVREF
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_GENERIC_SVREF
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_GP PERL_ARGS_ASSERT_SAVE_HASH
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_HDELETE
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_HEK_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_HELEM_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_HPTR PERL_ARGS_ASSERT_SAVE_I16
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_I32 PERL_ARGS_ASSERT_SAVE_I8
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_INT PERL_ARGS_ASSERT_SAVE_ITEM
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_IV PERL_ARGS_ASSERT_SAVE_LINES
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_LIST PERL_ARGS_ASSERT_SAVE_LONG
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_MAGIC_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_MORTALIZESV
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_NOGV PERL_ARGS_ASSERT_SAVE_PPTR
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_SCALAR
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_SCALAR_AT
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_SET_SVFLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_SHARED_PVREF
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_SPTR PERL_ARGS_ASSERT_SAVE_STRLEN
syn keyword xsMacro PERL_ARGS_ASSERT_SAVE_SVREF PERL_ARGS_ASSERT_SAVE_VPTR
syn keyword xsMacro PERL_ARGS_ASSERT_SCALARBOOLEAN
syn keyword xsMacro PERL_ARGS_ASSERT_SCALARVOID PERL_ARGS_ASSERT_SCAN_BIN
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_COMMIT PERL_ARGS_ASSERT_SCAN_CONST
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_FORMLINE
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_HEREDOC PERL_ARGS_ASSERT_SCAN_HEX
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_IDENT
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_INPUTSYMBOL
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_NUM PERL_ARGS_ASSERT_SCAN_OCT
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_PAT PERL_ARGS_ASSERT_SCAN_STR
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_SUBST PERL_ARGS_ASSERT_SCAN_TRANS
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_SCAN_VSTRING PERL_ARGS_ASSERT_SCAN_WORD
syn keyword xsMacro PERL_ARGS_ASSERT_SEARCH_CONST PERL_ARGS_ASSERT_SETDEFOUT
syn keyword xsMacro PERL_ARGS_ASSERT_SET_ANYOF_ARG
syn keyword xsMacro PERL_ARGS_ASSERT_SET_CONTEXT PERL_ARGS_ASSERT_SET_PADLIST
syn keyword xsMacro PERL_ARGS_ASSERT_SHARE_HEK
syn keyword xsMacro PERL_ARGS_ASSERT_SHARE_HEK_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SHOULD_WARN_NL
syn keyword xsMacro PERL_ARGS_ASSERT_SIMPLIFY_SORT PERL_ARGS_ASSERT_SI_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_SKIPSPACE_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SLAB_FREE PERL_ARGS_ASSERT_SLAB_TO_RO
syn keyword xsMacro PERL_ARGS_ASSERT_SLAB_TO_RW PERL_ARGS_ASSERT_SOFTREF2XV
syn keyword xsMacro PERL_ARGS_ASSERT_SORTCV PERL_ARGS_ASSERT_SORTCV_STACKED
syn keyword xsMacro PERL_ARGS_ASSERT_SORTCV_XSUB PERL_ARGS_ASSERT_SORTSV
syn keyword xsMacro PERL_ARGS_ASSERT_SORTSV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SPACE_JOIN_NAMES_MORTAL
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_ADD_RANGE PERL_ARGS_ASSERT_SSC_AND
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_ANYTHING
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_CLEAR_LOCALE
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_CP_AND PERL_ARGS_ASSERT_SSC_FINALIZE
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_INIT
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_INTERSECTION
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_IS_ANYTHING
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_IS_CP_POSIXL_INIT
syn keyword xsMacro PERL_ARGS_ASSERT_SSC_OR PERL_ARGS_ASSERT_SSC_UNION
syn keyword xsMacro PERL_ARGS_ASSERT_SS_DUP PERL_ARGS_ASSERT_STACK_GROW
syn keyword xsMacro PERL_ARGS_ASSERT_START_GLOB
syn keyword xsMacro PERL_ARGS_ASSERT_STDIZE_LOCALE
syn keyword xsMacro PERL_ARGS_ASSERT_STRIP_RETURN
syn keyword xsMacro PERL_ARGS_ASSERT_STR_TO_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_STUDY_CHUNK
syn keyword xsMacro PERL_ARGS_ASSERT_SUB_CRUSH_DEPTH
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2BOOL_FLAGS PERL_ARGS_ASSERT_SV_2CV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2IO PERL_ARGS_ASSERT_SV_2IUV_COMMON
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2IUV_NON_PRESERVE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2IV PERL_ARGS_ASSERT_SV_2IV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2NUM PERL_ARGS_ASSERT_SV_2NV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2PV PERL_ARGS_ASSERT_SV_2PVBYTE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2PVBYTE_NOLEN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2PVUTF8
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2PVUTF8_NOLEN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2PV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2PV_NOLEN PERL_ARGS_ASSERT_SV_2UV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_2UV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_ADD_ARENA
syn keyword xsMacro PERL_ARGS_ASSERT_SV_ADD_BACKREF
syn keyword xsMacro PERL_ARGS_ASSERT_SV_BACKOFF PERL_ARGS_ASSERT_SV_BLESS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_BUF_TO_RO
syn keyword xsMacro PERL_ARGS_ASSERT_SV_BUF_TO_RW PERL_ARGS_ASSERT_SV_CATPV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPVF PERL_ARGS_ASSERT_SV_CATPVF_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPVF_MG_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPVF_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPVN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPVN_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATPV_MG PERL_ARGS_ASSERT_SV_CATSV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATSV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CATSV_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CAT_DECODE PERL_ARGS_ASSERT_SV_CHOP
syn keyword xsMacro PERL_ARGS_ASSERT_SV_CLEAR
syn keyword xsMacro PERL_ARGS_ASSERT_SV_COLLXFRM_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_COPYPV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_COPYPV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DEL_BACKREF
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DERIVED_FROM
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DERIVED_FROM_PV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DERIVED_FROM_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DERIVED_FROM_SV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DISPLAY PERL_ARGS_ASSERT_SV_DOES
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DOES_PV PERL_ARGS_ASSERT_SV_DOES_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DOES_SV PERL_ARGS_ASSERT_SV_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DUP PERL_ARGS_ASSERT_SV_DUP_COMMON
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DUP_INC
syn keyword xsMacro PERL_ARGS_ASSERT_SV_DUP_INC_MULTIPLE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_EXP_GROW
syn keyword xsMacro PERL_ARGS_ASSERT_SV_FORCE_NORMAL
syn keyword xsMacro PERL_ARGS_ASSERT_SV_FORCE_NORMAL_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_FREE2 PERL_ARGS_ASSERT_SV_GETS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_GET_BACKREFS PERL_ARGS_ASSERT_SV_GROW
syn keyword xsMacro PERL_ARGS_ASSERT_SV_INSERT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_INSERT_FLAGS PERL_ARGS_ASSERT_SV_ISA
syn keyword xsMacro PERL_ARGS_ASSERT_SV_IV PERL_ARGS_ASSERT_SV_I_NCMP
syn keyword xsMacro PERL_ARGS_ASSERT_SV_KILL_BACKREFS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_LEN_UTF8_NOMG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_MAGIC PERL_ARGS_ASSERT_SV_MAGICEXT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_MAGICEXT_MGLOB
syn keyword xsMacro PERL_ARGS_ASSERT_SV_NCMP PERL_ARGS_ASSERT_SV_NV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_ONLY_TAINT_GMAGIC
syn keyword xsMacro PERL_ARGS_ASSERT_SV_OR_PV_POS_U2B
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_B2U
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_B2U_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_B2U_MIDWAY
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_U2B
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_U2B_CACHED
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_U2B_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_U2B_FORWARDS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_POS_U2B_MIDWAY PERL_ARGS_ASSERT_SV_PV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVBYTE PERL_ARGS_ASSERT_SV_PVBYTEN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVBYTEN_FORCE PERL_ARGS_ASSERT_SV_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVN_FORCE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVN_FORCE_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVN_NOMG PERL_ARGS_ASSERT_SV_PVUTF8
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVUTF8N
syn keyword xsMacro PERL_ARGS_ASSERT_SV_PVUTF8N_FORCE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_RECODE_TO_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_SV_REF PERL_ARGS_ASSERT_SV_REFTYPE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_RELEASE_COW
syn keyword xsMacro PERL_ARGS_ASSERT_SV_REPLACE PERL_ARGS_ASSERT_SV_RESET
syn keyword xsMacro PERL_ARGS_ASSERT_SV_RVWEAKEN PERL_ARGS_ASSERT_SV_SETHEK
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETIV PERL_ARGS_ASSERT_SV_SETIV_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETNV PERL_ARGS_ASSERT_SV_SETNV_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPV PERL_ARGS_ASSERT_SV_SETPVF
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPVF_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPVF_MG_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPVF_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPVIV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPVIV_MG PERL_ARGS_ASSERT_SV_SETPVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPVN_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETPV_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETREF_IV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETREF_NV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETREF_PV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETREF_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETREF_UV PERL_ARGS_ASSERT_SV_SETSV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETSV_COW
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETSV_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETSV_MG PERL_ARGS_ASSERT_SV_SETUV
syn keyword xsMacro PERL_ARGS_ASSERT_SV_SETUV_MG PERL_ARGS_ASSERT_SV_TAINT
syn keyword xsMacro PERL_ARGS_ASSERT_SV_TAINTED PERL_ARGS_ASSERT_SV_UNGLOB
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UNI_DISPLAY
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UNMAGIC
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UNMAGICEXT PERL_ARGS_ASSERT_SV_UNREF
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UNREF_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UNTAINT PERL_ARGS_ASSERT_SV_UPGRADE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_USEPVN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_USEPVN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_USEPVN_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UTF8_DECODE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UTF8_DOWNGRADE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UTF8_ENCODE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UTF8_UPGRADE
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UTF8_UPGRADE_FLAGS_GROW
syn keyword xsMacro PERL_ARGS_ASSERT_SV_UV PERL_ARGS_ASSERT_SV_VCATPVF
syn keyword xsMacro PERL_ARGS_ASSERT_SV_VCATPVFN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_VCATPVFN_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_SV_VCATPVF_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SV_VSETPVF PERL_ARGS_ASSERT_SV_VSETPVFN
syn keyword xsMacro PERL_ARGS_ASSERT_SV_VSETPVF_MG
syn keyword xsMacro PERL_ARGS_ASSERT_SWALLOW_BOM PERL_ARGS_ASSERT_SWASH_FETCH
syn keyword xsMacro PERL_ARGS_ASSERT_SWASH_INIT
syn keyword xsMacro PERL_ARGS_ASSERT_SWASH_SCAN_LIST_LINE
syn keyword xsMacro PERL_ARGS_ASSERT_SWATCH_GET PERL_ARGS_ASSERT_SYS_INIT
syn keyword xsMacro PERL_ARGS_ASSERT_SYS_INIT3
syn keyword xsMacro PERL_ARGS_ASSERT_SYS_INTERN_DUP
syn keyword xsMacro PERL_ARGS_ASSERT_TAINT_PROPER
syn keyword xsMacro PERL_ARGS_ASSERT_TIED_METHOD
syn keyword xsMacro PERL_ARGS_ASSERT_TOKENIZE_USE PERL_ARGS_ASSERT_TOKEQ
syn keyword xsMacro PERL_ARGS_ASSERT_TOKEREPORT
syn keyword xsMacro PERL_ARGS_ASSERT_TOO_FEW_ARGUMENTS_PV
syn keyword xsMacro PERL_ARGS_ASSERT_TOO_MANY_ARGUMENTS_PV
syn keyword xsMacro PERL_ARGS_ASSERT_TO_BYTE_SUBSTR
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UNI_LOWER
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UNI_TITLE
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UNI_UPPER
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UTF8_CASE
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UTF8_FOLD
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UTF8_LOWER
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UTF8_SUBSTR
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UTF8_TITLE
syn keyword xsMacro PERL_ARGS_ASSERT_TO_UTF8_UPPER
syn keyword xsMacro PERL_ARGS_ASSERT_TRANSLATE_SUBSTR_OFFSETS
syn keyword xsMacro PERL_ARGS_ASSERT_UIV_2BUF PERL_ARGS_ASSERT_UNLNK
syn keyword xsMacro PERL_ARGS_ASSERT_UNPACKSTRING PERL_ARGS_ASSERT_UNPACK_REC
syn keyword xsMacro PERL_ARGS_ASSERT_UNPACK_STR
syn keyword xsMacro PERL_ARGS_ASSERT_UNREFERENCED_TO_TMP_STACK
syn keyword xsMacro PERL_ARGS_ASSERT_UPG_VERSION
syn keyword xsMacro PERL_ARGS_ASSERT_UTF16_TEXTFILTER
syn keyword xsMacro PERL_ARGS_ASSERT_UTF16_TO_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_UTF16_TO_UTF8_REVERSED
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8N_TO_UVCHR
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8N_TO_UVUNI
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_DISTANCE PERL_ARGS_ASSERT_UTF8_HOP
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_LENGTH
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_MG_LEN_CACHE_UPDATE
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_MG_POS_CACHE_UPDATE
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_TO_BYTES
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_TO_UVCHR
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_TO_UVUNI
syn keyword xsMacro PERL_ARGS_ASSERT_UTF8_TO_UVUNI_BUF
syn keyword xsMacro PERL_ARGS_ASSERT_UTILIZE
syn keyword xsMacro PERL_ARGS_ASSERT_UVOFFUNI_TO_UTF8_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_UVUNI_TO_UTF8
syn keyword xsMacro PERL_ARGS_ASSERT_UVUNI_TO_UTF8_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT_VALIDATE_PROTO
syn keyword xsMacro PERL_ARGS_ASSERT_VALIDATE_SUID
syn keyword xsMacro PERL_ARGS_ASSERT_VALID_UTF8_TO_UVCHR
syn keyword xsMacro PERL_ARGS_ASSERT_VALID_UTF8_TO_UVUNI
syn keyword xsMacro PERL_ARGS_ASSERT_VCMP PERL_ARGS_ASSERT_VDEB
syn keyword xsMacro PERL_ARGS_ASSERT_VFORM PERL_ARGS_ASSERT_VISIT
syn keyword xsMacro PERL_ARGS_ASSERT_VIVIFY_DEFELEM
syn keyword xsMacro PERL_ARGS_ASSERT_VIVIFY_REF PERL_ARGS_ASSERT_VLOAD_MODULE
syn keyword xsMacro PERL_ARGS_ASSERT_VMESS PERL_ARGS_ASSERT_VNEWSVPVF
syn keyword xsMacro PERL_ARGS_ASSERT_VNORMAL PERL_ARGS_ASSERT_VNUMIFY
syn keyword xsMacro PERL_ARGS_ASSERT_VSTRINGIFY PERL_ARGS_ASSERT_VVERIFY
syn keyword xsMacro PERL_ARGS_ASSERT_VWARN PERL_ARGS_ASSERT_VWARNER
syn keyword xsMacro PERL_ARGS_ASSERT_WAIT4PID PERL_ARGS_ASSERT_WARN
syn keyword xsMacro PERL_ARGS_ASSERT_WARNER PERL_ARGS_ASSERT_WARNER_NOCONTEXT
syn keyword xsMacro PERL_ARGS_ASSERT_WARN_NOCONTEXT PERL_ARGS_ASSERT_WARN_SV
syn keyword xsMacro PERL_ARGS_ASSERT_WATCH PERL_ARGS_ASSERT_WHICHSIG_PV
syn keyword xsMacro PERL_ARGS_ASSERT_WHICHSIG_PVN
syn keyword xsMacro PERL_ARGS_ASSERT_WHICHSIG_SV
syn keyword xsMacro PERL_ARGS_ASSERT_WIN32_CROAK_NOT_IMPLEMENTED
syn keyword xsMacro PERL_ARGS_ASSERT_WITH_QUEUED_ERRORS
syn keyword xsMacro PERL_ARGS_ASSERT_WRAP_OP_CHECKER
syn keyword xsMacro PERL_ARGS_ASSERT_WRITE_TO_STDERR
syn keyword xsMacro PERL_ARGS_ASSERT_XS_HANDSHAKE
syn keyword xsMacro PERL_ARGS_ASSERT_XS_VERSION_BOOTCHECK
syn keyword xsMacro PERL_ARGS_ASSERT_YYERROR PERL_ARGS_ASSERT_YYERROR_PV
syn keyword xsMacro PERL_ARGS_ASSERT_YYERROR_PVN PERL_ARGS_ASSERT_YYWARN
syn keyword xsMacro PERL_ARGS_ASSERT__APPEND_RANGE_TO_INVLIST
syn keyword xsMacro PERL_ARGS_ASSERT__CORE_SWASH_INIT
syn keyword xsMacro PERL_ARGS_ASSERT__GET_REGCLASS_NONBITMAP_DATA
syn keyword xsMacro PERL_ARGS_ASSERT__GET_SWASH_INVLIST
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_ARRAY_INIT
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_CONTAINS_CP
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_CONTENTS
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_DUMP
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_INTERSECTION_MAYBE_COMPLEMENT_2ND
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_INVERT
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_LEN
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_POPULATE_SWATCH
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_SEARCH
syn keyword xsMacro PERL_ARGS_ASSERT__INVLIST_UNION_MAYBE_COMPLEMENT_2ND
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_CHAR_SLOW
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_FOO
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_IDCONT
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_IDSTART
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_MARK
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_PERL_IDCONT
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_PERL_IDSTART
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_XIDCONT
syn keyword xsMacro PERL_ARGS_ASSERT__IS_UTF8_XIDSTART
syn keyword xsMacro PERL_ARGS_ASSERT__MAKE_EXACTF_INVLIST
syn keyword xsMacro PERL_ARGS_ASSERT__NEW_INVLIST_C_ARRAY
syn keyword xsMacro PERL_ARGS_ASSERT__SETUP_CANNED_INVLIST
syn keyword xsMacro PERL_ARGS_ASSERT__SWASH_INVERSION_HASH
syn keyword xsMacro PERL_ARGS_ASSERT__SWASH_TO_INVLIST
syn keyword xsMacro PERL_ARGS_ASSERT__TO_FOLD_LATIN1
syn keyword xsMacro PERL_ARGS_ASSERT__TO_UNI_FOLD_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT__TO_UPPER_TITLE_LATIN1
syn keyword xsMacro PERL_ARGS_ASSERT__TO_UTF8_FOLD_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT__TO_UTF8_LOWER_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT__TO_UTF8_TITLE_FLAGS
syn keyword xsMacro PERL_ARGS_ASSERT__TO_UTF8_UPPER_FLAGS PERL_ASYNC_CHECK
syn keyword xsMacro PERL_BITFIELD16 PERL_BITFIELD32 PERL_BITFIELD8
syn keyword xsMacro PERL_CALLCONV PERL_CALLCONV_NO_RET PERL_CHECK_INITED
syn keyword xsMacro PERL_CKDEF PERL_DEB PERL_DEB2 PERL_DEBUG PERL_DEBUG_PAD
syn keyword xsMacro PERL_DEBUG_PAD_ZERO PERL_DECIMAL_VERSION
syn keyword xsMacro PERL_DEFAULT_DO_EXEC3_IMPLEMENTATION
syn keyword xsMacro PERL_DONT_CREATE_GVSV PERL_DRAND48_QUAD
syn keyword xsMacro PERL_ENABLE_EXPERIMENTAL_REGEX_OPTIMISATIONS
syn keyword xsMacro PERL_ENABLE_EXTENDED_TRIE_OPTIMISATION
syn keyword xsMacro PERL_ENABLE_POSITIVE_ASSERTION_STUDY
syn keyword xsMacro PERL_ENABLE_TRIE_OPTIMISATION PERL_EXIT_ABORT
syn keyword xsMacro PERL_EXIT_DESTRUCT_END PERL_EXIT_EXPECTED PERL_EXIT_WARN
syn keyword xsMacro PERL_EXPORT_C PERL_FILE_IS_ABSOLUTE PERL_FILTER_EXISTS
syn keyword xsMacro PERL_FLUSHALL_FOR_CHILD PERL_FPU_INIT PERL_FPU_POST_EXEC
syn keyword xsMacro PERL_FPU_PRE_EXEC PERL_FS_VERSION PERL_FS_VER_FMT
syn keyword xsMacro PERL_GCC_BRACE_GROUPS_FORBIDDEN PERL_GET_CONTEXT
syn keyword xsMacro PERL_GET_INTERP PERL_GET_THX PERL_GET_VARS
syn keyword xsMacro PERL_GIT_UNPUSHED_COMMITS PERL_GLOBAL_STRUCT
syn keyword xsMacro PERL_GPROF_MONCONTROL PERL_HASH PERL_HASH_DEFAULT_HvMAX
syn keyword xsMacro PERL_HASH_FUNC PERL_HASH_FUNC_ONE_AT_A_TIME_HARD
syn keyword xsMacro PERL_HASH_INTERNAL PERL_HASH_ITER_BUCKET
syn keyword xsMacro PERL_HASH_RANDOMIZE_KEYS PERL_HASH_SEED
syn keyword xsMacro PERL_HASH_SEED_BYTES PERL_HASH_WITH_SEED
syn keyword xsMacro PERL_HV_ALLOC_AUX_SIZE PERL_HV_ARRAY_ALLOC_BYTES
syn keyword xsMacro PERL_IMPLICIT_CONTEXT PERL_INTERPRETER_SIZE_UPTO_MEMBER
syn keyword xsMacro PERL_INT_MAX PERL_INT_MIN PERL_LOADMOD_DENY
syn keyword xsMacro PERL_LOADMOD_IMPORT_OPS PERL_LOADMOD_NOIMPORT
syn keyword xsMacro PERL_LONG_MAX PERL_LONG_MIN PERL_MALLOC_WRAP
syn keyword xsMacro PERL_MEMORY_DEBUG_HEADER_SIZE PERL_MG_UFUNC
syn keyword xsMacro PERL_MY_SNPRINTF_GUARDED PERL_MY_SNPRINTF_POST_GUARD
syn keyword xsMacro PERL_MY_VSNPRINTF_GUARDED PERL_MY_VSNPRINTF_POST_GUARD
syn keyword xsMacro PERL_NEW_COPY_ON_WRITE PERL_NO_DEV_RANDOM
syn keyword xsMacro PERL_OBJECT_THIS PERL_OBJECT_THIS_ PERL_PADNAME_MINIMAL
syn keyword xsMacro PERL_PADSEQ_INTRO PERL_PATCHNUM PERL_POISON_EXPR
syn keyword xsMacro PERL_PPADDR_INITED PERL_PPDEF PERL_PRESERVE_IVUV
syn keyword xsMacro PERL_PRIeldbl PERL_PRIfldbl PERL_PRIgldbl
syn keyword xsMacro PERL_PV_ESCAPE_ALL PERL_PV_ESCAPE_DWIM
syn keyword xsMacro PERL_PV_ESCAPE_FIRSTCHAR PERL_PV_ESCAPE_NOBACKSLASH
syn keyword xsMacro PERL_PV_ESCAPE_NOCLEAR PERL_PV_ESCAPE_NONASCII
syn keyword xsMacro PERL_PV_ESCAPE_QUOTE PERL_PV_ESCAPE_RE PERL_PV_ESCAPE_UNI
syn keyword xsMacro PERL_PV_ESCAPE_UNI_DETECT PERL_PV_PRETTY_DUMP
syn keyword xsMacro PERL_PV_PRETTY_ELLIPSES PERL_PV_PRETTY_EXACTSIZE
syn keyword xsMacro PERL_PV_PRETTY_LTGT PERL_PV_PRETTY_NOCLEAR
syn keyword xsMacro PERL_PV_PRETTY_QUOTE PERL_PV_PRETTY_REGPROP PERL_QUAD_MAX
syn keyword xsMacro PERL_QUAD_MIN PERL_REENTR_API PERL_REGMATCH_SLAB_SLOTS
syn keyword xsMacro PERL_RELOCATABLE_INC PERL_REVISION PERL_SAWAMPERSAND
syn keyword xsMacro PERL_SCAN_ALLOW_UNDERSCORES PERL_SCAN_DISALLOW_PREFIX
syn keyword xsMacro PERL_SCAN_GREATER_THAN_UV_MAX PERL_SCAN_SILENT_ILLDIGIT
syn keyword xsMacro PERL_SCAN_SILENT_NON_PORTABLE PERL_SCAN_TRAILING
syn keyword xsMacro PERL_SCNfldbl PERL_SCRIPT_MODE PERL_SEEN_HV_FUNC_H
syn keyword xsMacro PERL_SET_CONTEXT PERL_SET_INTERP PERL_SET_PHASE
syn keyword xsMacro PERL_SET_THX PERL_SHORT_MAX PERL_SHORT_MIN
syn keyword xsMacro PERL_SIGNALS_UNSAFE_FLAG PERL_SNPRINTF_CHECK
syn keyword xsMacro PERL_STACK_OVERFLOW_CHECK PERL_STATIC_INLINE
syn keyword xsMacro PERL_STATIC_INLINE_NO_RET PERL_STATIC_NO_RET
syn keyword xsMacro PERL_STRLEN_EXPAND_SHIFT PERL_STRLEN_ROUNDUP
syn keyword xsMacro PERL_STRLEN_ROUNDUP_QUANTUM PERL_SUBVERSION
syn keyword xsMacro PERL_SUB_DEPTH_WARN PERL_SYS_FPU_INIT PERL_SYS_INIT
syn keyword xsMacro PERL_SYS_INIT3 PERL_SYS_INIT3_BODY PERL_SYS_INIT_BODY
syn keyword xsMacro PERL_SYS_TERM PERL_SYS_TERM_BODY PERL_TARGETARCH
syn keyword xsMacro PERL_UCHAR_MAX PERL_UCHAR_MIN PERL_UINT_MAX PERL_UINT_MIN
syn keyword xsMacro PERL_ULONG_MAX PERL_ULONG_MIN PERL_UNICODE_ALL_FLAGS
syn keyword xsMacro PERL_UNICODE_ARGV PERL_UNICODE_ARGV_FLAG
syn keyword xsMacro PERL_UNICODE_DEFAULT_FLAGS PERL_UNICODE_IN
syn keyword xsMacro PERL_UNICODE_INOUT PERL_UNICODE_INOUT_FLAG
syn keyword xsMacro PERL_UNICODE_IN_FLAG PERL_UNICODE_LOCALE
syn keyword xsMacro PERL_UNICODE_LOCALE_FLAG PERL_UNICODE_MAX
syn keyword xsMacro PERL_UNICODE_OUT PERL_UNICODE_OUT_FLAG PERL_UNICODE_STD
syn keyword xsMacro PERL_UNICODE_STDERR PERL_UNICODE_STDERR_FLAG
syn keyword xsMacro PERL_UNICODE_STDIN PERL_UNICODE_STDIN_FLAG
syn keyword xsMacro PERL_UNICODE_STDOUT PERL_UNICODE_STDOUT_FLAG
syn keyword xsMacro PERL_UNICODE_STD_FLAG PERL_UNICODE_UTF8CACHEASSERT
syn keyword xsMacro PERL_UNICODE_UTF8CACHEASSERT_FLAG
syn keyword xsMacro PERL_UNICODE_WIDESYSCALLS PERL_UNICODE_WIDESYSCALLS_FLAG
syn keyword xsMacro PERL_UNUSED_ARG PERL_UNUSED_CONTEXT PERL_UNUSED_DECL
syn keyword xsMacro PERL_UNUSED_RESULT PERL_UNUSED_VAR PERL_UQUAD_MAX
syn keyword xsMacro PERL_UQUAD_MIN PERL_USES_PL_PIDSTATUS
syn keyword xsMacro PERL_USE_GCC_BRACE_GROUPS PERL_USHORT_MAX PERL_USHORT_MIN
syn keyword xsMacro PERL_VERSION PERL_VERSION_DECIMAL PERL_VERSION_GE
syn keyword xsMacro PERL_VERSION_LT PERL_VERSION_STRING
syn keyword xsMacro PERL_WAIT_FOR_CHILDREN PERL_WARNHOOK_FATAL
syn keyword xsMacro PERL_WRITE_MSG_TO_CONSOLE PERL_XS_EXPORT_C
syn keyword xsMacro PHASE_CHANGE_PROBE PHOSTNAME PIPESOCK_MODE PIPE_OPEN_MODE
syn keyword xsMacro PLUGEXPR PLUGSTMT PLUS PL_AboveLatin1 PL_Argv PL_Cmd
syn keyword xsMacro PL_DBcontrol PL_DBcv PL_DBgv PL_DBline PL_DBsignal
syn keyword xsMacro PL_DBsignal_iv PL_DBsingle PL_DBsingle_iv PL_DBsub
syn keyword xsMacro PL_DBtrace PL_DBtrace_iv PL_Dir PL_Env PL_GCB_invlist
syn keyword xsMacro PL_Gappctx PL_Gcheck PL_Gcheck_mutex PL_Gcsighandlerp
syn keyword xsMacro PL_Gcurinterp PL_Gdo_undump PL_Gdollarzero_mutex
syn keyword xsMacro PL_Gfold_locale PL_Ghash_seed PL_Ghash_seed_set
syn keyword xsMacro PL_Ghints_mutex PL_Gkeyword_plugin PL_Gmalloc_mutex
syn keyword xsMacro PL_Gmmap_page_size PL_Gmy_ctx_mutex PL_Gmy_cxt_index
syn keyword xsMacro PL_Gop_mutex PL_Gop_seq PL_Gop_sequence
syn keyword xsMacro PL_Gperlio_debug_fd PL_Gperlio_fd_refcnt
syn keyword xsMacro PL_Gperlio_fd_refcnt_size PL_Gperlio_mutex PL_Gppaddr
syn keyword xsMacro PL_Gsh_path PL_Gsig_defaulting PL_Gsig_handlers_initted
syn keyword xsMacro PL_Gsig_ignoring PL_Gsig_trapped PL_Gsigfpe_saved
syn keyword xsMacro PL_Gsv_placeholder PL_Gthr_key PL_Gtimesbase
syn keyword xsMacro PL_Guse_safe_putenv PL_Gveto_cleanup PL_Gwatch_pvx
syn keyword xsMacro PL_HASH_RAND_BITS_ENABLED PL_HasMultiCharFold PL_InBitmap
syn keyword xsMacro PL_LIO PL_Latin1 PL_Mem PL_MemParse PL_MemShared
syn keyword xsMacro PL_NonL1NonFinalFold PL_Posix_ptrs PL_Proc
syn keyword xsMacro PL_RANDOM_STATE_TYPE PL_SB_invlist PL_Sock PL_StdIO PL_Sv
syn keyword xsMacro PL_UpperLatin1 PL_WB_invlist PL_XPosix_ptrs PL_Xpv
syn keyword xsMacro PL_amagic_generation PL_an PL_appctx PL_argvgv
syn keyword xsMacro PL_argvout_stack PL_argvoutgv PL_basetime PL_beginav
syn keyword xsMacro PL_beginav_save PL_blockhooks PL_body_arenas
syn keyword xsMacro PL_body_roots PL_bodytarget PL_breakable_sub_gen
syn keyword xsMacro PL_check_mutex PL_checkav PL_checkav_save PL_chopset
syn keyword xsMacro PL_clocktick PL_collation_ix PL_collation_name
syn keyword xsMacro PL_collation_standard PL_collxfrm_base PL_collxfrm_mult
syn keyword xsMacro PL_colors PL_colorset PL_compcv PL_compiling PL_comppad
syn keyword xsMacro PL_comppad_name PL_comppad_name_fill
syn keyword xsMacro PL_comppad_name_floor PL_constpadix PL_cop_seqmax
syn keyword xsMacro PL_cryptseen PL_cshlen PL_csighandlerp PL_curcop
syn keyword xsMacro PL_curcopdb PL_curinterp PL_curpad PL_curpm PL_curstack
syn keyword xsMacro PL_curstackinfo PL_curstash PL_curstname
syn keyword xsMacro PL_custom_op_descs PL_custom_op_names PL_custom_ops
syn keyword xsMacro PL_cv_has_eval PL_dbargs PL_debstash PL_debug
syn keyword xsMacro PL_debug_pad PL_def_layerlist PL_defgv PL_defoutgv
syn keyword xsMacro PL_defstash PL_delaymagic PL_delaymagic_egid
syn keyword xsMacro PL_delaymagic_euid PL_delaymagic_gid PL_delaymagic_uid
syn keyword xsMacro PL_destroyhook PL_diehook PL_dirty PL_do_undump
syn keyword xsMacro PL_dollarzero_mutex PL_doswitches PL_dowarn PL_dumper_fd
syn keyword xsMacro PL_dumpindent PL_e_script PL_efloatbuf PL_efloatsize
syn keyword xsMacro PL_encoding PL_endav PL_envgv PL_errgv PL_errors
syn keyword xsMacro PL_eval_root PL_eval_start PL_evalseq PL_exit_flags
syn keyword xsMacro PL_exitlist PL_exitlistlen PL_fdpid PL_filemode
syn keyword xsMacro PL_firstgv PL_forkprocess PL_formtarget PL_generation
syn keyword xsMacro PL_gensym PL_globalstash PL_globhook PL_hash_rand_bits
syn keyword xsMacro PL_hash_rand_bits_enabled PL_hash_seed PL_hash_seed_set
syn keyword xsMacro PL_hintgv PL_hints PL_hints_mutex PL_hv_fetch_ent_mh
syn keyword xsMacro PL_in_clean_all PL_in_clean_objs PL_in_eval
syn keyword xsMacro PL_in_load_module PL_in_utf8_CTYPE_locale PL_incgv
syn keyword xsMacro PL_initav PL_inplace PL_isarev PL_keyword_plugin
syn keyword xsMacro PL_known_layers PL_last_in_gv PL_last_swash_hv
syn keyword xsMacro PL_last_swash_key PL_last_swash_klen PL_last_swash_slen
syn keyword xsMacro PL_last_swash_tmps PL_lastfd PL_lastgotoprobe
syn keyword xsMacro PL_laststatval PL_laststype PL_lex_encoding PL_localizing
syn keyword xsMacro PL_localpatches PL_lockhook PL_main_cv PL_main_root
syn keyword xsMacro PL_main_start PL_mainstack PL_malloc_mutex PL_markstack
syn keyword xsMacro PL_markstack_max PL_markstack_ptr PL_max_intro_pending
syn keyword xsMacro PL_maxo PL_maxsysfd PL_memory_debug_header PL_mess_sv
syn keyword xsMacro PL_min_intro_pending PL_minus_E PL_minus_F PL_minus_a
syn keyword xsMacro PL_minus_c PL_minus_l PL_minus_n PL_minus_p
syn keyword xsMacro PL_mmap_page_size PL_modcount PL_modglobal
syn keyword xsMacro PL_multideref_pc PL_my_ctx_mutex PL_my_cxt_index
syn keyword xsMacro PL_my_cxt_keys PL_my_cxt_list PL_my_cxt_size PL_nomemok
syn keyword xsMacro PL_numeric_local PL_numeric_name PL_numeric_radix_sv
syn keyword xsMacro PL_numeric_standard PL_ofsgv PL_oldname PL_op
syn keyword xsMacro PL_op_exec_cnt PL_op_mask PL_op_mutex PL_op_seq
syn keyword xsMacro PL_op_sequence PL_opfreehook PL_origalen PL_origargc
syn keyword xsMacro PL_origargv PL_origenviron PL_origfilename PL_ors_sv
syn keyword xsMacro PL_osname PL_pad_reset_pending PL_padix PL_padix_floor
syn keyword xsMacro PL_padlist_generation PL_padname_const PL_padname_undef
syn keyword xsMacro PL_parser PL_patchlevel PL_peepp PL_perl_destruct_level
syn keyword xsMacro PL_perldb PL_perlio PL_perlio_debug_fd
syn keyword xsMacro PL_perlio_fd_refcnt PL_perlio_fd_refcnt_size
syn keyword xsMacro PL_perlio_mutex PL_phase PL_pidstatus PL_preambleav
syn keyword xsMacro PL_profiledata PL_psig_name PL_psig_pend PL_psig_ptr
syn keyword xsMacro PL_ptr_table PL_random_state PL_reentrant_buffer
syn keyword xsMacro PL_reentrant_retint PL_reg_curpm PL_regex_pad
syn keyword xsMacro PL_regex_padav PL_registered_mros PL_regmatch_slab
syn keyword xsMacro PL_regmatch_state PL_replgv PL_restartjmpenv PL_restartop
syn keyword xsMacro PL_rpeepp PL_rs PL_runops PL_savebegin PL_savestack
syn keyword xsMacro PL_savestack_ix PL_savestack_max PL_sawalias
syn keyword xsMacro PL_sawampersand PL_scopestack PL_scopestack_ix
syn keyword xsMacro PL_scopestack_max PL_scopestack_name PL_secondgv
syn keyword xsMacro PL_sharehook PL_sig_defaulting PL_sig_handlers_initted
syn keyword xsMacro PL_sig_ignoring PL_sig_pending PL_sig_trapped
syn keyword xsMacro PL_sigfpe_saved PL_sighandlerp PL_signalhook PL_signals
syn keyword xsMacro PL_sort_RealCmp PL_sortcop PL_sortstash PL_splitstr
syn keyword xsMacro PL_srand_called PL_stack_base PL_stack_max PL_stack_sp
syn keyword xsMacro PL_start_env PL_stashcache PL_stashpad PL_stashpadix
syn keyword xsMacro PL_stashpadmax PL_statbuf PL_statcache PL_statgv
syn keyword xsMacro PL_statname PL_statusvalue PL_statusvalue_posix
syn keyword xsMacro PL_statusvalue_vms PL_stderrgv PL_stdingv PL_strtab
syn keyword xsMacro PL_sub_generation PL_subline PL_subname PL_sv_arenaroot
syn keyword xsMacro PL_sv_consts PL_sv_count PL_sv_no PL_sv_placeholder
syn keyword xsMacro PL_sv_root PL_sv_serial PL_sv_undef PL_sv_yes
syn keyword xsMacro PL_sys_intern PL_taint_warn PL_tainted PL_tainting
syn keyword xsMacro PL_thr_key PL_threadhook PL_timesbase PL_timesbuf
syn keyword xsMacro PL_tmps_floor PL_tmps_ix PL_tmps_max PL_tmps_stack
syn keyword xsMacro PL_top_env PL_toptarget PL_unicode PL_unitcheckav
syn keyword xsMacro PL_unitcheckav_save PL_unlockhook PL_unsafe
syn keyword xsMacro PL_use_safe_putenv PL_utf8_charname_begin
syn keyword xsMacro PL_utf8_charname_continue PL_utf8_foldable
syn keyword xsMacro PL_utf8_foldclosures PL_utf8_idcont PL_utf8_idstart
syn keyword xsMacro PL_utf8_mark PL_utf8_perl_idcont PL_utf8_perl_idstart
syn keyword xsMacro PL_utf8_swash_ptrs PL_utf8_tofold PL_utf8_tolower
syn keyword xsMacro PL_utf8_totitle PL_utf8_toupper PL_utf8_xidcont
syn keyword xsMacro PL_utf8_xidstart PL_utf8cache PL_utf8locale
syn keyword xsMacro PL_veto_cleanup PL_vtbl_arylen PL_vtbl_arylen_p
syn keyword xsMacro PL_vtbl_backref PL_vtbl_bm PL_vtbl_checkcall
syn keyword xsMacro PL_vtbl_collxfrm PL_vtbl_dbline PL_vtbl_debugvar
syn keyword xsMacro PL_vtbl_defelem PL_vtbl_env PL_vtbl_envelem PL_vtbl_fm
syn keyword xsMacro PL_vtbl_hints PL_vtbl_hintselem PL_vtbl_isa
syn keyword xsMacro PL_vtbl_isaelem PL_vtbl_lvref PL_vtbl_mglob PL_vtbl_nkeys
syn keyword xsMacro PL_vtbl_ovrld PL_vtbl_pack PL_vtbl_packelem PL_vtbl_pos
syn keyword xsMacro PL_vtbl_regdata PL_vtbl_regdatum PL_vtbl_regexp
syn keyword xsMacro PL_vtbl_sigelem PL_vtbl_substr PL_vtbl_sv PL_vtbl_taint
syn keyword xsMacro PL_vtbl_utf8 PL_vtbl_uvar PL_vtbl_vec PL_warn_locale
syn keyword xsMacro PL_warnhook PL_watch_pvx PL_watchaddr PL_watchok
syn keyword xsMacro PL_xsubfilename PMFUNC PM_GETRE PM_SETRE PMf_BASE_SHIFT
syn keyword xsMacro PMf_CHARSET PMf_CODELIST_PRIVATE PMf_CONST PMf_CONTINUE
syn keyword xsMacro PMf_EVAL PMf_EXTENDED PMf_EXTENDED_MORE PMf_FOLD
syn keyword xsMacro PMf_GLOBAL PMf_HAS_CV PMf_IS_QR PMf_KEEP PMf_KEEPCOPY
syn keyword xsMacro PMf_MULTILINE PMf_NOCAPTURE PMf_NONDESTRUCT PMf_ONCE
syn keyword xsMacro PMf_RETAINT PMf_SINGLELINE PMf_SPLIT PMf_STRICT PMf_USED
syn keyword xsMacro PMf_USE_RE_EVAL PNf PNfARG POPBLOCK POPEVAL POPFORMAT
syn keyword xsMacro POPLOOP POPMARK POPSTACK POPSTACK_TO POPSUB POPSUBST
syn keyword xsMacro POP_MULTICALL POP_SAVEARRAY POPi POPl POPn POPp POPpbytex
syn keyword xsMacro POPpconstx POPpx POPs POPu POPul POSIXA POSIXD POSIXL
syn keyword xsMacro POSIXU POSIX_CC_COUNT POSIX_SWASH_COUNT POSTDEC POSTINC
syn keyword xsMacro POSTJOIN POWOP PP PREC_LOW PREDEC PREGf_ANCH
syn keyword xsMacro PREGf_ANCH_GPOS PREGf_ANCH_MBOL PREGf_ANCH_SBOL
syn keyword xsMacro PREGf_CANY_SEEN PREGf_CUTGROUP_SEEN PREGf_GPOS_FLOAT
syn keyword xsMacro PREGf_GPOS_SEEN PREGf_IMPLICIT PREGf_NAUGHTY PREGf_NOSCAN
syn keyword xsMacro PREGf_SKIP PREGf_USE_RE_EVAL PREGf_VERBARG_SEEN PREINC
syn keyword xsMacro PRESCAN_VERSION PREVOPER PRINTF_FORMAT_NULL_OK PRIVATEREF
syn keyword xsMacro PRIVLIB PRIVLIB_EXP PRIVSHIFT PROCSELFEXE_PATH PRUNE
syn keyword xsMacro PSEUDO PTHREAD_ATFORK PTHREAD_ATTR_SETDETACHSTATE
syn keyword xsMacro PTHREAD_CREATE PTHREAD_CREATE_JOINABLE
syn keyword xsMacro PTHREAD_GETSPECIFIC PTHREAD_GETSPECIFIC_INT PTR2IV PTR2NV
syn keyword xsMacro PTR2UV PTR2nat PTR2ul PTRSIZE PTRV PUSHBLOCK PUSHEVAL
syn keyword xsMacro PUSHFORMAT PUSHGIVEN PUSHLOOP_FOR PUSHLOOP_PLAIN PUSHMARK
syn keyword xsMacro PUSHSTACK PUSHSTACKi PUSHSUB PUSHSUBST PUSHSUB_BASE
syn keyword xsMacro PUSHSUB_DB PUSHSUB_GET_LVALUE_MASK PUSHTARG PUSHWHEN
syn keyword xsMacro PUSH_MULTICALL PUSH_MULTICALL_FLAGS PUSHi PUSHmortal
syn keyword xsMacro PUSHn PUSHp PUSHs PUSHu PUTBACK PWGECOS PWPASSWD PadARRAY
syn keyword xsMacro PadMAX PadlistARRAY PadlistMAX PadlistNAMES
syn keyword xsMacro PadlistNAMESARRAY PadlistNAMESMAX PadlistREFCNT
syn keyword xsMacro PadnameFLAGS PadnameIsOUR PadnameIsSTATE
syn keyword xsMacro PadnameIsSTATE_on PadnameLEN PadnameLVALUE
syn keyword xsMacro PadnameLVALUE_on PadnameOURSTASH PadnameOURSTASH_set
syn keyword xsMacro PadnameOUTER PadnamePROTOCV PadnamePV PadnameREFCNT
syn keyword xsMacro PadnameREFCNT_dec PadnameSV PadnameTYPE PadnameTYPE_set
syn keyword xsMacro PadnameUTF8 PadnamelistARRAY PadnamelistMAX
syn keyword xsMacro PadnamelistMAXNAMED PadnamelistREFCNT
syn keyword xsMacro PadnamelistREFCNT_dec Pause PeRl_CaTiFy PeRl_INT64_C
syn keyword xsMacro PeRl_StGiFy PeRl_UINT64_C PerlDir_chdir PerlDir_close
syn keyword xsMacro PerlDir_mapA PerlDir_mapW PerlDir_mkdir PerlDir_open
syn keyword xsMacro PerlDir_read PerlDir_rewind PerlDir_rmdir PerlDir_seek
syn keyword xsMacro PerlDir_tell PerlEnv_ENVgetenv PerlEnv_ENVgetenv_len
syn keyword xsMacro PerlEnv_clearenv PerlEnv_free_childdir
syn keyword xsMacro PerlEnv_free_childenv PerlEnv_get_child_IO
syn keyword xsMacro PerlEnv_get_childdir PerlEnv_get_childenv PerlEnv_getenv
syn keyword xsMacro PerlEnv_getenv_len PerlEnv_lib_path PerlEnv_os_id
syn keyword xsMacro PerlEnv_putenv PerlEnv_sitelib_path PerlEnv_uname
syn keyword xsMacro PerlEnv_vendorlib_path PerlIOArg PerlIOBase PerlIONext
syn keyword xsMacro PerlIOSelf PerlIOValid PerlIO_canset_cnt
syn keyword xsMacro PerlIO_exportFILE PerlIO_fast_gets PerlIO_fdopen
syn keyword xsMacro PerlIO_findFILE PerlIO_getc PerlIO_getname
syn keyword xsMacro PerlIO_has_base PerlIO_has_cntptr PerlIO_importFILE
syn keyword xsMacro PerlIO_isutf8 PerlIO_open PerlIO_printf PerlIO_putc
syn keyword xsMacro PerlIO_puts PerlIO_releaseFILE PerlIO_reopen
syn keyword xsMacro PerlIO_rewind PerlIO_stdoutf PerlIO_tmpfile PerlIO_ungetc
syn keyword xsMacro PerlIO_vprintf PerlLIO_access PerlLIO_chmod PerlLIO_chown
syn keyword xsMacro PerlLIO_chsize PerlLIO_close PerlLIO_dup PerlLIO_dup2
syn keyword xsMacro PerlLIO_flock PerlLIO_fstat PerlLIO_ioctl PerlLIO_isatty
syn keyword xsMacro PerlLIO_link PerlLIO_lseek PerlLIO_lstat PerlLIO_mkstemp
syn keyword xsMacro PerlLIO_mktemp PerlLIO_open PerlLIO_open3 PerlLIO_read
syn keyword xsMacro PerlLIO_rename PerlLIO_setmode PerlLIO_stat
syn keyword xsMacro PerlLIO_tmpnam PerlLIO_umask PerlLIO_unlink PerlLIO_utime
syn keyword xsMacro PerlLIO_write PerlMemParse_calloc PerlMemParse_free
syn keyword xsMacro PerlMemParse_free_lock PerlMemParse_get_lock
syn keyword xsMacro PerlMemParse_is_locked PerlMemParse_malloc
syn keyword xsMacro PerlMemParse_realloc PerlMemShared_calloc
syn keyword xsMacro PerlMemShared_free PerlMemShared_free_lock
syn keyword xsMacro PerlMemShared_get_lock PerlMemShared_is_locked
syn keyword xsMacro PerlMemShared_malloc PerlMemShared_realloc PerlMem_calloc
syn keyword xsMacro PerlMem_free PerlMem_free_lock PerlMem_get_lock
syn keyword xsMacro PerlMem_is_locked PerlMem_malloc PerlMem_realloc
syn keyword xsMacro PerlProc_DynaLoad PerlProc_GetOSError PerlProc__exit
syn keyword xsMacro PerlProc_abort PerlProc_crypt PerlProc_execl
syn keyword xsMacro PerlProc_execv PerlProc_execvp PerlProc_exit
syn keyword xsMacro PerlProc_fork PerlProc_getegid PerlProc_geteuid
syn keyword xsMacro PerlProc_getgid PerlProc_getlogin PerlProc_getpid
syn keyword xsMacro PerlProc_gettimeofday PerlProc_getuid PerlProc_kill
syn keyword xsMacro PerlProc_killpg PerlProc_lasthost PerlProc_longjmp
syn keyword xsMacro PerlProc_pause PerlProc_pclose PerlProc_pipe
syn keyword xsMacro PerlProc_popen PerlProc_popen_list PerlProc_setgid
syn keyword xsMacro PerlProc_setjmp PerlProc_setuid PerlProc_signal
syn keyword xsMacro PerlProc_sleep PerlProc_spawnvp PerlProc_times
syn keyword xsMacro PerlProc_wait PerlProc_waitpid PerlSIO_canset_cnt
syn keyword xsMacro PerlSIO_clearerr PerlSIO_fast_gets PerlSIO_fclose
syn keyword xsMacro PerlSIO_fdopen PerlSIO_fdupopen PerlSIO_feof
syn keyword xsMacro PerlSIO_ferror PerlSIO_fflush PerlSIO_fgetc
syn keyword xsMacro PerlSIO_fgetpos PerlSIO_fgets PerlSIO_fileno
syn keyword xsMacro PerlSIO_fopen PerlSIO_fputc PerlSIO_fputs PerlSIO_fread
syn keyword xsMacro PerlSIO_freopen PerlSIO_fseek PerlSIO_fsetpos
syn keyword xsMacro PerlSIO_ftell PerlSIO_fwrite PerlSIO_get_base
syn keyword xsMacro PerlSIO_get_bufsiz PerlSIO_get_cnt PerlSIO_get_ptr
syn keyword xsMacro PerlSIO_has_base PerlSIO_has_cntptr PerlSIO_init
syn keyword xsMacro PerlSIO_printf PerlSIO_rewind PerlSIO_set_cnt
syn keyword xsMacro PerlSIO_set_ptr PerlSIO_setbuf PerlSIO_setlinebuf
syn keyword xsMacro PerlSIO_setvbuf PerlSIO_stderr PerlSIO_stdin
syn keyword xsMacro PerlSIO_stdout PerlSIO_stdoutf PerlSIO_tmpfile
syn keyword xsMacro PerlSIO_ungetc PerlSIO_vprintf PerlSock_accept
syn keyword xsMacro PerlSock_bind PerlSock_closesocket PerlSock_connect
syn keyword xsMacro PerlSock_endhostent PerlSock_endnetent
syn keyword xsMacro PerlSock_endprotoent PerlSock_endservent
syn keyword xsMacro PerlSock_gethostbyaddr PerlSock_gethostbyname
syn keyword xsMacro PerlSock_gethostent PerlSock_gethostname
syn keyword xsMacro PerlSock_getnetbyaddr PerlSock_getnetbyname
syn keyword xsMacro PerlSock_getnetent PerlSock_getpeername
syn keyword xsMacro PerlSock_getprotobyname PerlSock_getprotobynumber
syn keyword xsMacro PerlSock_getprotoent PerlSock_getservbyname
syn keyword xsMacro PerlSock_getservbyport PerlSock_getservent
syn keyword xsMacro PerlSock_getsockname PerlSock_getsockopt PerlSock_htonl
syn keyword xsMacro PerlSock_htons PerlSock_inet_addr PerlSock_inet_ntoa
syn keyword xsMacro PerlSock_listen PerlSock_ntohl PerlSock_ntohs
syn keyword xsMacro PerlSock_recv PerlSock_recvfrom PerlSock_select
syn keyword xsMacro PerlSock_send PerlSock_sendto PerlSock_sethostent
syn keyword xsMacro PerlSock_setnetent PerlSock_setprotoent
syn keyword xsMacro PerlSock_setservent PerlSock_setsockopt PerlSock_shutdown
syn keyword xsMacro PerlSock_socket PerlSock_socketpair Perl_acos Perl_asin
syn keyword xsMacro Perl_assert Perl_atan Perl_atan2 Perl_atof Perl_atof2
syn keyword xsMacro Perl_ceil Perl_cos Perl_cosh Perl_custom_op_xop
syn keyword xsMacro Perl_debug_log Perl_drand48 Perl_drand48_init
syn keyword xsMacro Perl_error_log Perl_exp Perl_floor Perl_fmod
syn keyword xsMacro Perl_fp_class_denorm Perl_fp_class_inf Perl_fp_class_nan
syn keyword xsMacro Perl_fp_class_ndenorm Perl_fp_class_ninf
syn keyword xsMacro Perl_fp_class_nnorm Perl_fp_class_norm
syn keyword xsMacro Perl_fp_class_nzero Perl_fp_class_pdenorm
syn keyword xsMacro Perl_fp_class_pinf Perl_fp_class_pnorm
syn keyword xsMacro Perl_fp_class_pzero Perl_fp_class_qnan Perl_fp_class_snan
syn keyword xsMacro Perl_fp_class_zero Perl_free_c_backtrace Perl_frexp
syn keyword xsMacro Perl_isfinite Perl_isfinitel Perl_isinf Perl_isnan
syn keyword xsMacro Perl_ldexp Perl_log Perl_log10 Perl_malloc_good_size
syn keyword xsMacro Perl_modf Perl_pow Perl_pp_accept Perl_pp_aelemfast_lex
syn keyword xsMacro Perl_pp_andassign Perl_pp_avalues Perl_pp_bind
syn keyword xsMacro Perl_pp_bit_xor Perl_pp_chmod Perl_pp_chomp
syn keyword xsMacro Perl_pp_connect Perl_pp_cos Perl_pp_custom
syn keyword xsMacro Perl_pp_dbmclose Perl_pp_dofile Perl_pp_dor
syn keyword xsMacro Perl_pp_dorassign Perl_pp_dump Perl_pp_egrent
syn keyword xsMacro Perl_pp_enetent Perl_pp_eprotoent Perl_pp_epwent
syn keyword xsMacro Perl_pp_eservent Perl_pp_exp Perl_pp_fcntl
syn keyword xsMacro Perl_pp_ftatime Perl_pp_ftbinary Perl_pp_ftblk
syn keyword xsMacro Perl_pp_ftchr Perl_pp_ftctime Perl_pp_ftdir
syn keyword xsMacro Perl_pp_fteexec Perl_pp_fteowned Perl_pp_fteread
syn keyword xsMacro Perl_pp_ftewrite Perl_pp_ftfile Perl_pp_ftmtime
syn keyword xsMacro Perl_pp_ftpipe Perl_pp_ftrexec Perl_pp_ftrwrite
syn keyword xsMacro Perl_pp_ftsgid Perl_pp_ftsize Perl_pp_ftsock
syn keyword xsMacro Perl_pp_ftsuid Perl_pp_ftsvtx Perl_pp_ftzero
syn keyword xsMacro Perl_pp_getpeername Perl_pp_getsockname Perl_pp_ggrgid
syn keyword xsMacro Perl_pp_ggrnam Perl_pp_ghbyaddr Perl_pp_ghbyname
syn keyword xsMacro Perl_pp_gnbyaddr Perl_pp_gnbyname Perl_pp_gpbyname
syn keyword xsMacro Perl_pp_gpbynumber Perl_pp_gpwnam Perl_pp_gpwuid
syn keyword xsMacro Perl_pp_gsbyname Perl_pp_gsbyport Perl_pp_gsockopt
syn keyword xsMacro Perl_pp_hex Perl_pp_i_postdec Perl_pp_i_postinc
syn keyword xsMacro Perl_pp_i_predec Perl_pp_i_preinc Perl_pp_keys
syn keyword xsMacro Perl_pp_kill Perl_pp_lcfirst Perl_pp_lineseq
syn keyword xsMacro Perl_pp_listen Perl_pp_localtime Perl_pp_log
syn keyword xsMacro Perl_pp_lstat Perl_pp_mapstart Perl_pp_msgctl
syn keyword xsMacro Perl_pp_msgget Perl_pp_msgrcv Perl_pp_msgsnd
syn keyword xsMacro Perl_pp_nbit_xor Perl_pp_orassign Perl_pp_padany
syn keyword xsMacro Perl_pp_pop Perl_pp_postdec Perl_pp_predec Perl_pp_reach
syn keyword xsMacro Perl_pp_read Perl_pp_recv Perl_pp_regcmaybe
syn keyword xsMacro Perl_pp_rindex Perl_pp_rv2hv Perl_pp_rvalues Perl_pp_say
syn keyword xsMacro Perl_pp_sbit_xor Perl_pp_scalar Perl_pp_schomp
syn keyword xsMacro Perl_pp_scope Perl_pp_seek Perl_pp_semop Perl_pp_send
syn keyword xsMacro Perl_pp_sge Perl_pp_sgrent Perl_pp_sgt Perl_pp_shmctl
syn keyword xsMacro Perl_pp_shmget Perl_pp_shmread Perl_pp_shutdown
syn keyword xsMacro Perl_pp_slt Perl_pp_snetent Perl_pp_socket
syn keyword xsMacro Perl_pp_sprotoent Perl_pp_spwent Perl_pp_sqrt
syn keyword xsMacro Perl_pp_sservent Perl_pp_ssockopt Perl_pp_symlink
syn keyword xsMacro Perl_pp_transr Perl_pp_unlink Perl_pp_utime
syn keyword xsMacro Perl_pp_values Perl_safesysmalloc_size Perl_sharepvn
syn keyword xsMacro Perl_signbit Perl_sin Perl_sinh Perl_sqrt Perl_strtod
syn keyword xsMacro Perl_tan Perl_tanh Perl_va_copy PmopSTASH PmopSTASHPV
syn keyword xsMacro PmopSTASHPV_set PmopSTASH_set Poison PoisonFree PoisonNew
syn keyword xsMacro PoisonPADLIST PoisonWith QR_PAT_MODS QUADKIND QUAD_IS_INT
syn keyword xsMacro QUAD_IS_INT64_T QUAD_IS_LONG QUAD_IS_LONG_LONG
syn keyword xsMacro QUAD_IS___INT64 QUESTION_MARK_CTRL QWLIST RANDBITS
syn keyword xsMacro RANDOM_R_PROTO RD_NODATA READDIR64_R_PROTO
syn keyword xsMacro READDIR_R_PROTO READ_XDIGIT REENTRANT_PROTO_B_B
syn keyword xsMacro REENTRANT_PROTO_B_BI REENTRANT_PROTO_B_BW
syn keyword xsMacro REENTRANT_PROTO_B_CCD REENTRANT_PROTO_B_CCS
syn keyword xsMacro REENTRANT_PROTO_B_IBI REENTRANT_PROTO_B_IBW
syn keyword xsMacro REENTRANT_PROTO_B_SB REENTRANT_PROTO_B_SBI
syn keyword xsMacro REENTRANT_PROTO_I_BI REENTRANT_PROTO_I_BW
syn keyword xsMacro REENTRANT_PROTO_I_CCSBWR REENTRANT_PROTO_I_CCSD
syn keyword xsMacro REENTRANT_PROTO_I_CII REENTRANT_PROTO_I_CIISD
syn keyword xsMacro REENTRANT_PROTO_I_CSBI REENTRANT_PROTO_I_CSBIR
syn keyword xsMacro REENTRANT_PROTO_I_CSBWR REENTRANT_PROTO_I_CSBWRE
syn keyword xsMacro REENTRANT_PROTO_I_CSD REENTRANT_PROTO_I_CWISBWRE
syn keyword xsMacro REENTRANT_PROTO_I_CWISD REENTRANT_PROTO_I_D
syn keyword xsMacro REENTRANT_PROTO_I_H REENTRANT_PROTO_I_IBI
syn keyword xsMacro REENTRANT_PROTO_I_IBW REENTRANT_PROTO_I_ICBI
syn keyword xsMacro REENTRANT_PROTO_I_ICSBWR REENTRANT_PROTO_I_ICSD
syn keyword xsMacro REENTRANT_PROTO_I_ID REENTRANT_PROTO_I_IISD
syn keyword xsMacro REENTRANT_PROTO_I_ISBWR REENTRANT_PROTO_I_ISD
syn keyword xsMacro REENTRANT_PROTO_I_LISBI REENTRANT_PROTO_I_LISD
syn keyword xsMacro REENTRANT_PROTO_I_SB REENTRANT_PROTO_I_SBI
syn keyword xsMacro REENTRANT_PROTO_I_SBIE REENTRANT_PROTO_I_SBIH
syn keyword xsMacro REENTRANT_PROTO_I_SBIR REENTRANT_PROTO_I_SBWR
syn keyword xsMacro REENTRANT_PROTO_I_SBWRE REENTRANT_PROTO_I_SD
syn keyword xsMacro REENTRANT_PROTO_I_TISD REENTRANT_PROTO_I_TS
syn keyword xsMacro REENTRANT_PROTO_I_TSBI REENTRANT_PROTO_I_TSBIR
syn keyword xsMacro REENTRANT_PROTO_I_TSBWR REENTRANT_PROTO_I_TSR
syn keyword xsMacro REENTRANT_PROTO_I_TsISBWRE REENTRANT_PROTO_I_UISBWRE
syn keyword xsMacro REENTRANT_PROTO_I_uISBWRE REENTRANT_PROTO_S_CBI
syn keyword xsMacro REENTRANT_PROTO_S_CCSBI REENTRANT_PROTO_S_CIISBIE
syn keyword xsMacro REENTRANT_PROTO_S_CSBI REENTRANT_PROTO_S_CSBIE
syn keyword xsMacro REENTRANT_PROTO_S_CWISBIE REENTRANT_PROTO_S_CWISBWIE
syn keyword xsMacro REENTRANT_PROTO_S_ICSBI REENTRANT_PROTO_S_ISBI
syn keyword xsMacro REENTRANT_PROTO_S_LISBI REENTRANT_PROTO_S_SBI
syn keyword xsMacro REENTRANT_PROTO_S_SBIE REENTRANT_PROTO_S_SBW
syn keyword xsMacro REENTRANT_PROTO_S_TISBI REENTRANT_PROTO_S_TSBI
syn keyword xsMacro REENTRANT_PROTO_S_TSBIE REENTRANT_PROTO_S_TWISBIE
syn keyword xsMacro REENTRANT_PROTO_V_D REENTRANT_PROTO_V_H
syn keyword xsMacro REENTRANT_PROTO_V_ID REENTR_H REENTR_MEMZERO REF
syn keyword xsMacro REFCOUNTED_HE_EXISTS REFCOUNTED_HE_KEY_UTF8 REFF REFFA
syn keyword xsMacro REFFL REFFU REFGEN REF_HE_KEY REGMATCH_STATE_MAX
syn keyword xsMacro REGNODE_MAX REGNODE_SIMPLE REGNODE_VARIES REG_ANY
syn keyword xsMacro REG_CANY_SEEN REG_CUTGROUP_SEEN REG_EXTFLAGS_NAME_SIZE
syn keyword xsMacro REG_GOSTART_SEEN REG_GPOS_SEEN REG_INFTY
syn keyword xsMacro REG_INTFLAGS_NAME_SIZE REG_LOOKBEHIND_SEEN REG_MAGIC
syn keyword xsMacro REG_RECURSE_SEEN REG_RUN_ON_COMMENT_SEEN
syn keyword xsMacro REG_TOP_LEVEL_BRANCHES_SEEN REG_UNBOUNDED_QUANTIFIER_SEEN
syn keyword xsMacro REG_UNFOLDED_MULTI_SEEN REG_VERBARG_SEEN
syn keyword xsMacro REG_ZERO_LEN_SEEN RELOP RENUM REQUIRE RESTORE_ERRNO
syn keyword xsMacro RESTORE_LC_NUMERIC RESTORE_LC_NUMERIC_STANDARD
syn keyword xsMacro RESTORE_LC_NUMERIC_UNDERLYING RESTORE_NUMERIC_LOCAL
syn keyword xsMacro RESTORE_NUMERIC_STANDARD RETPUSHNO RETPUSHUNDEF
syn keyword xsMacro RETPUSHYES RETSETNO RETSETTARG RETSETUNDEF RETSETYES
syn keyword xsMacro RETURN RETURNOP RETURNX RETURN_PROBE REXEC_CHECKED
syn keyword xsMacro REXEC_COPY_SKIP_POST REXEC_COPY_SKIP_PRE REXEC_COPY_STR
syn keyword xsMacro REXEC_FAIL_ON_UNDERFLOW REXEC_IGNOREPOS REXEC_NOT_FIRST
syn keyword xsMacro REXEC_SCREAM RE_DEBUG_COMPILE_DUMP RE_DEBUG_COMPILE_FLAGS
syn keyword xsMacro RE_DEBUG_COMPILE_MASK RE_DEBUG_COMPILE_OPTIMISE
syn keyword xsMacro RE_DEBUG_COMPILE_PARSE RE_DEBUG_COMPILE_TEST
syn keyword xsMacro RE_DEBUG_COMPILE_TRIE RE_DEBUG_EXECUTE_INTUIT
syn keyword xsMacro RE_DEBUG_EXECUTE_MASK RE_DEBUG_EXECUTE_MATCH
syn keyword xsMacro RE_DEBUG_EXECUTE_TRIE RE_DEBUG_EXTRA_BUFFERS
syn keyword xsMacro RE_DEBUG_EXTRA_GPOS RE_DEBUG_EXTRA_MASK
syn keyword xsMacro RE_DEBUG_EXTRA_OFFDEBUG RE_DEBUG_EXTRA_OFFSETS
syn keyword xsMacro RE_DEBUG_EXTRA_OPTIMISE RE_DEBUG_EXTRA_STACK
syn keyword xsMacro RE_DEBUG_EXTRA_STATE RE_DEBUG_EXTRA_TRIE RE_DEBUG_FLAG
syn keyword xsMacro RE_DEBUG_FLAGS RE_PV_COLOR_DECL RE_PV_QUOTED_DECL
syn keyword xsMacro RE_SV_DUMPLEN RE_SV_ESCAPE RE_SV_TAIL
syn keyword xsMacro RE_TRACK_PATTERN_OFFSETS RE_TRIE_MAXBUF_INIT
syn keyword xsMacro RE_TRIE_MAXBUF_NAME RMS_DIR RMS_FAC RMS_FEX RMS_FNF
syn keyword xsMacro RMS_IFI RMS_ISI RMS_PRV ROTL32 ROTL64 ROTL_UV
syn keyword xsMacro RUNOPS_DEFAULT RV2CVOPCV_FLAG_MASK RV2CVOPCV_MARK_EARLY
syn keyword xsMacro RV2CVOPCV_MAYBE_NAME_GV RV2CVOPCV_RETURN_NAME_GV
syn keyword xsMacro RV2CVOPCV_RETURN_STUB RX_ANCHORED_SUBSTR RX_ANCHORED_UTF8
syn keyword xsMacro RX_BUFF_IDX_CARET_FULLMATCH RX_BUFF_IDX_CARET_POSTMATCH
syn keyword xsMacro RX_BUFF_IDX_CARET_PREMATCH RX_BUFF_IDX_FULLMATCH
syn keyword xsMacro RX_BUFF_IDX_POSTMATCH RX_BUFF_IDX_PREMATCH
syn keyword xsMacro RX_CHECK_SUBSTR RX_COMPFLAGS RX_ENGINE RX_EXTFLAGS
syn keyword xsMacro RX_FLOAT_SUBSTR RX_FLOAT_UTF8 RX_GOFS RX_HAS_CUTGROUP
syn keyword xsMacro RX_INTFLAGS RX_ISTAINTED RX_LASTCLOSEPAREN RX_LASTPAREN
syn keyword xsMacro RX_MATCH_COPIED RX_MATCH_COPIED_off RX_MATCH_COPIED_on
syn keyword xsMacro RX_MATCH_COPIED_set RX_MATCH_COPY_FREE RX_MATCH_TAINTED
syn keyword xsMacro RX_MATCH_TAINTED_off RX_MATCH_TAINTED_on
syn keyword xsMacro RX_MATCH_TAINTED_set RX_MATCH_UTF8 RX_MATCH_UTF8_off
syn keyword xsMacro RX_MATCH_UTF8_on RX_MATCH_UTF8_set RX_MINLEN RX_MINLENRET
syn keyword xsMacro RX_NPARENS RX_OFFS RX_PRECOMP RX_PRECOMP_const RX_PRELEN
syn keyword xsMacro RX_REFCNT RX_SAVED_COPY RX_SUBBEG RX_SUBCOFFSET RX_SUBLEN
syn keyword xsMacro RX_SUBOFFSET RX_TAINT_on RX_UTF8 RX_WRAPLEN RX_WRAPPED
syn keyword xsMacro RX_WRAPPED_const RX_ZERO_LEN RXapif_ALL RXapif_CLEAR
syn keyword xsMacro RXapif_DELETE RXapif_EXISTS RXapif_FETCH RXapif_FIRSTKEY
syn keyword xsMacro RXapif_NEXTKEY RXapif_ONE RXapif_REGNAME RXapif_REGNAMES
syn keyword xsMacro RXapif_REGNAMES_COUNT RXapif_SCALAR RXapif_STORE
syn keyword xsMacro RXf_BASE_SHIFT RXf_CHECK_ALL RXf_COPY_DONE RXf_EVAL_SEEN
syn keyword xsMacro RXf_INTUIT_TAIL RXf_IS_ANCHORED RXf_MATCH_UTF8
syn keyword xsMacro RXf_NO_INPLACE_SUBST RXf_NULL RXf_PMf_CHARSET
syn keyword xsMacro RXf_PMf_COMPILETIME RXf_PMf_EXTENDED
syn keyword xsMacro RXf_PMf_EXTENDED_MORE RXf_PMf_FLAGCOPYMASK RXf_PMf_FOLD
syn keyword xsMacro RXf_PMf_KEEPCOPY RXf_PMf_MULTILINE RXf_PMf_NOCAPTURE
syn keyword xsMacro RXf_PMf_SINGLELINE RXf_PMf_SPLIT RXf_PMf_STD_PMMOD
syn keyword xsMacro RXf_PMf_STD_PMMOD_SHIFT RXf_PMf_STRICT RXf_SKIPWHITE
syn keyword xsMacro RXf_SPLIT RXf_START_ONLY RXf_TAINTED RXf_TAINTED_SEEN
syn keyword xsMacro RXf_UNBOUNDED_QUANTIFIER_SEEN RXf_USE_INTUIT
syn keyword xsMacro RXf_USE_INTUIT_ML RXf_USE_INTUIT_NOML RXf_WHITE RXi_GET
syn keyword xsMacro RXi_GET_DECL RXi_SET RXp_COMPFLAGS RXp_EXTFLAGS
syn keyword xsMacro RXp_INTFLAGS RXp_MATCH_COPIED RXp_MATCH_COPIED_off
syn keyword xsMacro RXp_MATCH_COPIED_on RXp_MATCH_TAINTED
syn keyword xsMacro RXp_MATCH_TAINTED_on RXp_MATCH_UTF8 RXp_PAREN_NAMES ReANY
syn keyword xsMacro ReREFCNT_dec ReREFCNT_inc Renew Renewc RsPARA RsRECORD
syn keyword xsMacro RsSIMPLE RsSNARF SAFE_TRIE_NODENUM SANY SAVEADELETE
syn keyword xsMacro SAVEBOOL SAVECLEARSV SAVECOMPILEWARNINGS SAVECOMPPAD
syn keyword xsMacro SAVECOPFILE SAVECOPFILE_FREE SAVECOPLINE
syn keyword xsMacro SAVECOPSTASH_FREE SAVEDELETE SAVEDESTRUCTOR
syn keyword xsMacro SAVEDESTRUCTOR_X SAVEFREECOPHH SAVEFREEOP SAVEFREEPADNAME
syn keyword xsMacro SAVEFREEPV SAVEFREESV SAVEGENERICPV SAVEGENERICSV
syn keyword xsMacro SAVEHDELETE SAVEHINTS SAVEI16 SAVEI32 SAVEI8 SAVEINT
syn keyword xsMacro SAVEIV SAVELONG SAVEMORTALIZESV SAVEOP
syn keyword xsMacro SAVEPADSVANDMORTALIZE SAVEPARSER SAVEPPTR SAVESETSVFLAGS
syn keyword xsMacro SAVESHAREDPV SAVESPTR SAVESTACK_POS SAVESWITCHSTACK
syn keyword xsMacro SAVETMPS SAVEVPTR SAVE_DEFSV SAVE_ERRNO SAVE_MASK
syn keyword xsMacro SAVE_TIGHT_SHIFT SAVEf_KEEPOLDELEM SAVEf_SETMAGIC
syn keyword xsMacro SAVEt_ADELETE SAVEt_AELEM SAVEt_ALLOC SAVEt_APTR
syn keyword xsMacro SAVEt_ARG0_MAX SAVEt_ARG1_MAX SAVEt_ARG2_MAX SAVEt_AV
syn keyword xsMacro SAVEt_BOOL SAVEt_CLEARPADRANGE SAVEt_CLEARSV
syn keyword xsMacro SAVEt_COMPILE_WARNINGS SAVEt_COMPPAD SAVEt_DELETE
syn keyword xsMacro SAVEt_DESTRUCTOR SAVEt_DESTRUCTOR_X SAVEt_FREECOPHH
syn keyword xsMacro SAVEt_FREEOP SAVEt_FREEPADNAME SAVEt_FREEPV SAVEt_FREESV
syn keyword xsMacro SAVEt_GENERIC_PVREF SAVEt_GENERIC_SVREF SAVEt_GP
syn keyword xsMacro SAVEt_GP_ALIASED_SV SAVEt_GVSLOT SAVEt_GVSV SAVEt_HELEM
syn keyword xsMacro SAVEt_HINTS SAVEt_HPTR SAVEt_HV SAVEt_I16 SAVEt_I32
syn keyword xsMacro SAVEt_I32_SMALL SAVEt_I8 SAVEt_INT SAVEt_INT_SMALL
syn keyword xsMacro SAVEt_ITEM SAVEt_IV SAVEt_LONG SAVEt_MORTALIZESV
syn keyword xsMacro SAVEt_NSTAB SAVEt_OP SAVEt_PADSV_AND_MORTALIZE
syn keyword xsMacro SAVEt_PARSER SAVEt_PPTR SAVEt_READONLY_OFF
syn keyword xsMacro SAVEt_REGCONTEXT SAVEt_SAVESWITCHSTACK SAVEt_SET_SVFLAGS
syn keyword xsMacro SAVEt_SHARED_PVREF SAVEt_SPTR SAVEt_STACK_POS
syn keyword xsMacro SAVEt_STRLEN SAVEt_SV SAVEt_SVREF SAVEt_VPTR
syn keyword xsMacro SAWAMPERSAND_LEFT SAWAMPERSAND_MIDDLE SAWAMPERSAND_RIGHT
syn keyword xsMacro SBOL SB_ENUM_COUNT SCAN_DEF SCAN_REPL SCAN_TR
syn keyword xsMacro SCAN_VERSION SCHED_YIELD SCOPE_SAVES_SIGNAL_MASK SEEK_CUR
syn keyword xsMacro SEEK_END SEEK_SET SELECT_MIN_BITS SEOL SETERRNO
syn keyword xsMacro SETGRENT_R_PROTO SETHOSTENT_R_PROTO SETLOCALE_R_PROTO
syn keyword xsMacro SETNETENT_R_PROTO SETPROTOENT_R_PROTO SETPWENT_R_PROTO
syn keyword xsMacro SETSERVENT_R_PROTO SETTARG SET_MARK_OFFSET
syn keyword xsMacro SET_NUMERIC_LOCAL SET_NUMERIC_STANDARD
syn keyword xsMacro SET_NUMERIC_UNDERLYING SET_THR SET_THREAD_SELF SETi SETn
syn keyword xsMacro SETp SETs SETu SHARP_S_SKIP SHIFTOP SHORTSIZE SH_PATH
syn keyword xsMacro SIGABRT SIGILL SIG_NAME SIG_NUM SIG_SIZE SINGLE_PAT_MOD
syn keyword xsMacro SIPROUND SITEARCH SITEARCH_EXP SITELIB SITELIB_EXP
syn keyword xsMacro SITELIB_STEM SIZE_ALIGN SIZE_ONLY SKIP SKIP_next
syn keyword xsMacro SKIP_next_fail SLOPPYDIVIDE SOCKET_OPEN_MODE SPAGAIN
syn keyword xsMacro SPRINTF_RETURNS_STRLEN SRAND48_R_PROTO SRANDOM_R_PROTO
syn keyword xsMacro SSCHECK SSC_MATCHES_EMPTY_STRING SSGROW SSNEW SSNEWa
syn keyword xsMacro SSNEWat SSNEWt SSPOPBOOL SSPOPDPTR SSPOPDXPTR SSPOPINT
syn keyword xsMacro SSPOPIV SSPOPLONG SSPOPPTR SSPOPUV SSPTR SSPTRt
syn keyword xsMacro SSPUSHBOOL SSPUSHDPTR SSPUSHDXPTR SSPUSHINT SSPUSHIV
syn keyword xsMacro SSPUSHLONG SSPUSHPTR SSPUSHUV SS_ACCVIO SS_ADD_BOOL
syn keyword xsMacro SS_ADD_DPTR SS_ADD_DXPTR SS_ADD_END SS_ADD_INT SS_ADD_IV
syn keyword xsMacro SS_ADD_LONG SS_ADD_PTR SS_ADD_UV SS_BUFFEROVF
syn keyword xsMacro SS_DEVOFFLINE SS_IVCHAN SS_MAXPUSH SS_NOPRIV SS_NORMAL
syn keyword xsMacro SSize_t_MAX ST STANDARD_C STAR STARTPERL START_EXTERN_C
syn keyword xsMacro START_MY_CXT STATIC STATIC_ASSERT_1 STATIC_ASSERT_2
syn keyword xsMacro STATIC_ASSERT_GLOBAL STATIC_ASSERT_STMT
syn keyword xsMacro STATUS_ALL_FAILURE STATUS_ALL_SUCCESS STATUS_CURRENT
syn keyword xsMacro STATUS_EXIT STATUS_EXIT_SET STATUS_NATIVE
syn keyword xsMacro STATUS_NATIVE_CHILD_SET STATUS_UNIX STATUS_UNIX_EXIT_SET
syn keyword xsMacro STATUS_UNIX_SET STDCHAR STDIO_STREAM_ARRAY STD_PAT_MODS
syn keyword xsMacro STD_PMMOD_FLAGS_CLEAR STD_PMMOD_FLAGS_PARSE_X_WARN
syn keyword xsMacro STMT_END STMT_START STORE_LC_NUMERIC_FORCE_TO_UNDERLYING
syn keyword xsMacro STORE_LC_NUMERIC_SET_TO_NEEDED
syn keyword xsMacro STORE_LC_NUMERIC_STANDARD_SET_UNDERLYING
syn keyword xsMacro STORE_LC_NUMERIC_UNDERLYING_SET_STANDARD
syn keyword xsMacro STORE_NUMERIC_LOCAL_SET_STANDARD
syn keyword xsMacro STORE_NUMERIC_STANDARD_FORCE_LOCAL
syn keyword xsMacro STORE_NUMERIC_STANDARD_SET_LOCAL STRERROR_R_PROTO STRING
syn keyword xsMacro STRINGIFY STRUCT_OFFSET STRUCT_SV STR_LEN STR_SZ
syn keyword xsMacro STR_WITH_LEN ST_INO_SIGN ST_INO_SIZE SUB
syn keyword xsMacro SUBST_TAINT_BOOLRET SUBST_TAINT_PAT SUBST_TAINT_REPL
syn keyword xsMacro SUBST_TAINT_RETAINT SUBST_TAINT_STR SUBVERSION SUCCEED
syn keyword xsMacro SUSPEND SVTYPEMASK SV_CATBYTES SV_CATUTF8
syn keyword xsMacro SV_CHECK_THINKFIRST SV_CHECK_THINKFIRST_COW_DROP SV_CONST
syn keyword xsMacro SV_CONSTS_COUNT SV_CONST_BINMODE SV_CONST_CLEAR
syn keyword xsMacro SV_CONST_CLOSE SV_CONST_DELETE SV_CONST_DESTROY
syn keyword xsMacro SV_CONST_EOF SV_CONST_EXISTS SV_CONST_EXTEND
syn keyword xsMacro SV_CONST_FETCH SV_CONST_FETCHSIZE SV_CONST_FILENO
syn keyword xsMacro SV_CONST_FIRSTKEY SV_CONST_GETC SV_CONST_NEXTKEY
syn keyword xsMacro SV_CONST_OPEN SV_CONST_POP SV_CONST_PRINT SV_CONST_PRINTF
syn keyword xsMacro SV_CONST_PUSH SV_CONST_READ SV_CONST_READLINE
syn keyword xsMacro SV_CONST_RETURN SV_CONST_SCALAR SV_CONST_SEEK
syn keyword xsMacro SV_CONST_SHIFT SV_CONST_SPLICE SV_CONST_STORE
syn keyword xsMacro SV_CONST_STORESIZE SV_CONST_TELL SV_CONST_TIEARRAY
syn keyword xsMacro SV_CONST_TIEHANDLE SV_CONST_TIEHASH SV_CONST_TIESCALAR
syn keyword xsMacro SV_CONST_UNSHIFT SV_CONST_UNTIE SV_CONST_WRITE
syn keyword xsMacro SV_COW_DROP_PV SV_COW_OTHER_PVS SV_COW_REFCNT_MAX
syn keyword xsMacro SV_COW_SHARED_HASH_KEYS SV_DO_COW_SVSETSV
syn keyword xsMacro SV_FORCE_UTF8_UPGRADE SV_GMAGIC SV_HAS_TRAILING_NUL
syn keyword xsMacro SV_IMMEDIATE_UNREF SV_MUTABLE_RETURN SV_NOSTEAL
syn keyword xsMacro SV_SAVED_COPY SV_SKIP_OVERLOAD SV_SMAGIC
syn keyword xsMacro SV_UNDEF_RETURNS_NULL SV_UTF8_NO_ENCODING SVrepl_EVAL
syn keyword xsMacro SVt_FIRST SVt_MASK SWITCHSTACK SYMBIAN SYSTEM_GMTIME_MAX
syn keyword xsMacro SYSTEM_GMTIME_MIN SYSTEM_LOCALTIME_MAX
syn keyword xsMacro SYSTEM_LOCALTIME_MIN S_IEXEC S_IFIFO S_IFMT S_IREAD
syn keyword xsMacro S_IRGRP S_IROTH S_IRUSR S_IRWXG S_IRWXO S_IRWXU S_ISBLK
syn keyword xsMacro S_ISCHR S_ISDIR S_ISFIFO S_ISGID S_ISLNK S_ISREG S_ISSOCK
syn keyword xsMacro S_ISUID S_IWGRP S_IWOTH S_IWRITE S_IWUSR S_IXGRP S_IXOTH
syn keyword xsMacro S_IXUSR S_PAT_MODS Safefree Semctl Sigjmp_buf Siglongjmp
syn keyword xsMacro Sigsetjmp Size_t_MAX Size_t_size StGiFy StashHANDLER Stat
syn keyword xsMacro Strerror Strtol Strtoul StructCopy SvAMAGIC SvANY
syn keyword xsMacro SvCANCOW SvCANEXISTDELETE SvCOMPILED SvCOMPILED_off
syn keyword xsMacro SvCOMPILED_on SvCUR SvCUR_set SvDESTROYABLE SvEND
syn keyword xsMacro SvEND_set SvENDx SvEVALED SvEVALED_off SvEVALED_on SvFAKE
syn keyword xsMacro SvFAKE_off SvFAKE_on SvFLAGS SvGAMAGIC SvGETMAGIC SvGID
syn keyword xsMacro SvGMAGICAL SvGMAGICAL_off SvGMAGICAL_on SvGROW
syn keyword xsMacro SvGROW_mutable SvIMMORTAL SvIOK SvIOK_UV SvIOK_nog
syn keyword xsMacro SvIOK_nogthink SvIOK_notUV SvIOK_off SvIOK_on SvIOK_only
syn keyword xsMacro SvIOK_only_UV SvIOKp SvIOKp_on SvIS_FREED SvIV SvIVX
syn keyword xsMacro SvIVXx SvIV_nomg SvIV_please SvIV_please_nomg SvIV_set
syn keyword xsMacro SvIVx SvIsCOW SvIsCOW_normal SvIsCOW_off SvIsCOW_on
syn keyword xsMacro SvIsCOW_shared_hash SvIsUV SvIsUV_off SvIsUV_on SvLEN
syn keyword xsMacro SvLEN_set SvLENx SvLOCK SvMAGIC SvMAGICAL SvMAGICAL_off
syn keyword xsMacro SvMAGICAL_on SvMAGIC_set SvNIOK SvNIOK_nog
syn keyword xsMacro SvNIOK_nogthink SvNIOK_off SvNIOKp SvNOK SvNOK_nog
syn keyword xsMacro SvNOK_nogthink SvNOK_off SvNOK_on SvNOK_only SvNOKp
syn keyword xsMacro SvNOKp_on SvNV SvNVX SvNVXx SvNV_nomg SvNV_set SvNVx
syn keyword xsMacro SvOBJECT SvOBJECT_off SvOBJECT_on SvOK SvOK_off
syn keyword xsMacro SvOK_off_exc_UV SvOKp SvOOK SvOOK_off SvOOK_offset
syn keyword xsMacro SvOOK_on SvOURSTASH SvOURSTASH_set SvPADMY SvPADMY_on
syn keyword xsMacro SvPADSTALE SvPADSTALE_off SvPADSTALE_on SvPADTMP
syn keyword xsMacro SvPADTMP_off SvPADTMP_on SvPAD_OUR SvPAD_OUR_on
syn keyword xsMacro SvPAD_STATE SvPAD_STATE_on SvPAD_TYPED SvPAD_TYPED_on
syn keyword xsMacro SvPCS_IMPORTED SvPCS_IMPORTED_off SvPCS_IMPORTED_on
syn keyword xsMacro SvPEEK SvPOK SvPOK_byte_nog SvPOK_byte_nogthink
syn keyword xsMacro SvPOK_byte_pure_nogthink SvPOK_nog SvPOK_nogthink
syn keyword xsMacro SvPOK_off SvPOK_on SvPOK_only SvPOK_only_UTF8
syn keyword xsMacro SvPOK_pure_nogthink SvPOK_utf8_nog SvPOK_utf8_nogthink
syn keyword xsMacro SvPOK_utf8_pure_nogthink SvPOKp SvPOKp_on SvPV SvPVX
syn keyword xsMacro SvPVX_const SvPVX_mutable SvPVXtrue SvPVXx SvPV_const
syn keyword xsMacro SvPV_flags SvPV_flags_const SvPV_flags_const_nolen
syn keyword xsMacro SvPV_flags_mutable SvPV_force SvPV_force_flags
syn keyword xsMacro SvPV_force_flags_mutable SvPV_force_flags_nolen
syn keyword xsMacro SvPV_force_mutable SvPV_force_nolen SvPV_force_nomg
syn keyword xsMacro SvPV_force_nomg_nolen SvPV_free SvPV_mutable SvPV_nolen
syn keyword xsMacro SvPV_nolen_const SvPV_nomg SvPV_nomg_const
syn keyword xsMacro SvPV_nomg_const_nolen SvPV_nomg_nolen SvPV_renew SvPV_set
syn keyword xsMacro SvPV_shrink_to_cur SvPVbyte SvPVbyte_force SvPVbyte_nolen
syn keyword xsMacro SvPVbytex SvPVbytex_force SvPVbytex_nolen SvPVutf8
syn keyword xsMacro SvPVutf8_force SvPVutf8_nolen SvPVutf8x SvPVutf8x_force
syn keyword xsMacro SvPVx SvPVx_const SvPVx_force SvPVx_nolen
syn keyword xsMacro SvPVx_nolen_const SvREADONLY SvREADONLY_off SvREADONLY_on
syn keyword xsMacro SvREFCNT SvREFCNT_IMMORTAL SvREFCNT_dec SvREFCNT_dec_NN
syn keyword xsMacro SvREFCNT_inc SvREFCNT_inc_NN SvREFCNT_inc_simple
syn keyword xsMacro SvREFCNT_inc_simple_NN SvREFCNT_inc_simple_void
syn keyword xsMacro SvREFCNT_inc_simple_void_NN SvREFCNT_inc_void
syn keyword xsMacro SvREFCNT_inc_void_NN SvRELEASE_IVX SvRELEASE_IVX_
syn keyword xsMacro SvRMAGICAL SvRMAGICAL_off SvRMAGICAL_on SvROK SvROK_off
syn keyword xsMacro SvROK_on SvRV SvRV_const SvRV_set SvRVx SvRX SvRXOK
syn keyword xsMacro SvSCREAM SvSCREAM_off SvSCREAM_on SvSETMAGIC SvSHARE
syn keyword xsMacro SvSHARED_HASH SvSHARED_HEK_FROM_PV SvSMAGICAL
syn keyword xsMacro SvSMAGICAL_off SvSMAGICAL_on SvSTASH SvSTASH_set
syn keyword xsMacro SvSetMagicSV SvSetMagicSV_nosteal SvSetSV SvSetSV_and
syn keyword xsMacro SvSetSV_nosteal SvSetSV_nosteal_and SvTAIL SvTAIL_off
syn keyword xsMacro SvTAIL_on SvTAINT SvTAINTED SvTAINTED_off SvTAINTED_on
syn keyword xsMacro SvTEMP SvTEMP_off SvTEMP_on SvTHINKFIRST SvTIED_mg
syn keyword xsMacro SvTIED_obj SvTRUE SvTRUE_NN SvTRUE_common SvTRUE_nomg
syn keyword xsMacro SvTRUE_nomg_NN SvTRUEx SvTRUEx_nomg SvTYPE SvUID SvUNLOCK
syn keyword xsMacro SvUOK SvUOK_nog SvUOK_nogthink SvUPGRADE SvUTF8
syn keyword xsMacro SvUTF8_off SvUTF8_on SvUV SvUVX SvUVXx SvUV_nomg SvUV_set
syn keyword xsMacro SvUVx SvVALID SvVALID_off SvVALID_on SvVOK SvVSTRING_mg
syn keyword xsMacro SvWEAKREF SvWEAKREF_off SvWEAKREF_on Sv_Grow TAIL TAINT
syn keyword xsMacro TAINTING_get TAINTING_set TAINT_ENV TAINT_IF TAINT_NOT
syn keyword xsMacro TAINT_PROPER TAINT_WARN_get TAINT_WARN_set TAINT_get
syn keyword xsMacro TAINT_set THING THR THREAD_CREATE
syn keyword xsMacro THREAD_CREATE_NEEDS_STACK THREAD_POST_CREATE
syn keyword xsMacro THREAD_RET_CAST THREAD_RET_TYPE
syn keyword xsMacro TIED_METHOD_ARGUMENTS_ON_STACK
syn keyword xsMacro TIED_METHOD_MORTALIZE_NOT_NEEDED TIED_METHOD_SAY
syn keyword xsMacro TIME64_CONFIG_H TIME64_H TM TMPNAM_R_PROTO TOO_LATE_FOR
syn keyword xsMacro TOO_LATE_FOR_ TOPBLOCK TOPMARK TOPi TOPl TOPm1s TOPn TOPp
syn keyword xsMacro TOPp1s TOPpx TOPs TOPu TOPul TRIE TRIEC TRIE_BITMAP
syn keyword xsMacro TRIE_BITMAP_BYTE TRIE_BITMAP_CLEAR TRIE_BITMAP_SET
syn keyword xsMacro TRIE_BITMAP_TEST TRIE_CHARCOUNT TRIE_NODEIDX TRIE_NODENUM
syn keyword xsMacro TRIE_WORDS_OFFSET TRIE_next TRIE_next_fail TRUE
syn keyword xsMacro TTYNAME_R_PROTO TWO_BYTE_UTF8_TO_NATIVE
syn keyword xsMacro TWO_BYTE_UTF8_TO_UNI TYPE_CHARS TYPE_DIGITS Timeval
syn keyword xsMacro U16SIZE U16TYPE U16_CONST U16_MAX U16_MIN U32SIZE U32TYPE
syn keyword xsMacro U32_ALIGNMENT_REQUIRED U32_CONST U32_MAX U32_MAX_P1
syn keyword xsMacro U32_MAX_P1_HALF U32_MIN U64SIZE U64TYPE U64_CONST U8SIZE
syn keyword xsMacro U8TO16_LE U8TO32_LE U8TO64_LE U8TYPE U8_MAX U8_MIN
syn keyword xsMacro UCHARAT UINT32_MIN UINT64_C UINT64_MIN UMINUS
syn keyword xsMacro UNALIGNED_SAFE UNDERBAR UNICODE_ALLOW_ANY
syn keyword xsMacro UNICODE_ALLOW_SUPER UNICODE_ALLOW_SURROGATE
syn keyword xsMacro UNICODE_BYTE_ORDER_MARK UNICODE_DISALLOW_FE_FF
syn keyword xsMacro UNICODE_DISALLOW_ILLEGAL_INTERCHANGE
syn keyword xsMacro UNICODE_DISALLOW_NONCHAR UNICODE_DISALLOW_SUPER
syn keyword xsMacro UNICODE_DISALLOW_SURROGATE
syn keyword xsMacro UNICODE_GREEK_CAPITAL_LETTER_SIGMA
syn keyword xsMacro UNICODE_GREEK_SMALL_LETTER_FINAL_SIGMA
syn keyword xsMacro UNICODE_GREEK_SMALL_LETTER_SIGMA
syn keyword xsMacro UNICODE_IS_BYTE_ORDER_MARK UNICODE_IS_FE_FF
syn keyword xsMacro UNICODE_IS_NONCHAR UNICODE_IS_REPLACEMENT
syn keyword xsMacro UNICODE_IS_SUPER UNICODE_IS_SURROGATE UNICODE_LINE_SEPA_0
syn keyword xsMacro UNICODE_LINE_SEPA_1 UNICODE_LINE_SEPA_2
syn keyword xsMacro UNICODE_PARA_SEPA_0 UNICODE_PARA_SEPA_1
syn keyword xsMacro UNICODE_PARA_SEPA_2 UNICODE_PAT_MOD UNICODE_PAT_MODS
syn keyword xsMacro UNICODE_REPLACEMENT UNICODE_SURROGATE_FIRST
syn keyword xsMacro UNICODE_SURROGATE_LAST UNICODE_WARN_FE_FF
syn keyword xsMacro UNICODE_WARN_ILLEGAL_INTERCHANGE UNICODE_WARN_NONCHAR
syn keyword xsMacro UNICODE_WARN_SUPER UNICODE_WARN_SURROGATE UNIOP UNIOPSUB
syn keyword xsMacro UNISKIP UNI_DISPLAY_BACKSLASH UNI_DISPLAY_ISPRINT
syn keyword xsMacro UNI_DISPLAY_QQ UNI_DISPLAY_REGEX UNI_IS_INVARIANT
syn keyword xsMacro UNI_TO_NATIVE UNKNOWN_ERRNO_MSG UNLESS UNLESSM UNLIKELY
syn keyword xsMacro UNLINK UNLOCK_DOLLARZERO_MUTEX UNLOCK_LC_NUMERIC_STANDARD
syn keyword xsMacro UNLOCK_NUMERIC_STANDARD UNOP_AUX_item_sv UNTIL
syn keyword xsMacro UPG_VERSION USE USE_64_BIT_ALL USE_64_BIT_INT
syn keyword xsMacro USE_64_BIT_RAWIO USE_64_BIT_STDIO USE_BSDPGRP
syn keyword xsMacro USE_DYNAMIC_LOADING USE_ENVIRON_ARRAY USE_HASH_SEED
syn keyword xsMacro USE_HEAP_INSTEAD_OF_STACK USE_LARGE_FILES USE_LEFT
syn keyword xsMacro USE_LOCALE USE_LOCALE_COLLATE USE_LOCALE_CTYPE
syn keyword xsMacro USE_LOCALE_MESSAGES USE_LOCALE_MONETARY
syn keyword xsMacro USE_LOCALE_NUMERIC USE_LOCALE_TIME USE_PERLIO
syn keyword xsMacro USE_PERL_PERTURB_KEYS USE_REENTRANT_API
syn keyword xsMacro USE_SEMCTL_SEMID_DS USE_SEMCTL_SEMUN USE_STAT_BLOCKS
syn keyword xsMacro USE_STAT_RDEV USE_STDIO USE_STRUCT_COPY USE_SYSTEM_GMTIME
syn keyword xsMacro USE_SYSTEM_LOCALTIME USE_THREADS USE_TM64
syn keyword xsMacro USE_UTF8_IN_NAMES USING_MSVC6 UTF8SKIP UTF8_ACCUMULATE
syn keyword xsMacro UTF8_ALLOW_ANY UTF8_ALLOW_ANYUV UTF8_ALLOW_CONTINUATION
syn keyword xsMacro UTF8_ALLOW_DEFAULT UTF8_ALLOW_EMPTY UTF8_ALLOW_FFFF
syn keyword xsMacro UTF8_ALLOW_LONG UTF8_ALLOW_NON_CONTINUATION
syn keyword xsMacro UTF8_ALLOW_SHORT UTF8_ALLOW_SURROGATE UTF8_CHECK_ONLY
syn keyword xsMacro UTF8_DISALLOW_FE_FF UTF8_DISALLOW_ILLEGAL_INTERCHANGE
syn keyword xsMacro UTF8_DISALLOW_NONCHAR UTF8_DISALLOW_SUPER
syn keyword xsMacro UTF8_DISALLOW_SURROGATE UTF8_EIGHT_BIT_HI
syn keyword xsMacro UTF8_EIGHT_BIT_LO
syn keyword xsMacro UTF8_FIRST_PROBLEMATIC_CODE_POINT_FIRST_BYTE
syn keyword xsMacro UTF8_IS_ABOVE_LATIN1 UTF8_IS_CONTINUATION
syn keyword xsMacro UTF8_IS_CONTINUED UTF8_IS_DOWNGRADEABLE_START
syn keyword xsMacro UTF8_IS_INVARIANT UTF8_IS_NEXT_CHAR_DOWNGRADEABLE
syn keyword xsMacro UTF8_IS_NONCHAR_
syn keyword xsMacro UTF8_IS_NONCHAR_GIVEN_THAT_NON_SUPER_AND_GE_PROBLEMATIC
syn keyword xsMacro UTF8_IS_REPLACEMENT UTF8_IS_START UTF8_IS_SUPER
syn keyword xsMacro UTF8_IS_SURROGATE UTF8_MAXBYTES UTF8_MAXBYTES_CASE
syn keyword xsMacro UTF8_MAXLEN UTF8_MAX_FOLD_CHAR_EXPAND UTF8_QUAD_MAX
syn keyword xsMacro UTF8_TWO_BYTE_HI UTF8_TWO_BYTE_HI_nocast UTF8_TWO_BYTE_LO
syn keyword xsMacro UTF8_TWO_BYTE_LO_nocast UTF8_WARN_FE_FF
syn keyword xsMacro UTF8_WARN_ILLEGAL_INTERCHANGE UTF8_WARN_NONCHAR
syn keyword xsMacro UTF8_WARN_SUPER UTF8_WARN_SURROGATE UTF8f UTF8fARG
syn keyword xsMacro UTF_ACCUMULATION_OVERFLOW_MASK UTF_ACCUMULATION_SHIFT
syn keyword xsMacro UTF_CONTINUATION_MARK UTF_CONTINUATION_MASK
syn keyword xsMacro UTF_START_MARK UTF_START_MASK UTF_TO_NATIVE
syn keyword xsMacro UVCHR_IS_INVARIANT UVCHR_SKIP UVSIZE UVTYPE UVXf UV_DIG
syn keyword xsMacro UV_MAX UV_MAX_P1 UV_MAX_P1_HALF UV_MIN UVf U_32 U_I U_L
syn keyword xsMacro U_S U_V Uid_t_f Uid_t_sign Uid_t_size VAL_EAGAIN
syn keyword xsMacro VAL_O_NONBLOCK VCMP VERB VNORMAL VNUMIFY VOL VSTRINGIFY
syn keyword xsMacro VTBL_amagic VTBL_amagicelem VTBL_arylen VTBL_bm
syn keyword xsMacro VTBL_collxfrm VTBL_dbline VTBL_defelem VTBL_env
syn keyword xsMacro VTBL_envelem VTBL_fm VTBL_glob VTBL_isa VTBL_isaelem
syn keyword xsMacro VTBL_mglob VTBL_nkeys VTBL_pack VTBL_packelem VTBL_pos
syn keyword xsMacro VTBL_regdata VTBL_regdatum VTBL_regexp VTBL_sigelem
syn keyword xsMacro VTBL_substr VTBL_sv VTBL_taint VTBL_uvar VTBL_vec
syn keyword xsMacro VT_NATIVE VUTIL_REPLACE_CORE VVERIFY WARN_ALL
syn keyword xsMacro WARN_ALLstring WARN_AMBIGUOUS WARN_BAREWORD WARN_CLOSED
syn keyword xsMacro WARN_CLOSURE WARN_DEBUGGING WARN_DEPRECATED WARN_DIGIT
syn keyword xsMacro WARN_EXEC WARN_EXITING WARN_EXPERIMENTAL
syn keyword xsMacro WARN_EXPERIMENTAL__AUTODEREF WARN_EXPERIMENTAL__BITWISE
syn keyword xsMacro WARN_EXPERIMENTAL__CONST_ATTR
syn keyword xsMacro WARN_EXPERIMENTAL__LEXICAL_SUBS
syn keyword xsMacro WARN_EXPERIMENTAL__LEXICAL_TOPIC
syn keyword xsMacro WARN_EXPERIMENTAL__POSTDEREF
syn keyword xsMacro WARN_EXPERIMENTAL__REFALIASING
syn keyword xsMacro WARN_EXPERIMENTAL__REGEX_SETS
syn keyword xsMacro WARN_EXPERIMENTAL__RE_STRICT
syn keyword xsMacro WARN_EXPERIMENTAL__SIGNATURES
syn keyword xsMacro WARN_EXPERIMENTAL__SMARTMATCH
syn keyword xsMacro WARN_EXPERIMENTAL__WIN32_PERLIO WARN_GLOB
syn keyword xsMacro WARN_ILLEGALPROTO WARN_IMPRECISION WARN_INPLACE
syn keyword xsMacro WARN_INTERNAL WARN_IO WARN_LAYER WARN_LOCALE WARN_MALLOC
syn keyword xsMacro WARN_MISC WARN_MISSING WARN_NEWLINE WARN_NONCHAR
syn keyword xsMacro WARN_NONEstring WARN_NON_UNICODE WARN_NUMERIC WARN_ONCE
syn keyword xsMacro WARN_OVERFLOW WARN_PACK WARN_PARENTHESIS WARN_PIPE
syn keyword xsMacro WARN_PORTABLE WARN_PRECEDENCE WARN_PRINTF WARN_PROTOTYPE
syn keyword xsMacro WARN_QW WARN_RECURSION WARN_REDEFINE WARN_REDUNDANT
syn keyword xsMacro WARN_REGEXP WARN_RESERVED WARN_SEMICOLON WARN_SEVERE
syn keyword xsMacro WARN_SIGNAL WARN_SUBSTR WARN_SURROGATE WARN_SYNTAX
syn keyword xsMacro WARN_SYSCALLS WARN_TAINT WARN_THREADS WARN_UNINITIALIZED
syn keyword xsMacro WARN_UNOPENED WARN_UNPACK WARN_UNTIE WARN_UTF8 WARN_VOID
syn keyword xsMacro WARNshift WARNsize WB_ENUM_COUNT WEXITSTATUS WHEN WHILE
syn keyword xsMacro WHILEM WHILEM_A_max WHILEM_A_max_fail WHILEM_A_min
syn keyword xsMacro WHILEM_A_min_fail WHILEM_A_pre WHILEM_A_pre_fail
syn keyword xsMacro WHILEM_B_max WHILEM_B_max_fail WHILEM_B_min
syn keyword xsMacro WHILEM_B_min_fail WIDEST_UTYPE WIFEXITED WIFSIGNALED
syn keyword xsMacro WIFSTOPPED WIN32SCK_IS_STDSCK WNOHANG WORD WSTOPSIG
syn keyword xsMacro WTERMSIG WUNTRACED XDIGIT_VALUE XHvTOTALKEYS
syn keyword xsMacro XOPd_xop_class XOPd_xop_desc XOPd_xop_name XOPd_xop_peep
syn keyword xsMacro XOPf_xop_class XOPf_xop_desc XOPf_xop_name XOPf_xop_peep
syn keyword xsMacro XPUSHTARG XPUSHi XPUSHmortal XPUSHn XPUSHp XPUSHs XPUSHu
syn keyword xsMacro XPUSHundef XS XSANY XSINTERFACE_CVT XSINTERFACE_CVT_ANON
syn keyword xsMacro XSINTERFACE_FUNC XSINTERFACE_FUNC_SET XSPROTO XSRETURN
syn keyword xsMacro XSRETURN_EMPTY XSRETURN_IV XSRETURN_NO XSRETURN_NV
syn keyword xsMacro XSRETURN_PV XSRETURN_PVN XSRETURN_UNDEF XSRETURN_UV
syn keyword xsMacro XSRETURN_YES XST_mIV XST_mNO XST_mNV XST_mPV XST_mPVN
syn keyword xsMacro XST_mUNDEF XST_mUV XST_mYES XS_APIVERSION_BOOTCHECK
syn keyword xsMacro XS_APIVERSION_POPMARK_BOOTCHECK
syn keyword xsMacro XS_APIVERSION_SETXSUBFN_POPMARK_BOOTCHECK
syn keyword xsMacro XS_BOTHVERSION_BOOTCHECK XS_BOTHVERSION_POPMARK_BOOTCHECK
syn keyword xsMacro XS_BOTHVERSION_SETXSUBFN_POPMARK_BOOTCHECK
syn keyword xsMacro XS_DYNAMIC_FILENAME XS_EXTERNAL XS_INTERNAL
syn keyword xsMacro XS_SETXSUBFN_POPMARK XS_VERSION_BOOTCHECK XSprePUSH
syn keyword xsMacro XTENDED_PAT_MOD XopDISABLE XopENABLE XopENTRY
syn keyword xsMacro XopENTRYCUSTOM XopENTRY_set XopFLAGS YADAYADA YIELD
syn keyword xsMacro YYEMPTY YYSTYPE_IS_DECLARED YYSTYPE_IS_TRIVIAL
syn keyword xsMacro YYTOKENTYPE Zero ZeroD _ _CANNOT _CC_ALPHA
syn keyword xsMacro _CC_ALPHANUMERIC _CC_ASCII _CC_BLANK _CC_CASED
syn keyword xsMacro _CC_CHARNAME_CONT _CC_CNTRL _CC_DIGIT _CC_GRAPH
syn keyword xsMacro _CC_IDFIRST _CC_IS_IN_SOME_FOLD _CC_LOWER
syn keyword xsMacro _CC_MNEMONIC_CNTRL _CC_NONLATIN1_FOLD
syn keyword xsMacro _CC_NONLATIN1_SIMPLE_FOLD _CC_NON_FINAL_FOLD _CC_PRINT
syn keyword xsMacro _CC_PUNCT _CC_QUOTEMETA _CC_SPACE _CC_UPPER _CC_VERTSPACE
syn keyword xsMacro _CC_WORDCHAR _CC_XDIGIT _CC_mask _CC_mask_A
syn keyword xsMacro _CHECK_AND_OUTPUT_WIDE_LOCALE_CP_MSG
syn keyword xsMacro _CHECK_AND_OUTPUT_WIDE_LOCALE_UTF8_MSG
syn keyword xsMacro _CHECK_AND_WARN_PROBLEMATIC_LOCALE
syn keyword xsMacro _CORE_SWASH_INIT_ACCEPT_INVLIST
syn keyword xsMacro _CORE_SWASH_INIT_RETURN_IF_UNDEF
syn keyword xsMacro _CORE_SWASH_INIT_USER_DEFINED_PROPERTY _CPERLarg
syn keyword xsMacro _FIRST_NON_SWASH_CC _GNU_SOURCE
syn keyword xsMacro _HAS_NONLATIN1_FOLD_CLOSURE_ONLY_FOR_USE_BY_REGCOMP_DOT_C_AND_REGEXEC_DOT_C
syn keyword xsMacro _HAS_NONLATIN1_SIMPLE_FOLD_CLOSURE_ONLY_FOR_USE_BY_REGCOMP_DOT_C_AND_REGEXEC_DOT_C
syn keyword xsMacro _HIGHEST_REGCOMP_DOT_H_SYNC _INC_PERL_XSUB_H
syn keyword xsMacro _IS_IN_SOME_FOLD_ONLY_FOR_USE_BY_REGCOMP_DOT_C
syn keyword xsMacro _IS_MNEMONIC_CNTRL_ONLY_FOR_USE_BY_REGCOMP_DOT_C
syn keyword xsMacro _IS_NON_FINAL_FOLD_ONLY_FOR_USE_BY_REGCOMP_DOT_C _LC_CAST
syn keyword xsMacro _MEM_WRAP_NEEDS_RUNTIME_CHECK _MEM_WRAP_WILL_WRAP
syn keyword xsMacro _NOT_IN_NUMERIC_STANDARD _NOT_IN_NUMERIC_UNDERLYING
syn keyword xsMacro _NV_BODYLESS_UNION _OP_SIBPARENT_FIELDNAME _PERLIOL_H
syn keyword xsMacro _PERLIO_H _PERL_OBJECT_THIS _REGEXP_COMMON
syn keyword xsMacro _RXf_PMf_CHARSET_SHIFT _RXf_PMf_SHIFT_COMPILETIME
syn keyword xsMacro _RXf_PMf_SHIFT_NEXT _STDIO_H _STDIO_INCLUDED _V
syn keyword xsMacro _XPVCV_COMMON _XPV_HEAD __ASSERT_ __BASE_TWO_BYTE_HI
syn keyword xsMacro __BASE_TWO_BYTE_LO __Inc__IPerl___
syn keyword xsMacro __PATCHLEVEL_H_INCLUDED__ __PL_inf_float_int32
syn keyword xsMacro __PL_nan_float_int32 __STDIO_LOADED
syn keyword xsMacro __attribute__deprecated__ __attribute__format__
syn keyword xsMacro __attribute__format__null_ok__ __attribute__malloc__
syn keyword xsMacro __attribute__nonnull__ __attribute__noreturn__
syn keyword xsMacro __attribute__pure__ __attribute__unused__
syn keyword xsMacro __attribute__warn_unused_result__ __filbuf __flsbuf
syn keyword xsMacro __has_builtin __perlapi_h__ _config_h_ _exit _filbuf
syn keyword xsMacro _flsbuf _generic_LC _generic_LC_base
syn keyword xsMacro _generic_LC_func_utf8 _generic_LC_swash_utf8
syn keyword xsMacro _generic_LC_swash_uvchr _generic_LC_underscore
syn keyword xsMacro _generic_LC_utf8 _generic_LC_uvchr _generic_func_utf8
syn keyword xsMacro _generic_isCC _generic_isCC_A _generic_swash_uni
syn keyword xsMacro _generic_swash_utf8 _generic_toFOLD_LC
syn keyword xsMacro _generic_toLOWER_LC _generic_toUPPER_LC _generic_uni
syn keyword xsMacro _generic_utf8 _generic_utf8_no_upper_latin1 _isQMC
syn keyword xsMacro _isQUOTEMETA _swab_16_ _swab_32_ _swab_64_ aTHXa aTHXo
syn keyword xsMacro aTHXo_ aTHXx aTHXx_ abort accept access
syn keyword xsMacro anchored_end_shift anchored_offset anchored_substr
syn keyword xsMacro anchored_utf8 asctime assert assert_ assert_not_ROK
syn keyword xsMacro assert_not_glob atoll av_tindex bcmp bind blk_eval
syn keyword xsMacro blk_format blk_gimme blk_givwhen blk_loop blk_oldcop
syn keyword xsMacro blk_oldmarksp blk_oldpm blk_oldscopesp blk_oldsp blk_sub
syn keyword xsMacro blk_u16 bool boolSV cBINOP cBINOPo cBINOPx cBOOL cCOP
syn keyword xsMacro cCOPo cCOPx cGVOP_gv cGVOPo_gv cGVOPx_gv cLISTOP cLISTOPo
syn keyword xsMacro cLISTOPx cLOGOP cLOGOPo cLOGOPx cLOOP cLOOPo cLOOPx
syn keyword xsMacro cMETHOPx cMETHOPx_meth cMETHOPx_rclass cPADOP cPADOPo
syn keyword xsMacro cPADOPx cPMOP cPMOPo cPMOPx cPVOP cPVOPo cPVOPx cSVOP
syn keyword xsMacro cSVOP_sv cSVOPo cSVOPo_sv cSVOPx cSVOPx_sv cSVOPx_svp
syn keyword xsMacro cUNOP cUNOP_AUX cUNOP_AUXo cUNOP_AUXx cUNOPo cUNOPx chdir
syn keyword xsMacro check_end_shift check_offset_max check_offset_min
syn keyword xsMacro check_substr check_utf8 child_offset_bits chmod chsize
syn keyword xsMacro ckDEAD ckWARN ckWARN2 ckWARN2_d ckWARN3 ckWARN3_d ckWARN4
syn keyword xsMacro ckWARN4_d ckWARN_d close closedir connect cop_hints_2hv
syn keyword xsMacro cop_hints_fetch_pv cop_hints_fetch_pvn
syn keyword xsMacro cop_hints_fetch_pvs cop_hints_fetch_sv cophh_2hv
syn keyword xsMacro cophh_copy cophh_delete_pv cophh_delete_pvn
syn keyword xsMacro cophh_delete_pvs cophh_delete_sv cophh_fetch_pv
syn keyword xsMacro cophh_fetch_pvn cophh_fetch_pvs cophh_fetch_sv cophh_free
syn keyword xsMacro cophh_new_empty cophh_store_pv cophh_store_pvn
syn keyword xsMacro cophh_store_pvs cophh_store_sv crypt ctermid ctime
syn keyword xsMacro cv_ckproto cx_type cxstack cxstack_ix cxstack_max
syn keyword xsMacro dATARGET dAX dAXMARK dEXT dEXTCONST dITEMS dJMPENV dMARK
syn keyword xsMacro dMULTICALL dMY_CXT dMY_CXT_INTERP dMY_CXT_SV dNOOP
syn keyword xsMacro dORIGMARK dPOPPOPiirl dPOPPOPnnrl dPOPPOPssrl dPOPTOPiirl
syn keyword xsMacro dPOPTOPiirl_nomg dPOPTOPiirl_ul_nomg dPOPTOPnnrl
syn keyword xsMacro dPOPTOPnnrl_nomg dPOPTOPssrl dPOPXiirl dPOPXiirl_ul_nomg
syn keyword xsMacro dPOPXnnrl dPOPXssrl dPOPiv dPOPnv dPOPnv_nomg dPOPss
syn keyword xsMacro dPOPuv dSAVEDERRNO dSAVE_ERRNO dSP dSS_ADD dTARG dTARGET
syn keyword xsMacro dTARGETSTACKED dTHR dTHX dTHXa dTHXo dTHXoa dTHXs dTHXx
syn keyword xsMacro dTOPiv dTOPnv dTOPss dTOPuv dUNDERBAR dVAR dXSARGS
syn keyword xsMacro dXSBOOTARGSAPIVERCHK dXSBOOTARGSNOVERCHK
syn keyword xsMacro dXSBOOTARGSXSAPIVERCHK dXSFUNCTION dXSI32 dXSTARG
syn keyword xsMacro dXSUB_SYS deprecate djSP do_open dup dup2 endgrent
syn keyword xsMacro endhostent endnetent endprotoent endpwent endservent
syn keyword xsMacro environ execl execv execvp fcntl fd_set fdopen fileno
syn keyword xsMacro float_end_shift float_max_offset float_min_offset
syn keyword xsMacro float_substr float_utf8 flock flockfile foldEQ_utf8
syn keyword xsMacro frewind fscanf fstat ftell ftruncate ftrylockfile
syn keyword xsMacro funlockfile fwrite1 get_cvs getc_unlocked getegid geteuid
syn keyword xsMacro getgid getgrent getgrgid getgrnam gethostbyaddr
syn keyword xsMacro gethostbyname gethostent gethostname getlogin
syn keyword xsMacro getnetbyaddr getnetbyname getnetent getpeername getpid
syn keyword xsMacro getprotobyname getprotobynumber getprotoent getpwent
syn keyword xsMacro getpwnam getpwuid getservbyname getservbyport getservent
syn keyword xsMacro getsockname getsockopt getspnam gettimeofday getuid getw
syn keyword xsMacro gv_AVadd gv_HVadd gv_IOadd gv_SVadd gv_autoload4
syn keyword xsMacro gv_efullname3 gv_fetchmeth gv_fetchmeth_autoload
syn keyword xsMacro gv_fetchmethod gv_fetchmethod_flags gv_fetchpvn
syn keyword xsMacro gv_fetchpvs gv_fetchsv_nomg gv_fullname3 gv_init
syn keyword xsMacro gv_method_changed gv_stashpvs htoni htonl htons htovl
syn keyword xsMacro htovs hv_delete hv_delete_ent hv_deletehek hv_exists
syn keyword xsMacro hv_exists_ent hv_fetch hv_fetch_ent hv_fetchhek hv_fetchs
syn keyword xsMacro hv_iternext hv_magic hv_store hv_store_ent hv_store_flags
syn keyword xsMacro hv_storehek hv_stores hv_undef ibcmp ibcmp_locale
syn keyword xsMacro ibcmp_utf8 inet_addr inet_ntoa init_os_extras ioctl
syn keyword xsMacro isALNUM isALNUMC isALNUMC_A isALNUMC_L1 isALNUMC_LC
syn keyword xsMacro isALNUMC_LC_utf8 isALNUMC_LC_uvchr isALNUMC_uni
syn keyword xsMacro isALNUMC_utf8 isALNUMU isALNUM_LC isALNUM_LC_utf8
syn keyword xsMacro isALNUM_LC_uvchr isALNUM_lazy_if isALNUM_uni isALNUM_utf8
syn keyword xsMacro isALPHA isALPHANUMERIC isALPHANUMERIC_A isALPHANUMERIC_L1
syn keyword xsMacro isALPHANUMERIC_LC isALPHANUMERIC_LC_utf8
syn keyword xsMacro isALPHANUMERIC_LC_uvchr isALPHANUMERIC_uni
syn keyword xsMacro isALPHANUMERIC_utf8 isALPHAU isALPHA_A isALPHA_FOLD_EQ
syn keyword xsMacro isALPHA_FOLD_NE isALPHA_L1 isALPHA_LC isALPHA_LC_utf8
syn keyword xsMacro isALPHA_LC_uvchr isALPHA_uni isALPHA_utf8 isASCII
syn keyword xsMacro isASCII_A isASCII_L1 isASCII_LC isASCII_LC_utf8
syn keyword xsMacro isASCII_LC_uvchr isASCII_uni isASCII_utf8 isBLANK
syn keyword xsMacro isBLANK_A isBLANK_L1 isBLANK_LC isBLANK_LC_uni
syn keyword xsMacro isBLANK_LC_utf8 isBLANK_LC_uvchr isBLANK_uni isBLANK_utf8
syn keyword xsMacro isCHARNAME_CONT isCNTRL isCNTRL_A isCNTRL_L1 isCNTRL_LC
syn keyword xsMacro isCNTRL_LC_utf8 isCNTRL_LC_uvchr isCNTRL_uni isCNTRL_utf8
syn keyword xsMacro isDIGIT isDIGIT_A isDIGIT_L1 isDIGIT_LC isDIGIT_LC_utf8
syn keyword xsMacro isDIGIT_LC_uvchr isDIGIT_uni isDIGIT_utf8 isGRAPH
syn keyword xsMacro isGRAPH_A isGRAPH_L1 isGRAPH_LC isGRAPH_LC_utf8
syn keyword xsMacro isGRAPH_LC_uvchr isGRAPH_uni isGRAPH_utf8 isGV
syn keyword xsMacro isGV_with_GP isGV_with_GP_off isGV_with_GP_on isIDCONT
syn keyword xsMacro isIDCONT_A isIDCONT_L1 isIDCONT_LC isIDCONT_LC_utf8
syn keyword xsMacro isIDCONT_LC_uvchr isIDCONT_uni isIDCONT_utf8 isIDFIRST
syn keyword xsMacro isIDFIRST_A isIDFIRST_L1 isIDFIRST_LC isIDFIRST_LC_utf8
syn keyword xsMacro isIDFIRST_LC_uvchr isIDFIRST_lazy_if isIDFIRST_uni
syn keyword xsMacro isIDFIRST_utf8 isLEXWARN_off isLEXWARN_on isLOWER
syn keyword xsMacro isLOWER_A isLOWER_L1 isLOWER_LC isLOWER_LC_utf8
syn keyword xsMacro isLOWER_LC_uvchr isLOWER_uni isLOWER_utf8 isOCTAL
syn keyword xsMacro isOCTAL_A isOCTAL_L1 isPRINT isPRINT_A isPRINT_L1
syn keyword xsMacro isPRINT_LC isPRINT_LC_utf8 isPRINT_LC_uvchr isPRINT_uni
syn keyword xsMacro isPRINT_utf8 isPSXSPC isPSXSPC_A isPSXSPC_L1 isPSXSPC_LC
syn keyword xsMacro isPSXSPC_LC_utf8 isPSXSPC_LC_uvchr isPSXSPC_uni
syn keyword xsMacro isPSXSPC_utf8 isPUNCT isPUNCT_A isPUNCT_L1 isPUNCT_LC
syn keyword xsMacro isPUNCT_LC_utf8 isPUNCT_LC_uvchr isPUNCT_uni isPUNCT_utf8
syn keyword xsMacro isREGEXP isSPACE isSPACE_A isSPACE_L1 isSPACE_LC
syn keyword xsMacro isSPACE_LC_utf8 isSPACE_LC_uvchr isSPACE_uni isSPACE_utf8
syn keyword xsMacro isUPPER isUPPER_A isUPPER_L1 isUPPER_LC isUPPER_LC_utf8
syn keyword xsMacro isUPPER_LC_uvchr isUPPER_uni isUPPER_utf8 isUTF8_CHAR
syn keyword xsMacro isVERTWS_uni isVERTWS_utf8 isWARN_ONCE isWARN_on
syn keyword xsMacro isWARNf_on isWORDCHAR isWORDCHAR_A isWORDCHAR_L1
syn keyword xsMacro isWORDCHAR_LC isWORDCHAR_LC_utf8 isWORDCHAR_LC_uvchr
syn keyword xsMacro isWORDCHAR_lazy_if isWORDCHAR_uni isWORDCHAR_utf8
syn keyword xsMacro isXDIGIT isXDIGIT_A isXDIGIT_L1 isXDIGIT_LC
syn keyword xsMacro isXDIGIT_LC_utf8 isXDIGIT_LC_uvchr isXDIGIT_uni
syn keyword xsMacro isXDIGIT_utf8 is_ANYOF_SYNTHETIC is_FOLDS_TO_MULTI_utf8
syn keyword xsMacro is_HORIZWS_cp_high is_HORIZWS_high is_LAX_VERSION
syn keyword xsMacro is_LNBREAK_latin1_safe is_LNBREAK_safe
syn keyword xsMacro is_LNBREAK_utf8_safe is_MULTI_CHAR_FOLD_latin1_safe
syn keyword xsMacro is_MULTI_CHAR_FOLD_utf8_safe
syn keyword xsMacro is_MULTI_CHAR_FOLD_utf8_safe_part0
syn keyword xsMacro is_MULTI_CHAR_FOLD_utf8_safe_part1 is_NONCHAR_utf8
syn keyword xsMacro is_PATWS_cp is_PATWS_safe
syn keyword xsMacro is_PROBLEMATIC_LOCALE_FOLDEDS_START_cp
syn keyword xsMacro is_PROBLEMATIC_LOCALE_FOLDEDS_START_utf8
syn keyword xsMacro is_PROBLEMATIC_LOCALE_FOLD_cp
syn keyword xsMacro is_PROBLEMATIC_LOCALE_FOLD_utf8 is_QUOTEMETA_high
syn keyword xsMacro is_QUOTEMETA_high_part0 is_QUOTEMETA_high_part1
syn keyword xsMacro is_REPLACEMENT_utf8_safe is_STRICT_VERSION
syn keyword xsMacro is_SURROGATE_utf8 is_UTF8_CHAR_utf8_no_length_checks
syn keyword xsMacro is_VERTWS_cp_high is_VERTWS_high is_XDIGIT_cp_high
syn keyword xsMacro is_XDIGIT_high is_XPERLSPACE_cp_high is_XPERLSPACE_high
syn keyword xsMacro is_ascii_string is_utf8_char_buf is_utf8_string_loc
syn keyword xsMacro isatty isnormal kBINOP kCOP kGVOP_gv kLISTOP kLOGOP kLOOP
syn keyword xsMacro kPADOP kPMOP kPVOP kSVOP kSVOP_sv kUNOP kUNOP_AUX kill
syn keyword xsMacro killpg lex_stuff_pvs link listen lockf longjmp lseek
syn keyword xsMacro lstat mPUSHi mPUSHn mPUSHp mPUSHs mPUSHu mXPUSHi mXPUSHn
syn keyword xsMacro mXPUSHp mXPUSHs mXPUSHu memEQ memEQs memNE memNEs memchr
syn keyword xsMacro memcmp memzero mkdir mktemp my my_binmode my_lstat
syn keyword xsMacro my_setlocale my_snprintf my_sprintf my_stat my_strlcat
syn keyword xsMacro my_strlcpy my_vsnprintf newATTRSUB newAV newGVgen newHV
syn keyword xsMacro newIO newRV_inc newSUB newSVpadname newSVpvn_utf8
syn keyword xsMacro newSVpvs newSVpvs_flags newSVpvs_share newXSproto ntohi
syn keyword xsMacro ntohl ntohs opASSIGN op_lvalue open opendir pTHX_1
syn keyword xsMacro pTHX_12 pTHX_2 pTHX_3 pTHX_4 pTHX_5 pTHX_6 pTHX_7 pTHX_8
syn keyword xsMacro pTHX_9 pTHX_FORMAT pTHX_VALUE pTHX_VALUE_ pTHX__FORMAT
syn keyword xsMacro pTHX__VALUE pTHX__VALUE_ pTHXo pTHXo_ pTHXx pTHXx_ pVAR
syn keyword xsMacro pWARN_ALL pWARN_NONE pWARN_STD packWARN packWARN2
syn keyword xsMacro packWARN3 packWARN4 pad_add_name_pvs pad_findmy_pvs
syn keyword xsMacro pad_peg padadd_NO_DUP_CHECK padadd_OUR padadd_STALEOK
syn keyword xsMacro padadd_STATE padnew_CLONE padnew_SAVE padnew_SAVESUB
syn keyword xsMacro panic_write2 pause pclose pipe popen prepare_SV_for_RV
syn keyword xsMacro pthread_attr_init pthread_condattr_default pthread_create
syn keyword xsMacro pthread_key_create pthread_keycreate
syn keyword xsMacro pthread_mutexattr_default pthread_mutexattr_init
syn keyword xsMacro pthread_mutexattr_settype putc_unlocked putenv putw read
syn keyword xsMacro readdir readdir64 recv recvfrom ref
syn keyword xsMacro refcounted_he_fetch_pvs refcounted_he_new_pvs rename
syn keyword xsMacro rewinddir rmdir safecalloc safefree safemalloc
syn keyword xsMacro saferealloc save_aelem save_freeop save_freepv
syn keyword xsMacro save_freesv save_helem save_mortalizesv save_op savepvs
syn keyword xsMacro savesharedpvs sb_dstr sb_iters sb_m sb_maxiters
syn keyword xsMacro sb_oldsave sb_orig sb_rflags sb_rx sb_rxres sb_rxtainted
syn keyword xsMacro sb_s sb_strend sb_targ seedDrand01 seekdir select send
syn keyword xsMacro sendto set_ANYOF_SYNTHETIC setbuf setgid setgrent
syn keyword xsMacro sethostent setjmp setlinebuf setlocale setmode setnetent
syn keyword xsMacro setprotoent setpwent setregid setreuid setservent
syn keyword xsMacro setsockopt setuid setvbuf share_hek_hek sharepvn shutdown
syn keyword xsMacro signal sleep socket socketpair specialWARN stat stdoutf
syn keyword xsMacro strEQ strGE strGT strLE strLT strNE strchr strerror
syn keyword xsMacro strnEQ strnNE strrchr strtoll strtoull sv_2bool
syn keyword xsMacro sv_2bool_nomg sv_2iv sv_2nv sv_2pv sv_2pv_nolen
syn keyword xsMacro sv_2pv_nomg sv_2pvbyte_nolen sv_2pvutf8_nolen sv_2uv
syn keyword xsMacro sv_cathek sv_catpv_nomg sv_catpvn sv_catpvn_mg
syn keyword xsMacro sv_catpvn_nomg sv_catpvn_nomg_maybeutf8
syn keyword xsMacro sv_catpvn_nomg_utf8_upgrade sv_catpvs sv_catpvs_flags
syn keyword xsMacro sv_catpvs_mg sv_catpvs_nomg sv_catsv sv_catsv_mg
syn keyword xsMacro sv_catsv_nomg sv_catxmlpvs sv_cmp sv_cmp_locale
syn keyword xsMacro sv_collxfrm sv_copypv_nomg sv_eq sv_force_normal
syn keyword xsMacro sv_insert sv_mortalcopy sv_nolocking sv_nounlocking
syn keyword xsMacro sv_or_pv_len_utf8 sv_pv sv_pvbyte sv_pvn_force
syn keyword xsMacro sv_pvn_force_nomg sv_pvutf8 sv_setgid sv_setpvs
syn keyword xsMacro sv_setpvs_mg sv_setref_pvs sv_setsv sv_setsv_nomg
syn keyword xsMacro sv_setuid sv_taint sv_unref sv_usepvn sv_usepvn_mg
syn keyword xsMacro sv_utf8_upgrade sv_utf8_upgrade_flags
syn keyword xsMacro sv_utf8_upgrade_nomg tTHX telldir times tmpfile tmpnam
syn keyword xsMacro toCTRL toFOLD toFOLD_A toFOLD_LC toFOLD_uni toFOLD_utf8
syn keyword xsMacro toLOWER toLOWER_A toLOWER_L1 toLOWER_LATIN1 toLOWER_LC
syn keyword xsMacro toLOWER_uni toLOWER_utf8 toTITLE toTITLE_A toTITLE_uni
syn keyword xsMacro toTITLE_utf8 toUPPER toUPPER_A toUPPER_LATIN1_MOD
syn keyword xsMacro toUPPER_LC toUPPER_uni toUPPER_utf8 to_uni_fold
syn keyword xsMacro to_utf8_fold to_utf8_lower to_utf8_title to_utf8_upper
syn keyword xsMacro truncate tryAMAGICbin_MG tryAMAGICunDEREF
syn keyword xsMacro tryAMAGICunTARGETlist tryAMAGICun_MG ttyname umask uname
syn keyword xsMacro unlink unpackWARN1 unpackWARN2 unpackWARN3 unpackWARN4
syn keyword xsMacro utf8_to_uvchr_buf utime uvchr_to_utf8 uvchr_to_utf8_flags
syn keyword xsMacro vTHX vfprintf vtohl vtohs wait want_vtbl_bm want_vtbl_fm
syn keyword xsMacro whichsig write xio_any xio_dirp xiv_iv xlv_targoff
syn keyword xsMacro xpv_len xuv_uv yystype

" Define the default highlighting.
hi def link xsPrivate    Error
hi def link xsSuperseded Error
hi def link xsType       Type
hi def link xsString     String
hi def link xsConstant   Constant
hi def link xsException  Exception
hi def link xsKeyword    Keyword
hi def link xsFunction   Function
hi def link xsVariable   Identifier
hi def link xsMacro      Macro

let b:current_syntax = "xs"

" vim: ts=8
