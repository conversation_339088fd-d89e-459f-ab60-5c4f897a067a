" Vim syntax file
" Language:	aidl (Android Interface Definition Language)
"		https://developer.android.com/guide/components/aidl
" Maintainer:	<PERSON> <<EMAIL>>
" LastChange:	2020/12/03

" Quit when a syntax file was already loaded.
if exists("b:current_syntax")
   finish
endif

source <sfile>:p:h/java.vim

syn keyword aidlParamDir in out inout
syn keyword aidlKeyword const oneway parcelable

" Needed for the 'in', 'out', 'inout' keywords to be highlighted.
syn cluster javaTop add=aidlParamDir

hi def link aidlParamDir StorageClass
hi def link aidlKeyword Keyword

let b:current_syntax = "aidl"
