" Vim syntax file
" Language:	   Vim script
" Maintainer:	   <PERSON><PERSON><PERSON><PERSON> <h.east.727 ATMARK gmail.com>
"	   <PERSON> <<EMAIL>>
" Last Change:	   2025 May 04
" Former Maintainer: <PERSON>

" DO NOT CHANGE DIRECTLY.
" THIS FILE PARTLY GENERATED BY gen_syntax_vim.vim.
" (Search string "GEN_SYN_VIM:" in this file)

" Quit when a syntax file was already loaded {{{1
if exists("b:current_syntax")
  finish
endif
let s:keepcpo= &cpo
set cpo&vim

" Feature testing {{{1

let s:vim9script = "\n" .. getline(1, 32)->join("\n") =~# '\n\s*vim9\%[script]\>'

function s:has(feature)
  return has(a:feature) || index(get(g:, "vimsyn_vim_features", []), a:feature) != -1
endfunction

" Automatically generated keyword lists: {{{1

" vimTodo: contains common special-notices for comments {{{2
" Use the vimCommentGroup cluster to add your own.
syn keyword vimTodo contained	COMBAK	FIXME	TODO	XXX
syn cluster vimCommentGroup	contains=vimTodo,@Spell

" regular vim commands {{{2
" GEN_SYN_VIM: vimCommand normal, START_STR='syn keyword vimCommand contained', END_STR='nextgroup=vimBang'
syn keyword vimCommand contained abo[veleft] al[l] ar[gs] arga[dd] argd[elete] argdo argded[upe] arge[dit] argg[lobal] argl[ocal] argu[ment] as[cii] b[uffer] bN[ext] ba[ll] bad[d] balt bd[elete] bel[owright] bf[irst] bl[ast] bm[odified] bn[ext] bo[tright] bp[revious] br[ewind] brea[k] breaka[dd] breakd[el] breakl[ist] bro[wse] buffers bufd[o] bun[load] bw[ipeout] c[hange] cN[ext] cNf[ile] cabo[ve] cad[dbuffer] cadde[xpr] caddf[ile] caf[ter] cb[uffer] cbe[fore] cbel[ow] cbo[ttom] cc ccl[ose] cd cdo ce[nter] cex[pr] cf[ile] cfd[o] cfir[st] cg[etfile] cgetb[uffer] cgete[xpr] chd[ir] changes che[ckpath] checkt[ime] chi[story] cl[ist] cla[st] clo[se] cle[arjumps] cn[ext] cnew[er] cnf[ile] co[py] col[der] colo[rscheme] com[mand] comc[lear] comp[iler] con[tinue] conf[irm] nextgroup=vimBang
syn keyword vimCommand contained cons[t] cope[n] cp[revious] cpf[ile] cq[uit] cr[ewind] cs[cope] cst[ag] cw[indow] d[elete] delm[arks] deb[ug] defc[ompile] defe[r] delf[unction] di[splay] dif[fupdate] diffg[et] diffo[ff] diffp[atch] diffpu[t] diffs[plit] difft[his] dig[raphs] disa[ssemble] dj[ump] dli[st] dr[op] ds[earch] dsp[lit] e[dit] ea[rlier] em[enu] endfo[r] endt[ry] endw[hile] ene[w] ev[al] ex exi[t] exu[sage] f[ile] files filet[ype] fin[d] fina[lly] fini[sh] fir[st] fix[del] fo[ld] foldc[lose] foldd[oopen] folddoc[losed] foldo[pen] g[lobal] go[to] gu[i] gv[im] h[elp] helpc[lose] helpf[ind] helpt[ags] ha[rdcopy] hi[ghlight] hid[e] his[tory] hor[izontal] ij[ump] il[ist] imp[ort] int[ro] ip[ut] is[earch] isp[lit] j[oin] ju[mps] k kee[pmarks] keepj[umps] keepp[atterns] nextgroup=vimBang
syn keyword vimCommand contained keepa[lt] l[ist] lN[ext] lNf[ile] la[st] lab[ove] lan[guage] lad[dexpr] laddb[uffer] laddf[ile] laf[ter] lat[er] lb[uffer] lbe[fore] lbel[ow] lbo[ttom] lc[d] lch[dir] lcl[ose] lcs[cope] ld[o] le[ft] lefta[bove] lex[pr] leg[acy] lf[ile] lfd[o] lfir[st] lg[etfile] lgetb[uffer] lgete[xpr] lgr[ep] lgrepa[dd] lhi[story] ll lla[st] lli[st] lmak[e] lne[xt] lnew[er] lnf[ile] lo[adview] loc[kmarks] lockv[ar] lol[der] lop[en] lp[revious] lpf[ile] lr[ewind] lt[ag] lua luad[o] luaf[ile] lw[indow] ls m[ove] ma[rk] marks menut[ranslate] mes[sages] mk[exrc] mks[ession] mksp[ell] mkv[imrc] mkvie[w] mod[e] mz[scheme] mzf[ile] n[ext] nb[key] nbc[lose] nbs[tart] noa[utocmd] noh[lsearch] nos[wapfile] nu[mber] o[pen] ol[dfiles] on[ly] opt[ions] ow[nsyntax] nextgroup=vimBang
syn keyword vimCommand contained p[rint] pa[ckadd] packl[oadall] pb[uffer] pc[lose] pe[rl] perld[o] ped[it] po[p] pp[op] pre[serve] prev[ious] pro[mptfind] promptr[epl] prof[ile] profd[el] ps[earch] pt[ag] ptN[ext] ptf[irst] ptj[ump] ptl[ast] ptn[ext] ptp[revious] ptr[ewind] pts[elect] pu[t] pw[d] py[thon] pyd[o] pyf[ile] py3 py3d[o] python3 py3f[ile] pyx pyxd[o] pythonx pyxf[ile] q[uit] quita[ll] qa[ll] r[ead] rec[over] red[o] redr[aw] redraws[tatus] redrawt[abline] reg[isters] res[ize] ret[ab] rew[ind] ri[ght] rightb[elow] ru[ntime] rub[y] rubyd[o] rubyf[ile] rund[o] rv[iminfo] sN[ext] sa[rgument] sal[l] san[dbox] sav[eas] sb[uffer] sbN[ext] sba[ll] sbf[irst] sbl[ast] sbm[odified] sbn[ext] sbp[revious] sbr[ewind] scr[iptnames] scripte[ncoding] scriptv[ersion] nextgroup=vimBang
syn keyword vimCommand contained scs[cope] setf[iletype] sf[ind] sfir[st] sh[ell] sim[alt] sig[n] sil[ent] sla[st] sn[ext] so[urce] sp[lit] spe[llgood] spelld[ump] spelli[nfo] spellr[epall] spellra[re] spellu[ndo] spellw[rong] spr[evious] sre[wind] st[op] sta[g] star[tinsert] startg[replace] startr[eplace] stopi[nsert] stj[ump] sts[elect] sun[hide] sus[pend] sv[iew] sw[apname] synti[me] sync[bind] smi[le] t tN[ext] ta[g] tags tab tabc[lose] tabd[o] tabe[dit] tabf[ind] tabfir[st] tabm[ove] tabl[ast] tabn[ext] tabnew tabo[nly] tabp[revious] tabN[ext] tabr[ewind] tabs tc[d] tch[dir] tcl tcld[o] tclf[ile] te[aroff] ter[minal] tf[irst] tj[ump] tl[ast] tn[ext] to[pleft] tp[revious] tr[ewind] try ts[elect] u[ndo] undoj[oin] undol[ist] unh[ide] unlo[ckvar] uns[ilent] nextgroup=vimBang
syn keyword vimCommand contained up[date] v[global] ve[rsion] verb[ose] vert[ical] vi[sual] vie[w] vim9[cmd] viu[sage] vne[w] vs[plit] w[rite] wN[ext] wa[ll] wi[nsize] winc[md] wind[o] winp[os] wn[ext] wp[revious] wq wqa[ll] wu[ndo] wv[iminfo] x[it] xa[ll] xr[estore] y[ank] z dl dell delel deletl deletel dp dep delp delep deletp deletep a i nextgroup=vimBang

" Lower priority for _new_ to distinguish constructors from the command.
syn match   vimCommand contained	"\<new\>(\@!"
syn match   vimCommand contained	"\<z[-+^.=]\=\>"
syn keyword vimStdPlugin contained	Arguments Asm Break Cfilter Clear Continue DiffOrig Evaluate Finish Gdb Lfilter Man Over Program Run S Source Step Stop Termdebug TermdebugCommand TOhtml Until Winbar XMLent XMLns

" vimOptions are caught only when contained in a vimSet {{{2
" GEN_SYN_VIM: vimOption normal, START_STR='syn keyword vimOption contained', END_STR='skipwhite nextgroup=vimSetEqual,vimSetMod'
syn keyword vimOption contained al aleph ari allowrevins ambw ambiwidth arab arabic arshape arabicshape acd autochdir ai autoindent ar autoread asd autoshelldir aw autowrite awa autowriteall bg background bs backspace bk backup bkc backupcopy bdir backupdir bex backupext bsk backupskip bdlay balloondelay beval ballooneval bevalterm balloonevalterm bexpr balloonexpr bo belloff bin binary bomb brk breakat bri breakindent briopt breakindentopt bsdir browsedir bh bufhidden bl buflisted bt buftype cmp casemap cdh cdhome cd cdpath cedit ccv charconvert chi chistory cin cindent cink cinkeys cino cinoptions cinsd cinscopedecls cinw cinwords cb clipboard ch cmdheight cwh cmdwinheight cc colorcolumn co columns com comments cms commentstring cp compatible cpt complete cfu completefunc skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained cfc completefuzzycollect cia completeitemalign cot completeopt cpp completepopup csl completeslash cocu concealcursor cole conceallevel cf confirm ci copyindent cpo cpoptions cm cryptmethod cspc cscopepathcomp csprg cscopeprg csqf cscopequickfix csre cscoperelative cst cscopetag csto cscopetagorder csverb cscopeverbose crb cursorbind cuc cursorcolumn cul cursorline culopt cursorlineopt debug def define deco delcombine dict dictionary diff dex diffexpr dip diffopt dg digraph dir directory dy display ead eadirection ed edcompatible emo emoji enc encoding eof endoffile eol endofline ea equalalways ep equalprg eb errorbells ef errorfile efm errorformat ek esckeys ei eventignore eiw eventignorewin et expandtab ex exrc fenc fileencoding skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained fencs fileencodings ff fileformat ffs fileformats fic fileignorecase ft filetype fcs fillchars ffu findfunc fixeol fixendofline fcl foldclose fdc foldcolumn fen foldenable fde foldexpr fdi foldignore fdl foldlevel fdls foldlevelstart fmr foldmarker fdm foldmethod fml foldminlines fdn foldnestmax fdo foldopen fdt foldtext fex formatexpr flp formatlistpat fo formatoptions fp formatprg fs fsync gd gdefault gfm grepformat gp grepprg gcr guicursor gfn guifont gfs guifontset gfw guifontwide ghr guiheadroom gli guiligatures go guioptions guipty gtl guitablabel gtt guitabtooltip hf helpfile hh helpheight hlg helplang hid hidden hl highlight hi history hk hkmap hkp hkmapp hls hlsearch icon iconstring ic ignorecase imaf imactivatefunc imak imactivatekey skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained imc imcmdline imd imdisable imi iminsert ims imsearch imsf imstatusfunc imst imstyle inc include inex includeexpr is incsearch inde indentexpr indk indentkeys inf infercase im insertmode ise isexpand isf isfname isi isident isk iskeyword isp isprint js joinspaces jop jumpoptions key kmp keymap km keymodel kpc keyprotocol kp keywordprg lmap langmap lm langmenu lnr langnoremap lrm langremap ls laststatus lz lazyredraw lhi lhistory lbr linebreak lines lsp linespace lisp lop lispoptions lw lispwords list lcs listchars lpl loadplugins luadll magic mef makeef menc makeencoding mp makeprg mps matchpairs mat matchtime mco maxcombine mfd maxfuncdepth mmd maxmapdepth mm maxmem mmp maxmempattern mmt maxmemtot mis menuitems mopt messagesopt skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained msm mkspellmem ml modeline mle modelineexpr mls modelines ma modifiable mod modified more mouse mousef mousefocus mh mousehide mousem mousemodel mousemev mousemoveevent mouses mouseshape mouset mousetime mzq mzquantum mzschemedll mzschemegcdll nf nrformats nu number nuw numberwidth ofu omnifunc odev opendevice opfunc operatorfunc pp packpath para paragraphs paste pt pastetoggle pex patchexpr pm patchmode pa path perldll pi preserveindent pvh previewheight pvp previewpopup pvw previewwindow pdev printdevice penc printencoding pexpr printexpr pfn printfont pheader printheader pmbcs printmbcharset pmbfn printmbfont popt printoptions prompt ph pumheight pmw pummaxwidth pw pumwidth pythondll pythonhome pythonthreedll pythonthreehome skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained pyx pyxversion qftf quickfixtextfunc qe quoteescape ro readonly rdt redrawtime re regexpengine rnu relativenumber remap rop renderoptions report rs restorescreen ri revins rl rightleft rlc rightleftcmd rubydll ru ruler ruf rulerformat rtp runtimepath scr scroll scb scrollbind scf scrollfocus sj scrolljump so scrolloff sbo scrollopt sect sections secure sel selection slm selectmode ssop sessionoptions sh shell shcf shellcmdflag sp shellpipe shq shellquote srr shellredir ssl shellslash stmp shelltemp st shelltype sxe shellxescape sxq shellxquote sr shiftround sw shiftwidth shm shortmess sn shortname sbr showbreak sc showcmd sloc showcmdloc sft showfulltag sm showmatch smd showmode stal showtabline ss sidescroll siso sidescrolloff skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained scl signcolumn scs smartcase si smartindent sta smarttab sms smoothscroll sts softtabstop spell spc spellcapcheck spf spellfile spl spelllang spo spelloptions sps spellsuggest sb splitbelow spk splitkeep spr splitright sol startofline stl statusline su suffixes sua suffixesadd swf swapfile sws swapsync swb switchbuf smc synmaxcol syn syntax tcl tabclose tal tabline tpm tabpagemax ts tabstop tbs tagbsearch tc tagcase tfu tagfunc tl taglength tr tagrelative tag tags tgst tagstack tcldll term tbidi termbidi tenc termencoding tgc termguicolors twk termwinkey twsl termwinscroll tws termwinsize twt termwintype terse ta textauto tx textmode tw textwidth tsr thesaurus tsrfu thesaurusfunc top tildeop to timeout tm timeoutlen title titlelen skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained titleold titlestring tb toolbar tbis toolbariconsize ttimeout ttm ttimeoutlen tbi ttybuiltin tf ttyfast ttym ttymouse tsl ttyscroll tty ttytype udir undodir udf undofile ul undolevels ur undoreload uc updatecount ut updatetime vsts varsofttabstop vts vartabstop vbs verbose vfile verbosefile vdir viewdir vop viewoptions vi viminfo vif viminfofile ve virtualedit vb visualbell warn wiv weirdinvert ww whichwrap wc wildchar wcm wildcharm wig wildignore wic wildignorecase wmnu wildmenu wim wildmode wop wildoptions wak winaltkeys wcr wincolor wi window wfb winfixbuf wfh winfixheight wfw winfixwidth wh winheight wmh winminheight wmw winminwidth winptydll wiw winwidth wrap wm wrapmargin ws wrapscan write wa writeany wb writebackup wd writedelay skipwhite nextgroup=vimSetEqual,vimSetMod
syn keyword vimOption contained xtermcodes skipwhite nextgroup=vimSetEqual,vimSetMod

" vimOptions: These are the turn-off setting variants {{{2
" GEN_SYN_VIM: vimOption turn-off, START_STR='syn keyword vimOption contained', END_STR=''
syn keyword vimOption contained noari noallowrevins noarab noarabic noarshape noarabicshape noacd noautochdir noai noautoindent noar noautoread noasd noautoshelldir noaw noautowrite noawa noautowriteall nobk nobackup nobeval noballooneval nobevalterm noballoonevalterm nobin nobinary nobomb nobri nobreakindent nobl nobuflisted nocdh nocdhome nocin nocindent nocp nocompatible nocf noconfirm noci nocopyindent nocsre nocscoperelative nocst nocscopetag nocsverb nocscopeverbose nocrb nocursorbind nocuc nocursorcolumn nocul nocursorline nodeco nodelcombine nodiff nodg nodigraph noed noedcompatible noemo noemoji noeof noendoffile noeol noendofline noea noequalalways noeb noerrorbells noek noesckeys noet noexpandtab noex noexrc nofic nofileignorecase nofixeol nofixendofline
syn keyword vimOption contained nofen nofoldenable nofs nofsync nogd nogdefault noguipty nohid nohidden nohk nohkmap nohkp nohkmapp nohls nohlsearch noicon noic noignorecase noimc noimcmdline noimd noimdisable nois noincsearch noinf noinfercase noim noinsertmode nojs nojoinspaces nolnr nolangnoremap nolrm nolangremap nolz nolazyredraw nolbr nolinebreak nolisp nolist nolpl noloadplugins nomagic noml nomodeline nomle nomodelineexpr noma nomodifiable nomod nomodified nomore nomousef nomousefocus nomh nomousehide nomousemev nomousemoveevent nonu nonumber noodev noopendevice nopaste nopi nopreserveindent nopvw nopreviewwindow noprompt noro noreadonly nornu norelativenumber noremap nors norestorescreen nori norevins norl norightleft noru noruler noscb noscrollbind noscf noscrollfocus
syn keyword vimOption contained nosecure nossl noshellslash nostmp noshelltemp nosr noshiftround nosn noshortname nosc noshowcmd nosft noshowfulltag nosm noshowmatch nosmd noshowmode noscs nosmartcase nosi nosmartindent nosta nosmarttab nosms nosmoothscroll nospell nosb nosplitbelow nospr nosplitright nosol nostartofline noswf noswapfile notbs notagbsearch notr notagrelative notgst notagstack notbidi notermbidi notgc notermguicolors noterse nota notextauto notx notextmode notop notildeop noto notimeout notitle nottimeout notbi nottybuiltin notf nottyfast noudf noundofile novb novisualbell nowarn nowiv noweirdinvert nowic nowildignorecase nowmnu nowildmenu nowfb nowinfixbuf nowfh nowinfixheight nowfw nowinfixwidth nowrap nows nowrapscan nowrite nowa nowriteany
syn keyword vimOption contained nowb nowritebackup noxtermcodes

" vimOptions: These are the invertible variants {{{2
" GEN_SYN_VIM: vimOption invertible, START_STR='syn keyword vimOption contained', END_STR=''
syn keyword vimOption contained invari invallowrevins invarab invarabic invarshape invarabicshape invacd invautochdir invai invautoindent invar invautoread invasd invautoshelldir invaw invautowrite invawa invautowriteall invbk invbackup invbeval invballooneval invbevalterm invballoonevalterm invbin invbinary invbomb invbri invbreakindent invbl invbuflisted invcdh invcdhome invcin invcindent invcp invcompatible invcf invconfirm invci invcopyindent invcsre invcscoperelative invcst invcscopetag invcsverb invcscopeverbose invcrb invcursorbind invcuc invcursorcolumn invcul invcursorline invdeco invdelcombine invdiff invdg invdigraph inved invedcompatible invemo invemoji inveof invendoffile inveol invendofline invea invequalalways inveb inverrorbells invek invesckeys
syn keyword vimOption contained invet invexpandtab invex invexrc invfic invfileignorecase invfixeol invfixendofline invfen invfoldenable invfs invfsync invgd invgdefault invguipty invhid invhidden invhk invhkmap invhkp invhkmapp invhls invhlsearch invicon invic invignorecase invimc invimcmdline invimd invimdisable invis invincsearch invinf invinfercase invim invinsertmode invjs invjoinspaces invlnr invlangnoremap invlrm invlangremap invlz invlazyredraw invlbr invlinebreak invlisp invlist invlpl invloadplugins invmagic invml invmodeline invmle invmodelineexpr invma invmodifiable invmod invmodified invmore invmousef invmousefocus invmh invmousehide invmousemev invmousemoveevent invnu invnumber invodev invopendevice invpaste invpi invpreserveindent invpvw invpreviewwindow
syn keyword vimOption contained invprompt invro invreadonly invrnu invrelativenumber invremap invrs invrestorescreen invri invrevins invrl invrightleft invru invruler invscb invscrollbind invscf invscrollfocus invsecure invssl invshellslash invstmp invshelltemp invsr invshiftround invsn invshortname invsc invshowcmd invsft invshowfulltag invsm invshowmatch invsmd invshowmode invscs invsmartcase invsi invsmartindent invsta invsmarttab invsms invsmoothscroll invspell invsb invsplitbelow invspr invsplitright invsol invstartofline invswf invswapfile invtbs invtagbsearch invtr invtagrelative invtgst invtagstack invtbidi invtermbidi invtgc invtermguicolors invterse invta invtextauto invtx invtextmode invtop invtildeop invto invtimeout invtitle invttimeout invtbi invttybuiltin
syn keyword vimOption contained invtf invttyfast invudf invundofile invvb invvisualbell invwarn invwiv invweirdinvert invwic invwildignorecase invwmnu invwildmenu invwfb invwinfixbuf invwfh invwinfixheight invwfw invwinfixwidth invwrap invws invwrapscan invwrite invwa invwriteany invwb invwritebackup invxtermcodes
" termcap codes (which can also be set) {{{2
" GEN_SYN_VIM: vimOption term output code, START_STR='syn keyword vimOption contained', END_STR='skipwhite nextgroup=vimSetEqual,vimSetMod'
syn keyword vimOption contained t_AB t_AF t_AU t_AL t_al t_bc t_BE t_BD t_cd t_ce t_Ce t_CF t_cl t_cm t_Co t_CS t_Cs t_cs t_CV t_da t_db t_DL t_dl t_ds t_Ds t_EC t_EI t_fs t_fd t_fe t_GP t_IE t_IS t_ke t_ks t_le t_mb t_md t_me t_mr t_ms t_nd t_op t_RF t_RB t_RC t_RI t_Ri t_RK t_RS t_RT t_RV t_Sb t_SC t_se t_Sf t_SH t_SI t_Si t_so t_SR t_sr t_ST t_Te t_te t_TE t_ti t_TI t_Ts t_ts t_u7 t_ue t_us t_Us t_ut t_vb t_ve t_vi t_VS t_vs t_WP t_WS t_XM t_xn t_xs t_ZH t_ZR t_8f t_8b t_8u t_xo skipwhite nextgroup=vimSetEqual,vimSetMod
" term key codes
syn keyword vimOption contained	t_F1 t_F2 t_F3 t_F4 t_F5 t_F6 t_F7 t_F8 t_F9 t_k1 t_K1 t_k2 t_k3 t_K3 t_k4 t_K4 t_k5 t_K5 t_k6 t_K6 t_k7 t_K7 t_k8 t_K8 t_k9 t_K9 t_KA t_kb t_kB t_KB t_KC t_kd t_kD t_KD t_KE t_KF t_KG t_kh t_KH t_kI t_KI t_KJ t_KK t_kl t_KL t_kN t_kP t_kr t_ku
syn match   vimOption contained	"t_%1"
syn match   vimOption contained	"t_#2"
syn match   vimOption contained	"t_#4"
syn match   vimOption contained	"t_@7"
syn match   vimOption contained	"t_*7"
syn match   vimOption contained	"t_&8"
syn match   vimOption contained	"t_%i"
syn match   vimOption contained	"t_k;"

" vimOptions: These are the variable names {{{2
" GEN_SYN_VIM: vimOption normal variable,           START_STR='syn keyword vimOptionVarName contained', END_STR=''
syn keyword vimOptionVarName contained al aleph ari allowrevins ambw ambiwidth arab arabic arshape arabicshape acd autochdir ai autoindent ar autoread asd autoshelldir aw autowrite awa autowriteall bg background bs backspace bk backup bkc backupcopy bdir backupdir bex backupext bsk backupskip bdlay balloondelay beval ballooneval bevalterm balloonevalterm bexpr balloonexpr bo belloff bin binary bomb brk breakat bri breakindent briopt breakindentopt bsdir browsedir bh bufhidden bl buflisted bt buftype cmp casemap cdh cdhome cd cdpath cedit ccv charconvert chi chistory cin cindent cink cinkeys cino cinoptions cinsd cinscopedecls cinw cinwords cb clipboard ch cmdheight cwh cmdwinheight cc colorcolumn co columns com comments cms commentstring cp compatible cpt complete
syn keyword vimOptionVarName contained cfu completefunc cfc completefuzzycollect cia completeitemalign cot completeopt cpp completepopup csl completeslash cocu concealcursor cole conceallevel cf confirm ci copyindent cpo cpoptions cm cryptmethod cspc cscopepathcomp csprg cscopeprg csqf cscopequickfix csre cscoperelative cst cscopetag csto cscopetagorder csverb cscopeverbose crb cursorbind cuc cursorcolumn cul cursorline culopt cursorlineopt debug def define deco delcombine dict dictionary diff dex diffexpr dip diffopt dg digraph dir directory dy display ead eadirection ed edcompatible emo emoji enc encoding eof endoffile eol endofline ea equalalways ep equalprg eb errorbells ef errorfile efm errorformat ek esckeys ei eventignore eiw eventignorewin et expandtab
syn keyword vimOptionVarName contained ex exrc fenc fileencoding fencs fileencodings ff fileformat ffs fileformats fic fileignorecase ft filetype fcs fillchars ffu findfunc fixeol fixendofline fcl foldclose fdc foldcolumn fen foldenable fde foldexpr fdi foldignore fdl foldlevel fdls foldlevelstart fmr foldmarker fdm foldmethod fml foldminlines fdn foldnestmax fdo foldopen fdt foldtext fex formatexpr flp formatlistpat fo formatoptions fp formatprg fs fsync gd gdefault gfm grepformat gp grepprg gcr guicursor gfn guifont gfs guifontset gfw guifontwide ghr guiheadroom gli guiligatures go guioptions guipty gtl guitablabel gtt guitabtooltip hf helpfile hh helpheight hlg helplang hid hidden hl highlight hi history hk hkmap hkp hkmapp hls hlsearch icon iconstring ic ignorecase
syn keyword vimOptionVarName contained imaf imactivatefunc imak imactivatekey imc imcmdline imd imdisable imi iminsert ims imsearch imsf imstatusfunc imst imstyle inc include inex includeexpr is incsearch inde indentexpr indk indentkeys inf infercase im insertmode ise isexpand isf isfname isi isident isk iskeyword isp isprint js joinspaces jop jumpoptions key kmp keymap km keymodel kpc keyprotocol kp keywordprg lmap langmap lm langmenu lnr langnoremap lrm langremap ls laststatus lz lazyredraw lhi lhistory lbr linebreak lines lsp linespace lisp lop lispoptions lw lispwords list lcs listchars lpl loadplugins luadll magic mef makeef menc makeencoding mp makeprg mps matchpairs mat matchtime mco maxcombine mfd maxfuncdepth mmd maxmapdepth mm maxmem mmp maxmempattern
syn keyword vimOptionVarName contained mmt maxmemtot mis menuitems mopt messagesopt msm mkspellmem ml modeline mle modelineexpr mls modelines ma modifiable mod modified more mouse mousef mousefocus mh mousehide mousem mousemodel mousemev mousemoveevent mouses mouseshape mouset mousetime mzq mzquantum mzschemedll mzschemegcdll nf nrformats nu number nuw numberwidth ofu omnifunc odev opendevice opfunc operatorfunc pp packpath para paragraphs paste pt pastetoggle pex patchexpr pm patchmode pa path perldll pi preserveindent pvh previewheight pvp previewpopup pvw previewwindow pdev printdevice penc printencoding pexpr printexpr pfn printfont pheader printheader pmbcs printmbcharset pmbfn printmbfont popt printoptions prompt ph pumheight pmw pummaxwidth pw pumwidth
syn keyword vimOptionVarName contained pythondll pythonhome pythonthreedll pythonthreehome pyx pyxversion qftf quickfixtextfunc qe quoteescape ro readonly rdt redrawtime re regexpengine rnu relativenumber remap rop renderoptions report rs restorescreen ri revins rl rightleft rlc rightleftcmd rubydll ru ruler ruf rulerformat rtp runtimepath scr scroll scb scrollbind scf scrollfocus sj scrolljump so scrolloff sbo scrollopt sect sections secure sel selection slm selectmode ssop sessionoptions sh shell shcf shellcmdflag sp shellpipe shq shellquote srr shellredir ssl shellslash stmp shelltemp st shelltype sxe shellxescape sxq shellxquote sr shiftround sw shiftwidth shm shortmess sn shortname sbr showbreak sc showcmd sloc showcmdloc sft showfulltag sm showmatch smd showmode
syn keyword vimOptionVarName contained stal showtabline ss sidescroll siso sidescrolloff scl signcolumn scs smartcase si smartindent sta smarttab sms smoothscroll sts softtabstop spell spc spellcapcheck spf spellfile spl spelllang spo spelloptions sps spellsuggest sb splitbelow spk splitkeep spr splitright sol startofline stl statusline su suffixes sua suffixesadd swf swapfile sws swapsync swb switchbuf smc synmaxcol syn syntax tcl tabclose tal tabline tpm tabpagemax ts tabstop tbs tagbsearch tc tagcase tfu tagfunc tl taglength tr tagrelative tag tags tgst tagstack tcldll term tbidi termbidi tenc termencoding tgc termguicolors twk termwinkey twsl termwinscroll tws termwinsize twt termwintype terse ta textauto tx textmode tw textwidth tsr thesaurus tsrfu thesaurusfunc
syn keyword vimOptionVarName contained top tildeop to timeout tm timeoutlen title titlelen titleold titlestring tb toolbar tbis toolbariconsize ttimeout ttm ttimeoutlen tbi ttybuiltin tf ttyfast ttym ttymouse tsl ttyscroll tty ttytype udir undodir udf undofile ul undolevels ur undoreload uc updatecount ut updatetime vsts varsofttabstop vts vartabstop vbs verbose vfile verbosefile vdir viewdir vop viewoptions vi viminfo vif viminfofile ve virtualedit vb visualbell warn wiv weirdinvert ww whichwrap wc wildchar wcm wildcharm wig wildignore wic wildignorecase wmnu wildmenu wim wildmode wop wildoptions wak winaltkeys wcr wincolor wi window wfb winfixbuf wfh winfixheight wfw winfixwidth wh winheight wmh winminheight wmw winminwidth winptydll wiw winwidth wrap wm wrapmargin
syn keyword vimOptionVarName contained ws wrapscan write wa writeany wb writebackup wd writedelay xtermcodes
" GEN_SYN_VIM: vimOption term output code variable, START_STR='syn keyword vimOptionVarName contained', END_STR=''
syn keyword vimOptionVarName contained t_AB t_AF t_AU t_AL t_al t_bc t_BE t_BD t_cd t_ce t_Ce t_CF t_cl t_cm t_Co t_CS t_Cs t_cs t_CV t_da t_db t_DL t_dl t_ds t_Ds t_EC t_EI t_fs t_fd t_fe t_GP t_IE t_IS t_ke t_ks t_le t_mb t_md t_me t_mr t_ms t_nd t_op t_RF t_RB t_RC t_RI t_Ri t_RK t_RS t_RT t_RV t_Sb t_SC t_se t_Sf t_SH t_SI t_Si t_so t_SR t_sr t_ST t_Te t_te t_TE t_ti t_TI t_Ts t_ts t_u7 t_ue t_us t_Us t_ut t_vb t_ve t_vi t_VS t_vs t_WP t_WS t_XM t_xn t_xs t_ZH t_ZR t_8f t_8b t_8u t_xo
syn keyword vimOptionVarName contained	t_F1 t_F2 t_F3 t_F4 t_F5 t_F6 t_F7 t_F8 t_F9 t_k1 t_K1 t_k2 t_k3 t_K3 t_k4 t_K4 t_k5 t_K5 t_k6 t_K6 t_k7 t_K7 t_k8 t_K8 t_k9 t_K9 t_KA t_kb t_kB t_KB t_KC t_kd t_kD t_KD t_KE t_KF t_KG t_kh t_KH t_kI t_KI t_KJ t_KK t_kl t_KL t_kN t_kP t_kr t_ku
syn match   vimOptionVarName contained	"t_%1"
syn match   vimOptionVarName contained	"t_#2"
syn match   vimOptionVarName contained	"t_#4"
syn match   vimOptionVarName contained	"t_@7"
syn match   vimOptionVarName contained	"t_*7"
syn match   vimOptionVarName contained	"t_&8"
syn match   vimOptionVarName contained	"t_%i"
syn match   vimOptionVarName contained	"t_k;"

" unsupported settings: some were supported by vi but don't do anything in vim {{{2
" GEN_SYN_VIM: Missing vimOption, START_STR='syn keyword vimErrSetting contained', END_STR=''
syn keyword vimErrSetting contained akm altkeymap anti antialias ap autoprint bf beautify biosk bioskey consk conskey fk fkmap fl flash gr graphic ht hardtabs macatsui mesg novice open opt optimize oft osfiletype redraw slow slowopen sourceany w1200 w300 w9600
syn keyword vimErrSetting contained noakm noaltkeymap noanti noantialias noap noautoprint nobf nobeautify nobiosk nobioskey noconsk noconskey nofk nofkmap nofl noflash nogr nographic nomacatsui nomesg nonovice noopen noopt nooptimize noredraw noslow noslowopen nosourceany
syn keyword vimErrSetting contained invakm invaltkeymap invanti invantialias invap invautoprint invbf invbeautify invbiosk invbioskey invconsk invconskey invfk invfkmap invfl invflash invgr invgraphic invmacatsui invmesg invnovice invopen invopt invoptimize invredraw invslow invslowopen invsourceany

" AutoCmd Events {{{2
syn case ignore
" GEN_SYN_VIM: vimAutoEvent, START_STR='syn keyword vimAutoEvent contained', END_STR='skipwhite nextgroup=vimAutoEventSep,@vimAutocmdPattern'
syn keyword vimAutoEvent contained BufAdd BufCreate BufDelete BufEnter BufFilePost BufFilePre BufHidden BufLeave BufNew BufNewFile BufRead BufReadCmd BufReadPost BufReadPre BufUnload BufWinEnter BufWinLeave BufWipeout BufWrite BufWriteCmd BufWritePost BufWritePre CmdlineChanged CmdlineEnter CmdlineLeave CmdlineLeavePre CmdUndefined CmdwinEnter CmdwinLeave ColorScheme ColorSchemePre CompleteChanged CompleteDone CompleteDonePre CursorHold CursorHoldI CursorMoved CursorMovedC CursorMovedI DiffUpdated DirChanged DirChangedPre EncodingChanged ExitPre FileAppendCmd FileAppendPost FileAppendPre FileChangedRO FileChangedShell FileChangedShellPost FileEncoding FileReadCmd FileReadPost FileReadPre FileType FileWriteCmd FileWritePost FileWritePre FilterReadPost FilterReadPre skipwhite nextgroup=vimAutoEventSep,@vimAutocmdPattern
syn keyword vimAutoEvent contained FilterWritePost FilterWritePre FocusGained FocusLost FuncUndefined GUIEnter GUIFailed InsertChange InsertCharPre InsertEnter InsertLeave InsertLeavePre KeyInputPre MenuPopup ModeChanged OptionSet QuickFixCmdPost QuickFixCmdPre QuitPre RemoteReply SafeState SafeStateAgain SessionLoadPost SessionWritePost ShellCmdPost ShellFilterPost SigUSR1 SourceCmd SourcePost SourcePre SpellFileMissing StdinReadPost StdinReadPre SwapExists Syntax TabClosed TabClosedPre TabEnter TabLeave TabNew TermChanged TerminalOpen TerminalWinOpen TermResponse TermResponseAll TextChanged TextChangedI TextChangedP TextChangedT TextYankPost VimEnter VimLeave VimLeavePre VimResized VimResume VimSuspend WinClosed WinEnter WinLeave WinNew WinNewPre WinResized skipwhite nextgroup=vimAutoEventSep,@vimAutocmdPattern
syn keyword vimAutoEvent contained WinScrolled skipwhite nextgroup=vimAutoEventSep,@vimAutocmdPattern

syn keyword	vimAutoEvent	contained	User	skipwhite nextgroup=vimUserAutoEvent
syn match	vimUserAutoEvent	contained	"\<\h\w*\>"	skipwhite nextgroup=vimAutoEventSep,@vimAutocmdPattern

" Highlight commonly used Groupnames {{{2
syn keyword vimGroup contained	Comment Constant String Character Number Boolean Float Identifier Function Statement Conditional Repeat Label Operator Keyword Exception PreProc Include Define Macro PreCondit Type StorageClass Structure Typedef Special SpecialChar Tag Delimiter SpecialComment Debug Underlined Ignore Error Todo

" Default highlighting groups {{{2
" GEN_SYN_VIM: vimHLGroup, START_STR='syn keyword vimHLGroup contained', END_STR=''
syn keyword vimHLGroup contained ErrorMsg IncSearch ModeMsg NonText StatusLine StatusLineNC EndOfBuffer VertSplit VisualNOS DiffText DiffTextAdd PmenuSbar TabLineSel TabLineFill Cursor lCursor QuickFixLine CursorLineSign CursorLineFold CurSearch PmenuKind PmenuKindSel PmenuMatch PmenuMatchSel PmenuExtra PmenuExtraSel PopupSelected MessageWindow PopupNotification Normal Directory LineNr CursorLineNr MoreMsg Question Search SpellBad SpellCap SpellRare SpellLocal PmenuThumb Pmenu PmenuSel SpecialKey Title WarningMsg WildMenu Folded FoldColumn SignColumn Visual DiffAdd DiffChange DiffDelete TabLine CursorColumn CursorLine ColorColumn MatchParen StatusLineTerm StatusLineTermNC ToolbarLine ToolbarButton Menu Tooltip Scrollbar CursorIM LineNrAbove LineNrBelow
syn match vimHLGroup contained "\<Conceal\>"
syn case match

" Function Names {{{2
" GEN_SYN_VIM: vimFuncName, START_STR='syn keyword vimFuncName contained', END_STR=''
syn keyword vimFuncName contained abs acos add and append appendbufline argc argidx arglistid argv asin assert_beeps assert_equal assert_equalfile assert_exception assert_fails assert_false assert_inrange assert_match assert_nobeep assert_notequal assert_notmatch assert_report assert_true atan atan2 autocmd_add autocmd_delete autocmd_get balloon_gettext balloon_show balloon_split base64_decode base64_encode bindtextdomain blob2list blob2str browse browsedir bufadd bufexists buflisted bufload bufloaded bufname bufnr bufwinid bufwinnr byte2line byteidx byteidxcomp call ceil ch_canread ch_close ch_close_in ch_evalexpr ch_evalraw ch_getbufnr ch_getjob ch_info ch_log ch_logfile ch_open ch_read ch_readblob ch_readraw ch_sendexpr ch_sendraw ch_setoptions ch_status changenr
syn keyword vimFuncName contained char2nr charclass charcol charidx chdir cindent clearmatches cmdcomplete_info col complete complete_add complete_check complete_info complete_match confirm copy cos cosh count cscope_connection cursor debugbreak deepcopy delete deletebufline did_filetype diff diff_filler diff_hlID digraph_get digraph_getlist digraph_set digraph_setlist echoraw empty environ err_teapot escape eval eventhandler executable execute exepath exists exists_compiled exp expand expandcmd extend extendnew feedkeys filecopy filereadable filewritable filter finddir findfile flatten flattennew float2nr floor fmod fnameescape fnamemodify foldclosed foldclosedend foldlevel foldtext foldtextresult foreach foreground fullcommand funcref function garbagecollect
syn keyword vimFuncName contained get getbufinfo getbufline getbufoneline getbufvar getcellpixels getcellwidths getchangelist getchar getcharmod getcharpos getcharsearch getcharstr getcmdcomplpat getcmdcompltype getcmdline getcmdpos getcmdprompt getcmdscreenpos getcmdtype getcmdwintype getcompletion getcurpos getcursorcharpos getcwd getenv getfontname getfperm getfsize getftime getftype getimstatus getjumplist getline getloclist getmarklist getmatches getmousepos getmouseshape getpid getpos getqflist getreg getreginfo getregion getregionpos getregtype getscriptinfo getstacktrace gettabinfo gettabvar gettabwinvar gettagstack gettext getwininfo getwinpos getwinposx getwinposy getwinvar glob glob2regpat globpath has has_key haslocaldir hasmapto histadd histdel histget
syn keyword vimFuncName contained histnr hlID hlexists hlget hlset hostname iconv id indent index indexof input inputdialog inputlist inputrestore inputsave inputsecret insert instanceof interrupt invert isabsolutepath isdirectory isinf islocked isnan items job_getchannel job_info job_setoptions job_start job_status job_stop join js_decode js_encode json_decode json_encode keys keytrans len libcall libcallnr line line2byte lispindent list2blob list2str list2tuple listener_add listener_flush listener_remove localtime log log10 luaeval map maparg mapcheck maplist mapnew mapset match matchadd matchaddpos matcharg matchbufline matchdelete matchend matchfuzzy matchfuzzypos matchlist matchstr matchstrlist matchstrpos max menu_info min mkdir mode mzeval nextnonblank
syn keyword vimFuncName contained ngettext nr2char or pathshorten perleval popup_atcursor popup_beval popup_clear popup_close popup_create popup_dialog popup_filter_menu popup_filter_yesno popup_findecho popup_findinfo popup_findpreview popup_getoptions popup_getpos popup_hide popup_list popup_locate popup_menu popup_move popup_notification popup_setbuf popup_setoptions popup_settext popup_show pow prevnonblank printf prompt_getprompt prompt_setcallback prompt_setinterrupt prompt_setprompt prop_add prop_add_list prop_clear prop_find prop_list prop_remove prop_type_add prop_type_change prop_type_delete prop_type_get prop_type_list pum_getpos pumvisible py3eval pyeval pyxeval rand range readblob readdir readdirex readfile reduce reg_executing reg_recording reltime
syn keyword vimFuncName contained reltimefloat reltimestr remote_expr remote_foreground remote_peek remote_read remote_send remote_startserver remove rename repeat resolve reverse round rubyeval screenattr screenchar screenchars screencol screenpos screenrow screenstring search searchcount searchdecl searchpair searchpairpos searchpos server2client serverlist setbufline setbufvar setcellwidths setcharpos setcharsearch setcmdline setcmdpos setcursorcharpos setenv setfperm setline setloclist setmatches setpos setqflist setreg settabvar settabwinvar settagstack setwinvar sha256 shellescape shiftwidth sign_define sign_getdefined sign_getplaced sign_jump sign_place sign_placelist sign_undefine sign_unplace sign_unplacelist simplify sin sinh slice sort sound_clear sound_playevent
syn keyword vimFuncName contained sound_playfile sound_stop soundfold spellbadword spellsuggest split sqrt srand state str2blob str2float str2list str2nr strcharlen strcharpart strchars strdisplaywidth strftime strgetchar stridx string strlen strpart strptime strridx strtrans strutf16len strwidth submatch substitute swapfilelist swapinfo swapname synID synIDattr synIDtrans synconcealed synstack system systemlist tabpagebuflist tabpagenr tabpagewinnr tagfiles taglist tan tanh tempname term_dumpdiff term_dumpload term_dumpwrite term_getaltscreen term_getansicolors term_getattr term_getcursor term_getjob term_getline term_getscrolled term_getsize term_getstatus term_gettitle term_gettty term_list term_scrape term_sendkeys term_setansicolors term_setapi term_setkill
syn keyword vimFuncName contained term_setrestore term_setsize term_start term_wait terminalprops test_alloc_fail test_autochdir test_feedinput test_garbagecollect_now test_garbagecollect_soon test_getvalue test_gui_event test_ignore_error test_mswin_event test_null_blob test_null_channel test_null_dict test_null_function test_null_job test_null_list test_null_partial test_null_string test_null_tuple test_option_not_set test_override test_refcount test_setmouse test_settime test_srand_seed test_unknown test_void timer_info timer_pause timer_start timer_stop timer_stopall tolower toupper tr trim trunc tuple2list type typename undofile undotree uniq utf16idx values virtcol virtcol2col visualmode wildmenumode win_execute win_findbuf win_getid win_gettype win_gotoid
syn keyword vimFuncName contained win_id2tabwin win_id2win win_move_separator win_move_statusline win_screenpos win_splitmove winbufnr wincol windowsversion winheight winlayout winline winnr winrestcmd winrestview winsaveview winwidth wordcount writefile xor
" Predefined variable names {{{2
" GEN_SYN_VIM: vimVarName, START_STR='syn keyword vimVimVarName contained', END_STR=''
syn keyword vimVimVarName contained count count1 prevcount errmsg warningmsg statusmsg shell_error this_session version lnum termresponse fname lang lc_time ctype charconvert_from charconvert_to fname_in fname_out fname_new fname_diff cmdarg foldstart foldend folddashes foldlevel progname servername dying exception throwpoint register cmdbang insertmode val key profiling fcs_reason fcs_choice beval_bufnr beval_winnr beval_winid beval_lnum beval_col beval_text scrollstart swapname swapchoice swapcommand char mouse_win mouse_winid mouse_lnum mouse_col operator searchforward hlsearch oldfiles windowid progpath completed_item option_new option_old option_oldlocal option_oldglobal option_command option_type errors false true none null numbermax numbermin numbersize
syn keyword vimVimVarName contained vim_did_enter testing t_number t_string t_func t_list t_dict t_float t_bool t_none t_job t_channel t_blob t_class t_object termrfgresp termrbgresp termu7resp termstyleresp termblinkresp event versionlong echospace argv collate exiting colornames sizeofint sizeoflong sizeofpointer maxcol python3_version t_typealias t_enum t_enumvalue stacktrace t_tuple

"--- syntax here and above generated by runtime/syntax/generator/gen_syntax_vim.vim ---

" Special Vim Highlighting (not automatic) {{{1

" Neovim keyword list additions {{{2

if s:has("nvim")
  syn keyword vimOptionVarName contained channel inccommand mousescroll pumblend redrawdebug scrollback shada shadafile statuscolumn termpastefilter termsync winbar winblend winhighlight
  syn keyword vimVimVarName    contained lua msgpack_types relnum stderr termrequest virtnum
endif

" Set up commands for this syntax highlighting file {{{2

com! -nargs=* Vim9 execute <q-args> s:vim9script ? "" : "contained"
com! -nargs=* VimL execute <q-args> s:vim9script ? "contained" : ""

if exists("g:vimsyn_folding") && g:vimsyn_folding =~# '[acefhiHlmpPrt]'
 if g:vimsyn_folding =~# 'a'
  com! -nargs=* VimFolda <args> fold
 else
  com! -nargs=* VimFolda <args>
 endif
 if g:vimsyn_folding =~# 'c'
  com! -nargs=* VimFoldc <args> fold
 else
  com! -nargs=* VimFoldc <args>
 endif
 if g:vimsyn_folding =~# 'e'
  com! -nargs=* VimFolde <args> fold
 else
  com! -nargs=* VimFolde <args>
 endif
 if g:vimsyn_folding =~# 'f'
  com! -nargs=* VimFoldf <args> fold
 else
  com! -nargs=* VimFoldf <args>
 endif
 if g:vimsyn_folding =~# 'h'
  com! -nargs=* VimFoldh <args> fold
 else
  com! -nargs=* VimFoldh <args>
 endif
 if g:vimsyn_folding =~# 'H'
  com! -nargs=* VimFoldH <args> fold
 else
  com! -nargs=* VimFoldH <args>
 endif
 if g:vimsyn_folding =~# 'i'
  com! -nargs=* VimFoldi <args> fold
 else
  com! -nargs=* VimFoldi <args>
 endif
 if g:vimsyn_folding =~# 'l'
  com! -nargs=* VimFoldl <args> fold
 else
  com! -nargs=* VimFoldl <args>
 endif
 if g:vimsyn_folding =~# 'm'
  com! -nargs=* VimFoldm <args> fold
 else
  com! -nargs=* VimFoldm <args>
 endif
 if g:vimsyn_folding =~# 'p'
  com! -nargs=* VimFoldp <args> fold
 else
  com! -nargs=* VimFoldp <args>
 endif
 if g:vimsyn_folding =~# 'P'
  com! -nargs=* VimFoldP <args> fold
 else
  com! -nargs=* VimFoldP <args>
 endif
 if g:vimsyn_folding =~# 'r'
  com! -nargs=* VimFoldr <args> fold
 else
  com! -nargs=* VimFoldr <args>
 endif
 if g:vimsyn_folding =~# 't'
  com! -nargs=* VimFoldt <args> fold
 else
  com! -nargs=* VimFoldt <args>
 endif
else
 com! -nargs=*	VimFolda	<args>
 com! -nargs=*	VimFoldc	<args>
 com! -nargs=*	VimFolde	<args>
 com! -nargs=*	VimFoldf	<args>
 com! -nargs=*	VimFoldi	<args>
 com! -nargs=*	VimFoldh	<args>
 com! -nargs=*	VimFoldH	<args>
 com! -nargs=*	VimFoldl	<args>
 com! -nargs=*	VimFoldm	<args>
 com! -nargs=*	VimFoldp	<args>
 com! -nargs=*	VimFoldP	<args>
 com! -nargs=*	VimFoldr	<args>
 com! -nargs=*	VimFoldt	<args>
endif

" Deprecated variable options {{{2
if exists("g:vim_minlines")
 let g:vimsyn_minlines= g:vim_minlines
endif
if exists("g:vim_maxlines")
 let g:vimsyn_maxlines= g:vim_maxlines
endif
if exists("g:vimsyntax_noerror")
 let g:vimsyn_noerror= g:vimsyntax_noerror
endif

" Variable options {{{2
if exists("g:vim_maxlines")
 let s:vimsyn_maxlines= g:vim_maxlines
else
 let s:vimsyn_maxlines= 60
endif

" Nulls {{{2
" =====
Vim9 syn keyword  vim9Null	null null_blob null_channel null_class null_dict null_function null_job null_list null_object null_partial null_string

" Booleans {{{2
" ========
Vim9 syn keyword vim9Boolean	true false

" Numbers {{{2
" =======
syn case ignore
syn match	vimNumber	"\<\d\+\%('\d\+\)*"		skipwhite nextgroup=@vimComment,vimSubscript,vimGlobal,vimSubst1
syn match	vimNumber	"\<\d\+\%('\d\+\)*\.\d\+\%(e[+-]\=\d\+\)\="	skipwhite nextgroup=@vimComment
syn match	vimNumber	"\<0b[01]\+\%('[01]\+\)*"		skipwhite nextgroup=@vimComment,vimSubscript
syn match	vimNumber	"\<0o\=\o\+\%('\o\+\)*"		skipwhite nextgroup=@vimComment,vimSubscript
syn match	vimNumber	"\<0x\x\+\%('\x\+\)*"		skipwhite nextgroup=@vimComment,vimSubscript
syn match	vimNumber	'\<0z\>'			skipwhite nextgroup=@vimComment
syn match	vimNumber	'\<0z\%(\x\x\)\+\%(\.\%(\x\x\)\+\)*'	skipwhite nextgroup=@vimComment,vimSubscript
syn case match

" All vimCommands are contained by vimIsCommand. {{{2
syn cluster vimCmdList	contains=vimAbb,vimAddress,vimAutocmd,vimAugroup,vimBehave,vimCall,vimCatch,vimConst,vimDoautocmd,vimDebuggreedy,vimDef,vimDefFold,vimDelcommand,@vimEcho,vimElse,vimEnddef,vimEndfunction,vimEndif,vimExecute,vimIsCommand,vimExtCmd,vimExFilter,vimFor,vimFunction,vimFuncFold,vimGrep,vimGrepAdd,vimGlobal,vimHelpgrep,vimHighlight,vimLet,vimLoadkeymap,vimLockvar,vimMake,vimMap,vimMark,vimMatch,vimNotFunc,vimNormal,vimRedir,vimSet,vimSleep,vimSort,vimSyntax,vimThrow,vimUnlet,vimUnlockvar,vimUnmap,vimUserCmd,vimVimgrep,vimVimgrepadd,vimMenu,vimMenutranslate,@vim9CmdList,@vimExUserCmdList
syn cluster vim9CmdList	contains=vim9Abstract,vim9Class,vim9Const,vim9Enum,vim9Export,vim9Final,vim9For,vim9Interface,vim9Type,vim9Var
syn match vimCmdSep	"\\\@1<!|"	skipwhite nextgroup=@vimCmdList,vimSubst1,vimFunc
syn match vimCmdSep	":\+"	skipwhite nextgroup=@vimCmdList,vimSubst1
syn match vimCount	contained	"\d\+"
syn match vimIsCommand	"\<\%(\h\w*\|[23]mat\%[ch]\)\>"	 nextgroup=vimBang contains=vimCommand
syn match vimBang	      contained	"!"
syn match vimWhitespace contained	"\s\+"

syn region vimSubscript contained	matchgroup=vimSubscriptBracket start="\[" end="]" nextgroup=vimSubscript contains=@vimExprList

syn match vimVar	      contained	"\<\h[a-zA-Z0-9#_]*\>"	nextgroup=vimSubscript contains=vim9Super,vim9This
syn match vimVar		"\<[bwglstav]:\h[a-zA-Z0-9#_]*\>"	nextgroup=vimSubscript contains=vimVarScope
syn match vimVar		"\<a:\%(000\|1\=[0-9]\|20\)\>"	nextgroup=vimSubscript contains=vimVarScope
syn match vimFBVar      contained	"\<[bwglsta]:\h[a-zA-Z0-9#_]*\>"	nextgroup=vimSubscript contains=vimVarScope

" match the scope prefix independently of the retrofitted scope dictionary
syn match vimVarScope   contained	"\<[bwglstav]:"
syn match vimVimVar     contained	"\<[bwglstav]:\%(\h\|\d\)\@!"	nextgroup=vimSubscript

syn match vimVarNameError contained "\<\h\w*\>"
syn match vimVimVar	"\<v:"		nextgroup=vimSubscript,vimVimVarName,vimVarNameError
syn match vimOptionVar	"&\%([lg]:\)\="		nextgroup=vimSubscript,vimOptionVarName,vimVarNameError
syn cluster vimSpecialVar	contains=vimEnvvar,vimLetRegister,vimOptionVar,vimVimVar

Vim9 syn match vim9LhsVariable	"\s\=\h[a-zA-Z0-9#_]*\ze\s\+[-+/*%]\=="
Vim9 syn match vim9LhsVariable	"\s\=\h[a-zA-Z0-9#_]*\ze\s\+\.\.="
Vim9 syn match vim9LhsVariable	"\s\=\h[a-zA-Z0-9#_]*\ze\s\+=<<"
Vim9 syn match vim9LhsVariable	"\s\=\h[a-zA-Z0-9#_]*\ze\s*->"		contains=vim9Super,vim9This
Vim9 syn match vim9LhsVariable	"\s\=\h[a-zA-Z0-9#_]*\ze\["	nextgroup=vimSubscript
Vim9 syn match vim9LhsVariable	"\s\=\h[a-zA-Z0-9#_]*\ze\."	nextgroup=vimOper contains=vim9Super,vim9This

Vim9 syn match vim9LhsVariableList	"\[\_[^]]\+]\ze\s\+[-+/*%]\=="	contains=vimVar,@vimSpecialVar
Vim9 syn match vim9LhsVariableList	"\[\_[^]]\+]\ze\s\+\.\.="	contains=vimVar,@vimSpecialVar

Vim9 syn match vim9LhsRegister	"@["0-9\-a-zA-Z#=*+_/]\ze\s\+\%(\.\.\)\=="

syn cluster vimExprList	contains=@vimSpecialVar,vimFunc,vimNumber,vimOper,vimOperParen,vimLambda,vimString,vimVar,@vim9ExprList
syn cluster vim9ExprList	contains=vim9Boolean,vim9LambdaParams,vim9Null

" Insertions And Appends: insert append {{{2
"   (buftype != nofile test avoids having append, change, insert show up in the command window)
" =======================
if &buftype != 'nofile'
 syn region vimInsert	matchgroup=vimCommand start="^[: \t]*\(\d\+\(,\d\+\)\=\)\=a\%[ppend]$"		matchgroup=vimCommand end="^\.$" extend
 syn region vimInsert	matchgroup=vimCommand start="^[: \t]*\(\d\+\(,\d\+\)\=\)\=c\%[hange]$"		matchgroup=vimCommand end="^\.$" extend
 syn region vimInsert	matchgroup=vimCommand start="^[: \t]*\(\d\+\(,\d\+\)\=\)\=i\%[nsert]$"		matchgroup=vimCommand end="^\.$" extend
endif

" Behave! {{{2
" =======
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_nobehaveerror")
 syn match   vimBehaveError contained	"[^ ]\+"
endif
syn match   vimBehave	"\<be\%[have]\>"	nextgroup=vimBehaveBang,vimBehaveModel,vimBehaveError skipwhite
syn match   vimBehaveBang	contained	"\a\@1<=!" nextgroup=vimBehaveModel skipwhite
syn keyword vimBehaveModel	contained	mswin	xterm

" Call {{{2
" ====
syn match vimCall	"\<call\=\>"	skipwhite nextgroup=vimFunc

" Debuggreedy {{{2
" ===========
" TODO: special-cased until generalised range/count support is implemented
syn match	vimDebuggreedy	"\<0\=debugg\%[reedy]\>" contains=vimCount

" Exception Handling {{{2
syn keyword	vimThrow	th[row]	skipwhite nextgroup=@vimExprList
syn keyword	vimCatch	cat[ch]	skipwhite nextgroup=vimCatchPattern
syn region	vimCatchPattern	contained	matchgroup=Delimiter start="\z([!#$%&'()*+,-./:;<=>?@[\]^_`{}~]\)" skip="\\\\\|\\\z1" end="\z1" contains=@vimSubstList oneline

" Export {{{2
" ======
if s:vim9script
  syn keyword	vim9Export	export	skipwhite nextgroup=vim9Abstract,vim9ClassBody,vim9Const,vim9Def,vim9EnumBody,vim9Final,vim9InterfaceBody,vim9Type,vim9Var
endif

" Filetypes {{{2
" =========
syn match   vimFiletype	"\<filet\%[ype]\(\s\+\I\i*\)*"	skipwhite contains=vimFTCmd,vimFTOption,vimFTError
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_vimFTError")
 syn match   vimFTError  contained	"\I\i*"
endif
syn keyword vimFTCmd    contained	filet[ype]
syn keyword vimFTOption contained	detect indent off on plugin

" Augroup : vimAugroupError removed because long augroups caused sync'ing problems. {{{2
" ======= : Trade-off: Increasing synclines with slower editing vs augroup END error checking.
syn cluster vimAugroupList	contains=@vimCmdList,vimFilter,vimFunc,vimLineComment,vimSpecFile,vimOper,vimNumber,vimOperParen,@vimComment,vimString,vimSubst,vimRegister,vimCmplxRepeat,vimNotation,vimCtrlChar,vimContinue

" define
VimFolda syn region vimAugroup
      \ start="\<aug\%[roup]\>\ze\s\+\%([eE][nN][dD]\)\@!\S\+"
      \ matchgroup=vimAugroupKey
      \ end="\<aug\%[roup]\>\ze\s\+[eE][nN][dD]\>"
      \ skipwhite nextgroup=vimAugroupEnd
      \ contains=vimAutocmd,@vimAugroupList,vimAugroupkey
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_noaugrouperror")
  syn match	vimAugroupError	"\<aug\%[roup]\>\s\+[eE][nN][dD]\>"
endif

" TODO: Vim9 comment
syn match	vimAugroupName	contained	"\%(\\["|[:space:]]\|[^"|[:space:]]\)\+"
      \                                                                 skipwhite nextgroup=vimCmdSep,vimComment
syn match	vimAugroupEnd	contained	"\c\<END\>"	skipwhite nextgroup=vimCmdSep,vimComment
syn match	vimAugroupBang	contained	"\a\@1<=!"	skipwhite nextgroup=vimAugroupName
syn keyword	vimAugroupKey	contained	aug[roup] 	skipwhite nextgroup=vimAugroupBang,vimAugroupName,vimAugroupEnd

" remove
syn match	vimAugroup	"\<aug\%[roup]!"		skipwhite nextgroup=vimAugroupName contains=vimAugroupKey,vimAugroupBang 

" list
VimL syn match	vimAugroup	"\<aug\%[roup]\>\ze\s*\%(["|]\|$\)"	skipwhite nextgroup=vimCmdSep,vimComment  contains=vimAugroupKey
Vim9 syn match 	vimAugroup	"\<aug\%[roup]\>\ze\s*\%([#|]\|$\)"	skipwhite nextgroup=vimCmdSep,vim9Comment contains=vimAugroupKey

" Operators: {{{2
" =========
syn cluster	vimOperGroup	contains=@vimContinue,@vimExprList,vim9Comment,vim9LineComment,vimContinueString
syn match	vimOper	"\a\@<!!"			skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimSpecFile
syn match	vimOper	"||\|&&\|[-+*/%.]"		skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimSpecFile
syn match	vimOper	"?"			skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString
" distinguish ternary : from ex-colon
syn match	vimOper	"\s\@1<=:\ze\s\|\s\@1<=:$"		skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString
syn match	vimOper	"??"			skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString
syn match	vimOper	"="			skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString,vimSpecFile
syn match	vimOper	"\%#=1\%(==\|!=\|>=\|<=\|=\~\|!\~\|>\|<\)[?#]\="	skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString,vimSpecFile
syn match	vimOper	"\<is\%(not\)\=\>"		skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString,vimSpecFile
syn match	vimOper	"\<is\%(not\)\=[?#]"		skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList,vimContinueString,vimSpecFile
syn region	vimOperParen 		matchgroup=vimParenSep start="("    end=")" contains=@vimOperGroup nextgroup=vimSubscript
syn region	vimOperParen		matchgroup=vimSep	     start="#\={" end="}" contains=@vimOperGroup nextgroup=vimSubscript,vimVar
syn region	vimOperParen	contained	matchgroup=vimSep	     start="\["	end="]" contains=@vimOperGroup nextgroup=vimSubscript,vimVar
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_noopererror")
 syn match	vimOperError	")"
endif

syn match	vimOperContinue		contained	"^\s*\\"        skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList contains=vimWhitespace
syn match         vimOperContinueComment	contained	'^\s*["#]\\ .*' skipwhite skipnl nextgroup=@vimOperContinue,@vimExprList contains=vimWhitespace
syn cluster	vimOperContinue		contains=vimOperContinue,vimOperContinueComment

" Lambda Expressions: {{{2
" ==================
syn match	vimLambdaOperator	contained	"->" skipwhite nextgroup=@vimExprList
syn region	vimLambda	contained
      \ matchgroup=vimLambdaBrace
      \ start=+{\ze[[:space:][:alnum:]_.,]*\%(\n\s*\%(\\[[:space:][:alnum:]_.,]*\|"\\ .*\)\)*->+
      \ skip=+\n\s*\%(\\\|"\\ \)+
      \ end="}" end="$"
      \ contains=@vimContinue,@vimExprList,vimLambdaParams
syn match	vimLambdaParams	contained	"\%({\n\=\)\@1<=\_.\{-}\%(->\)\@=" nextgroup=vimLambdaOperator contains=@vimContinue,vimFuncParam

syn match	vim9LambdaOperator   contained	"=>" skipwhite skipempty nextgroup=@vimExprList,vim9LambdaBlock,vim9LambdaOperatorComment
syn match	vim9LambdaParen      contained	"[()]"
syn match	vim9LambdaParams	   contained
      \ "(\%(\<func(\|[^(]\)*\%(\n\s*\\\%(\<func(\|[^(]\)*\|\n\s*#\\ .*\)*\ze\s\+=>"
      \ skipwhite nextgroup=vim9LambdaOperator
      \ contains=@vim9Continue,vimDefParam,vim9LambdaParen,vim9LambdaReturnType
syn region	vim9LambdaReturnType contained	start=")\@<=:\s" end="\ze\s*#" end="\ze\s*=>" contains=@vim9Continue,@vimType transparent
syn region	vim9LambdaBlock	   contained	matchgroup=vimSep start="{" end="^\s*\zs}" contains=@vimDefBodyList

syn match	vim9LambdaOperatorComment contained "#.*" skipwhite skipempty nextgroup=@vimExprList,vim9LambdaBlock,vim9LambdaOperatorComment

" Functions: Tag is provided for those who wish to highlight tagged functions {{{2
" =========
syn cluster	vimFuncList	contains=vimFuncBang,vimFunctionError,vimFuncKey,vimFuncScope,vimFuncSID,Tag
syn cluster	vimDefList	contains=vimFuncBang,vimFunctionError,vimDefKey,vimFuncScope,vimFuncSID,Tag

syn cluster	vimFuncBodyCommon	contains=@vimCmdList,vimCmplxRepeat,vimContinue,vimCtrlChar,vimDef,vimFBVar,vimFunc,vimFunction,vimLetHereDoc,vimNotFunc,vimNumber,vimOper,vimOperParen,vimRegister,vimSpecFile,vimString,vimSubst,vimFuncFold,vimDefFold
syn cluster	vimFuncBodyList	contains=@vimFuncBodyCommon,vimComment,vimLineComment,vimInsert,vimConst,vimLet,vimSearch
syn cluster	vimDefBodyList	contains=@vimFuncBodyCommon,vim9Comment,vim9LineComment,vim9Block,vim9Const,vim9Final,vim9Var,vim9Null,vim9Boolean,vim9For,vim9LhsVariable,vim9LhsVariableList,vim9LhsRegister,vim9Search,@vimSpecialVar

syn region	vimFuncPattern	contained		matchgroup=vimOper start="/" end="$" contains=@vimSubstList
syn match	vimFunction	"\<fu\%[nction]\>"	skipwhite nextgroup=vimCmdSep,vimComment,vimFuncPattern contains=vimFuncKey
syn match	vimDef	"\<def\>"		skipwhite nextgroup=vimCmdSep,vimComment,vimFuncPattern contains=vimDefKey

syn match	vimFunction	"\<fu\%[nction]\>!\=\s*\%(<[sS][iI][dD]>\|[sg]:\)\=\%(\i\|[#.]\|{.\{-1,}}\)\+"	contains=@vimFuncList skipwhite nextgroup=vimFuncParams
syn match	vimDef	"\<def\>!\=\s*\%(<[sS][iI][dD]>\|[sg]:\)\=\%(\i\|[#.]\|{.\{-1,}}\)\+"		contains=@vimDefList            nextgroup=vimDefParams

syn region	vimFuncComment	contained
      \ start=+".*+
      \ skip=+\n\s*\\\|\n\s*"\\ +
      \ end="$"
      \ skipwhite skipempty nextgroup=vimFuncBody,vimEndfunction
syn region	vimDefComment	contained
      \ start="#.*"
      \ skip=+\n\s*\\\|\n\s*#\\ +
      \ end="$"
      \ skipwhite skipempty nextgroup=vimDefBody,vimEnddef

syn match	vimFuncBang	contained	"!"
syn match	vimFuncSID	contained	"\c<sid>"
syn match	vimFuncScope	contained	"\<[sg]:"
syn keyword	vimFuncKey	contained	fu[nction]
syn keyword	vimDefKey	contained	def

syn region	vimFuncParams	contained	matchgroup=Delimiter start="(" skip=+\n\s*\\\|\n\s*"\\ + end=")" skipwhite skipempty nextgroup=vimFuncBody,vimFuncComment,vimEndfunction,vimFuncMod,vim9CommentError	contains=vimFuncParam,vimOperParen,@vimContinue
syn region	vimDefParams	contained	matchgroup=Delimiter start="("		   end=")" skipwhite skipempty nextgroup=vimDefBody,vimDefComment,vimEnddef,vimReturnType,vimCommentError	contains=vimDefParam,vim9Comment,vimFuncParamEquals,vimOperParen
syn match	vimFuncParam	contained	"\<\h\w*\>\|\.\.\."	skipwhite nextgroup=vimFuncParamEquals
syn match	vimDefParam	contained	"\<\h\w*\>"		skipwhite nextgroup=vimParamType,vimFuncParamEquals

syn match	vimFuncParamEquals contained	"="			skipwhite           nextgroup=@vimExprList
syn match	vimFuncMod	 contained	"\<\%(abort\|closure\|dict\|range\)\>"	skipwhite skipempty nextgroup=vimFuncBody,vimFuncComment,vimEndfunction,vimFuncMod,vim9CommentError

syn region	vimFuncBody	contained	start="^." matchgroup=vimCmdSep start="|" matchgroup=vimCommand end="\<endfu\%[nction]\>"	contains=@vimFuncBodyList skipwhite nextgroup=vimCmdSep,vimComment,vim9CommentError
syn region	vimDefBody	contained	start="^." matchgroup=vimCmdSep start="|" matchgroup=vimCommand end="\<enddef\>"	contains=@vimDefBodyList  skipwhite nextgroup=vimCmdSep,vim9Comment,vimCommentError

syn match	vimEndfunction	"\<endf\%[unction]\>" skipwhite nextgroup=vimCmdSep,vimComment,vim9CommentError
syn match	vimEnddef	"\<enddef\>"	    skipwhite nextgroup=vimCmdSep,vim9Comment,vimCommentError

if exists("g:vimsyn_folding") && g:vimsyn_folding =~# 'f'
 syn region	vimFuncFold	start="\<fu\%[nction]\>!\=\s*\%(<[sS][iI][dD]>\|[sg]:\)\=\%(\i\|[#.]\|{.\{-1,}}\)\+\s*(" end="\<endf\%[unction]\>" contains=vimFunction fold keepend extend transparent
 syn region	vimDefFold	start="\<def\>!\=\s*\%(<[sS][iI][dD]>\|[sg]:\)\=\%(\i\|[#.]\)\+("	                 end="\<enddef\>"	       contains=vimDef      fold keepend extend transparent
endif

" Types: {{{2
" =====

syn region	vimReturnType	contained
      \ start=":\%(\s\|\n\)\@="
      \ skip=+\n\s*\\\|\n\s*#\\ \|^\s*#\\ +
      \ end="$"
      \ matchgroup=vim9Comment
      "\ allow for legacy script tail comment error
      \ end="\ze[#"]"
      \ skipwhite skipempty nextgroup=vimDefBody,vimDefComment,vimEnddef,vimCommentError
      \ contains=@vim9Continue,@vimType
      \ transparent
syn match	vimParamType	contained	":\s"	skipwhite skipnl nextgroup=@vimType contains=vimTypeSep

syn match	vimTypeSep	contained	":\%(\s\|\n\)\@=" skipwhite nextgroup=@vimType
syn keyword	vimType	contained	any blob bool channel float job number string void
syn match	vimType	contained	"\<\%(func\)\>"
syn region	vimCompoundType	contained	matchgroup=vimType start="\<func("	          end=")" nextgroup=vimTypeSep contains=@vim9Continue,@vimType transparent
syn region	vimCompoundType	contained	matchgroup=vimType start="\<tuple<"           end=">"                      contains=@vim9Continue,@vimType transparent
syn region	vimCompoundType	contained	matchgroup=vimType start="\<\%(list\|dict\)<" end=">"		   contains=@vimType oneline       transparent
syn match	vimUserType	contained	"\<\%(\h\w*\.\)*\u\w*\>"

syn cluster vimType contains=vimType,vimCompoundType,vimUserType

" Classes, Enums And Interfaces: {{{2
" =============================

if s:vim9script

  " Methods {{{3
  syn match	vim9MethodDef		contained	"\<def\>"	skipwhite nextgroup=vim9MethodDefName,vim9ConstructorDefName
  syn match	vim9MethodDefName		contained	"\<\h\w*\>"	nextgroup=vim9MethodDefParams contains=@vim9MethodName
  syn region	vim9MethodDefParams	contained
        \ matchgroup=Delimiter start="(" end=")"
        \ skipwhite skipnl nextgroup=vim9MethodDefBody,vim9MethodDefComment,vimEnddef,vim9MethodDefReturnType,vimCommentError
        \ contains=vimDefParam,vim9Comment,vimFuncParamEquals

  syn match	vim9ConstructorDefName	contained	"\<_\=new\w*\>"
        \ nextgroup=vim9ConstructorDefParams
        \ contains=@vim9MethodName
  syn match	vim9ConstructorDefParam	contained	"\<\%(this\.\)\=\h\w*\>"
        \ skipwhite nextgroup=vimParamType,vimFuncParamEquals
        \ contains=vim9This,vimOper
  syn region	vim9ConstructorDefParams	contained
        \ matchgroup=Delimiter start="(" end=")"
        \ skipwhite skipnl nextgroup=vim9MethodDefBody,vim9MethodDefComment,vimEnddef,vimCommentError
        \ contains=vim9ConstructorDefParam,vim9Comment,vimFuncParamEquals

  syn region	vim9MethodDefReturnType	contained
        \ start=":\%(\s\|\n\)\@="
        \ skip=+\n\s*\\\|\n\s*#\\ \|^\s*#\\ +
        \ end="$"
        \ matchgroup=vim9Comment
        \ end="\ze#"
        \ skipwhite skipnl nextgroup=vim9MethodDefBody,vim9MethodDefComment,vimEnddef,vimCommentError
        \ contains=@vim9Continue,vimType,vimTypeSep
        \ transparent

  syn region	vim9MethodDefComment	contained
        \ start="#.*"
        \ skip=+\n\s*\\\|\n\s*#\\ +
        \ end="$"
        \ skipwhite skipempty nextgroup=vim9MethodDefBody,vimEnddef

  syn region	vim9MethodDefBody		contained
        \ start="^.\=" matchgroup=vimCommand end="\<enddef\>"
        \ skipwhite nextgroup=vimCmdSep,vim9Comment,vimCommentError
        \ contains=@vim9MethodDefBodyList

  syn cluster	vim9MethodDefBodyList contains=@vimDefBodyList,vim9This,vim9Super

  if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimfunctionerror")
    syn match	vim9MethodNameError contained	"\<[a-z0-9]\i\>"
  endif
  syn match	vim9MethodName	contained	"\<_\=new\w*\>"
  syn keyword	vim9MethodName	contained	empty len string

  syn cluster	vim9MethodName	contains=vim9MethodName,vim9MethodNameError

  if exists("g:vimsyn_folding") && g:vimsyn_folding =~# 'f'
    syn region	vim9MethodDefFold	contained	start="\%(^\s*\%(:\=static\s\+\)\=\)\@16<=:\=def\s\+\h\i*(" end="^\s*:\=enddef\>" contains=vim9MethodDef fold keepend extend transparent
    syn region	vim9MethodDefFold	contained	start="^\s*:\=def\s\+_\=new\i*("		      end="^\s*:\=enddef\>" contains=vim9MethodDef fold keepend extend transparent
  endif

  syn cluster vim9MethodDef contains=vim9MethodDef,vim9MethodDefFold

  " Classes {{{3
  syn cluster	vim9ClassBodyList		contains=vim9Abstract,vim9Class,vim9Comment,vim9LineComment,@vim9Continue,@vimExprList,vim9Extends,vim9Implements,@vim9MethodDef,vim9Public,vim9Static,vim9Const,vim9Final,vim9This,vim9Super,vim9Var

  syn match	vim9Class		contained	"\<class\>"	skipwhite        nextgroup=vim9ClassName
  syn match	vim9ClassName		contained	"\<\u\w*\>"	skipwhite skipnl nextgroup=vim9Extends,vim9Implements
  syn match	vim9SuperClass		contained	"\<\u\w*\>"	skipwhite skipnl nextgroup=vim9Implements
  syn match	vim9ImplementedInterface	contained	"\<\u\w*\>"	skipwhite skipnl nextgroup=vim9InterfaceListComma,vim9Extends
  syn match	vim9InterfaceListComma	contained	","	skipwhite skipnl nextgroup=vim9ImplementedInterface
  syn keyword	vim9Abstract			abstract	skipwhite skipnl nextgroup=vim9ClassBody,vim9AbstractDef
  syn keyword	vim9Extends		contained	extends	skipwhite skipnl nextgroup=vim9SuperClass
  syn keyword	vim9Implements		contained	implements	skipwhite skipnl nextgroup=vim9ImplementedInterface
  syn keyword	vim9Public		contained	public
  syn keyword	vim9Static		contained	static
  " FIXME: don't match as dictionary keys, remove when operators are not
  "        shared between Vim9 and legacy script
  syn match	vim9This		contained	"\.\@1<!\<this\>:\@!"
  " super must be folowed by '.'
  syn match	vim9Super		contained	"\.\@1<!\<super\.\@="

  VimFoldc syn region	vim9ClassBody	start="\<class\>" matchgroup=vimCommand end="\<endclass\>" contains=@vim9ClassBodyList transparent

  " Enums {{{3
  syn cluster	vim9EnumBodyList		contains=vim9Comment,vim9LineComment,@vim9Continue,vim9Enum,@vimExprList,@vim9MethodDef,vim9Public,vim9Static,vim9Const,vim9Final,vim9This,vim9Var

  syn match	vim9Enum		contained	"\<enum\>"	skipwhite           nextgroup=vim9EnumName

  syn match	vim9EnumName		contained	"\<\u\w*\>"	skipwhite skipempty nextgroup=vim9EnumNameTrailing,vim9EnumNameEmpty,vim9EnumNameComment,@vim9EnumNameContinue,vim9EnumImplements
  syn match	vim9EnumNameTrailing	contained	"\S.*"
  syn region	vim9EnumNameComment	contained
        \ start="#" skip="\n\s*\%(\\\|#\\ \)" end="$"
        \ skipwhite skipempty nextgroup=vim9EnumNameComment,vim9EnumValue
        \ contains=@vimCommentGroup,vimCommentString
  " vim9EnumName's "skipempty" should only apply to comments and enum values and not implements clauses
  syn match	vim9EnumNameEmpty		contained	"^"	skipwhite skipempty nextgroup=vim9EnumNameComment,vim9EnumValue
  " allow line continuation between enum name and "implements"
  syn match	vim9EnumNameContinue	contained
        \ "^\s*\\"
        \ skipwhite skipnl nextgroup=vim9EnumNameTrailing,vim9EnumNameEmpty,vim9EnumNameComment,@vim9EnumNameContinue,vim9EnumImplements
        \ contains=vimWhitespace
  syn match	vim9EnumNameContinueComment	contained
        \ "^\s*#\\ .*"
        \ skipwhite skipnl nextgroup=vim9EnumNameEmpty,vim9EnumNameComment,@vim9EnumNameContinue
        \ contains=vimWhitespace
  syn cluster	vim9EnumNameContinue	contains=vim9EnumNameContinue,vim9EnumNameContinueComment

  " enforce enum value list location
  syn match	vim9EnumValue		contained	"\<\a\w*\>"		  nextgroup=vim9EnumValueArgList,vim9EnumValueListComma,vim9Comment
  syn match	vim9EnumValueListComma	contained	","	skipwhite skipempty nextgroup=vim9EnumValue,vim9EnumValueListCommaComment
  syn region	vim9EnumValueListCommaComment	contained
        \ start="#" skip="\n\s*\%(\\\|#\\ \)" end="$"
        \ skipwhite skipempty nextgroup=vim9EnumValueListCommaComment,vim9EnumValue
        \ contains=@vimCommentGroup,vimCommentString
  syn region	vim9EnumValueArgList	contained
        \ matchgroup=vimParenSep start="(" end=")"
        \ nextgroup=vim9EnumValueListComma
        \ contains=@vimExprList,vimContinueString,vim9Comment

  syn keyword	vim9EnumImplements	contained	implements	skipwhite        nextgroup=vim9EnumImplementedInterface
  syn match	vim9EnumImplementedInterface	contained	"\<\u\w*\>"	skipwhite skipnl nextgroup=vim9EnumInterfaceListComma,vim9EnumImplementedInterfaceComment,vim9EnumValue
  syn match	vim9EnumInterfaceListComma	contained	","	skipwhite        nextgroup=vim9EnumImplementedInterface
  syn region	vim9EnumImplementedInterfaceComment	contained
        \ start="#" skip="\n\s*\%(\\\|#\\ \)" end="$"
        \ skipwhite skipempty nextgroup=vim9EnumImplementedInterfaceComment,vim9EnumValue
        \ contains=@vimCommentGroup,vimCommentString

  VimFolde syn region	vim9EnumBody	start="\<enum\>" matchgroup=vimCommand end="\<endenum\>" contains=@vim9EnumBodyList transparent

  " Interfaces {{{3
  " TODO: limit to decl only - no init values
  syn cluster	vim9InterfaceBodyList	contains=vim9Comment,vim9LineComment,@vim9Continue,vim9Extends,vim9Interface,vim9AbstractDef,vim9Var

  syn match	vim9Interface		contained	"\<interface\>"	skipwhite nextgroup=vim9InterfaceName
  syn match	vim9InterfaceName		contained	"\<\u\w*\>"	skipwhite skipnl nextgroup=vim9Extends

  syn keyword	vim9AbstractDef		contained	def	skipwhite nextgroup=vim9AbstractDefName
  syn match	vim9AbstractDefName	contained	"\<\h\w*\>"	skipwhite nextgroup=vim9AbstractDefParams contains=@vim9MethodName
  syn region	vim9AbstractDefParams	contained
        \ matchgroup=Delimiter start="(" end=")"
        \ skipwhite skipnl nextgroup=vimDefComment,vim9AbstractDefReturnType,vimCommentError
        \ contains=vimDefParam,vim9Comment,vimFuncParamEquals
  syn region	vim9AbstractDefReturnType	contained
        \ start=":\s" end="$" matchgroup=vim9Comment end="\ze[#"]"
        \ skipwhite skipnl nextgroup=vimDefComment,vimCommentError
        \ contains=vimTypeSep
        \ transparent

  VimFoldi syn region	vim9InterfaceBody	start="\<interface\>" matchgroup=vimCommand end="\<endinterface\>" contains=@vim9InterfaceBodyList transparent

  " Type Aliases {{{3
  syn match	vim9Type		"\<ty\%[pe]\>"	skipwhite nextgroup=vim9TypeAlias,vim9TypeAliasError
  syn match	vim9TypeAlias	 contained	"\<\u\w*\>"	skipwhite nextgroup=vim9TypeEquals
  syn match	vim9TypeEquals	 contained	"="	skipwhite nextgroup=@vimType
  if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_notypealiaserror")
    syn match	vim9TypeAliasError contained	"\<\l\w*\>"	skipwhite nextgroup=vim9TypeEquals
  endif
endif

" Blocks: {{{2
" ======
Vim9 syn region	vim9Block	matchgroup=vimSep start="{" end="^\s*\zs}" contains=@vimDefBodyList

" Keymaps: {{{2
" =======

syn match  vimKeymapStart	"^"	contained skipwhite nextgroup=vimKeymapLhs,@vimKeymapLineComment
syn match  vimKeymapLhs	"\S\+"	contained skipwhite nextgroup=vimKeymapRhs contains=vimNotation
syn match  vimKeymapRhs	"\S\+"	contained skipwhite nextgroup=vimKeymapTailComment contains=vimNotation
syn match  vimKeymapTailComment	"\S.*"	contained

" TODO: remove when :" comment is matched in parts as "ex-colon comment" --djk
if s:vim9script
  syn match  vim9KeymapLineComment	"#.*"	contained contains=@vimCommentGroup,vimCommentString,vim9CommentTitle
else
  syn match  vimKeymapLineComment	+".*+	contained contains=@vimCommentGroup,vimCommentString,vimCommentTitle
endif
syn cluster vimKeymapLineComment contains=vim9\=KeymapLineComment

syn region vimLoadkeymap matchgroup=vimCommand start="\<loadk\%[eymap]\>" end="\%$" contains=vimKeymapStart

" Special Filenames, Modifiers, Extension Removal: {{{2
" ===============================================
syn match	vimSpecFile	"<c\(word\|WORD\)>"	nextgroup=vimSpecFileMod,vimSubst1
syn match	vimSpecFile	"<\([acs]file\|amatch\|abuf\)>"	nextgroup=vimSpecFileMod,vimSubst1
syn match	vimSpecFile	"\s%[ \t:]"ms=s+1,me=e-1	nextgroup=vimSpecFileMod,vimSubst1
syn match	vimSpecFile	"\s%$"ms=s+1		nextgroup=vimSpecFileMod,vimSubst1
syn match	vimSpecFile	"\s%<"ms=s+1,me=e-1	nextgroup=vimSpecFileMod,vimSubst1
syn match	vimSpecFile	"#\d\+\|[#%]<\>"		nextgroup=vimSpecFileMod,vimSubst1
syn match	vimSpecFileMod	"\(:[phtre]\)\+"	contained

" User-Specified Commands: {{{2
" =======================
syn cluster	vimUserCmdList	contains=@vimCmdList,vimCmplxRepeat,@vimComment,vimCtrlChar,vimEscapeBrace,vimFunc,vimNotation,vimNumber,vimOper,vimRegister,vimSpecFile,vimString,vimSubst,vimSubstRep,vimSubstRange

syn match	vimUserCmd	"\<com\%[mand]\>!\="		skipwhite        nextgroup=vimUserCmdAttrs,vimUserCmdName contains=vimBang
syn match	vimUserCmd	+\<com\%[mand]\>!\=\ze\s*\n\s*\%(\\\|["#]\\ \)+	skipwhite skipnl nextgroup=vimUserCmdAttrs,vimUserCmdName contains=vimBang

syn region	vimUserCmdAttrs	    contained
      \ start="-\l"
      \ start=+^\s*\%(\\\|["#]\\ \)+
      \ end="\ze\s\u"
      \ skipwhite nextgroup=vimUserCmdName
      \ contains=@vimContinue,vimUserCmdAttr,vimUserCmdAttrError
      \ transparent
syn match	vimUserCmdAttrError contained	"-\a\+\ze\%(\s\|=\)"
syn match	vimUserCmdAttr	  contained	"-addr="		contains=vimUserCmdAttrKey nextgroup=vimUserCmdAttrAddr
syn match	vimUserCmdAttr	  contained	"-bang\>"		contains=vimUserCmdAttrKey
syn match	vimUserCmdAttr	  contained	"-bar\>"		contains=vimUserCmdAttrKey
syn match	vimUserCmdAttr	  contained	"-buffer\>"		contains=vimUserCmdAttrKey
syn match	vimUserCmdAttr	  contained	"-complete="		contains=vimUserCmdAttrKey nextgroup=vimUserCmdAttrComplete,vimUserCmdError
syn match	vimUserCmdAttr	  contained	"-count\>"		contains=vimUserCmdAttrKey
syn match	vimUserCmdAttr	  contained	"-count="		contains=vimUserCmdAttrKey nextgroup=vimNumber
syn match	vimUserCmdAttr	  contained	"-keepscript\>"		contains=vimUserCmdAttrKey
syn match	vimUserCmdAttr	  contained	"-nargs="		contains=vimUserCmdAttrKey nextgroup=vimUserCmdAttrNargs
syn match	vimUserCmdAttr	  contained	"-range\>"		contains=vimUserCmdAttrKey
syn match	vimUserCmdAttr	  contained	"-range="		contains=vimUserCmdAttrKey nextgroup=vimNumber,vimUserCmdAttrRange
syn match	vimUserCmdAttr	  contained	"-register\>"		contains=vimUserCmdAttrKey

syn match	vimUserCmdAttrNargs	contained	"[01*?+]"
syn match	vimUserCmdAttrRange	contained	"%"

if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_nousercmderror")
 syn match	vimUserCmdError	contained	"\S\+\>"
endif

syn case ignore
syn keyword	vimUserCmdAttrKey   contained	a[ddr] ban[g] bar bu[ffer] com[plete] cou[nt] k[eepscript] n[args] ra[nge] re[gister]

" GEN_SYN_VIM: vimUserCmdAttrComplete, START_STR='syn keyword vimUserCmdAttrComplete contained', END_STR=''
syn keyword vimUserCmdAttrComplete contained arglist augroup behave breakpoint buffer color command compiler cscope diff_buffer dir dir_in_path environment event expression file file_in_path filetype filetypecmd function help highlight history keymap locale mapclear mapping menu messages option packadd runtime scriptnames shellcmd shellcmdline sign syntax syntime tag tag_listfiles user var
syn keyword vimUserCmdAttrComplete contained arglist augroup behave breakpoint buffer color command compiler cscope diff_buffer dir dir_in_path environment event expression file file_in_path filetype function help highlight history keymap locale mapclear mapping menu messages option packadd runtime scriptnames shellcmd shellcmdline sign syntax syntime tag tag_listfiles user var
syn keyword	vimUserCmdAttrComplete	contained	custom customlist nextgroup=vimUserCmdAttrCompleteFunc,vimUserCmdError
syn match	vimUserCmdAttrCompleteFunc	contained	",\%([bwglstav]:\|<[sS][iI][dD]>\)\=\h\w*\%([.#]\h\w*\)*"hs=s+1 nextgroup=vimUserCmdError contains=vimVarScope,vimFuncSID

" GEN_SYN_VIM: vimUserCmdAttrAddr, START_STR='syn keyword vimUserCmdAttrAddr contained', END_STR=''
syn keyword vimUserCmdAttrAddr contained arguments arg buffers buf lines line loaded_buffers load other quickfix qf tabs tab windows win
syn keyword vimUserCmdAttrAddr contained arguments arg buffers buf lines line loaded_buffers load other quickfix qf tabs tab windows win
syn match	vimUserCmdAttrAddr     contained	"?"
syn case match

syn match	vimUserCmdName	    contained	"\<\u[[:alnum:]]*\>"		skipwhite        nextgroup=vimUserCmdBlock,vimUserCmdReplacement
syn match	vimUserCmdName	    contained	+\<\u[[:alnum:]]*\>\ze\s*\n\s*\%(\\\|["#]\\ \)+	skipwhite skipnl nextgroup=vimUserCmdBlock,vimUserCmdReplacement
syn region	vimUserCmdReplacement contained
      \ start="\S"
      \ start=+^\s*\%(\\\|["#]\\ \)+
      \ skip=+\n\s*\%(\\\|["#]\\ \)+
      \ end="$"
      \ contains=@vimContinue,@vimUserCmdList,vimComFilter
syn region	vimUserCmdBlock	    contained
      \ matchgroup=vimSep
      \ start="{"
      \ end="^\s*\zs}"
      \ contains=@vimDefBodyList

syn match	vimDelcommand		"\<delc\%[ommand]\>"	skipwhite nextgroup=vimDelcommandAttr,vimDelcommandName
syn match	vimDelcommandAttr	contained	"-buffer\>"		skipwhite nextgroup=vimDelcommandName
syn match	vimDelcommandName	contained	"\<\u[[:alnum:]]*\>"

" Lower Priority Comments: after some vim commands... {{{2
" =======================
if get(g:, "vimsyn_comment_strings", 1)
  syn region	vimCommentString	contained oneline start='\S\s\+"'ms=e end='"' extend
endif

if s:vim9script
  syn cluster vimComment contains=vim9Comment
else
  syn cluster vimComment contains=vimComment
endif

VimL syn region	vimComment
      \ excludenl
      \ start=+"+
      \ skip=+\n\s*\%(\\\|"\\ \)+
      \ end="$"
      \ contains=@vimCommentGroup,vimCommentString
      \ extend
Vim9 syn region	vim9Comment
      \ excludenl
      \ start="\%#=1\s\@1<=#\%({\@!\|{{\)"
      \ skip="\n\s*\%(\\\|#\\ \)"
      \ end="$"
      \ contains=@vimCommentGroup,vimCommentString
      \ extend

syn match	vim9CommentError	contained	"#.*"
syn match	vimCommentError	contained	+".*+

" Environment Variables: {{{2
" =====================
syn match	vimEnvvar	"\$\I\i*"
syn match	vimEnvvar	"\${\I\i*}"

" Strings {{{2
" =======

" In-String Specials:
" Try to catch strings, if nothing else matches (therefore it must precede the others!)
"  vimEscapeBrace handles ["]  []"] (ie. "s don't terminate string inside [])
" syn region	vimEscapeBrace	oneline   contained transparent start="[^\\]\(\\\\\)*\[\zs\^\=\]\=" skip="\\\\\|\\\]" end="]"me=e-1
syn match	vimPatSepErr	contained	"\\)"
syn match	vimPatSep	contained	"\\|"
syn region	vimPatSepZone	oneline   contained   matchgroup=vimPatSepZ start="\\%\=\ze(" skip="\\\\" end="\\)\|[^\\]['"]"	contains=@vimStringGroup
syn region	vimPatRegion	contained transparent matchgroup=vimPatSepR start="\\[z%]\=(" end="\\)"	contains=@vimSubstList oneline
syn match	vimNotPatSep	contained	"\\\\"
syn cluster	vimStringGroup	contains=vimEscape,vimEscapeBrace,vimPatSep,vimNotPatSep,vimPatSepErr,vimPatSepZone,@Spell
syn region	vimString	oneline keepend	matchgroup=vimString start=+[^a-zA-Z>\\@]"+lc=1 skip=+\\\\\|\\"+ matchgroup=vimStringEnd end=+"+ nextgroup=vimSubscript contains=@vimStringGroup extend
syn region	vimString	oneline	matchgroup=vimString start=+[^a-zA-Z>\\@]'+lc=1 end=+'+		       nextgroup=vimSubscript contains=vimQuoteEscape  extend
"syn region	vimString	oneline	start="\s/\s*\A"lc=1 skip="\\\\\|\\+" end="/"	contains=@vimStringGroup  " see tst45.vim

syn match	vimEscape	contained	"\\."
" syn match	vimEscape	contained	+\\[befnrt\"]+
syn match	vimEscape	contained	"\\\o\{1,3}\|\\[xX]\x\{1,2}\|\\u\x\{1,4}\|\\U\x\{1,8}"
syn match	vimEscape	contained	"\\<" contains=vimNotation
syn match	vimEscape	contained	"\\<\*[^>]*>\=>"
syn match	vimQuoteEscape	contained	"''"

syn region	vimString	oneline matchgroup=vimString start=+$'+ end=+'+ nextgroup=vimSubscript contains=@vimStringInterpolation,vimQuoteEscape  extend
syn region	vimString	oneline matchgroup=vimString start=+$"+ end=+"+ nextgroup=vimSubscript contains=@vimStringInterpolation,@vimStringGroup extend
syn region	vimStringInterpolationExpr  oneline contained matchgroup=vimSep start=+{+ end=+}+ contains=@vimExprList
syn match	vimStringInterpolationBrace contained "{{"
syn match	vimStringInterpolationBrace contained "}}"
syn cluster	vimStringInterpolation contains=vimStringInterpolationExpr,vimStringInterpolationBrace

syn region	vimContinueString	contained	matchgroup=vimContinueString start=+"+  skip=+\n\s*\%(\\\|["#]\\ \)+ end=+"+ end="$" skipwhite nextgroup=vimSubscript,vimComment contains=@vimContinue,@vimStringGroup
syn region	vimContinueString	contained	matchgroup=vimContinueString start=+'+  skip=+\n\s*\%(\\\|["#]\\ \)+ end=+'+ end="$" skipwhite nextgroup=vimSubscript,vimComment contains=@vimContinue,vimQuoteEscape
syn region	vimContinueString	contained	matchgroup=vimContinueString start=+$"+ skip=+\n\s*\%(\\\|["#]\\ \)+ end=+"+ end="$" skipwhite nextgroup=vimSubscript,vimComment contains=@vimContinue,@vimStringInterpolation,@vimStringGroup
syn region	vimContinueString	contained	matchgroup=vimContinueString start=+$'+ skip=+\n\s*\%(\\\|["#]\\ \)+ end=+'+ end="$" skipwhite nextgroup=vimSubscript,vimComment contains=@vimContinue,@vimStringInterpolation,vimQuoteEscape

" Substitutions: {{{2
" =============
syn cluster	vimSubstList	contains=vimPatSep,vimPatRegion,vimPatSepErr,vimSubstTwoBS,vimSubstRange,vimNotation
syn cluster	vimSubstRepList	contains=vimSubstSubstr,vimSubstTwoBS,vimNotation
syn cluster	vimSubstList	add=vimCollection
syn match	vimSubst	"^\s*\%(s\%[ubstitute]\|sm\%[agic]\|sno\%[magic]\)\>"			skipwhite nextgroup=vimSubstPat
syn match	vimSubst	"^\s*\%(s\%[ubstitute]\|sm\%[agic]\|sno\%[magic]\)[_#]\@="		skipwhite nextgroup=vimSubstPat
syn match	vimSubst1	contained	"\%(s\%[ubstitute]\|sm\%[agic]\>\|sno\%[magic]\)\>"		skipwhite nextgroup=vimSubstPat
syn match	vimSubst1	contained	"\%(s\%[ubstitute]\|sm\%[agic]\>\|sno\%[magic]\)[_#]\@="	skipwhite nextgroup=vimSubstPat
" TODO: Vim9 illegal separators for abbreviated :s form are [-.:], :su\%[...] required
"     : # is allowed but "not recommended" (see :h pattern-delimiter)
syn region	vimSubstPat	contained	matchgroup=vimSubstDelim start="\z([!#$%&'()*+,-./:;<=>?@[\]^_`{}~]\)"rs=s+1 skip="\\\\\|\\\z1" end="\z1"re=e-1,me=e-1	contains=@vimSubstList	nextgroup=vimSubstRep4	oneline
syn region	vimSubstRep4	contained	matchgroup=vimSubstDelim start="\z(.\)" skip="\\\\\|\\\z1" end="\z1" matchgroup=vimNotation end="<[cC][rR]>"	contains=@vimSubstRepList	nextgroup=vimSubstFlagErr	oneline
syn region	vimCollection	contained 	transparent	start="\\\@<!\[" skip="\\\[" end="\]"	contains=vimCollClass
syn match	vimCollClassErr	contained	"\[:.\{-\}:\]"
syn match	vimCollClass	contained 	transparent	"\%#=1\[:\(alnum\|alpha\|blank\|cntrl\|digit\|graph\|lower\|print\|punct\|space\|upper\|xdigit\|retu\%[rn]\|tab\|escape\|backspace\):\]"
syn match	vimSubstSubstr	contained	"\\z\=\d"
syn match	vimSubstTwoBS	contained	"\\\\"
syn match	vimSubstFlagErr	contained	"[^< \t\r|]\+" contains=vimSubstFlags
syn match	vimSubstFlags	contained	"[&cegiIlnpr#]\+"

" Vi compatibility
syn match	vimSubstDelim	contained	"\\"
syn match	vimSubstPat	contained	"\\\ze[/?&]" contains=vimSubstDelim nextgroup=vimSubstRep4

" Marks, Registers, Addresses, Filters: {{{2
syn match	vimMark	"'[a-zA-Z0-9]\ze[-+,!]"	nextgroup=vimFilter,vimMarkNumber,vimSubst1
syn match	vimMark	"'[[\]{}()<>]\ze[-+,!]"	nextgroup=vimFilter,vimMarkNumber,vimSubst1
syn match	vimMark	",\zs'[[\]{}()<>]\ze"	nextgroup=vimFilter,vimMarkNumber,vimSubst1
syn match	vimMark	"[!,:]\zs'[a-zA-Z0-9]"	nextgroup=vimFilter,vimMarkNumber,vimSubst1
syn match	vimMark	"\<norm\%[al]\s\zs'[a-zA-Z0-9]"	nextgroup=vimFilter,vimMarkNumber,vimSubst1
syn match	vimMarkNumber	"[-+]\d\+"		contained contains=vimOper nextgroup=vimSubst1
syn match	vimPlainMark contained	"'[a-zA-Z0-9]"
syn match	vimRange	"[`'][a-zA-Z0-9],[`'][a-zA-Z0-9]"	contains=vimMark	skipwhite nextgroup=vimFilter

syn match	vimRegister	'[^,;[{: \t]\zs"[a-zA-Z0-9.%#:_\-/]\ze[^a-zA-Z_":0-9]'
syn match	vimRegister	'\<norm\s\+\zs"[a-zA-Z0-9]'
syn match	vimRegister	'\<normal\s\+\zs"[a-zA-Z0-9]'
syn match	vimRegister	'@"'
syn match	vimPlainRegister contained	'"[a-zA-Z0-9\-:.%#*+=]'
syn match	vimLetRegister	contained	'@["0-9\-a-zA-Z:.%#=*+~_/]'

syn match	vimAddress	",\zs[.$]"	skipwhite nextgroup=vimSubst1
syn match	vimAddress	"%\ze\a"	skipwhite nextgroup=vimString,vimSubst1

syn match	vimFilter 		"^!!\=[^"]\{-}\(|\|\ze\"\|$\)"	contains=vimOper,vimSpecFile
syn match	vimFilter    contained	"!!\=[^"]\{-}\(|\|\ze\"\|$\)"	contains=vimOper,vimSpecFile
syn match	vimComFilter contained	"|!!\=[^"]\{-}\(|\|\ze\"\|$\)"      contains=vimOper,vimSpecFile

" Complex Repeats: (:h complex-repeat) {{{2
" ===============
syn match	vimCmplxRepeat	'[^a-zA-Z_/\\()]q[0-9a-zA-Z"]\>'lc=1
syn match	vimCmplxRepeat	'@[0-9a-z".=@:]\ze\($\|[^a-zA-Z]\>\)'

" Set command and associated set-options (vimOptions) with comment {{{2
syn match	vimSet		"\<\%(setl\%[ocal]\|setg\%[lobal]\|se\%[t]\)\>" skipwhite nextgroup=vimSetBang,vimSetArgs
syn region	vimSetComment	contained	start=+"+ skip=+\n\s*\%(\\\||"\\ \)+ end="$" contains=@vimCommentGroup,vimCommentString extend
syn match	vimSetCmdSep	contained	"|" skipwhite nextgroup=@vimCmdList,vimSubst1,vimFunc
syn match	vimSetEscape	contained	"\\\%(\\[|"]\|.\)"
syn match	vimSetBarEscape	contained	"\\|"
syn match	vimSetQuoteEscape	contained	+\\"+
syn region	vimSetArgs	contained
      \ start="\S"
      \ skip=+\n\s*\%(\\\|["#]\\ \)\|^\s*"\\ +
      \ end=+\ze\\\@1<![|"]+
      "\ assume this isn't an escaped char with backslash on the previous line
      \ end=+^\s*\\\ze[|"]+
      \ end="\ze\s#"
      \ end="$"
      \ nextgroup=vimSetCmdSep,vimSetComment,vim9Comment
      \ contains=@vimContinue,vimErrSetting,vimOption,vimSetAll,vimSetTermcap
      \ keepend
syn region	vimSetEqual	contained
      \ matchgroup=vimOper
      \ start="[=:]\|[-+^]="
      \ skip=+\\\s\|^\s*\%(\\\|["#]\\ \)+
      \ end="\ze\s"
      \ contains=@vimContinue,vimCtrlChar,vimEnvvar,vimNotation,vimSetSep,vimSetEscape,vimSetBarEscape,vimSetQuoteEscape
syn match	vimSetBang	contained	"\a\@1<=!" skipwhite nextgroup=vimSetAll,vimSetTermcap
syn keyword	vimSetAll	contained	all nextgroup=vimSetMod
syn keyword	vimSetTermcap	contained	termcap
syn match	vimSetSep	contained	"[,:]"
syn match	vimSetMod	contained	"\a\@1<=\%(&vim\=\|[!&?<]\)"

" Variable Declarations: {{{2
" =====================
VimL syn keyword	vimLet	let		skipwhite nextgroup=@vimSpecialVar,vimVar,vimVarList
VimL syn keyword	vimConst	cons[t]		skipwhite nextgroup=@vimSpecialVar,vimVar,vimVarList
syn region	vimVarList	contained
      \ start="\[" end="]"
      \ contains=@vimContinue,@vimSpecialVar,vimVar

VimL syn keyword	vimUnlet		unl[et]	skipwhite nextgroup=vimUnletBang,vimUnletVars
syn match	vimUnletBang	contained	"\a\@1<=!"	skipwhite nextgroup=vimUnletVars
syn region	vimUnletVars	contained
      \ start="$\I\|\h" skip=+\n\s*\\\|\n\s*"\\ \|^\s*"\\ + end="$" end="\ze[|"]"
      \ nextgroup=vimCmdSep,vimComment
      \ contains=@vimContinue,vimEnvvar,vimVar,vimVimVar

VimFoldh syn region vimLetHereDoc	matchgroup=vimLetHereDocStart start='\%(^\z(\s*\)\S.*\)\@<==<<\s*trim\%(\s\+\)\@>\z(\L\S*\)'	matchgroup=vimLetHereDocStop end='^\z1\=\z2$' extend
VimFoldh syn region vimLetHereDoc	matchgroup=vimLetHereDocStart start='=<<\%(\s*\)\@>\z(\L\S*\)'			matchgroup=vimLetHereDocStop end='^\z1$' extend
VimFoldh syn region vimLetHereDoc	matchgroup=vimLetHereDocStart start='\%(^\z(\s*\)\S.*\)\@<==<<\s*\%(trim\s\+eval\|eval\s\+trim\)\%(\s\+\)\@>\z(\L\S*\)'	matchgroup=vimLetHereDocStop end='^\z1\=\z2$' contains=@vimStringInterpolation extend
VimFoldh syn region vimLetHereDoc	matchgroup=vimLetHereDocStart start='=<<\s*eval\%(\s\+\)\@>\z(\L\S*\)'			matchgroup=vimLetHereDocStop end='^\z1$' contains=@vimStringInterpolation extend

Vim9 syn keyword	vim9Const	const	skipwhite nextgroup=vim9Variable,vim9VariableList
Vim9 syn keyword	vim9Final	final	skipwhite nextgroup=vim9Variable,vim9VariableList
Vim9 syn keyword	vim9Var	var	skipwhite nextgroup=vim9Variable,vim9VariableList

syn match	vim9Variable	contained	"\<\h\w*\>"	skipwhite nextgroup=vimTypeSep,vimLetHereDoc,vimOper
syn region	vim9VariableList	contained	start="\[" end="]" contains=@vimContinue,@vimSpecialVar,vim9Variable

" Lockvar and Unlockvar: {{{2
" =====================
syn keyword	vimLockvar	lockv[ar]	skipwhite nextgroup=vimLockvarBang,vimLockvarDepth,vimLockvarVars
syn keyword	vimUnlockvar	unlo[ckvar]	skipwhite nextgroup=vimLockvarBang,vimLockvarDepth,vimLockvarVars
syn match	vimLockvarBang	contained	"\a\@1<=!"	skipwhite nextgroup=vimLockvarVars
syn match	vimLockvarDepth	contained	"\<[0-3]\>"	skipwhite nextgroup=vimLockvarVars
syn region	vimLockvarVars	contained
      \ start="\h" skip=+\n\s*\\\|\n\s*"\\ \|^\s*"\\ + end="$" end="\ze[|"]"
      \ nextgroup=vimCmdSep,vimComment
      \ contains=@vimContinue,vimVar

hi def link vimLockvar vimCommand
hi def link vimUnlockvar vimCommand
hi def link vimLockvarBang vimBang
hi def link vimLockvarDepth vimNumber

" For: {{{2
" ===
" handles Vim9 and legacy for now
syn region	vimFor
      \ matchgroup=vimCommand
      \ start="\<for\>" end="\<in\>"
      \ skipwhite skipnl nextgroup=@vimForInContinue,vim9ForInComment,@vimExprList
      \ contains=@vimContinue,vimVar,vimVarList,vim9VariableList
      \ transparent

syn match	vim9ForInComment		contained	"#.*"	skipwhite skipempty nextgroup=vimForInComment,@vimExprList

syn match	vimForInContinue		contained	"^\s*\zs\\"	 skipwhite skipnl nextgroup=@vimForInContinue,@vimExprList
syn match         vimForInContinueComment	contained	'^\s*\zs["#]\\ .*' skipwhite skipnl nextgroup=@vimForInContinue,@vimExprList
syn cluster	vimForInContinue		contains=vimForInContinue,vimForInContinueComment

" Abbreviations: {{{2
" =============
" GEN_SYN_VIM: vimCommand abbrev, START_STR='syn keyword vimAbb', END_STR='skipwhite nextgroup=vimMapMod,vimMapLhs'
syn keyword vimAbb ab[breviate] ca[bbrev] cnorea[bbrev] cuna[bbrev] ia[bbrev] inorea[bbrev] iuna[bbrev] norea[bbrev] una[bbreviate] skipwhite nextgroup=vimMapMod,vimMapLhs
" GEN_SYN_VIM: vimCommand abclear, START_STR='syn keyword vimAbb', END_STR='skipwhite nextgroup=vimMapMod'
syn keyword vimAbb abc[lear] cabc[lear] iabc[lear] skipwhite nextgroup=vimMapMod

" Filename Patterns: {{{2
" =================

syn match	vimWildcardQuestion	contained	"?"
syn match	vimWildcardStar		contained	"*"

syn match	vimWildcardBraceComma	contained	","
syn region	vimWildcardBrace		contained
      \ matchgroup=vimWildcard
      \ start="{"
      \ end="}"
      \ contains=vimWildcardEscape,vimWildcardBrace,vimWildcardBraceComma,vimWildcardQuestion,vimWildcardStar,vimWildcardBracket
      \ oneline

syn match	vimWildcardIntervalNumber	contained	"\d\+"
syn match	vimWildcardInterval	contained	"\\\\\\{\d\+\%(,\d\+\)\=\\}" contains=vimWildcardIntervalNumber


syn match	vimWildcardBracket	contained	"\[\%(\^\=]\=\%(\\.\|\[\([:.=]\)[^:.=]\+\1]\|[^][:space:]]\)*\)\@>]"
      \ contains=vimWildcardBracketStart,vimWildcardEscape

syn match	vimWildcardBracketCharacter	contained	"."	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketHyphen,vimWildcardBracketEnd
syn match	vimWildcardBracketRightBracket	contained	"]"	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketEnd
syn match	vimWildcardBracketHyphen	contained	"-]\@!"	nextgroup=@vimWildcardBracketCharacter
syn match	vimWildcardBracketEscape	contained	"\\."	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketHyphen,vimWildcardBracketEnd
syn match	vimWildcardBracketCharacterClass	contained	"\[:[^:]\+:]"	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketEnd
syn match	vimWildcardBracketEquivalenceClass	contained	"\[=[^=]\+=]"	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketEnd
syn match	vimWildcardBracketCollatingSymbol	contained	"\[\.[^.]\+\.]"	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketEnd

syn match	vimWildcardBracketStart	contained	"\["	nextgroup=vimWildcardBracketCaret,vimWildcardBracketRightBracket,@vimWildcardBracketCharacter
syn match	vimWildcardBracketCaret	contained	"\^"	nextgroup=@vimWildcardBracketCharacter,vimWildcardBracketRightBracket 
syn match	vimWildcardBracketEnd	contained	"]"

syn cluster	vimWildcardBracketCharacter	contains=vimWildcardBracketCharacter,vimWildcardBracketEscape,vimWildcardBracketCharacterClass,vimWildcardBracketEquivalenceClass,vimWildcardBracketCollatingSymbol

syn match	vimWildcardEscape	contained	"\\."

syn cluster	vimWildcard		contains=vimWildcardQuestion,vimWildcardStar,vimWildcardBrace,vimWildcardBracket,vimWildcardInterval

" Autocmd and Doauto{cmd,all}: {{{2
" ===========================

" TODO: explicitly match the {cmd} arg rather than bailing out to TOP
syn region	vimAutocmdBlock	contained	matchgroup=vimSep start="{" end="^\s*\zs}" contains=@vimDefBodyList

syn match	vimAutocmdGroup	contained	"\%(\\["|[:space:]]\|[^"|[:space:]]\)\+" skipwhite nextgroup=vimAutoEvent,vimAutoEventGlob
syn match	vimAutocmdBang	contained	"\a\@1<=!"		     skipwhite nextgroup=vimAutocmdGroup,vimAutoEvent,vimAutoEventGlob

" TODO: cleaner handling of | in pattern position
"     : match pattern items in addition to wildcards
syn region	vimAutocmdPattern	contained
      \ start="|\@!\S"
      \ skip="\\\\\|\\[,[:space:]]"
      \ end="\ze[,[:space:]]"
      \ end="$"
      \ skipwhite nextgroup=vimAutocmdPatternSep,vimAutocmdMod,vimAutocmdBlock
      \ contains=vimEnvvar,@vimWildcard,vimAutocmdPatternEscape
syn match	vimAutocmdBufferPattern	contained	"<buffer\%(=\%(\d\+\|abuf\)\)\=>" skipwhite nextgroup=vimAutocmdPatternSep,vimAutocmdMod,vimAutocmdBlock
" trailing pattern separator comma allowed
syn match	vimAutocmdPatternSep	contained	","	                skipwhite nextgroup=@vimAutocmdPattern,vimAutocmdMod,vimAutocmdBlock
syn match	vimAutocmdPatternEscape	contained	"\\."
syn cluster	vimAutocmdPattern		contains=vimAutocmdPattern,vimAutocmdBufferPattern

" TODO: Vim9 requires '++' prefix
syn match	vimAutocmdMod	contained	"\%(++\)\=\<nested\>" skipwhite nextgroup=vimAutocmdMod,vimAutocmdBlock
syn match	vimAutocmdMod	contained	"++once\>"	skipwhite nextgroup=vimAutocmdMod,vimAutocmdBlock

" higher priority than vimAutocmdGroup, assume no group is so named
syn match	vimAutoEventGlob	contained	"*"	skipwhite nextgroup=@vimAutocmdPattern
syn match	vimAutoEventSep	contained	"\a\@1<=,"	nextgroup=vimAutoEvent

syn match	vimAutocmd		"\<au\%[tocmd]\>"	skipwhite nextgroup=vimAutocmdBang,vimAutocmdGroup,vimAutoEvent,vimAutoEventGlob


syn match	vimDoautocmdMod	contained	"<nomodeline>"		skipwhite nextgroup=vimAutocmdGroup,vimAutoEvent
syn match	vimDoautocmd		"\<do\%[autocmd]\>"	skipwhite nextgroup=vimDoautocmdMod,vimAutocmdGroup,vimAutoEvent
syn match	vimDoautocmd		"\<doautoa\%[ll]\>"	skipwhite nextgroup=vimDoautocmdMod,vimAutocmdGroup,vimAutoEvent

" Echo And Execute: -- prefer strings! {{{2
" ================
" NOTE: No trailing comments

syn region	vimEcho
      \ matchgroup=vimCommand
      \ start="\<ec\%[ho]\>"
      \ start="\<echoe\%[rr]\>"
      \ start="\<echom\%[sg]\>"
      \ start="\<echoc\%[onsole]\>"
      \ start="\<echon\>"
      \ start="\<echow\%[indow]\>"
      \ skip=+\\|\|\n\s*\\\|\n\s*"\\ +
      \ matchgroup=vimCmdSep end="|" excludenl end="$" contains=@vimContinue,@vimExprList transparent

syn match	vimEchohl	"\<echohl\=\>"	skipwhite nextgroup=vimGroup,vimHLGroup,vimEchohlNone
syn case ignore
syn keyword	vimEchohlNone	contained none
syn case match

syn cluster	vimEcho	contains=vimEcho,vimEchohl

syn region	vimExecute	matchgroup=vimCommand start="\<exe\%[cute]\>" skip=+\\|\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|" excludenl end="$" contains=@vimContinue,@vimExprList transparent

" Filter: {{{2
" ======
syn match	vimExFilter		"\<filt\%[er]\>"	skipwhite nextgroup=vimExFilterBang,vimExFilterPattern
syn match	vimExFilterBang	 contained	"\a\@1<=!"	skipwhite nextgroup=vimExFilterPattern
syn region	vimExFilterPattern contained
      \ start="[[:ident:]]"	
      \ end="\ze[[:space:]\n]"
      \ skipwhite nextgroup=@vimCmdList
      \ contains=@vimSubstList
      \ oneline
syn region	vimExFilterPattern contained
      \ matchgroup=Delimiter
      \ start="\z([^[:space:][:ident:]|"]\)"
      \ skip="\\\\\|\\\z1"
      \ end="\z1"
      \ skipwhite nextgroup=@vimCmdList
      \ contains=@vimSubstList
      \ oneline

" Grep and Make: {{{2
" =============
" | is the command separator, escaped with \| all other backslashes are passed through literally, no tail comments
syn match	vimGrep		"\<l\=gr\%[ep]\>"		skipwhite nextgroup=vimGrepBang,vimGrepArgs,vimCmdSep
syn match	vimGrepadd		"\<l\=grepa\%[dd]\>"	skipwhite nextgroup=vimGrepBang,vimGrepArgs,vimCmdSep
syn region	vimGrepArgs	contained
      \ start="|\@!\S"
      \ skip=+\n\s*\%(\\\|[#"]\\ \)+
      \ matchgroup=vimCmdSep
      \ end="|"
      \ end="$"
      "\ TODO: include vimSpecFile
      \ contains=vimGrepBarEscape
syn match	vimGrepBarEscape	contained	"\\|"
syn match	vimGrepBang	contained	"\a\@1<=!"		skipwhite nextgroup=vimGrepArgs,vimCmdSep

syn match	vimMake		"\<l\=make\=\>"		skipwhite nextgroup=vimMakeBang,vimMakeArgs,vimCmdSep
syn region	vimMakeArgs	contained
      \ start="|\@!\S"
      \ skip=+\n\s*\%(\\\|[#"]\\ \)+
      \ matchgroup=vimCmdSep
      \ end="|"
      \ end="$"
      "\ TODO: include vimSpecFile
      \ contains=vimMakeBarEscape
syn match	vimMakeBarEscape	contained	"\\|"
syn match	vimMakeBang	contained	"\a\@1<=!"		skipwhite nextgroup=vimMakeArgs,vimCmdSep

syn match	vimHelpgrep		"\<l\=helpg\%[rep]\>"	skipwhite nextgroup=vimHelpgrepBang,vimHelpgrepPattern
syn region	vimHelpgrepPattern	contained
      \ start="\S"
      \ matchgroup=Special
      \ end="@\w\w\>"
      \ end="$"
      \ contains=@vimSubstList
      \ oneline

" Vimgrep: {{{2
" =======
syn match	vimVimgrep		"\<l\=vim\%[grep]\>"	skipwhite nextgroup=vimVimgrepBang,vimVimgrepPattern
syn match	vimVimgrepadd		"\<l\=vimgrepa\%[dd]\>"	skipwhite nextgroup=vimVimgrepBang,vimVimgrepPattern
syn match	vimVimgrepBang	  contained	"\a\@1<=!"		skipwhite nextgroup=vimVimgrepPattern
syn region	vimVimgrepPattern   contained
      \ start="[[:ident:]]"			    
      \ end="\ze[[:space:]\n]"
      \ skipwhite nextgroup=vimVimgrepFile,vimCmdSep
      \ contains=@vimSubstList
      \ oneline
syn region	vimVimgrepPattern   contained
      \ matchgroup=Delimiter
      \ start="\z([^[:space:][:ident:]|"]\)"
      \ skip="\\\\\|\\\z1"
      \ end="\z1"
      \ skipwhite nextgroup=vimVimgrepFlags,vimVimgrepFile,vimCmdSep
      \ contains=@vimSubstList
      \ oneline
syn match	vimVimgrepEscape	  contained	"\\\%(\\|\|.\)"
syn match	vimVimgrepBarEscape contained	"\\|"
syn region	vimVimgrepFile	  contained
      \ start="|\@!\S"
      \ matchgroup=vimCmdSep
      \ end="|"
      \ end="\ze\s"
      \ end="$"
      \ skipwhite nextgroup=vimVimgrepFile
      \ contains=vimSpecFile,vimVimgrepEscape,vimVimgrepBarEscape
syn match	vimVimgrepFlags	  contained	"\<[gjf]\{,3\}\>" skipwhite nextgroup=vimVimgrepfile

" Maps: {{{2
" ====
" GEN_SYN_VIM: vimCommand map, START_STR='syn keyword vimMap', END_STR='skipwhite nextgroup=vimMapMod,vimMapLhs'
syn keyword vimMap cm[ap] cno[remap] im[ap] ino[remap] lm[ap] ln[oremap] nm[ap] nn[oremap] om[ap] ono[remap] smap snor[emap] tma[p] tno[remap] vm[ap] vn[oremap] xm[ap] xn[oremap] skipwhite nextgroup=vimMapMod,vimMapLhs
syn match	vimMap	"\<map\>"	skipwhite nextgroup=vimMapBang,vimMapMod,vimMapLhs
syn keyword	vimMap	no[remap]	skipwhite nextgroup=vimMapBang,vimMapMod,vimMapLhs
" GEN_SYN_VIM: vimCommand mapclear, START_STR='syn keyword vimMap', END_STR='skipwhite nextgroup=vimMapMod'
syn keyword vimMap cmapc[lear] imapc[lear] lmapc[lear] nmapc[lear] omapc[lear] smapc[lear] tmapc[lear] vmapc[lear] xmapc[lear] skipwhite nextgroup=vimMapMod
syn keyword	vimMap	mapc[lear]	skipwhite nextgroup=vimMapBang,vimMapMod
" GEN_SYN_VIM: vimCommand unmap, START_STR='syn keyword vimUnmap', END_STR='skipwhite nextgroup=vimMapMod,vimMapLhs'
syn keyword vimUnmap cu[nmap] iu[nmap] lu[nmap] nun[map] ou[nmap] sunm[ap] tunma[p] vu[nmap] xu[nmap] skipwhite nextgroup=vimMapMod,vimMapLhs
syn keyword	vimUnmap	unm[ap]	skipwhite nextgroup=vimMapBang,vimMapMod,vimMapLhs

syn match	vimMapLhs	contained	"\%(.\|\S\)\+"		contains=vimCtrlChar,vimNotation,vimMapLeader skipwhite        nextgroup=vimMapRhs
syn match	vimMapLhs	contained	"\%(.\|\S\)\+\ze\s*$"	contains=vimCtrlChar,vimNotation,vimMapLeader skipwhite skipnl nextgroup=vimMapRhsContinue
syn match	vimMapBang	contained	"\a\@1<=!"		skipwhite nextgroup=vimMapMod,vimMapLhs
syn match	vimMapMod	contained	"\%#=1<\%(buffer\|expr\|nowait\|script\|silent\|special\|unique\)\+>" contains=vimMapModKey,vimMapModErr skipwhite nextgroup=vimMapMod,vimMapLhs
syn region	vimMapRhs	contained	start="\S" 	        skip=+\\|\|\@1<=|\|\n\s*\\\|\n\s*"\\ + end="|" end="$" contains=@vimContinue,vimCtrlChar,vimNotation,vimMapLeader skipnl nextgroup=vimMapRhsContinue
" assume a continuation comment introduces the RHS
syn region	vimMapRhsContinue	contained	start=+^\s*\%(\\\|"\\ \)+ skip=+\\|\|\@1<=|\|\n\s*\\\|\n\s*"\\ + end="|" end="$" contains=@vimContinue,vimCtrlChar,vimNotation,vimMapLeader
syn match	vimMapLeader	contained	"\%#=1\c<\%(local\)\=leader>"	contains=vimMapLeaderKey
syn keyword	vimMapModKey	contained	buffer expr nowait script silent special unique
syn case ignore
syn keyword	vimMapLeaderKey	contained	leader localleader
syn case match

" Menus: {{{2
" =====
" NOTE: tail comments disallowed
" GEN_SYN_VIM: vimCommand menu, START_STR='syn keyword vimMenu', END_STR='skipwhite nextgroup=vimMenuBang,vimMenuMod,vimMenuName,vimMenuPriority,vimMenuStatus'
syn keyword vimMenu am[enu] an[oremenu] aun[menu] cme[nu] cnoreme[nu] cunme[nu] ime[nu] inoreme[nu] iunme[nu] me[nu] nme[nu] nnoreme[nu] noreme[nu] nunme[nu] ome[nu] onoreme[nu] ounme[nu] sme[nu] snoreme[nu] sunme[nu] tlm[enu] tln[oremenu] tlu[nmenu] tm[enu] tu[nmenu] unme[nu] vme[nu] vnoreme[nu] vunme[nu] xme[nu] xnoreme[nu] xunme[nu] skipwhite nextgroup=vimMenuBang,vimMenuMod,vimMenuName,vimMenuPriority,vimMenuStatus
syn keyword vimMenu popu[p] skipwhite nextgroup=vimMenuBang,vimMenuName
syn region	vimMenuRhs	 contained contains=@vimContinue,vimNotation start="|\@!\S"            skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + end="$" matchgroup=vimSep end="|"
syn region	vimMenuRhsContinue contained contains=@vimContinue,vimNotation start=+^\s*\%(\\\|"\\ \)+ skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + end="$" matchgroup=vimSep end="|"
syn match	vimMenuName	"\.\@!\%(\\\s\|\S\)\+"        contained contains=vimMenuNotation,vimNotation skipwhite        nextgroup=vimCmdSep,vimMenuRhs
syn match	vimMenuName	"\.\@!\%(\\\s\|\S\)\+\ze\s*$" contained contains=vimMenuNotation,vimNotation skipwhite skipnl nextgroup=vimCmdSep,vimMenuRhsContinue
syn match	vimMenuNotation	"&\a\|&&\|\\\s\|\\\." contained
syn match	vimMenuPriority	"\<\d\+\%(\.\d\+\)*\>" contained skipwhite nextgroup=vimMenuName
syn match	vimMenuMod	"\c<\%(script\|silent\|special\)>" contained skipwhite nextgroup=vimMenuName,vimMenuPriority,vimMenuMod contains=vimMapModKey,vimMapModErr
syn keyword	vimMenuStatus	enable disable nextgroup=vimMenuName skipwhite
syn match	vimMenuBang	"\a\@1<=!" contained skipwhite nextgroup=vimMenuName,vimMenuMod

syn region	vimMenutranslate
      \ matchgroup=vimCommand start="\<menut\%[ranslate]\>"
      \ skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ +
      \ end="$" matchgroup=vimCmdSep end="|" matchgroup=vimMenuClear end="\<clear\ze\s*\%(["#|]\|$\)"
      \ contains=@vimContinue,vimMenutranslateName keepend transparent
" oneline is sufficient to match the current formatting in runtime/lang/*.vim
syn match	vimMenutranslateName "\%(\\\s\|\S\)\+" contained contains=vimMenuNotation,vimNotation
syn match	vimMenutranslateComment +".*+ contained containedin=vimMenutranslate

" Angle-Bracket Notation: (tnx to Michael Geddes) {{{2
" ======================
syn case ignore
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd]-\)\{0,4}x\=\%(f\d\{1,2}\|[^ \t:]\|space\|bar\|bslash\|nl\|newline\|lf\|linefeed\|cr\|retu\%[rn]\|enter\|k\=del\%[ete]\|bs\|backspace\|tab\|esc\|csi\|right\|paste\%(start\|end\)\|left\|help\|undo\|k\=insert\|ins\|mouse\|[kz]\=home\|[kz]\=end\|kplus\|kminus\|kdivide\|kmultiply\|kenter\|kpoint\|space\|k\=\%(page\)\=\%(\|down\|up\|k\d\>\)\)>" contains=vimBracket

syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}\%(net\|dec\|jsb\|pterm\|urxvt\|sgr\)mouse>"		contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}\%(left\|middle\|right\)\%(mouse\|drag\|release\)>"	contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}left\%(mouse\|release\)nm>"			contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}x[12]\%(mouse\|drag\|release\)>"		contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}sgrmouserelease>"			contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}mouse\%(up\|down\|move\)>"			contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd2-4]-\)\{0,4}scrollwheel\%(up\|down\|right\|left\)>"		contains=vimBracket

syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%(sid\|nop\|nul\|lt\|drop\)>"				contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%(snr\|plug\|cursorhold\|ignore\|cmd\|scriptcmd\|focus\%(gained\|lost\)\)>"	contains=vimBracket
" syn match	vimNotation	contained	'\%(\\\|<lt>\)\=<C-R>[0-9a-z"%#:.\-=]'he=e-1				contains=vimBracket
syn match	vimNotation	contained	'\%#=1\%(\\\|<lt>\)\=<\%(q-\)\=\%(line[12]\|count\|bang\|reg\|args\|mods\|f-args\|f-mods\|lt\)>'	contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([cas]file\|abuf\|amatch\|cexpr\|cword\|cWORD\|client\|stack\|script\|sf\=lnum\)>"	contains=vimBracket
syn match	vimNotation	contained	"\%#=1\%(\\\|<lt>\)\=<\%([scamd]-\)\{0,4}char-\%(\d\+\|0\o\+\|0x\x\+\)>"		contains=vimBracket

syn match	vimBracket contained	"[\\<>]"
syn case match

" User Function Highlighting: {{{2
" (following Gautam Iyer's suggestion)
" ==========================
syn match	vimFunc              	"\%(\%([sSgGbBwWtTlL]:\|<[sS][iI][dD]>\)\=\%(\w\+\.\)*\I[a-zA-Z0-9_.]*\)\ze\s*("                	skipwhite nextgroup=vimOperParen contains=vimFuncEcho,vimFuncName,vimUserFunc,vimExecute
syn match	vimUserFunc	contained        	"\%(\%([sSgGbBwWtTlL]:\|<[sS][iI][dD]>\)\=\%(\w\+\.\)*\I[a-zA-Z0-9_.]*\)\|\<\u[a-zA-Z0-9.]*\>\|\<if\>"	contains=vimNotation,vim9MethodName,vim9Super,vim9This
syn keyword	vimFuncEcho	contained      	ec ech echo

syn match	vimMap	"\<map\%(\s\+(\)\@="	skipwhite nextgroup=vimMapBang,vimMapMod,vimMapLhs

" User Command Highlighting: {{{2
syn match vimUsrCmd	'^\s*\zs\u\%(\w*\)\@>\%([(#[]\|\s\+\%([-+*/%]\=\|\.\.\)=\)\@!'

" Vim user commands

" Compiler plugins
syn match	vimCompilerSet	"\<CompilerSet\>"	skipwhite nextgroup=vimSetArgs

" runtime/makemenu.vim
syn match	vimSynMenu		"\<SynMenu\>"	skipwhite nextgroup=vimSynMenuPath
syn match	vimSynMenuPath	contained	".*\ze:"	nextgroup=vimSynMenuColon contains=vimMenuNotation
syn match	vimSynMenuColon	contained	":"	nextgroup=vimSynMenuName
syn match	vimSynMenuName	contained	"\w\+"

syn cluster vimExUserCmdList contains=vimCompilerSet,vimSynMenu

" Errors And Warnings: {{{2
" ====================
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimfunctionerror")
 syn match	vimFunctionError	"\s\zs[a-z0-9]\i\{-}\ze\s*("			contained contains=vimFuncKey,vimFuncBlank
 syn match	vimFunctionError	"\s\zs\%(<[sS][iI][dD]>\|[sSgGbBwWtTlL]:\)\d\i\{-}\ze\s*("	contained contains=vimFuncKey,vimFuncBlank
 syn match	vimElseIfErr	"\<else\s\+if\>"
 syn match	vimBufnrWarn	/\<bufnr\s*(\s*["']\.['"]\s*)/
endif

" If, While and Return: {{{2
" ====================
syn match	vimNotFunc	"\%#=1\<\%(if\|el\%[seif]\|retu\%[rn]\|while\)\>"	skipwhite nextgroup=@vimExprList,vimNotation
syn match	vimElse	"\<el\%[se]\>"			skipwhite nextgroup=vimComment,vim9Comment
syn match	vimEndif	"\<en\%[dif]\>"			skipwhite nextgroup=vimComment,vim9Comment

" Match: {{{2
" =====
syn match	vimMatch		"\<[23]\=mat\%[ch]\>" skipwhite nextgroup=vimMatchGroup,vimMatchNone
syn match	vimMatchGroup	contained	"[[:alnum:]._-]\+"    skipwhite nextgroup=vimMatchPattern
syn case ignore
syn keyword	vimMatchNone	contained	none
syn case match
syn region	vimMatchPattern	contained	matchgroup=Delimiter start="\z([!#$%&'()*+,-./:;<=>?@[\]^_`{}~]\)" skip="\\\\\|\\\z1" end="\z1" contains=@vimSubstList oneline

" Normal: {{{2
" ======
syn match	vimNormal		"\<norm\%[al]\>!\=" skipwhite nextgroup=vimNormalArg contains=vimBang
syn region	vimNormalArg	contained	start="\S" skip=+\n\s*\\\|\n\s*["#]\\ + end="$" contains=@vimContinue

" Redir: {{{2
" =====
syn match	vimRedir		"\<redir\=\>"	skipwhite nextgroup=vimRedirBang,vimRedirFileOperator,vimRedirVariableOperator,vimRedirRegister,vimRedirEnd
syn match	vimRedirBang	       contained	"\a\@1<=!"	skipwhite nextgroup=vimRedirFileOperator

syn match	vimRedirFileOperator     contained	">>\="	skipwhite nextgroup=vimRedirFile
syn region	vimRedirFile	       contained
      \ start="\S"
      \ matchgroup=Normal
      \ end="\s*$"
      \ end="\s*\ze[|"]"
      \ nextgroup=vimCmdSep,vimComment
      \ contains=vimSpecFile
syn match	vimRedirRegisterOperator contained	">>\="
syn match	vimRedirRegister	       contained	"@[a-zA-Z*+"]"	nextgroup=vimRedirRegisterOperator
syn match	vimRedirVariableOperator contained	"=>>\="	skipwhite nextgroup=vimVar
syn keyword	vimRedirEnd	       contained	END

" Sleep: {{{2
" =====
syn keyword	vimSleep		sl[eep]		skipwhite nextgroup=vimSleepBang,vimSleepArg
syn match	vimSleepBang	contained	"\a\@1<=!"		skipwhite nextgroup=vimSleepArg
syn match	vimSleepArg	contained	"\<\%(\d\+\)\=m\=\>"

" Sort: {{{2
" ====
syn match	vimSort		"\<sort\=\>"			skipwhite nextgroup=vimSortBang,@vimSortOptions,vimSortPattern,vimCmdSep
syn match	vimSortBang	  contained	"\a\@1<=!"			skipwhite nextgroup=@vimSortOptions,vimSortPattern,vimCmdSep
syn match	vimSortOptionsError contained	"\a\+"
syn match	vimSortOptions	  contained	"\<[ilur]*[nfxob]\=[ilur]*\>"		skipwhite nextgroup=vimSortPattern,vimCmdSep
syn region	vimSortPattern	  contained
      \ matchgroup=Delimiter
      \ start="\z([^[:space:][:alpha:]|]\)"
      \ skip="\\\\\|\\\z1"
      \ end="\z1"
      \ skipwhite nextgroup=@vimSortOptions,vimCmdSep
      \ contains=@vimSubstList
      \ oneline

syn cluster vimSortOptions contains=vimSortOptions,vimSortOptionsError

" Syntax: {{{2
"=======
syn match	vimGroupList	contained	"[^[:space:],]\+\%(\s*,\s*[^[:space:],]\+\)*" contains=vimGroupSpecial
syn region	vimGroupList	contained	start=/^\s*["#]\\ \|^\s*\\\|[^[:space:],]\+\s*,/ skip=/\s*\n\s*\\\|\s*\n\s*["#]\\ \|^\s*\\\|^\s*["#]\\ / end=/[^[:space:],]\s*$\|[^[:space:],]\ze\s\+\w/ contains=@vimContinue,vimGroupSpecial
syn keyword	vimGroupSpecial	contained	ALL	ALLBUT	CONTAINED	TOP

if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimsynerror")
 syn match	vimSynError	contained	"\i\+"
 syn match	vimSynError	contained	"\i\+="	nextgroup=vimGroupList
endif
syn match	vimSynContains	contained	"\<contain\%(s\|edin\)="	skipwhite skipnl nextgroup=vimGroupList
syn match	vimSynKeyContainedin	contained	"\<containedin="	skipwhite skipnl nextgroup=vimGroupList
syn match	vimSynNextgroup	contained	"\<nextgroup="		skipwhite skipnl nextgroup=vimGroupList
if has("conceal")
 " no whitespace allowed after '='
 syn match	vimSynCchar	contained	"\<cchar="	nextgroup=vimSynCcharValue
 syn match	vimSynCcharValue	contained	"\S"
endif

syn match	vimSyntax	"\<sy\%[ntax]\>"	contains=vimCommand skipwhite nextgroup=vimSynType,@vimComment
syn cluster vimFuncBodyList add=vimSyntax

" Syntax: case {{{2
syn keyword	vimSynType	contained	case	skipwhite nextgroup=vimSynCase,vimSynCaseError
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimsyncaseerror")
 syn match	vimSynCaseError	contained	"\i\+"
endif
syn keyword	vimSynCase	contained	ignore	match

" Syntax: clear {{{2
syn keyword	vimSynType	contained	clear	skipwhite nextgroup=vimGroupList

" Syntax: cluster {{{2
syn keyword	vimSynType	contained	cluster	skipwhite nextgroup=vimClusterName
syn region	vimClusterName	contained keepend	matchgroup=vimGroupName start="\h\w*\>" skip=+\\\\\|\\\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="$\||" contains=@vimContinue,vimGroupAdd,vimGroupRem,vimSynContains,vimSynError
syn match	vimGroupAdd	contained keepend	"\<add="	skipwhite skipnl nextgroup=vimGroupList
syn match	vimGroupRem	contained keepend	"\<remove="	skipwhite skipnl nextgroup=vimGroupList

" Syntax: conceal {{{2
syn match	vimSynType	contained	"\<conceal\>"	skipwhite nextgroup=vimSynConceal,vimSynConcealError
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimsynconcealerror")
 syn match	vimSynConcealError contained	"\i\+"
endif
syn keyword	vimSynConceal	contained	on	off

" Syntax: foldlevel {{{2
syn keyword	vimSynType	contained	foldlevel	skipwhite nextgroup=vimSynFoldlevel,vimSynFoldlevelError
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimsynfoldlevelerror")
 syn match	vimSynFoldlevelError	contained	"\i\+"
endif
syn keyword	vimSynFoldlevel	contained	start	minimum

" Syntax: iskeyword {{{2
syn keyword	vimSynType		contained	iskeyword	skipwhite nextgroup=vimSynIskeyword
syn keyword	vimSynIskeyword		contained	clear
syn match	vimSynIskeyword		contained	"\S\+"	contains=vimSynIskeywordSep
syn match	vimSynIskeywordSep	contained	","

" Syntax: include {{{2
syn keyword	vimSynType	contained	include	skipwhite nextgroup=vimGroupList

" Syntax: keyword {{{2
syn cluster	vimSynKeyGroup	contains=@vimContinue,vimSynCchar,vimSynNextgroup,vimSynKeyOpt,vimSynKeyContainedin
syn keyword	vimSynType	contained	keyword	skipwhite nextgroup=vimSynKeyRegion
syn region	vimSynKeyRegion	contained         keepend	matchgroup=vimGroupName start="\h\w*\>" skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|\|$" contains=@vimSynKeyGroup
syn match	vimSynKeyOpt	contained	"\%#=1\<\%(conceal\|contained\|transparent\|skipempty\|skipwhite\|skipnl\)\>"

" Syntax: match {{{2
syn cluster	vimSynMtchGroup	contains=@vimContinue,vimSynCchar,vimSynContains,vimSynError,vimSynMtchOpt,vimSynNextgroup,vimSynRegPat,vimNotation,vimMtchComment
syn keyword	vimSynType	contained	match	skipwhite nextgroup=vimSynMatchRegion
syn region	vimSynMatchRegion	contained keepend	matchgroup=vimGroupName start="\h\w*\>" skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|\|$" contains=@vimSynMtchGroup
syn match	vimSynMtchOpt	contained	"\%#=1\<\%(conceal\|transparent\|contained\|excludenl\|keepend\|skipempty\|skipwhite\|display\|extend\|skipnl\|fold\)\>"

" Syntax: off and on {{{2
syn keyword	vimSynType	contained	enable	list	manual	off	on	reset

" Syntax: region {{{2
syn cluster	vimSynRegPatGroup	contains=@vimContinue,vimPatSep,vimNotPatSep,vimSynPatRange,vimSynNotPatRange,vimSubstSubstr,vimPatRegion,vimPatSepErr,vimNotation
syn cluster	vimSynRegGroup	contains=@vimContinue,vimSynCchar,vimSynContains,vimSynNextgroup,vimSynRegOpt,vimSynReg,vimSynMtchGrp
syn keyword	vimSynType	contained	region	skipwhite nextgroup=vimSynRegion
syn region	vimSynRegion	contained keepend	matchgroup=vimGroupName start="\h\w*" skip=+\\\\\|\\\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|\|$" contains=@vimSynRegGroup
syn match	vimSynRegOpt	contained	"\%#=1\<\%(conceal\%(ends\)\=\|transparent\|contained\|excludenl\|skipempty\|skipwhite\|display\|keepend\|oneline\|extend\|skipnl\|fold\)\>"
syn match	vimSynReg	contained	"\<\%(start\|skip\|end\)="	nextgroup=vimSynRegPat
syn match	vimSynMtchGrp	contained	"matchgroup="	nextgroup=vimGroup,vimHLGroup
syn region	vimSynRegPat	contained extend	start="\z([-`~!@#$%^&*_=+;:'",./?]\)"  skip=/\\\\\|\\\z1\|\n\s*\\\|\n\s*"\\ /  end="\z1"  contains=@vimSynRegPatGroup skipwhite nextgroup=vimSynPatMod,vimSynReg
syn match	vimSynPatMod	contained	"\%#=1\%(hs\|ms\|me\|hs\|he\|rs\|re\)=[se]\%([-+]\d\+\)\="
syn match	vimSynPatMod	contained	"\%#=1\%(hs\|ms\|me\|hs\|he\|rs\|re\)=[se]\%([-+]\d\+\)\=," nextgroup=vimSynPatMod
syn match	vimSynPatMod	contained	"lc=\d\+"
syn match	vimSynPatMod	contained	"lc=\d\+," nextgroup=vimSynPatMod
syn region	vimSynPatRange	contained	start="\["	skip="\\\\\|\\]"   end="]"
syn match	vimSynNotPatRange	contained	"\\\\\|\\\["
syn match	vimMtchComment	contained	'"[^"]\+$'

" Syntax: spell {{{2
syn keyword	vimSynType	contained	spell	skipwhite nextgroup=vimSynSpell,vimSynSpellError
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimsynspellerror")
 syn match	vimSynSpellError contained	"\i\+"
endif
syn keyword	vimSynSpell	contained	default	notoplevel	toplevel

" Syntax: sync {{{2
" ============
syn keyword vimSynType	contained	sync	skipwhite	nextgroup=vimSyncClear,vimSyncMatch,vimSyncError,vimSyncRegion,vimSyncArgs
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimsyncerror")
 syn match	vimSyncError	contained	"\i\+"
endif

syn region	vimSyncArgs	contained	start="\S" skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|\|$" contains=vimSyncLines,vimSyncLinebreak,vimSyncLinecont,vimSyncFromstart,vimSyncCcomment

syn keyword	vimSyncCcomment	contained	ccomment	skipwhite	nextgroup=vimGroupName
syn keyword	vimSyncClear	contained	clear	skipwhite	nextgroup=vimSyncGroupName
syn keyword	vimSyncFromstart	contained	fromstart
syn keyword	vimSyncMatch	contained	match	skipwhite	nextgroup=vimSyncGroupName
syn keyword	vimSyncRegion	contained	region	skipwhite	nextgroup=vimSynRegion
syn match	vimSyncLinebreak	contained	"\<linebreaks="		nextgroup=vimNumber
syn keyword	vimSyncLinecont	contained	linecont	skipwhite	nextgroup=vimSynRegPat
syn match	vimSyncLines	contained	"\<lines="		nextgroup=vimNumber
syn match	vimSyncLines	contained	"\<minlines="		nextgroup=vimNumber
syn match	vimSyncLines	contained	"\<maxlines="		nextgroup=vimNumber
syn match	vimSyncGroupName	contained	"\<\h\w*\>"	skipwhite	nextgroup=vimSyncKey
syn match	vimSyncKey	contained	"\<grouphere\>"	skipwhite	nextgroup=vimSyncGroup
syn match	vimSyncKey	contained	"\<groupthere\>"	skipwhite	nextgroup=vimSyncGroup
syn match	vimSyncGroup	contained	"\<\h\w*\>"	skipwhite	nextgroup=vimSynRegPat,vimSyncNone
syn keyword	vimSyncNone	contained	NONE

" Additional IsCommand: here by reasons of precedence {{{2
" ====================
syn match	vimIsCommand	"<Bar>\s*\a\+"	transparent contains=vimCommand,vimNotation

" Highlighting: {{{2
" ============
syn cluster	vimHighlightCluster		contains=vimHiLink,vimHiClear,vimHiKeyList,@vimComment
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_novimhictermerror")
 syn match	vimHiCtermError	contained	"\D\i*"
endif
syn match	vimHighlight	"\<hi\%[ghlight]\>"	skipwhite nextgroup=vimHiBang,@vimHighlightCluster
syn match	vimHiBang	contained	"\a\@1<=!"	skipwhite nextgroup=@vimHighlightCluster

syn match	vimHiGroup	contained	"\i\+"
syn case ignore
syn keyword	vimHiNone	contained	NONE
syn keyword	vimHiAttrib	contained	none bold inverse italic nocombine reverse standout strikethrough underline undercurl underdashed underdotted underdouble
syn keyword	vimFgBgAttrib	contained	none bg background fg foreground
syn case match
syn match	vimHiAttribList	contained	"\i\+"	contains=vimHiAttrib
syn match	vimHiAttribList	contained	"\i\+,"he=e-1	contains=vimHiAttrib nextgroup=vimHiAttribList
syn case ignore
syn keyword	vimHiCtermColor	contained	black blue brown cyan darkblue darkcyan darkgray darkgreen darkgrey darkmagenta darkred darkyellow gray green grey grey40 grey50 grey90 lightblue lightcyan lightgray lightgreen lightgrey lightmagenta lightred lightyellow magenta red seagreen white yellow
syn match	vimHiCtermColor	contained	"\<color\d\{1,3}\>"
syn case match

syn match	vimHiFontname	contained	"[a-zA-Z\-*]\+"
syn match	vimHiGuiFontname	contained	"'[a-zA-Z\-* ]\+'"
syn match	vimHiGuiRgb	contained	"#\x\{6}"

" Highlighting: hi group key=arg ... {{{2
syn cluster	vimHiCluster contains=vimGroup,vimHiGroup,vimHiNone,vimHiTerm,vimHiCTerm,vimHiStartStop,vimHiCtermFgBg,vimHiCtermul,vimHiCtermfont,vimHiGui,vimHiGuiFont,vimHiGuiFgBg,vimHiKeyError,vimNotation,vimComment,vim9comment
syn region	vimHiKeyList	contained 	start="\i\+" skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|" excludenl end="$" contains=@vimContinue,@vimHiCluster
if !exists("g:vimsyn_noerror") && !exists("g:vimsyn_vimhikeyerror")
 syn match	vimHiKeyError	contained	"\i\+="he=e-1
endif
syn match	vimHiTerm	contained	"\cterm="he=e-1		nextgroup=vimHiAttribList
syn match	vimHiStartStop	contained	"\c\%(start\|stop\)="he=e-1	nextgroup=vimHiTermcap,vimOption
syn match	vimHiCTerm	contained	"\ccterm="he=e-1		nextgroup=vimHiAttribList
syn match	vimHiCtermFgBg	contained	"\ccterm[fb]g="he=e-1	nextgroup=vimHiNmbr,vimHiCtermColor,vimFgBgAttrib,vimHiCtermError
syn match	vimHiCtermul	contained	"\cctermul="he=e-1	nextgroup=vimHiNmbr,vimHiCtermColor,vimFgBgAttrib,vimHiCtermError
syn match	vimHiCtermfont	contained	"\cctermfont="he=e-1	nextgroup=vimHiNmbr,vimHiCtermColor,vimFgBgAttrib,vimHiCtermError
syn match	vimHiGui	contained	"\cgui="he=e-1		nextgroup=vimHiAttribList
syn match	vimHiGuiFont	contained	"\cfont="he=e-1		nextgroup=vimHiFontname
syn match	vimHiGuiFgBg	contained	"\cgui\%([fb]g\|sp\)="he=e-1	nextgroup=vimHiGroup,vimHiGuiFontname,vimHiGuiRgb,vimFgBgAttrib
syn match	vimHiTermcap	contained	"\S\+"		contains=vimNotation
syn match	vimHiNmbr	contained	'\d\+'

" Highlight: clear {{{2
syn keyword	vimHiClear	contained	clear	skipwhite nextgroup=vimGroup,vimHiGroup

" Highlight: link {{{2
" see tst24 (hi def vs hi) (Jul 06, 2018)
"syn region	vimHiLink	contained oneline matchgroup=vimCommand start="\(\<hi\%[ghlight]\s\+\)\@<=\(\(def\%[ault]\s\+\)\=link\>\|\<def\>\)" end="$"	contains=vimHiGroup,vimGroup,vimHLGroup,vimNotation
" TODO: simplify and allow line continuations --djk
syn region	vimHiLink	contained matchgroup=Type start="\%(\<hi\%[ghlight]!\=\s\+\)\@<=\%(\%(def\%[ault]\s\+\)\=link\>\|\<def\%[ault]\>\)" skip=+\\\\\|\\|\|\n\s*\\\|\n\s*"\\ + matchgroup=vimCmdSep end="|" excludenl end="$" contains=@vimContinue,@vimHiCluster

" Control Characters: {{{2
" ==================
syn match	vimCtrlChar	"[--]"

" Beginners - Patterns that involve ^ {{{2
" =========
Vim9 syn region	vim9LineComment	start=+^[ \t:]*\zs#.*$+ skip=+\n\s*\\\|\n\s*#\\ + end="$" contains=@vimCommentGroup,vimCommentString,vim9CommentTitle extend
VimL syn region	vimLineComment	start=+^[ \t:]*\zs".*$+ skip=+\n\s*\\\|\n\s*"\\ + end="$" contains=@vimCommentGroup,vimCommentString,vimCommentTitle extend

syn match	vimCommentTitle	'"\s*\%([sS]:\|\h\w*#\)\=\u\w*\(\s\+\u\w*\)*:'hs=s+1	contained contains=vimCommentTitleLeader,vimTodo,@vimCommentGroup
syn match	vim9CommentTitle	'#\s*\%([sS]:\|\h\w*#\)\=\u\w*\(\s\+\u\w*\)*:'hs=s+1	contained contains=vim9CommentTitleLeader,vimTodo,@vimCommentGroup

" allowed anywhere in the file
if !s:vim9script
  syn match	vimShebangError	"^\s*\zs#!.*" display
endif
syn match	vimShebang	"\%^#!.*" display

syn match	vimContinue		"^\s*\zs\\"
syn match	vimContinueComment	'^\s*\zs["#]\\ .*' extend
syn match	vim9ContinueComment	"^\s*\zs#\\ .*"	 extend
syn cluster	vimContinue	contains=vimContinue,vimContinueComment
syn cluster	vim9Continue	contains=vimContinue,vim9ContinueComment

syn region	vimString	start='^\s*\\"' end='"' oneline keepend contains=@vimStringGroup,vimContinue
syn region	vimString	start="^\s*\\'" end="'" oneline keepend contains=vimQuoteEscape,vimContinue

syn match	vimCommentTitleLeader	'"\s\+'ms=s+1	contained
syn match	vim9CommentTitleLeader	'#\s\+'ms=s+1	contained

" Searches And Globals: {{{2
" ====================
VimL syn match	vimSearch	'^\s*[/?].*'		contains=vimSearchDelim
syn match	vimSearchDelim	'^\s*\zs[/?]\|[/?]$'	contained
Vim9 syn match	vim9Search	'^\s*:[/?].*'		contains=vim9SearchDelim
syn match	vim9SearchDelim	'^\s*\zs:[/?]\|[/?]$'	contained contains=vimCmdSep
syn region	vimGlobal	matchgroup=Statement start='\<g\%[lobal]!\=/'  skip='\\.' end='/'	skipwhite nextgroup=vimSubst1
syn region	vimGlobal	matchgroup=Statement start='\<v\%[global]!\=/' skip='\\.' end='/'	skipwhite nextgroup=vimSubst1

" Vim9 script Regions: {{{2
" ==================

if s:vim9script
  syn cluster vimLegacyTop	contains=TOP,vim9LegacyHeader,vim9Comment,vim9LineComment
  VimFoldH syn region vim9LegacyHeader start="\%^" end="^\ze\s*vim9s\%[cript]\>" contains=@vimLegacyTop,vimComment,vimLineComment

  syn keyword vim9Vim9ScriptArg	noclear contained
  syn keyword vim9Vim9Script	vim9s[cript] nextgroup=vim9Vim9ScriptArg skipwhite
endif

" Embedded Scripts:  {{{2
" ================
"   perl,ruby     : Benoit Cerrina
"   python,tcl    : Johannes Zellner
"   mzscheme, lua : Charles Campbell

" Allows users to specify the type of embedded script highlighting
" they want:  (perl/python/ruby/tcl support)
"   g:vimsyn_embed == 0   : don't embed any scripts
"   g:vimsyn_embed =~# 'l' : embed Lua      (but only if vim supports it)
"   g:vimsyn_embed =~# 'm' : embed MzScheme (but only if vim supports it)
"   g:vimsyn_embed =~# 'p' : embed Perl     (but only if vim supports it)
"   g:vimsyn_embed =~# 'P' : embed Python   (but only if vim supports it)
"   g:vimsyn_embed =~# 'r' : embed Ruby     (but only if vim supports it)
"   g:vimsyn_embed =~# 't' : embed Tcl      (but only if vim supports it)
if !exists("g:vimsyn_embed")
 let g:vimsyn_embed= "lmpPr"
endif

" [-- lua --] {{{3
let s:luapath= fnameescape(expand("<sfile>:p:h")."/lua.vim")
if !filereadable(s:luapath)
 for s:luapath in split(globpath(&rtp,"syntax/lua.vim"),"\n")
  if filereadable(fnameescape(s:luapath))
   let s:luapath= fnameescape(s:luapath)
   break
  endif
 endfor
endif
if (g:vimsyn_embed =~# 'l' && has("lua")) && filereadable(s:luapath)
 unlet! b:current_syntax
 syn cluster vimFuncBodyList	add=vimLuaRegion
 exe "syn include @vimLuaScript ".s:luapath
 VimFoldl syn region vimLuaRegion matchgroup=vimScriptDelim start=+^\z(\s*\)lua\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+	contains=@vimLuaScript
 VimFoldl syn region vimLuaRegion matchgroup=vimScriptDelim start=+lua\s*<<\s*\z(\S*\)+ end=+^\z1$+		contains=@vimLuaScript
 VimFoldl syn region vimLuaRegion matchgroup=vimScriptDelim start=+^\z(\s*\)lua\s*<<\s*trim\s*$+ end=+^\z1\.$+	contains=@vimLuaScript
 VimFoldl syn region vimLuaRegion matchgroup=vimScriptDelim start=+lua\s*<<\s*$+ end=+^\.$+		contains=@vimLuaScript
 syn cluster vimFuncBodyList	add=vimLuaRegion
else
 syn region vimEmbedError start=+^\z(\s*\)lua\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
 syn region vimEmbedError start=+lua\s*<<\s*\z(\S*\)+ end=+^\z1$+
 syn region vimEmbedError start=+^\z(\s*\)lua\s*<<\s*trim\s\*$+ end=+^\z1\.$+
 syn region vimEmbedError start=+lua\s*<<\s*$+ end=+^\.$+
endif
unlet s:luapath

" [-- perl --] {{{3
let s:perlpath= fnameescape(expand("<sfile>:p:h")."/perl.vim")
if !filereadable(s:perlpath)
 for s:perlpath in split(globpath(&rtp,"syntax/perl.vim"),"\n")
  if filereadable(fnameescape(s:perlpath))
   let s:perlpath= fnameescape(s:perlpath)
   break
  endif
 endfor
endif
if (g:vimsyn_embed =~# 'p' && has("perl")) && filereadable(s:perlpath)
 unlet! b:current_syntax
 syn cluster vimFuncBodyList	add=vimPerlRegion
 exe "syn include @vimPerlScript ".s:perlpath
 VimFoldp syn region vimPerlRegion	matchgroup=vimScriptDelim start=+^\z(\s*\)pe\%[rl]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+	contains=@vimPerlScript
 VimFoldp syn region vimPerlRegion	matchgroup=vimScriptDelim start=+pe\%[rl]\s*<<\s*\z(\S*\)+ end=+^\z1$+		contains=@vimPerlScript
 VimFoldp syn region vimPerlRegion	matchgroup=vimScriptDelim start=+^\z(\s*\)pe\%[rl]\s*<<\s*trim\s*$+ end=+^\z1\.$+	contains=@vimPerlScript
 VimFoldp syn region vimPerlRegion	matchgroup=vimScriptDelim start=+pe\%[rl]\s*<<\s*$+ end=+\.$+		contains=@vimPerlScript
 syn cluster vimFuncBodyList	add=vimPerlRegion
else
 syn region vimEmbedError start=+^\z(\s*\)pe\%[rl]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
 syn region vimEmbedError start=+pe\%[rl]\s*<<\s*\z(\S*\)+ end=+^\z1$+
 syn region vimEmbedError start=+^\z(\s*\)pe\%[rl]\s*<<\s*trim\s\*$+ end=+^\z1\.$+
 syn region vimEmbedError start=+pe\%[rl]\s*<<\s*$+ end=+^\.$+
endif
unlet s:perlpath

" [-- ruby --] {{{3
let s:rubypath= fnameescape(expand("<sfile>:p:h")."/ruby.vim")
if !filereadable(s:rubypath)
 for s:rubypath in split(globpath(&rtp,"syntax/ruby.vim"),"\n")
  if filereadable(fnameescape(s:rubypath))
   let s:rubypath= fnameescape(s:rubypath)
   break
  endif
 endfor
endif
if (g:vimsyn_embed =~# 'r' && has("ruby")) && filereadable(s:rubypath)
 syn cluster vimFuncBodyList	add=vimRubyRegion
 unlet! b:current_syntax
 exe "syn include @vimRubyScript ".s:rubypath
 VimFoldr syn region vimRubyRegion	matchgroup=vimScriptDelim start=+^\z(\s*\)rub\%[y]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+	contains=@vimRubyScript
 VimFoldr syn region vimRubyRegion	matchgroup=vimScriptDelim start=+rub\%[y]\s*<<\s*\z(\S*\)+ end=+^\z1$+		contains=@vimRubyScript
 VimFoldr syn region vimRubyRegion	matchgroup=vimScriptDelim start=+^\z(\s*\)rub\%[y]\s*<<\s*trim\s*$+ end=+^\z1\.$+	contains=@vimRubyScript
 VimFoldr syn region vimRubyRegion	matchgroup=vimScriptDelim start=+rub\%[y]\s*<<\s*$+ end=+\.$+		contains=@vimRubyScript
 syn cluster vimFuncBodyList	add=vimRubyRegion
else
 syn region vimEmbedError start=+^\z(\s*\)rub\%[y]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
 syn region vimEmbedError start=+rub\%[y]\s*<<\s*\z(\S.*\)+ end=+^\z1$+
 syn region vimEmbedError start=+^\z(\s*\)rub\%[y]\s*<<\s*trim\s\*$+ end=+^\z1\.$+
 syn region vimEmbedError start=+rub\%[y]\s*<<\s*$+ end=+^\.$+
endif
unlet s:rubypath

" [-- python --] {{{3
let s:pythonpath= fnameescape(expand("<sfile>:p:h")."/python.vim")
if !filereadable(s:pythonpath)
 for s:pythonpath in split(globpath(&rtp,"syntax/python.vim"),"\n")
  if filereadable(fnameescape(s:pythonpath))
   let s:pythonpath= fnameescape(s:pythonpath)
   break
  endif
 endfor
endif
if g:vimsyn_embed =~# 'P' && has("pythonx") && filereadable(s:pythonpath)
 unlet! b:current_syntax
 syn cluster vimFuncBodyList	add=vimPythonRegion
 exe "syn include @vimPythonScript ".s:pythonpath
 VimFoldP syn region vimPythonRegion matchgroup=vimScriptDelim start=+^\z(\s*\)py\%[thon][3x]\=\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+	contains=@vimPythonScript
 VimFoldP syn region vimPythonRegion matchgroup=vimScriptDelim start=+py\%[thon][3x]\=\s*<<\s*\z(\S\+\)+ end=+^\z1$+		contains=@vimPythonScript
 VimFoldP syn region vimPythonRegion matchgroup=vimScriptDelim start=+^\z(\s*\)py\%[thon][3x]\=\s*<<\s*trim\s*$+ end=+^\z1\.$+	contains=@vimPythonScript
 VimFoldP syn region vimPythonRegion matchgroup=vimScriptDelim start=+py\%[thon][3x]\=\s*<<\s*$+ end=+^\.$+			contains=@vimPythonScript
 VimFoldP syn region vimPythonRegion matchgroup=vimScriptDelim start=+Py\%[thon]2or3\s*<<\s*\%(trim\s*\)\=\z(\S\+\)+ end=+^\z1$+	contains=@vimPythonScript
 VimFoldP syn region vimPythonRegion matchgroup=vimScriptDelim start=+Py\%[thon]2or3\s*<<\s*\%(trim\s*\)\=$+ end=+^\.$+		contains=@vimPythonScript
 syn cluster vimFuncBodyList	add=vimPythonRegion
else
 syn region vimEmbedError start=+^\z(\s*\)py\%[thon][3x]\=\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
 syn region vimEmbedError start=+py\%[thon][3x]\=\s*<<\s*\z(\S\+\)+ end=+^\z1$+
 syn region vimEmbedError start=+^\z(\s*\)py\%[thon][3x]\=\s*<<\s*trim\s*$+ end=+^\z1\.$+
 syn region vimEmbedError start=+py\%[thon][3x]\=\s*<<\s*$+ end=+^\.$+
endif
unlet s:pythonpath

" [-- tcl --] {{{3
if has("win32") || has("win95") || has("win64") || has("win16")
 " apparently has("tcl") has been hanging vim on some windows systems with cygwin
 let s:trytcl= (&shell !~ '\<\%(bash\>\|4[nN][tT]\|\<zsh\)\>\%(\.exe\)\=$')
else
 let s:trytcl= 1
endif
if s:trytcl
 let s:tclpath= fnameescape(expand("<sfile>:p:h")."/tcl.vim")
 if !filereadable(s:tclpath)
  for s:tclpath in split(globpath(&rtp,"syntax/tcl.vim"),"\n")
   if filereadable(fnameescape(s:tclpath))
    let s:tclpath= fnameescape(s:tclpath)
    break
   endif
  endfor
 endif
 if (g:vimsyn_embed =~# 't' && has("tcl")) && filereadable(s:tclpath)
  unlet! b:current_syntax
  syn cluster vimFuncBodyList	add=vimTclRegion
  exe "syn include @vimTclScript ".s:tclpath
  VimFoldt syn region vimTclRegion matchgroup=vimScriptDelim start=+^\z(\s*\)tc\%[l]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+	contains=@vimTclScript
  VimFoldt syn region vimTclRegion matchgroup=vimScriptDelim start=+tc\%[l]\=\s*<<\s*\z(\S*\)+ end=+^\z1$+		contains=@vimTclScript
  VimFoldt syn region vimTclRegion matchgroup=vimScriptDelim start=+^\z(\s*\)tc\%[l]\s*<<\s*trim\s*$+ end=+^\z1\.$+	contains=@vimTclScript
  VimFoldt syn region vimTclRegion matchgroup=vimScriptDelim start=+tc\%[l]\=\s*<<\s*$+ end=+^\.$+		contains=@vimTclScript
  syn cluster vimFuncBodyList	add=vimTclScript
 else
  syn region vimEmbedError start=+^\z(\s*\)tc\%[l]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
  syn region vimEmbedError start=+tc\%[l]\=\s*<<\s*\z(\S*\)+ end=+^\z1$+
  syn region vimEmbedError start=+^\z(\s*\)tc\%[l]\s*<<\s*trim\s\*$+ end=+^\z1\.$+
  syn region vimEmbedError start=+tc\%[l]\=\s*<<\s*$+ end=+^\.$+
 endif
 unlet s:tclpath
else
 syn region vimEmbedError start=+^\z(\s*\)tc\%[l]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
 syn region vimEmbedError start=+tc\%[l]\=\s*<<\s*\z(\S*\)+ end=+^\z1$+
 syn region vimEmbedError start=+^\z(\s*\)tc\%[l]\s*<<\s*trim\s\*$+ end=+^\z1\.$+
 syn region vimEmbedError start=+tc\%[l]\=\s*<<\s*$+ end=+^\.$+
endif
unlet s:trytcl

" [-- mzscheme --] {{{3
let s:mzschemepath= fnameescape(expand("<sfile>:p:h")."/scheme.vim")
if !filereadable(s:mzschemepath)
 for s:mzschemepath in split(globpath(&rtp,"syntax/mzscheme.vim"),"\n")
  if filereadable(fnameescape(s:mzschemepath))
   let s:mzschemepath= fnameescape(s:mzschemepath)
   break
  endif
 endfor
endif
if (g:vimsyn_embed =~# 'm' && has("mzscheme")) && filereadable(s:mzschemepath)
 unlet! b:current_syntax
 let s:iskKeep= &isk
 syn cluster vimFuncBodyList	add=vimMzSchemeRegion
 exe "syn include @vimMzSchemeScript ".s:mzschemepath
 let &isk= s:iskKeep
 unlet s:iskKeep
 VimFoldm syn region vimMzSchemeRegion matchgroup=vimScriptDelim start=+^\z(\s*\)mz\%[scheme]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+	contains=@vimMzSchemeScript
 VimFoldm syn region vimMzSchemeRegion matchgroup=vimScriptDelim start=+mz\%[scheme]\s*<<\s*\z(\S*\)+ end=+^\z1$+	contains=@vimMzSchemeScript
 VimFoldm syn region vimMzSchemeRegion matchgroup=vimScriptDelim start=+^\z(\s*\)mz\%[scheme]\s*<<\s*trim\s*$+ end=+^\z1\.$+	contains=@vimMzSchemeScript
 VimFoldm syn region vimMzSchemeRegion matchgroup=vimScriptDelim start=+mz\%[scheme]\s*<<\s*$+ end=+^\.$+		contains=@vimMzSchemeScript
 syn cluster vimFuncBodyList	add=vimMzSchemeRegion
else
 syn region vimEmbedError start=+^\z(\s*\)mz\%[scheme]\s*<<\s*trim\s\+\z(\S\+\)+ end=+^\z1\z2$+
 syn region vimEmbedError start=+mz\%[scheme]\s*<<\s*\z(\S*\)+ end=+^\z1$+
 syn region vimEmbedError start=+^\z(\s*\)mz\%[scheme]\s*<<\s*trim\s\*$+ end=+^\z1\.$+
 syn region vimEmbedError start=+mz\%[scheme]\s*<<\s*$+ end=+^\.$+
endif
unlet s:mzschemepath

" Synchronize (speed) {{{2
"============
if exists("g:vimsyn_minlines")
 exe "syn sync minlines=".g:vimsyn_minlines
endif
exe "syn sync maxlines=".s:vimsyn_maxlines
syn sync linecont	"^\s\+\\"
syn sync linebreaks=1
syn sync match vimAugroupSyncA	groupthere NONE	"\<aug\%[roup]\>\s\+[eE][nN][dD]"

" ====================
" Highlighting Settings {{{2
" ====================

if !exists("skip_vim_syntax_inits")
 if !exists("g:vimsyn_noerror")
  hi def link vimBehaveError	vimError
  hi def link vimCollClassErr	vimError
  hi def link vimErrSetting	vimError
  hi def link vimEmbedError	vimError
  hi def link vimFTError	vimError
  hi def link vimFunctionError	vimError
  hi def link vimFunc         	vimError
  hi def link vimHiAttribList	vimError
  hi def link vimHiCtermError	vimError
  hi def link vimHiKeyError	vimError
  hi def link vimMapModErr	vimError
  hi def link vimShebangError	vimError
  hi def link vimSortOptionsError	Error
  hi def link vimSubstFlagErr	vimError
  hi def link vimSynCaseError	vimError
  hi def link vimSyncError	vimError
  hi def link vimSynConcealError	vimError
  hi def link vimSynError	vimError
  hi def link vimSynFoldlevelError	vimError
  hi def link vimSynIskeywordError	vimError
  hi def link vimSynSpellError	vimError
  hi def link vimBufnrWarn	vimWarn

  hi def link vim9TypeAliasError	vimError
 endif

 hi def link vimAbb	vimCommand
 hi def link vimAddress	vimMark
 hi def link vimAugroupBang	vimBang
 hi def link vimAugroupError	vimError
 hi def link vimAugroupKey	vimCommand
 hi def link vimAutocmd	vimCommand
 hi def link vimAutocmdBang	vimBang
 hi def link vimAutocmdPatternEscape	Special
 hi def link vimAutoEvent	Type
 hi def link vimAutoEventGlob	Type
 hi def link vimAutocmdBufferPattern	Special
 hi def link vimAutocmdMod	Special
 hi def link vimAutocmdPatternSep	vimSep
 hi def link vimBang	vimOper
 hi def link vimBehaveBang	vimBang
 hi def link vimBehaveModel	vimBehave
 hi def link vimBehave	vimCommand
 hi def link vimBracket	Delimiter
 hi def link vimCall	vimCommand
 hi def link vimCatch	vimCommand
 hi def link vimCmplxRepeat	SpecialChar
 hi def link vimCommand	Statement
 hi def link vimComment	Comment
 hi def link vimCommentError	vimError
 hi def link vimCommentString	vimString
 hi def link vimCommentTitle	PreProc
 hi def link vimCondHL	vimCommand
 hi def link vimConst	vimCommand
 hi def link vimContinue	Special
 hi def link vimContinueComment	vimComment
 hi def link vimContinueString	vimString
 hi def link vimCount	Number
 hi def link vimCtrlChar	SpecialChar
 hi def link vimDebuggreedy	vimCommand
 hi def link vimDefComment	vim9Comment
 hi def link vimDefKey	vimCommand
 hi def link vimDefParam	vimVar
 hi def link vimDelcommand	vimCommand
 hi def link vimDelcommandAttr	vimUserCmdAttr
 hi def link vimDoautocmd	vimCommand
 hi def link vimDoautocmdMod	Special
 hi def link vimEcho	vimCommand
 hi def link vimEchohlNone	vimGroup
 hi def link vimEchohl	vimCommand
 hi def link vimElse	vimCommand
 hi def link vimElseIfErr	Error
 hi def link vimEndfunction	vimCommand
 hi def link vimEnddef	vimCommand
 hi def link vimEndif	vimCommand
 hi def link vimEnvvar	PreProc
 hi def link vimError	Error
 hi def link vimEscape	Special
 hi def link vimExFilter	vimCommand
 hi def link vimExFilterBang	vimCommand
 hi def link vimFBVar	vimVar
 hi def link vimFgBgAttrib	vimHiAttrib
 hi def link vimFuncEcho	vimCommand
 hi def link vimFor	vimCommand
 hi def link vimForInContinue	vimContinue
 hi def link vimForInContinueComment	vimContinueComment
 hi def link vimFTCmd	vimCommand
 hi def link vimFTOption	vimSynType
 hi def link vimFuncBang	vimBang
 hi def link vimFuncComment	vimComment
 hi def link vimFuncKey	vimCommand
 hi def link vimFuncName	Function
 hi def link vimFuncMod	Special
 hi def link vimFuncParam	vimVar
 hi def link vimFuncParamEquals	vimOper
 hi def link vimFuncScope	vimVarScope
 hi def link vimFuncSID	vimNotation
 hi def link vimGrep	vimCommand
 hi def link vimGrepadd	vimCommand
 hi def link vimGrepBang	vimBang
 hi def link vimGroupAdd	vimSynOption
 hi def link vimGroupName	Normal
 hi def link vimGroupRem	vimSynOption
 hi def link vimGroupSpecial	Special
 hi def link vimGroup	Type
 hi def link vimHelpgrep	vimCommand
 hi def link vimHiAttrib	PreProc
 hi def link vimHiBang	vimBang
 hi def link vimHiClear	Type
 hi def link vimHiCtermColor	Constant
 hi def link vimHiCtermFgBg	vimHiTerm
 hi def link vimHiCtermfont	vimHiTerm
 hi def link vimHiCtermul	vimHiTerm
 hi def link vimHiCTerm	vimHiTerm
 hi def link vimHighlight	vimCommand
 hi def link vimHiGroup	vimGroupName
 hi def link vimHiGuiFgBg	vimHiTerm
 hi def link vimHiGuiFont	vimHiTerm
 hi def link vimHiGuiRgb	vimNumber
 hi def link vimHiGui	vimHiTerm
 hi def link vimHiNmbr	Number
 hi def link vimHiNone	vimGroup
 hi def link vimHiStartStop	vimHiTerm
 hi def link vimHiTerm	Type
 hi def link vimHLGroup	vimGroup
 hi def link vimInsert	vimString
 hi def link vim9KeymapLineComment	vimKeymapLineComment
 hi def link vimKeymapLineComment	vimComment
 hi def link vimKeymapTailComment	vimComment
 hi def link vimLambdaBrace	Delimiter
 hi def link vimLambdaOperator	vimOper
 hi def link vimLet	vimCommand
 hi def link vimLetHereDoc	vimString
 hi def link vimLetHereDocStart	Special
 hi def link vimLetHereDocStop	Special
 hi def link vimLetRegister	vimRegister
 hi def link vimLineComment	vimComment
 hi def link vimMake	vimCommand
 hi def link vimMakeadd	vimCommand
 hi def link vimMakeBang	vimBang
 hi def link vimMapBang	vimBang
 hi def link vimMapLeader	vimBracket
 hi def link vimMapLeaderKey	vimNotation
 hi def link vimMapModKey	vimFuncSID
 hi def link vimMapMod	vimBracket
 hi def link vimMap	vimCommand
 hi def link vimMark	Number
 hi def link vimMarkNumber	vimNumber
 hi def link vimMatch	vimCommand
 hi def link vimMatchGroup	vimGroup
 hi def link vimMatchNone	vimGroup
 hi def link vimMenuBang	vimBang
 hi def link vimMenuClear	Special
 hi def link vimMenuMod	vimMapMod
 hi def link vimMenuName	PreProc
 hi def link vimMenu	vimCommand
 hi def link vimMenuNotation	vimNotation
 hi def link vimMenuPriority	Number
 hi def link vimMenuStatus	Special
 hi def link vimMenutranslateComment	vimComment
 hi def link vim9MethodName	vimFuncName
 hi def link vimMtchComment	vimComment
 hi def link vimNormal	vimCommand
 hi def link vimNotation	Special
 hi def link vimNotFunc	vimCommand
 hi def link vimNotPatSep	vimString
 hi def link vimNumber	Number
 hi def link vimOperError	Error
 hi def link vimOper	Operator
 hi def link vimOperContinue	vimContinue
 hi def link vimOperContinueComment	vimContinueComment
 hi def link vimOption	PreProc
 hi def link vimOptionVar	Identifier
 hi def link vimOptionVarName	Identifier
 hi def link vimParenSep	Delimiter
 hi def link vimPatSepErr	vimError
 hi def link vimPatSepR	vimPatSep
 hi def link vimPatSep	SpecialChar
 hi def link vimPatSepZone	vimString
 hi def link vimPatSepZ	vimPatSep
 hi def link vimPattern	Type
 hi def link vimPlainMark	vimMark
 hi def link vimPlainRegister	vimRegister
 hi def link vimQuoteEscape	vimEscape
 hi def link vimRedir	vimCommand
 hi def link vimRedirBang	vimBang
 hi def link vimRedirFileOperator	vimOper
 hi def link vimRedirRegisterOperator	vimOper
 hi def link vimRedirVariableOperator	vimOper
 hi def link vimRedirEnd	Special
 hi def link vimRedirRegister	vimRegister
 hi def link vimRegister	SpecialChar
 hi def link vimScriptDelim	Comment
 hi def link vimSearch	vimString
 hi def link vimSearchDelim	Delimiter
 hi def link vimSep	Delimiter
 hi def link vimSet	vimCommand
 hi def link vimSetAll	vimOption
 hi def link vimSetBang	vimBang
 hi def link vimSetComment	vimComment
 hi def link vimSetMod	vimOption
 hi def link vimSetSep	vimSep
 hi def link vimSetTermcap	vimOption
 hi def link vimShebang	PreProc
 hi def link vimSleep	vimCommand
 hi def link vimSleepArg	Constant
 hi def link vimSleepBang	vimBang
 hi def link vimSort	vimCommand
 hi def link vimSortBang	vimBang
 hi def link vimSortOptions	Special
 hi def link vimSpecFile	Identifier
 hi def link vimSpecFileMod	vimSpecFile
 hi def link vimSpecial	Type
 hi def link vimStringCont	vimString
 hi def link vimString	String
 hi def link vimStringEnd	vimString
 hi def link vimStringInterpolationBrace	vimEscape
 hi def link vimSubst1	vimSubst
 hi def link vimSubstDelim	Delimiter
 hi def link vimSubstFlags	Special
 hi def link vimSubstSubstr	SpecialChar
 hi def link vimSubstTwoBS	vimString
 hi def link vimSubst	vimCommand
 hi def link vimSynCase	Type
 hi def link vimSyncCcomment	Type
 hi def link vimSynCchar	vimSynOption
 hi def link vimSynCcharValue	Character
 hi def link vimSyncClear	Type
 hi def link vimSyncFromstart	Type
 hi def link vimSyncGroup	vimGroupName
 hi def link vimSyncGroupName	vimGroupName
 hi def link vimSyncKey	Type
 hi def link vimSyncLinebreak	Type
 hi def link vimSyncLinecont	Type
 hi def link vimSyncLines	Type
 hi def link vimSyncMatch	Type
 hi def link vimSyncNone	Type
 hi def link vimSynConceal	Type
 hi def link vimSynContains	vimSynOption
 hi def link vimSyncRegion	Type
 hi def link vimSynFoldlevel	Type
 hi def link vimSynIskeyword	Type
 hi def link vimSynIskeywordSep	Delimiter
 hi def link vimSynKeyContainedin	vimSynContains
 hi def link vimSynKeyOpt	vimSynOption
 hi def link vimSynMtchGrp	vimSynOption
 hi def link vimSynMtchOpt	vimSynOption
 hi def link vimSynNextgroup	vimSynOption
 hi def link vimSynNotPatRange	vimSynRegPat
 hi def link vimSynOption	Special
 hi def link vimSynPatRange	vimString
 hi def link vimSynReg	Type
 hi def link vimSynRegOpt	vimSynOption
 hi def link vimSynRegPat	vimString
 hi def link vimSynSpell	Type
 hi def link vimSyntax	vimCommand
 hi def link vimSynType	vimSpecial
 hi def link vimThrow	vimCommand
 hi def link vimTodo	Todo
 hi def link vimType	Type
 hi def link vimUnlet	vimCommand
 hi def link vimUnletBang	vimBang
 hi def link vimUnmap	vimMap
 hi def link vimUserCmd	vimCommand
 hi def link vimUserCmdAttrAddr	vimSpecial
 hi def link vimUserCmdAttrComplete	vimSpecial
 hi def link vimUserCmdAttrCompleteFunc	vimVar
 hi def link vimUserCmdAttrNargs	vimSpecial
 hi def link vimUserCmdAttrRange	vimSpecial
 hi def link vimUserCmdAttrKey	vimUserCmdAttr
 hi def link vimUserCmdAttr	Special
 hi def link vimUserCmdAttrError	Error
 hi def link vimUserCmdError	Error
 hi def link vimUserCmdKey	vimCommand
 hi def link vimUserFunc	Normal
 hi def link vimVar	Normal
 hi def link vimVarScope	Identifier
 hi def link vimVimgrep	vimCommand
 hi def link vimVimgrepadd	vimCommand
 hi def link vimVimgrepBang	vimBang
 hi def link vimVimgrepFlags	Special
 hi def link vimVimVar	Identifier
 hi def link vimVimVarName	Identifier
 hi def link vimWarn	WarningMsg
 hi def link vimWildcard	Special
 hi def link vimWildcardBraceComma	vimWildcard
 hi def link vimWildcardBracket	vimWildcard
 hi def link vimWildcardBracketCaret	vimWildcard
 hi def link vimWildcardBracketCharacter	Normal
 hi def link vimWildcardBracketCharacter	Normal
 hi def link vimWildcardBracketCharacterClass	vimWildCard
 hi def link vimWildcardBracketCollatingSymbol	vimWildCard
 hi def link vimWildcardBracketEnd		vimWildcard
 hi def link vimWildcardBracketEquivalenceClass	vimWildCard
 hi def link vimWildcardBracketEscape	vimWildcard
 hi def link vimWildcardBracketHyphen	vimWildcard
 hi def link vimWildcardBracketRightBracket	vimWildcardBracketCharacter
 hi def link vimWildcardBracketStart	vimWildcard
 hi def link vimWildcardEscape	vimWildcard
 hi def link vimWildcardInterval	vimWildcard
 hi def link vimWildcardQuestion	vimWildcard
 hi def link vimWildcardStar	vimWildcard

 hi def link vim9Abstract	vimCommand
 hi def link vim9Boolean	Boolean
 hi def link vim9Class	vimCommand
 hi def link vim9Comment	Comment
 hi def link vim9CommentError	vimError
 hi def link vim9CommentTitle	PreProc
 hi def link vim9ConstructorDefParam	vimVar
 hi def link vim9Const	vimCommand
 hi def link vim9ContinueComment	vimContinueComment
 hi def link vim9Enum	vimCommand
 hi def link vim9EnumImplementedInterfaceComment	vim9Comment
 hi def link vim9EnumImplements	vim9Implements
 hi def link vim9EnumNameComment	vim9Comment
 hi def link vim9EnumNameContinue	vimContinue
 hi def link vim9EnumNameContinueComment	vim9Comment
 hi def link vim9EnumValueListCommaComment	vim9Comment
 hi def link vim9Export	vimCommand
 hi def link vim9Extends	Keyword
 hi def link vim9Final	vimCommand
 hi def link vim9For	vimCommand
 hi def link vim9ForInComment	vim9Comment
 hi def link vim9Implements	Keyword
 hi def link vim9AbstractDef	vimCommand
 hi def link vim9Interface	vimCommand
 hi def link vim9LambdaOperator	vimOper
 hi def link vim9LambdaOperatorComment	vim9Comment
 hi def link vim9LambdaParen	vimParenSep
 hi def link vim9LhsRegister	vimLetRegister
 hi def link vim9LhsVariable	vimVar
 hi def link vim9LineComment	vimComment
 hi def link vim9MethodDef	vimCommand
 hi def link vim9MethodDefComment	vimDefComment
 hi def link vim9MethodNameError	vimFunctionError
 hi def link vim9Null	Constant
 hi def link vim9Public	vimCommand
 hi def link vim9Search	vimString
 hi def link vim9SearchDelim	Delimiter
 hi def link vim9Static	vimCommand
 hi def link vim9Super	Identifier
 hi def link vim9This	Identifier
 hi def link vim9Type	vimCommand
 hi def link vim9TypeEquals	vimOper
 hi def link vim9Variable	vimVar
 hi def link vim9Var	vimCommand
 hi def link vim9Vim9ScriptArg	Special
 hi def link vim9Vim9Script	vimCommand

 hi def link vimCompilerSet	vimCommand
 hi def link vimSynMenu	vimCommand
 hi def link vimSynMenuPath	vimMenuName
endif

" Current Syntax Variable: {{{2
let b:current_syntax = "vim"

" ---------------------------------------------------------------------
" Cleanup: {{{1
delc Vim9
delc VimL
delc VimFolda
delc VimFoldc
delc VimFolde
delc VimFoldf
delc VimFoldh
delc VimFoldH
delc VimFoldi
delc VimFoldl
delc VimFoldm
delc VimFoldp
delc VimFoldP
delc VimFoldr
delc VimFoldt
let &cpo = s:keepcpo
unlet s:keepcpo s:vim9script
" vim:ts=18 fdm=marker ft=vim
